# Guia de Componentes - DataHub Clinic

## Visão Geral

Este documento descreve todos os componentes reutilizáveis do DataHub Clinic, suas props, uso e exemplos.

## Estrutura de Componentes

```
src/components/
├── common/          # Componentes básicos
├── charts/          # Gráficos e visualizações
├── layout/          # Layout e navegação
└── analytics/       # Componentes de análise
```

## Componentes Common

### KPICard

Componente para exibir KPIs com suporte a trends e categorias.

#### Props
```typescript
interface KPICardProps {
  name: string                    // Nome do KPI
  value: number                   // Valor numérico
  unit: string                    // Unidade (R$, %, etc)
  category: string                // Categoria para cores
  trend_value?: number            // Valor da tendência
  trend_direction?: 'up' | 'down' // Direção da tendência
  description: string             // Descrição para tooltip
}
```

#### Uso
```tsx
<KPICard
  name="Total Atendimentos"
  value={150}
  unit=""
  category="agenda"
  trend_value={8.5}
  trend_direction="up"
  description="Total de consultas realizadas"
/>
```

#### Categorias Disponíveis
- `dashboard`: Azul
- `agenda`: Verde
- `financeiro`: Roxo
- `paciente`: Laranja
- `amigocare`: Rosa

### FilterControls

Componente para controles de filtro reutilizáveis.

#### Props
```typescript
interface FilterControlsProps {
  periodType: string
  onPeriodChange: (period: string) => void
  showDateRange?: boolean
  showExport?: boolean
  onExport?: () => void
}
```

#### Uso
```tsx
<FilterControls
  periodType="last_30_days"
  onPeriodChange={setPeriodType}
  showExport={true}
  onExport={handleExport}
/>
```

### LoadingSpinner

Componente de loading consistente.

#### Props
```typescript
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  color?: string
  text?: string
}
```

#### Uso
```tsx
<LoadingSpinner size="md" text="Carregando dados..." />
```

## Componentes Charts

### LineChart

Gráfico de linha responsivo usando Recharts.

#### Props
```typescript
interface LineChartProps {
  data: Array<any>
  dataKey: string
  color?: string
  height?: number
  showGrid?: boolean
  showTooltip?: boolean
}
```

#### Uso
```tsx
<LineChart
  data={revenueData}
  dataKey="receita"
  color="#059669"
  height={300}
  showGrid={true}
/>
```

### BarChart

Gráfico de barras responsivo.

#### Props
```typescript
interface BarChartProps {
  data: Array<any>
  dataKey: string
  color?: string
  height?: number
  orientation?: 'vertical' | 'horizontal'
}
```

#### Uso
```tsx
<BarChart
  data={ocupacaoData}
  dataKey="atendimentos"
  color="#2563eb"
  height={350}
/>
```

### PieChart

Gráfico de pizza para distribuições.

#### Props
```typescript
interface PieChartProps {
  data: Array<{name: string, value: number}>
  colors?: string[]
  height?: number
  showLabels?: boolean
}
```

#### Uso
```tsx
<PieChart
  data={demografiaData}
  colors={['#2563eb', '#059669', '#dc2626']}
  height={300}
  showLabels={true}
/>
```

## Componentes Layout

### ModuleLayout

Layout base para módulos com breadcrumbs.

#### Props
```typescript
interface ModuleLayoutProps {
  moduleName: string
  moduleIcon: React.ReactNode
  breadcrumbs?: Array<{name: string, path: string}>
}
```

#### Uso
```tsx
<ModuleLayout
  moduleName="Agenda"
  moduleIcon={<Calendar className="w-4 h-4" />}
/>
```

### Sidebar

Navegação lateral da aplicação.

#### Props
```typescript
interface SidebarProps {
  isCollapsed?: boolean
  onToggle?: () => void
}
```

### Header

Cabeçalho da aplicação com navegação.

#### Props
```typescript
interface HeaderProps {
  title?: string
  showUserMenu?: boolean
  showNotifications?: boolean
}
```

## Componentes Analytics

### InsightCard

Card para exibir insights de IA.

#### Props
```typescript
interface InsightCardProps {
  title: string
  description: string
  impact: 'high' | 'medium' | 'low'
  recommendation: string
  confidence: number
}
```

#### Uso
```tsx
<InsightCard
  title="Crescimento de Receita"
  description="Receita cresceu 15% no último mês"
  impact="high"
  recommendation="Manter estratégia atual"
  confidence={0.85}
/>
```

### TrendIndicator

Indicador de tendência com ícone e cor.

#### Props
```typescript
interface TrendIndicatorProps {
  value: number
  direction: 'up' | 'down' | 'stable'
  size?: 'sm' | 'md' | 'lg'
  showValue?: boolean
}
```

#### Uso
```tsx
<TrendIndicator
  value={8.5}
  direction="up"
  size="md"
  showValue={true}
/>
```

## Hooks Customizados

### useNavigation

Hook para navegação e breadcrumbs.

```typescript
const {
  navigate,
  getCurrentModule,
  isActive,
  getBreadcrumbs
} = useNavigation()
```

### useFilters

Hook para gerenciamento de filtros.

```typescript
const {
  periodType,
  setPeriodType,
  dateRange,
  setDateRange,
  resetFilters
} = useFilters()
```

### useApi

Hook para chamadas de API com React Query.

```typescript
const {
  data,
  isLoading,
  error,
  refetch
} = useApi('/dashboard/overview', { periodType })
```

## Padrões de Uso

### Composição de Componentes

```tsx
function DashboardPage() {
  return (
    <div className="space-y-6">
      <FilterControls
        periodType={periodType}
        onPeriodChange={setPeriodType}
      />
      
      <div className="grid grid-cols-4 gap-4">
        {kpis.map(kpi => (
          <KPICard key={kpi.id} {...kpi} />
        ))}
      </div>
      
      <div className="grid grid-cols-2 gap-6">
        <LineChart
          data={revenueData}
          dataKey="receita"
          height={300}
        />
        <BarChart
          data={ocupacaoData}
          dataKey="atendimentos"
          height={300}
        />
      </div>
    </div>
  )
}
```

### Tratamento de Estados

```tsx
function ComponenteComEstados() {
  if (isLoading) {
    return <LoadingSpinner text="Carregando..." />
  }
  
  if (error) {
    return <ErrorMessage error={error} />
  }
  
  if (!data || data.length === 0) {
    return <EmptyState message="Nenhum dado encontrado" />
  }
  
  return <ComponentePrincipal data={data} />
}
```

### Responsividade

```tsx
// Grid responsivo
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
  {items.map(item => <Card key={item.id} {...item} />)}
</div>

// Altura responsiva para gráficos
<LineChart
  data={data}
  height={window.innerWidth < 768 ? 200 : 300}
/>
```

## Storybook

Todos os componentes possuem stories no Storybook para desenvolvimento isolado.

### Executar Storybook
```bash
npm run storybook
```

### Estrutura de Stories
```typescript
export default {
  title: 'Components/Common/KPICard',
  component: KPICard,
  parameters: {
    docs: {
      description: {
        component: 'Componente para exibir KPIs...'
      }
    }
  }
} satisfies Meta<typeof KPICard>

export const Default: Story = {
  args: {
    name: 'Total Atendimentos',
    value: 150,
    // ...
  }
}
```

## Testes

### Testes de Componentes
```typescript
import { render, screen } from '@testing-library/react'
import KPICard from './KPICard'

test('renders KPI card with correct values', () => {
  render(
    <KPICard
      name="Test KPI"
      value={100}
      unit=""
      category="dashboard"
      description="Test description"
    />
  )
  
  expect(screen.getByText('Test KPI')).toBeInTheDocument()
  expect(screen.getByText('100')).toBeInTheDocument()
})
```

## Contribuição

### Criando Novos Componentes

1. Crie o componente na pasta apropriada
2. Defina interfaces TypeScript claras
3. Implemente responsividade
4. Crie stories no Storybook
5. Adicione testes unitários
6. Documente no README

### Padrões de Código

- Use TypeScript para tipagem
- Siga convenções de nomenclatura
- Implemente acessibilidade (a11y)
- Use Tailwind CSS para estilos
- Mantenha componentes pequenos e focados
