# Arquitetura do DataHub Clinic

## Visão Geral da Arquitetura

O DataHub Clinic segue uma arquitetura moderna de microserviços com separação clara entre frontend e backend, utilizando padrões RESTful e design responsivo.

## Diagrama de Arquitetura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   React + TS    │◄──►│   FastAPI       │◄──►│  PostgreSQL     │
│   Port 3001     │    │   Port 8000     │    │   Port 5432     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Componentes Principais

### Frontend (React + TypeScript)

#### Estrutura de Pastas
```
src/
├── components/          # Componentes reutilizáveis
│   ├── common/         # Componentes básicos (KPICard, etc)
│   ├── charts/         # Gráficos (LineChart, BarChart)
│   ├── layout/         # Layout e navegação
│   └── analytics/      # Componentes de análise
├── pages/              # Páginas da aplicação
│   ├── Dashboard.tsx   # Dashboard principal
│   ├── agenda/         # Módulo agenda
│   ├── financeiro/     # Módulo financeiro
│   ├── paciente/       # Mó<PERSON><PERSON> pacientes
│   └── amigocare/      # Módulo marketing
├── hooks/              # Hooks customizados
├── services/           # Serviços de API
├── types/              # Tipos TypeScript
└── routes/             # Configuração de rotas
```

#### Tecnologias Frontend
- **React 18**: Biblioteca principal
- **TypeScript**: Tipagem estática
- **Vite**: Build tool e dev server
- **Tailwind CSS**: Framework CSS
- **React Query**: Gerenciamento de estado servidor
- **React Router**: Roteamento hierárquico
- **Recharts**: Biblioteca de gráficos
- **Lucide React**: Ícones

### Backend (FastAPI + Python)

#### Estrutura de Pastas
```
app/
├── api/                # Endpoints da API
│   └── v1/
│       ├── endpoints/  # Endpoints por módulo
│       └── api.py      # Router principal
├── core/               # Configurações centrais
│   ├── config.py       # Configurações
│   ├── database.py     # Conexão com banco
│   └── rest_standards.py # Padrões RESTful
├── db/                 # Modelos de banco
├── middleware/         # Middlewares RESTful
├── schemas/            # Schemas Pydantic
│   ├── base.py         # Schemas base
│   ├── dashboard.py    # Schemas dashboard
│   ├── agenda.py       # Schemas agenda
│   ├── financeiro.py   # Schemas financeiro
│   ├── paciente.py     # Schemas pacientes
│   └── amigocare.py    # Schemas marketing
└── services/           # Lógica de negócio
```

#### Tecnologias Backend
- **FastAPI**: Framework web moderno
- **Pydantic**: Validação e serialização
- **SQLAlchemy**: ORM
- **PostgreSQL**: Banco de dados
- **Uvicorn**: Servidor ASGI

### Database (PostgreSQL)

#### Schema OLAP
```sql
-- Tabelas de fatos
fato_atendimento        # Atendimentos realizados
fato_agendamento        # Agendamentos
fato_lead               # Leads de marketing
fato_campanha           # Campanhas de marketing

-- Dimensões
dim_paciente            # Dados dos pacientes
dim_profissional        # Dados dos profissionais
dim_tempo               # Dimensão temporal
dim_procedimento        # Procedimentos médicos
```

## Padrões de Design

### Frontend

#### Component Pattern
```typescript
// Componente base com props tipadas
interface KPICardProps {
  name: string
  value: number
  unit: string
  category: string
  trend_value?: number
  trend_direction?: 'up' | 'down'
  description: string
}

export default function KPICard(props: KPICardProps) {
  // Implementação
}
```

#### Hook Pattern
```typescript
// Hook customizado para navegação
export function useNavigation() {
  const navigate = useNavigate()
  const location = useLocation()
  
  return {
    navigate,
    getCurrentModule,
    isActive,
    getBreadcrumbs
  }
}
```

#### Service Pattern
```typescript
// Serviço de API tipado
class DashboardService {
  async getOverview(periodType: string): Promise<DashboardOverview> {
    const response = await api.get(`/dashboard/overview?period_type=${periodType}`)
    return response.data
  }
}
```

### Backend

#### Service Layer Pattern
```python
class DashboardService:
    def __init__(self, db: Session):
        self.db = db
    
    def get_executive_kpis(self, period_type: str) -> Dict[str, Any]:
        # Lógica de negócio
        pass
```

#### Repository Pattern
```python
class BaseRepository:
    def __init__(self, db: Session):
        self.db = db
    
    def get_by_id(self, id: int):
        pass
    
    def get_all(self, skip: int = 0, limit: int = 100):
        pass
```

#### Schema Pattern
```python
class DashboardOverviewResponse(BaseResponse):
    kpis: List[KPIMetric]
    periodo: PeriodParams
    resumo: DashboardKPIs
```

## Fluxo de Dados

### Request Flow
```
1. Frontend (React) → 2. API Call (Axios) → 3. Backend (FastAPI) → 4. Service Layer → 5. Database (PostgreSQL)
```

### Response Flow
```
1. Database → 2. Service Layer → 3. Pydantic Schema → 4. JSON Response → 5. React Query → 6. Component Update
```

## Padrões RESTful

### URL Structure
```
GET    /api/v1/pacientes              # Listar pacientes
GET    /api/v1/pacientes/{id}         # Obter paciente
POST   /api/v1/pacientes              # Criar paciente
PUT    /api/v1/pacientes/{id}         # Atualizar paciente
DELETE /api/v1/pacientes/{id}         # Remover paciente

# Sub-recursos
GET    /api/v1/pacientes/{id}/atendimentos

# Analytics
GET    /api/v1/pacientes/analytics/overview
```

### HTTP Status Codes
- `200 OK`: Sucesso
- `201 Created`: Recurso criado
- `400 Bad Request`: Erro do cliente
- `404 Not Found`: Recurso não encontrado
- `500 Internal Server Error`: Erro do servidor

### Response Format
```json
{
  "success": true,
  "data": {...},
  "meta": {
    "timestamp": "2025-06-26T10:00:00Z",
    "request_id": "uuid"
  }
}
```

## Middleware Stack

### Backend Middlewares
1. **RESTMiddleware**: Headers padrão e request ID
2. **PaginationMiddleware**: Normalização de paginação
3. **ContentNegotiationMiddleware**: Negociação de conteúdo
4. **HTTPMethodOverrideMiddleware**: Override de métodos
5. **ETagMiddleware**: Cache com ETags
6. **CORSMiddleware**: Configuração CORS

## Performance e Otimização

### Frontend
- **Code Splitting**: Lazy loading de rotas
- **React Query**: Cache inteligente
- **Memoization**: React.memo e useMemo
- **Bundle Optimization**: Vite otimizações

### Backend
- **Connection Pooling**: SQLAlchemy pool
- **Query Optimization**: Índices no PostgreSQL
- **Response Caching**: ETags e Cache-Control
- **Async/Await**: Operações assíncronas

### Database
- **Índices**: Otimização de queries
- **OLAP Schema**: Estrutura para análises
- **Connection Pool**: Gerenciamento de conexões

## Segurança

### Frontend
- **HTTPS**: Comunicação segura
- **CORS**: Configuração adequada
- **Input Validation**: Validação no frontend

### Backend
- **Pydantic Validation**: Validação de entrada
- **SQL Injection Protection**: SQLAlchemy ORM
- **Rate Limiting**: Controle de requisições
- **CORS**: Configuração restritiva

## Monitoramento

### Logs
- **Request ID**: Rastreamento de requisições
- **Structured Logging**: Logs estruturados
- **Error Tracking**: Captura de erros

### Métricas
- **Response Time**: Tempo de resposta
- **Error Rate**: Taxa de erro
- **Throughput**: Requisições por segundo

## Deployment

### Desenvolvimento
```bash
# Backend
uvicorn app.main:app --reload --port 8000

# Frontend
npm run dev
```

### Produção
```bash
# Docker Compose
docker-compose up -d
```

## Escalabilidade

### Horizontal Scaling
- **Load Balancer**: Nginx
- **Multiple Instances**: Docker containers
- **Database Replication**: PostgreSQL replicas

### Vertical Scaling
- **Resource Optimization**: CPU e memória
- **Connection Pooling**: Otimização de conexões
- **Caching**: Redis para cache distribuído
