#!/bin/bash

# Script para testar todos os endpoints usados pelo frontend
echo "🧪 Testando Endpoints do Frontend - DataHub Clinic"
echo "=================================================="

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

BASE_URL="http://localhost:8000/api/v1"
FRONTEND_URL="http://localhost:3001/api/v1"

# Função para testar endpoint
test_endpoint() {
    local url=$1
    local name=$2
    local expected_status=${3:-200}
    
    echo -n "  Testing $name... "
    
    response=$(curl -s -w "%{http_code}" -o /tmp/response.json "$url")
    status_code="${response: -3}"
    
    if [ "$status_code" -eq "$expected_status" ]; then
        echo -e "${GREEN}✓ OK${NC} (${status_code})"
        return 0
    else
        echo -e "${RED}✗ FAIL${NC} (${status_code})"
        if [ -f /tmp/response.json ]; then
            echo "    Response: $(cat /tmp/response.json | head -c 100)..."
        fi
        return 1
    fi
}

echo -e "\n${BLUE}1. Dashboard Endpoints${NC}"
test_endpoint "$BASE_URL/dashboard/overview?period_type=last_90_days" "Dashboard Overview"
test_endpoint "$BASE_URL/dashboard/year-over-year?metric=receita" "Year over Year"
test_endpoint "$BASE_URL/dashboard/heatmap?period_type=last_90_days" "Specialties Heatmap"

echo -e "\n${BLUE}2. Agenda Endpoints${NC}"
test_endpoint "$BASE_URL/agenda/overview?period_type=last_30_days" "Agenda Overview"
test_endpoint "$BASE_URL/agenda/analytics/ocupacao-semanal?period_type=last_30_days" "Ocupação Semanal"
test_endpoint "$BASE_URL/agenda/ranking-profissionais?period_type=last_90_days&limit=20" "Ranking Profissionais"
test_endpoint "$BASE_URL/agenda/cancelamentos?period_type=last_90_days" "Cancelamentos"

echo -e "\n${BLUE}3. Financeiro Endpoints${NC}"
test_endpoint "$BASE_URL/financeiro?period_type=last_90_days" "Financeiro Overview"
test_endpoint "$BASE_URL/financeiro/fluxo-caixa?period_type=last_12_months" "Fluxo de Caixa"
test_endpoint "$BASE_URL/financeiro/analytics/revenue-evolution?period_type=last_90_days" "Revenue Evolution"
test_endpoint "$BASE_URL/financeiro/contas-receber/aging" "Contas a Receber Aging"
test_endpoint "$BASE_URL/financeiro/formas-pagamento?period_type=last_90_days" "Formas de Pagamento"

echo -e "\n${BLUE}4. Pacientes Endpoints${NC}"
test_endpoint "$BASE_URL/pacientes/overview?period_type=last_90_days" "Pacientes Overview"
test_endpoint "$BASE_URL/pacientes/demografico?period_type=last_90_days" "Demografia"
test_endpoint "$BASE_URL/pacientes/fidelidade?period_type=last_12_months" "Fidelidade"
test_endpoint "$BASE_URL/pacientes/ranking?period_type=last_12_months&limit=50" "Ranking Pacientes"

echo -e "\n${BLUE}5. AmigoCare Endpoints${NC}"
test_endpoint "$BASE_URL/amigocare?period_type=last_90_days" "AmigoCare Overview"
test_endpoint "$BASE_URL/amigocare/funil-vendas?period_type=last_90_days" "Funil de Vendas"
test_endpoint "$BASE_URL/amigocare/campanhas?period_type=last_90_days" "Campanhas"
test_endpoint "$BASE_URL/amigocare/fontes-leads?period_type=last_90_days" "Fontes de Leads"
test_endpoint "$BASE_URL/amigocare/timeline-conversoes?period_type=last_12_months" "Timeline Conversões"

echo -e "\n${BLUE}6. Health Endpoints${NC}"
test_endpoint "$BASE_URL/health/" "API Health"
test_endpoint "$BASE_URL/health/frontend-status" "Frontend Status"

echo -e "\n${BLUE}7. Frontend Proxy Test${NC}"
test_endpoint "$FRONTEND_URL/health/frontend-status" "Frontend Proxy Health"
test_endpoint "$FRONTEND_URL/dashboard/overview?period_type=last_90_days" "Dashboard via Proxy"

echo -e "\n${GREEN}✅ Teste de endpoints finalizado!${NC}"
