from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import settings
from app.api.v1.api import api_router

# Configuração detalhada da documentação OpenAPI
app = FastAPI(
    title="DataHub Clinic API",
    version=settings.VERSION,
    description="""
    ## DataHub Clinic - Sistema de Análise de Dados para Clínicas

    API RESTful completa para análise de dados clínicos com foco em:

    * **Dashboard Executivo** - KPIs e métricas principais
    * **Gestão de Agenda** - Agendamentos, ocupação e performance médica
    * **Controle Financeiro** - Receitas, despesas e fluxo de caixa
    * **Análise de Pacientes** - Demografia, fidelidade e segmentação
    * **AmigoCare Marketing** - Leads, campanhas e funil de vendas

    ### Características Técnicas

    - **Dados Reais**: 100% PostgreSQL, zero dados mockados
    - **OLAP Schema**: Estrutura otimizada para análises
    - **RESTful**: Padrões REST completos
    - **Validação**: Schemas Pydantic estruturados
    - **Performance**: Cache inteligente e queries otimizadas

    ### Autenticação

    Atualmente em modo desenvolvimento sem autenticação.
    Em produção, utilizará JWT tokens.

    ### Rate Limiting

    - **Desenvolvimento**: Sem limites
    - **Produção**: 1000 requests/hora por IP

    ### Suporte

    - **Documentação**: [GitHub Wiki](https://github.com/datahub-clinic/docs)
    - **Issues**: [GitHub Issues](https://github.com/datahub-clinic/issues)
    - **Email**: <EMAIL>
    """,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    contact={
        "name": "DataHub Clinic Support",
        "url": "https://datahub-clinic.com/support",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    servers=[
        {
            "url": "http://localhost:8000",
            "description": "Desenvolvimento Local"
        },
        {
            "url": "https://api.datahub-clinic.com",
            "description": "Produção"
        }
    ],
    openapi_tags=[
        {
            "name": "Health",
            "description": "Endpoints de saúde e status da aplicação"
        },
        {
            "name": "Dashboard",
            "description": "KPIs executivos e métricas principais do dashboard"
        },
        {
            "name": "Agenda",
            "description": "Gestão de agendamentos, ocupação e performance médica"
        },
        {
            "name": "Financeiro",
            "description": "Controle financeiro, receitas, despesas e fluxo de caixa"
        },
        {
            "name": "Pacientes",
            "description": "Análise de pacientes, demografia e fidelidade"
        },
        {
            "name": "AmigoCare",
            "description": "Marketing, leads, campanhas e funil de vendas"
        },
        {
            "name": "OLAP",
            "description": "Acesso direto aos dados OLAP para análises avançadas"
        }
    ]
)

# Middlewares RESTful
from app.middleware.rest_middleware import (
    RESTMiddleware,
    PaginationMiddleware,
    ContentNegotiationMiddleware,
    HTTPMethodOverrideMiddleware,
    ETagMiddleware
)

# Aplicar middlewares na ordem correta
app.add_middleware(RESTMiddleware)
app.add_middleware(PaginationMiddleware)
app.add_middleware(ContentNegotiationMiddleware)
app.add_middleware(HTTPMethodOverrideMiddleware)
app.add_middleware(ETagMiddleware)

# CORS (deve ser o último middleware)
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:5173"
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"],
    allow_headers=[
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-HTTP-Method-Override",
        "If-None-Match",
        "Cache-Control"
    ],
    expose_headers=[
        "X-Request-ID",
        "X-Total-Count",
        "X-Process-Time",
        "ETag",
        "Last-Modified",
        "Cache-Control"
    ]
)

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)

@app.get(
    "/",
    tags=["Health"],
    summary="API Root",
    description="Endpoint raiz da API com informações básicas",
    response_description="Informações da API"
)
async def root():
    """
    **Endpoint raiz da API DataHub Clinic**

    Retorna informações básicas sobre a API incluindo:
    - Nome e versão da aplicação
    - Modo de operação (desenvolvimento/produção)
    - Status de dados mockados

    Útil para verificar se a API está respondendo corretamente.
    """
    return {
        "message": "DataHub Clinic API",
        "version": settings.VERSION,
        "mode": "desenvolvimento",
        "data_source": "postgresql_real_data",
        "docs_url": "/docs",
        "redoc_url": "/redoc"
    }

@app.get(
    "/health",
    tags=["Health"],
    summary="Health Check",
    description="Verifica o status de saúde da aplicação e dependências",
    response_description="Status de saúde da aplicação"
)
async def health_check():
    """
    **Health Check da Aplicação**

    Endpoint para monitoramento que verifica:
    - Status da aplicação
    - Conectividade com banco de dados
    - Tempo de resposta
    - Modo de operação

    Usado por ferramentas de monitoramento e load balancers.

    **Códigos de Resposta:**
    - `200`: Aplicação saudável
    - `503`: Aplicação com problemas
    """
    return {
        "status": "healthy",
        "mode": "development",
        "timestamp": "2025-06-26T10:00:00Z",
        "database": "connected",
        "version": settings.VERSION
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
