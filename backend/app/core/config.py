from pydantic_settings import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    PROJECT_NAME: str = "DataHub Clinic"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"

    # Database Configuration usando variáveis de ambiente padrão PostgreSQL
    POSTGRES_SERVER: str = os.getenv('PGHOST', 'localhost')
    POSTGRES_PORT: int = int(os.getenv('PGPORT', '5432'))
    POSTGRES_DB: str = os.getenv('PGDATABASE', 'dbbruno-teste')
    POSTGRES_USER: str = os.getenv('PGUSER', 'postgres')
    POSTGRES_PASSWORD: str = os.getenv('PGPASSWORD', 'postgres')

    # Sistema sempre usa dados reais do PostgreSQL
    # Sem fallbacks ou dados mockados
    USE_MOCK_DATA: bool = False
    USE_MOCK_DATA: bool = False

    @property
    def DATABASE_URL(self) -> str:
        """Constrói URL de conexão com PostgreSQL"""
        return f"postgresql+psycopg2://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"

    @property
    def ASYNC_DATABASE_URL(self) -> str:
        """URL para conexões assíncronas"""
        return f"postgresql+asyncpg://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"

    # Database Pool Settings
    DB_POOL_SIZE: int = 10
    DB_MAX_OVERFLOW: int = 20
    DB_POOL_TIMEOUT: int = 30
    DB_POOL_RECYCLE: int = 3600

    # Security
    SECRET_KEY: str = "datahub-clinic-production-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Logging
    LOG_LEVEL: str = "INFO"

    model_config = {
        "env_file": ".env",
        "case_sensitive": True,
        "extra": "ignore"
    }

settings = Settings()
