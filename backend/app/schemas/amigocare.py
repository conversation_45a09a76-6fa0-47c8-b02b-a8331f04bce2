"""
Schemas para módulo AmigoCare
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from enum import Enum

from .base import BaseResponse, KPIMetric, PeriodParams


class StatusLead(str, Enum):
    """Status do lead"""
    novo = "Novo"
    contatado = "Contatado"
    qualificado = "Qualificado"
    proposta = "Proposta"
    negociacao = "Negociação"
    convertido = "Convertido"
    perdido = "Perdido"


class FonteLead(str, Enum):
    """Fonte do lead"""
    site = "Site"
    redes_sociais = "Redes Sociais"
    google_ads = "Google Ads"
    facebook_ads = "Facebook Ads"
    indicacao = "Indicação"
    telefone = "Telefone"
    whatsapp = "WhatsApp"
    email = "Email"
    outros = "Outros"


class TipoCampanha(str, Enum):
    """Tipo de campanha"""
    digital = "Digital"
    tradicional = "Tradicional"
    email_marketing = "Email Marketing"
    sms = "SMS"
    whatsapp = "WhatsApp"
    indicacao = "Indicação"


class AmigoCareOverview(BaseModel):
    """Overview do AmigoCare"""
    total_leads: int = Field(..., description="Total de leads")
    leads_convertidos: int = Field(..., description="Leads convertidos")
    leads_perdidos: int = Field(..., description="Leads perdidos")
    leads_em_andamento: int = Field(..., description="Leads em andamento")
    taxa_conversao: float = Field(..., description="Taxa de conversão (%)")
    valor_potencial_total: float = Field(..., description="Valor potencial total")
    receita_campanhas: float = Field(..., description="Receita gerada por campanhas")
    roi_total: float = Field(..., description="ROI total (%)")
    campanhas_ativas: int = Field(..., description="Campanhas ativas")
    probabilidade_media: float = Field(..., description="Probabilidade média de conversão (%)")
    crescimento_leads: Optional[float] = Field(None, description="Crescimento de leads (%)")
    crescimento_conversao: Optional[float] = Field(None, description="Crescimento da conversão (%)")


class AmigoCareOverviewResponse(BaseResponse):
    """Resposta do overview do AmigoCare"""
    data: AmigoCareOverview = Field(..., description="Dados do AmigoCare")
    periodo: PeriodParams = Field(..., description="Período dos dados")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "timestamp": "2025-06-26T10:00:00",
                "source": "postgresql_real_data",
                "data": {
                    "total_leads": 129,
                    "leads_convertidos": 26,
                    "leads_perdidos": 45,
                    "leads_em_andamento": 58,
                    "taxa_conversao": 20.16,
                    "valor_potencial_total": 711480.89,
                    "receita_campanhas": 145678.90,
                    "roi_total": 224.59,
                    "campanhas_ativas": 8,
                    "probabilidade_media": 52.2,
                    "crescimento_leads": 15.3,
                    "crescimento_conversao": 8.7
                },
                "periodo": {
                    "inicio": "2025-03-28",
                    "fim": "2025-06-26",
                    "tipo": "last_90_days"
                }
            }
        }


class CampanhaPerformance(BaseModel):
    """Performance de campanha"""
    campanha_id: int = Field(..., description="ID da campanha")
    nome: str = Field(..., description="Nome da campanha")
    tipo: TipoCampanha = Field(..., description="Tipo da campanha")
    leads_gerados: int = Field(..., description="Leads gerados")
    leads_convertidos: int = Field(..., description="Leads convertidos")
    taxa_conversao: float = Field(..., description="Taxa de conversão (%)")
    investimento: float = Field(..., description="Investimento na campanha")
    receita_gerada: float = Field(..., description="Receita gerada")
    roi: float = Field(..., description="ROI da campanha (%)")
    cac: float = Field(..., description="Custo de aquisição por cliente")
    data_inicio: date = Field(..., description="Data de início")
    data_fim: Optional[date] = Field(None, description="Data de fim")
    ativa: bool = Field(..., description="Campanha ativa")


class CampanhaPerformanceResponse(BaseResponse):
    """Resposta da performance de campanhas"""
    data: List[CampanhaPerformance] = Field(..., description="Performance das campanhas")
    total_campanhas: int = Field(..., description="Total de campanhas")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "timestamp": "2025-06-26T10:00:00",
                "source": "postgresql_real_data",
                "data": [
                    {
                        "campanha_id": 1,
                        "nome": "Campanha Cardiologia",
                        "tipo": "Digital",
                        "leads_gerados": 45,
                        "leads_convertidos": 12,
                        "taxa_conversao": 26.67,
                        "investimento": 5000.00,
                        "receita_gerada": 18000.00,
                        "roi": 260.0,
                        "cac": 416.67,
                        "data_inicio": "2025-01-01",
                        "data_fim": None,
                        "ativa": True
                    }
                ],
                "total_campanhas": 8
            }
        }


class FunilEtapa(str, Enum):
    """Etapas do funil"""
    visitantes = "Visitantes"
    leads = "Leads"
    qualificados = "Qualificados"
    propostas = "Propostas"
    negociacao = "Negociação"
    convertidos = "Convertidos"


class FunilVendas(BaseModel):
    """Funil de vendas"""
    etapa: FunilEtapa = Field(..., description="Etapa do funil")
    quantidade: int = Field(..., description="Quantidade na etapa")
    valor_potencial: float = Field(..., description="Valor potencial da etapa")
    taxa_conversao: float = Field(..., description="Taxa de conversão para próxima etapa (%)")
    tempo_medio_etapa: float = Field(..., description="Tempo médio na etapa (dias)")


class FunilVendasResponse(BaseResponse):
    """Resposta do funil de vendas"""
    data: List[FunilVendas] = Field(..., description="Etapas do funil")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "timestamp": "2025-06-26T10:00:00",
                "source": "postgresql_real_data",
                "data": [
                    {
                        "etapa": "Leads",
                        "quantidade": 129,
                        "valor_potencial": 711480.89,
                        "taxa_conversao": 65.0,
                        "tempo_medio_etapa": 2.5
                    }
                ]
            }
        }


class LeadSources(BaseModel):
    """Fontes de leads"""
    fonte: FonteLead = Field(..., description="Fonte do lead")
    quantidade: int = Field(..., description="Quantidade de leads")
    percentual: float = Field(..., description="Percentual do total (%)")
    taxa_conversao: float = Field(..., description="Taxa de conversão da fonte (%)")
    cac_medio: float = Field(..., description="CAC médio da fonte")


class LeadSourcesResponse(BaseResponse):
    """Resposta das fontes de leads"""
    data: List[LeadSources] = Field(..., description="Fontes de leads")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "timestamp": "2025-06-26T10:00:00",
                "source": "postgresql_real_data",
                "data": [
                    {
                        "fonte": "Google Ads",
                        "quantidade": 45,
                        "percentual": 34.9,
                        "taxa_conversao": 22.2,
                        "cac_medio": 350.00
                    }
                ]
            }
        }


class LeadDetalhado(BaseModel):
    """Detalhes de um lead"""
    id: int = Field(..., description="ID do lead")
    nome: str = Field(..., description="Nome do lead")
    email: str = Field(..., description="Email do lead")
    telefone: Optional[str] = Field(None, description="Telefone do lead")
    fonte: FonteLead = Field(..., description="Fonte do lead")
    status: StatusLead = Field(..., description="Status atual")
    valor_potencial: float = Field(..., description="Valor potencial")
    probabilidade: float = Field(..., description="Probabilidade de conversão (%)")
    data_criacao: datetime = Field(..., description="Data de criação")
    ultima_interacao: datetime = Field(..., description="Última interação")
    campanha_origem: Optional[str] = Field(None, description="Campanha de origem")


class LeadsListResponse(BaseResponse):
    """Resposta da lista de leads"""
    data: List[LeadDetalhado] = Field(..., description="Lista de leads")
    total: int = Field(..., description="Total de leads")
    page: int = Field(..., description="Página atual")
    limit: int = Field(..., description="Itens por página")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "timestamp": "2025-06-26T10:00:00",
                "source": "postgresql_real_data",
                "data": [
                    {
                        "id": 1,
                        "nome": "João Silva",
                        "email": "<EMAIL>",
                        "telefone": "(11) 99999-9999",
                        "fonte": "Google Ads",
                        "status": "Qualificado",
                        "valor_potencial": 2500.00,
                        "probabilidade": 75.0,
                        "data_criacao": "2025-06-01T10:00:00",
                        "ultima_interacao": "2025-06-25T15:30:00",
                        "campanha_origem": "Campanha Cardiologia"
                    }
                ],
                "total": 129,
                "page": 1,
                "limit": 20
            }
        }
