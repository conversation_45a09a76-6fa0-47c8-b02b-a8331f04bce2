"""
Endpoints do Módulo AmigoCare - Padrão RESTful Completo
Documentação completa com schemas Pydantic e OpenAPI
"""

from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any

from app.services.amigocare_service import AmigoCareService
from app.db.base import get_db
from app.schemas import (
    AmigoCareOverviewResponse,
    CampanhaPerformanceResponse,
    FunilVendasResponse,
    LeadSourcesResponse,
    LeadsListResponse,
    PeriodType,
    ErrorResponse
)
from app.core.rest_standards import RESTStatusCode

router = APIRouter(tags=["AmigoCare"], prefix="/amigocare")


@router.get(
    "",
    summary="AmigoCare Overview",
    description="Retorna visão geral do AmigoCare com métricas de marketing e leads",
    response_model=AmigoCareOverviewResponse,
    status_code=RESTStatusCode.OK,
    responses={
        200: {
            "description": "Overview do AmigoCare retornado com sucesso",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "timestamp": "2025-06-26T10:00:00",
                        "source": "postgresql_real_data",
                        "data": {
                            "total_leads": 129,
                            "leads_convertidos": 26,
                            "leads_perdidos": 45,
                            "leads_em_andamento": 58,
                            "taxa_conversao": 20.16,
                            "valor_potencial_total": 711480.89,
                            "receita_campanhas": 145678.90,
                            "roi_total": 224.59,
                            "campanhas_ativas": 8,
                            "probabilidade_media": 52.2
                        }
                    }
                }
            }
        },
        500: {"model": ErrorResponse, "description": "Erro interno do servidor"}
    }
)
def get_marketing_overview(
    period_type: str = Query(
        "last_90_days",
        description="Período para análise de marketing",
        enum=["last_7_days", "last_30_days", "last_90_days", "last_12_months"]
    ),
    db: Session = Depends(get_db)
) -> AmigoCareOverviewResponse:
    """
    **AmigoCare Overview - KPIs de Marketing**

    Retorna uma visão consolidada das atividades de marketing:

    ### KPIs de Leads:
    - **Total de Leads**: Número total de leads gerados
    - **Taxa de Conversão**: Percentual de leads convertidos
    - **Leads em Andamento**: Leads ativos no funil
    - **Valor Potencial**: Valor total em pipeline

    ### KPIs de Campanhas:
    - **ROI Total**: Retorno sobre investimento
    - **Receita de Campanhas**: Receita gerada
    - **Campanhas Ativas**: Número de campanhas em execução
    - **Probabilidade Média**: Chance média de conversão

    ### Características:
    - **Dados Reais**: Extraídos do schema OLAP PostgreSQL
    - **Sem Fallbacks**: Falha se dados indisponíveis
    - **Atualização**: Dados atualizados em tempo real

    ### Uso Recomendado:
    Ideal para gestão de marketing e análise de performance de campanhas.
    """
    amigocare_service = AmigoCareService(db)
    overview = amigocare_service.get_marketing_overview(period_type)

    return AmigoCareOverviewResponse(
        success=True,
        data=overview,
        periodo=overview.get("periodo", {})
    )


@router.get("/funil-vendas")
def get_leads_funnel_analysis(
    period_type: str = Query("last_90_days", description="Período para análise"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Retorna análise do funil de vendas
    """
    amigocare_service = AmigoCareService(db)
    funnel = amigocare_service.get_leads_funnel_analysis(period_type)

    return {
        "funnel": funnel,
        "source": "postgresql_real_data"
    }


@router.get("/campanhas")
def get_campaigns_performance(
    period_type: str = Query("last_90_days", description="Período para análise"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Retorna performance de campanhas
    """
    amigocare_service = AmigoCareService(db)
    campaigns = amigocare_service.get_campaigns_performance(period_type)

    return {
        "campaigns": campaigns,
        "source": "postgresql_real_data"
    }


@router.get("/fontes-leads")
def get_lead_sources_analysis(
    period_type: str = Query("last_90_days", description="Período para análise"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Retorna análise de fontes de leads
    """
    amigocare_service = AmigoCareService(db)
    sources = amigocare_service.get_lead_sources_analysis(period_type)

    return {
        "sources": sources,
        "source": "postgresql_real_data"
    }


@router.get("/timeline-conversoes")
def get_conversion_timeline(
    period_type: str = Query("last_12_months", description="Período para análise"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Retorna timeline de conversões
    """
    amigocare_service = AmigoCareService(db)
    timeline = amigocare_service.get_conversion_timeline(period_type)

    return {
        "timeline": timeline,
        "source": "postgresql_real_data"
    }
