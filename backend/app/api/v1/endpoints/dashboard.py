"""
Endpoints do Dashboard - Apenas dados reais do PostgreSQL
Documentação completa com schemas Pydantic e OpenAPI
"""

from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any

from app.services.dashboard_service import DashboardService
from app.db.base import get_db
from app.schemas import (
    DashboardOverviewResponse,
    DashboardChartsResponse,
    DashboardInsightsResponse,
    PeriodType,
    ErrorResponse
)

router = APIRouter(tags=["Dashboard"])


@router.get(
    "/overview",
    summary="Dashboard Overview",
    description="Retorna KPIs executivos e métricas principais do dashboard",
    response_description="Dados consolidados do dashboard com KPIs e período",
    responses={
        200: {
            "description": "Dashboard overview retornado com sucesso",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "timestamp": "2025-06-26T10:00:00",
                        "source": "postgresql_real_data",
                        "kpis": [
                            {
                                "id": "total_atendimentos",
                                "name": "Total Atendimentos",
                                "value": 67,
                                "unit": "",
                                "category": "dashboard",
                                "trend_value": 8.5,
                                "trend_direction": "up",
                                "description": "Total de atendimentos realizados"
                            }
                        ],
                        "periodo": {
                            "inicio": "2025-03-28",
                            "fim": "2025-06-26",
                            "tipo": "last_90_days"
                        }
                    }
                }
            }
        },
        500: {
            "description": "Erro interno do servidor",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error": "Erro ao conectar com banco de dados",
                        "error_code": "DB_CONNECTION_ERROR"
                    }
                }
            }
        }
    }
)
def get_dashboard_overview(
    period_type: str = Query(
        "last_90_days",
        description="Período para análise dos dados",
        enum=["last_7_days", "last_30_days", "last_90_days", "last_12_months"]
    ),
    db: Session = Depends(get_db)
) -> DashboardOverviewResponse:
    """
    **Dashboard Overview - KPIs Executivos**

    Retorna uma visão consolidada dos principais indicadores da clínica:

    ### KPIs Incluídos:
    - **Total de Atendimentos**: Número total de consultas realizadas
    - **Receita Total**: Faturamento total do período
    - **Pacientes Únicos**: Número de pacientes distintos atendidos
    - **Taxa de Ocupação**: Percentual de ocupação da agenda

    ### Características:
    - **Dados 100% Reais**: Extraídos diretamente do PostgreSQL
    - **Zero Fallbacks**: Falha se banco indisponível
    - **Cache Inteligente**: Otimizado para performance
    - **Trends**: Inclui direção e percentual de crescimento

    ### Períodos Disponíveis:
    - `last_7_days`: Últimos 7 dias
    - `last_30_days`: Últimos 30 dias
    - `last_90_days`: Últimos 90 dias (padrão)
    - `last_12_months`: Últimos 12 meses

    ### Uso Recomendado:
    Ideal para dashboards executivos e relatórios gerenciais.
    Atualizar a cada 30 minutos para dados em tempo real.
    """
    dashboard_service = DashboardService(db)

    # KPIs executivos
    kpis_data = dashboard_service.get_executive_kpis(period_type)

    # Insights executivos (removido por enquanto)

    # Retornar usando schema Pydantic
    return DashboardOverviewResponse(
        success=True,
        kpis=kpis_data.get("kpis", []),
        periodo=kpis_data.get("periodo", {}),
        resumo={
            "total_atendimentos": kpis_data.get("total_atendimentos", 0),
            "receita_total": kpis_data.get("receita_total", 0),
            "pacientes_unicos": kpis_data.get("pacientes_unicos", 0),
            "taxa_ocupacao": kpis_data.get("taxa_ocupacao", 0),
            "crescimento_receita": kpis_data.get("crescimento_receita", 0),
            "crescimento_atendimentos": kpis_data.get("crescimento_atendimentos", 0)
        }
    )


@router.get("/year-over-year")
def get_year_over_year_analysis(
    metric: str = Query("receita", description="Métrica para análise Y/Y"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Análise Year over Year com dados reais
    """
    dashboard_service = DashboardService(db)
    yoy_data = dashboard_service.get_year_over_year_analysis(metric)

    # Organiza dados para gráfico
    anos = list({item["ano"] for item in yoy_data})
    meses = list(range(1, 13))

    chart_data = []
    for mes in meses:
        data_point = {"mes": mes, "mes_nome": dashboard_service.date_utils.get_month_name(mes)}
        for ano in sorted(anos):
            mes_data = next((item for item in yoy_data if item["mes"] == mes and item["ano"] == ano), None)
            data_point[f"{metric}_{ano}"] = mes_data["valor"] if mes_data else 0
        chart_data.append(data_point)

    return {
        "chart_data": chart_data,
        "anos_disponiveis": sorted(anos),
        "metric": metric,
        "source": "postgresql_real_data"
    }


@router.get("/heatmap")
def get_specialties_heatmap(
    period_type: str = Query("last_90_days", description="Período para análise"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Heatmap de performance por especialidade
    """
    dashboard_service = DashboardService(db)
    heatmap_data = dashboard_service.get_specialties_heatmap(period_type)

    return {
        "heatmap_data": heatmap_data,
        "source": "postgresql_real_data"
    }
