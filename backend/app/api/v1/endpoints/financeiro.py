"""
Endpoints do Módulo Financeiro - Padrão RESTful Completo
Documentação completa com schemas Pydantic e OpenAPI
"""

from fastapi import APIRouter, Depends, Query, HTTPException, status
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional
from datetime import date, datetime

from app.services.financeiro_service import FinanceiroService
from app.db.base import get_db
from app.schemas import (
    FinanceiroOverviewResponse,
    RevenueEvolutionResponse,
    FluxoCaixaResponse,
    ContasReceberResponse,
    PeriodType,
    ErrorResponse
)
from app.core.rest_standards import RESTStatusCode

router = APIRouter(tags=["Financeiro"], prefix="/financeiro")


@router.get(
    "",
    summary="Financeiro Overview",
    description="Retorna visão geral financeira com KPIs e métricas principais",
    response_model=FinanceiroOverviewResponse,
    status_code=RESTStatusCode.OK,
    responses={
        200: {
            "description": "Overview financeiro retornado com sucesso",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "timestamp": "2025-06-26T10:00:00",
                        "source": "postgresql_real_data",
                        "data": {
                            "receita_total": 67839.04,
                            "despesa_total": 25000.00,
                            "lucro_liquido": 42839.04,
                            "margem_lucro": 63.15,
                            "crescimento_receita": 15.3,
                            "receitas_pagas": 45000.00,
                            "receitas_pendentes": 22839.04,
                            "clientes_ativos": 208,
                            "taxa_inadimplencia": 5.2
                        }
                    }
                }
            }
        },
        500: {"model": ErrorResponse, "description": "Erro interno do servidor"}
    }
)
def get_financial_overview(
    period_type: str = Query(
        "last_90_days",
        description="Período para análise financeira",
        enum=["last_7_days", "last_30_days", "last_90_days", "last_12_months"]
    ),
    db: Session = Depends(get_db)
) -> FinanceiroOverviewResponse:
    """
    **Financeiro Overview - KPIs Financeiros**

    Retorna uma visão consolidada da situação financeira da clínica:

    ### KPIs Principais:
    - **Receita Total**: Faturamento total do período
    - **Lucro Líquido**: Resultado após despesas
    - **Margem de Lucro**: Percentual de lucratividade
    - **Taxa de Inadimplência**: Percentual de contas em atraso

    ### Análise de Fluxo:
    - **Receitas Pagas vs Pendentes**: Status de recebimentos
    - **Clientes Ativos**: Clientes com movimentação
    - **Crescimento**: Comparação com período anterior

    ### Características:
    - **Dados Reais**: Extraídos do schema OLAP PostgreSQL
    - **Sem Fallbacks**: Falha se dados indisponíveis
    - **Atualização**: Dados atualizados em tempo real

    ### Uso Recomendado:
    Ideal para controle financeiro e tomada de decisões estratégicas.
    """
    financeiro_service = FinanceiroService(db)
    overview = financeiro_service.get_financial_overview(period_type)

    return FinanceiroOverviewResponse(
        success=True,
        data=overview,
        periodo=overview.get("periodo", {})
    )


@router.get("/fluxo-caixa")
def get_cash_flow_analysis(
    period_type: str = Query("last_12_months", description="Período para análise"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Retorna análise de fluxo de caixa
    """
    financeiro_service = FinanceiroService(db)
    cash_flow = financeiro_service.get_cash_flow_analysis(period_type)

    return {
        "cash_flow": cash_flow,
        "source": "postgresql_real_data"
    }


@router.get("/contas-receber/aging")
def get_accounts_receivable_aging(
    reference_date: Optional[date] = Query(None, description="Data de referência"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Retorna aging de contas a receber
    """
    financeiro_service = FinanceiroService(db)
    aging = financeiro_service.get_accounts_receivable_aging(reference_date)

    return aging


@router.get("/formas-pagamento")
def get_payment_methods_analysis(
    period_type: str = Query("last_90_days", description="Período para análise"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Retorna análise de formas de pagamento
    """
    financeiro_service = FinanceiroService(db)
    payment_methods = financeiro_service.get_payment_methods_analysis(period_type)

    return {
        "payment_methods": payment_methods,
        "source": "postgresql_real_data"
    }


@router.get(
    "/analytics/revenue-evolution",
    summary="Evolução da Receita",
    description="Retorna dados de evolução da receita para análises e gráficos",
    response_model=RevenueEvolutionResponse,
    status_code=RESTStatusCode.OK
)
def get_revenue_evolution(
    period_type: str = Query(
        "last_90_days",
        description="Período para análise da evolução",
        enum=["last_7_days", "last_30_days", "last_90_days"]
    ),
    db: Session = Depends(get_db)
) -> RevenueEvolutionResponse:
    """
    **Evolução da Receita - Analytics**

    Endpoint RESTful para análise temporal da receita:
    - Evolução diária/mensal da receita
    - Receita acumulada
    - Número de transações
    - Clientes únicos por período
    - Ticket médio
    """
    financeiro_service = FinanceiroService(db)
    revenue_evolution = financeiro_service.get_revenue_evolution(period_type)

    return {
        "success": True,
        "data": revenue_evolution,
        "source": "postgresql_real_data",
        "timestamp": datetime.now().isoformat()
    }
