from fastapi import APIRouter
from app.api.v1.endpoints import dashboard, agenda, financeiro, paciente, amigocare, olap_analytics, health

api_router = APIRouter()

# Health check - verificação de saúde do sistema
api_router.include_router(health.router, prefix="/health", tags=["Health"])

# Endpoints principais com dados reais do PostgreSQL - Padrão RESTful
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["Dashboard"])
api_router.include_router(agenda.router, prefix="/agenda", tags=["Agenda"])

# Endpoints RESTful com prefixos nos próprios routers
api_router.include_router(financeiro.router)  # prefix="/financeiro" já definido no router
api_router.include_router(amigocare.router)   # prefix="/amigocare" já definido no router
api_router.include_router(paciente.router)    # prefix="/pacientes" já definido no router

# Endpoints OLAP avançados (análises detalhadas) - RESTful
api_router.include_router(olap_analytics.router)  # prefix="/olap" já definido no router
