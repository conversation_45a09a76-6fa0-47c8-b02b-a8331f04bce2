"""
Serviço de Dashboard - Domínio de análises executivas
Responsável por KPIs, análises Y/Y, heatmaps e visão geral
"""

from typing import List, Dict, Any, Optional
from datetime import date, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import text
import logging

from app.utils.date_utils import DateUtils
from app.utils.metrics_utils import MetricsUtils
from app.utils.query_utils import QueryUtils


logger = logging.getLogger(__name__)


class DashboardService:
    """Serviço para análises de dashboard executivo"""
    
    def __init__(self, db: Session):
        self.db = db
        self.date_utils = DateUtils()
        self.metrics_utils = MetricsUtils()
        self.query_utils = QueryUtils()
    
    def get_executive_kpis(self, period_type: str = 'last_90_days') -> Dict[str, Any]:
        """
        Retorna KPIs executivos principais
        
        Args:
            period_type: Tipo de período para análise
        
        Returns:
            Dicionário com KPIs executivos
        """
        try:
            # Define período
            start_date, end_date = self.date_utils.get_period_range(period_type)
            
            # Período de comparação
            comp_start, comp_end = self.date_utils.get_comparison_period(
                start_date, end_date, 'same_period_last_year'
            )
            
            # Query para KPIs principais
            kpi_query = text("""
                WITH current_period AS (
                    SELECT 
                        COUNT(DISTINCT a.id) as total_atendimentos,
                        COALESCE(SUM(a.valor), 0) as receita_total,
                        COALESCE(AVG(a.valor), 0) as ticket_medio,
                        COUNT(DISTINCT a.paciente_id) as pacientes_unicos,
                        COUNT(DISTINCT a.profissional_id) as profissionais_ativos
                    FROM fato_atendimento a
                    WHERE a.data BETWEEN :start_date AND :end_date
                    AND a.status = 'Realizado'
                ),
                comparison_period AS (
                    SELECT 
                        COUNT(DISTINCT a.id) as total_atendimentos_comp,
                        COALESCE(SUM(a.valor), 0) as receita_total_comp,
                        COALESCE(AVG(a.valor), 0) as ticket_medio_comp
                    FROM fato_atendimento a
                    WHERE a.data BETWEEN :comp_start AND :comp_end
                    AND a.status = 'Realizado'
                ),
                cancelamentos AS (
                    SELECT 
                        COUNT(CASE WHEN ag.status = 'Cancelado' THEN 1 END) as total_cancelamentos,
                        COUNT(*) as total_agendamentos
                    FROM fato_agendamento ag
                    WHERE ag.data BETWEEN :start_date AND :end_date
                )
                SELECT 
                    cp.*,
                    comp.*,
                    c.total_cancelamentos,
                    c.total_agendamentos,
                    CASE 
                        WHEN c.total_agendamentos > 0 
                        THEN ROUND((c.total_cancelamentos * 100.0 / c.total_agendamentos), 2)
                        ELSE 0 
                    END as taxa_cancelamento
                FROM current_period cp
                CROSS JOIN comparison_period comp
                CROSS JOIN cancelamentos c
            """)
            
            result = self.db.execute(kpi_query, {
                'start_date': start_date,
                'end_date': end_date,
                'comp_start': comp_start,
                'comp_end': comp_end
            })
            
            row = result.fetchone()
            
            if not row:
                return self._get_empty_kpis()
            
            # Calcula crescimentos
            crescimento_atendimentos = self.metrics_utils.calculate_growth_rate(
                row.total_atendimentos, row.total_atendimentos_comp
            )

            crescimento_receita = self.metrics_utils.calculate_growth_rate(
                float(row.receita_total or 0), float(row.receita_total_comp or 0)
            )

            crescimento_ticket = self.metrics_utils.calculate_growth_rate(
                float(row.ticket_medio or 0), float(row.ticket_medio_comp or 0)
            )
            
            return {
                "kpis": [
                    {
                        "id": "total_atendimentos",
                        "name": "Total Atendimentos",
                        "value": int(row.total_atendimentos),
                        "unit": "atendimentos",
                        "category": "operacional",
                        "trend_value": crescimento_atendimentos,
                        "trend_direction": "up" if crescimento_atendimentos and crescimento_atendimentos > 0 else "down",
                        "description": "Total de atendimentos realizados no período",
                        "comparison_value": int(row.total_atendimentos_comp),
                        "comparison_period": "Mesmo período ano anterior"
                    },
                    {
                        "id": "receita_total",
                        "name": "Receita Total",
                        "value": float(row.receita_total),
                        "unit": "R$",
                        "category": "financeiro",
                        "trend_value": crescimento_receita,
                        "trend_direction": "up" if crescimento_receita and crescimento_receita > 0 else "down",
                        "description": "Receita total gerada no período",
                        "comparison_value": float(row.receita_total_comp),
                        "comparison_period": "Mesmo período ano anterior"
                    },
                    {
                        "id": "ticket_medio",
                        "name": "Ticket Médio",
                        "value": round(float(row.ticket_medio), 2),
                        "unit": "R$",
                        "category": "financeiro",
                        "trend_value": crescimento_ticket,
                        "trend_direction": "up" if crescimento_ticket and crescimento_ticket > 0 else "down",
                        "description": "Valor médio por atendimento",
                        "comparison_value": round(float(row.ticket_medio_comp), 2),
                        "comparison_period": "Mesmo período ano anterior"
                    },
                    {
                        "id": "taxa_cancelamento",
                        "name": "Taxa Cancelamento",
                        "value": float(row.taxa_cancelamento),
                        "unit": "%",
                        "category": "operacional",
                        "trend_value": None,  # Será calculado em análise específica
                        "trend_direction": "down" if row.taxa_cancelamento < 15 else "up",
                        "description": "Percentual de agendamentos cancelados",
                        "comparison_value": None,
                        "comparison_period": "Período atual"
                    }
                ],
                "pacientes_unicos": int(row.pacientes_unicos),
                "profissionais_ativos": int(row.profissionais_ativos),
                "periodo": {
                    "inicio": start_date.isoformat(),
                    "fim": end_date.isoformat(),
                    "tipo": period_type
                },
                "source": "postgresql_real_data"
            }
            
        except Exception as e:
            logger.error(f"Erro ao buscar KPIs executivos: {e}")
            return self._get_empty_kpis()
    
    def get_year_over_year_analysis(self, metric: str = 'receita') -> List[Dict[str, Any]]:
        """
        Análise Year over Year para gráficos comparativos
        
        Args:
            metric: Métrica para análise ('receita', 'atendimentos', 'pacientes')
        
        Returns:
            Lista com dados Y/Y por mês
        """
        try:
            # Mapeia métricas para SQL
            metric_sql = {
                'receita': 'SUM(valor)',
                'atendimentos': 'COUNT(*)',
                'pacientes': 'COUNT(DISTINCT paciente_id)'
            }
            
            if metric not in metric_sql:
                raise ValueError(f"Métrica não suportada: {metric}")
            
            yoy_query = text(f"""
                SELECT 
                    EXTRACT(YEAR FROM data) as ano,
                    EXTRACT(MONTH FROM data) as mes,
                    {metric_sql[metric]} as valor
                FROM fato_atendimento
                WHERE data >= '2023-01-01'
                AND status = 'Realizado'
                GROUP BY EXTRACT(YEAR FROM data), EXTRACT(MONTH FROM data)
                ORDER BY ano, mes
            """)
            
            result = self.db.execute(yoy_query)
            rows = result.fetchall()
            
            yoy_data = []
            for row in rows:
                yoy_data.append({
                    "ano": int(row.ano),
                    "mes": int(row.mes),
                    "mes_nome": self.date_utils.get_month_name(int(row.mes)),
                    "valor": float(row.valor) if row.valor else 0.0,
                    "periodo": f"{int(row.mes):02d}/{int(row.ano)}",
                    "metric_type": metric
                })
            
            return yoy_data
            
        except Exception as e:
            logger.error(f"Erro ao buscar análise Y/Y: {e}")
            return []
    
    def get_specialties_heatmap(self, period_type: str = 'last_90_days') -> List[Dict[str, Any]]:
        """
        Heatmap de performance por especialidade
        
        Args:
            period_type: Período para análise
        
        Returns:
            Lista com dados de heatmap
        """
        try:
            start_date, end_date = self.date_utils.get_period_range(period_type)
            
            heatmap_query = text("""
                SELECT 
                    e.nome as especialidade,
                    COUNT(a.id) as total_atendimentos,
                    SUM(a.valor) as receita_total,
                    AVG(a.valor) as ticket_medio,
                    COUNT(DISTINCT a.profissional_id) as profissionais,
                    COUNT(DISTINCT a.paciente_id) as pacientes_unicos
                FROM dim_especialidade e
                LEFT JOIN fato_atendimento a ON e.id = a.especialidade_id
                WHERE a.data BETWEEN :start_date AND :end_date
                AND a.status = 'Realizado'
                GROUP BY e.id, e.nome
                HAVING COUNT(a.id) > 0
                ORDER BY COUNT(a.id) DESC
            """)
            
            result = self.db.execute(heatmap_query, {
                'start_date': start_date,
                'end_date': end_date
            })
            rows = result.fetchall()
            
            heatmap_data = []
            for row in rows:
                # Calcula performance score
                performance_score = self.metrics_utils.calculate_performance_score(
                    {
                        'atendimentos': float(row.total_atendimentos),
                        'receita': float(row.receita_total or 0) / 1000,  # Normaliza para milhares
                        'pacientes': float(row.pacientes_unicos)
                    },
                    {
                        'atendimentos': 0.3,
                        'receita': 0.4,
                        'pacientes': 0.3
                    }
                )
                
                heatmap_data.append({
                    "especialidade": row.especialidade,
                    "total_atendimentos": int(row.total_atendimentos),
                    "receita_total": float(row.receita_total or 0),
                    "ticket_medio": round(float(row.ticket_medio or 0), 2),
                    "profissionais": int(row.profissionais),
                    "pacientes_unicos": int(row.pacientes_unicos),
                    "performance_score": performance_score
                })
            
            return heatmap_data
            
        except Exception as e:
            logger.error(f"Erro ao buscar heatmap de especialidades: {e}")
            return []
    
    def get_executive_insights(self, period_type: str = 'last_90_days') -> List[Dict[str, Any]]:
        """
        Gera insights executivos baseados em dados reais

        Args:
            period_type: Período para análise

        Returns:
            Lista de insights executivos
        """
        try:
            kpis = self.get_executive_kpis(period_type)
            insights = []

            # Insight sobre crescimento de receita
            receita_kpi = next((k for k in kpis["kpis"] if k["id"] == "receita_total"), None)
            if receita_kpi and receita_kpi["trend_value"]:
                if receita_kpi["trend_value"] > 10:
                    insights.append({
                        "id": "receita_crescimento",
                        "title": "Crescimento Acelerado de Receita",
                        "content": f"Receita cresceu {receita_kpi['trend_value']:.1f}% vs mesmo período ano anterior. Performance excepcional!",
                        "insight_type": "positive",
                        "category": "financeiro",
                        "priority": "high",
                        "module": "dashboard",
                        "page": "overview",
                        "is_active": True
                    })
                elif receita_kpi["trend_value"] < -5:
                    insights.append({
                        "id": "receita_declinio",
                        "title": "Atenção: Declínio na Receita",
                        "content": f"Receita caiu {abs(receita_kpi['trend_value']):.1f}% vs ano anterior. Revisar estratégias comerciais.",
                        "insight_type": "warning",
                        "category": "financeiro",
                        "priority": "high",
                        "module": "dashboard",
                        "page": "overview",
                        "is_active": True
                    })

            # Insight sobre taxa de cancelamento
            cancel_kpi = next((k for k in kpis["kpis"] if k["id"] == "taxa_cancelamento"), None)
            if cancel_kpi:
                if cancel_kpi["value"] > 20:
                    insights.append({
                        "id": "alta_taxa_cancelamento",
                        "title": "Taxa de Cancelamento Elevada",
                        "content": f"Taxa de cancelamento em {cancel_kpi['value']:.1f}%. Investigar causas e implementar melhorias.",
                        "insight_type": "warning",
                        "category": "operacional",
                        "priority": "medium",
                        "module": "agenda",
                        "page": "cancelamentos",
                        "is_active": True
                    })
                elif cancel_kpi["value"] < 10:
                    insights.append({
                        "id": "baixa_taxa_cancelamento",
                        "title": "Excelente Controle de Cancelamentos",
                        "content": f"Taxa de cancelamento em apenas {cancel_kpi['value']:.1f}%. Processo bem otimizado!",
                        "insight_type": "positive",
                        "category": "operacional",
                        "priority": "low",
                        "module": "agenda",
                        "page": "cancelamentos",
                        "is_active": True
                    })

            return insights

        except Exception as e:
            logger.error(f"Erro ao gerar insights executivos: {e}")
            return []

    def _get_empty_kpis(self) -> Dict[str, Any]:
        """Retorna estrutura vazia de KPIs"""
        return {
            "kpis": [],
            "pacientes_unicos": 0,
            "profissionais_ativos": 0,
            "periodo": {},
            "source": "postgresql_real_data"
        }
