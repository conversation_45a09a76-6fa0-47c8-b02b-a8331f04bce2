"""
Serviço AmigoCare - Domínio de marketing e relacionamento
Responsável por leads, campanhas, NPS, funil de vendas e conversão
"""

from typing import List, Dict, Any, Optional
from datetime import date, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import text
import logging

from app.utils.date_utils import DateUtils
from app.utils.metrics_utils import MetricsUtils
from app.utils.query_utils import QueryUtils


logger = logging.getLogger(__name__)


class AmigoCareService:
    """Serviço para análises de marketing e relacionamento"""
    
    def __init__(self, db: Session):
        self.db = db
        self.date_utils = DateUtils()
        self.metrics_utils = MetricsUtils()
        self.query_utils = QueryUtils()
    
    def get_marketing_overview(self, period_type: str = 'last_90_days') -> Dict[str, Any]:
        """
        Visão geral de marketing
        
        Args:
            period_type: Período para análise
        
        Returns:
            Dicionário com overview de marketing
        """
        try:
            start_date, end_date = self.date_utils.get_period_range(period_type)
            comp_start, comp_end = self.date_utils.get_comparison_period(
                start_date, end_date, 'same_period_last_year'
            )
            
            overview_query = text("""
                WITH current_period AS (
                    SELECT 
                        COUNT(l.id) as total_leads,
                        COUNT(CASE WHEN l.status = 'Convertido' THEN 1 END) as leads_convertidos,
                        COUNT(CASE WHEN l.status = 'Qualificado' THEN 1 END) as leads_qualificados,
                        COUNT(CASE WHEN l.status = 'Novo' THEN 1 END) as leads_novos,
                        SUM(l.valor_potencial) as valor_potencial_total,
                        AVG(l.valor_potencial) as valor_potencial_medio,
                        AVG(l.probabilidade) as probabilidade_media
                    FROM fato_lead l
                    WHERE l.data_criacao BETWEEN :start_date AND :end_date
                ),
                comparison_period AS (
                    SELECT 
                        COUNT(l.id) as total_leads_comp,
                        COUNT(CASE WHEN l.status = 'Convertido' THEN 1 END) as leads_convertidos_comp
                    FROM fato_lead l
                    WHERE l.data_criacao BETWEEN :comp_start AND :comp_end
                ),
                campaigns_stats AS (
                    SELECT 
                        COUNT(c.id) as campanhas_ativas,
                        SUM(c.leads_gerados) as leads_campanhas,
                        SUM(c.conversoes) as conversoes_campanhas,
                        SUM(c.custo) as custo_total,
                        SUM(c.receita) as receita_campanhas,
                        AVG(c.roi) as roi_medio
                    FROM fato_campanha c
                    WHERE c.data_inicio <= :end_date 
                    AND (c.data_fim >= :start_date OR c.data_fim IS NULL)
                    AND c.status = 'Ativa'
                )
                SELECT 
                    cp.*,
                    comp.*,
                    cs.*
                FROM current_period cp
                CROSS JOIN comparison_period comp
                CROSS JOIN campaigns_stats cs
            """)
            
            result = self.db.execute(overview_query, {
                'start_date': start_date,
                'end_date': end_date,
                'comp_start': comp_start,
                'comp_end': comp_end
            })
            
            row = result.fetchone()
            
            if not row:
                return self._get_empty_overview()
            
            # Calcula métricas
            total_leads = int(row.total_leads or 0)
            leads_convertidos = int(row.leads_convertidos or 0)
            
            taxa_conversao = self.metrics_utils.calculate_conversion_rate(
                leads_convertidos, total_leads
            ) if total_leads > 0 else 0
            
            crescimento_leads = self.metrics_utils.calculate_growth_rate(
                total_leads, row.total_leads_comp or 0
            )
            
            crescimento_conversao = self.metrics_utils.calculate_growth_rate(
                leads_convertidos, row.leads_convertidos_comp or 0
            )
            
            # Calcula ROI total das campanhas
            custo_total = float(row.custo_total or 0)
            receita_campanhas = float(row.receita_campanhas or 0)
            roi_total = self.metrics_utils.calculate_roi(receita_campanhas, custo_total)
            
            return {
                "total_leads": total_leads,
                "leads_convertidos": leads_convertidos,
                "leads_qualificados": int(row.leads_qualificados or 0),
                "leads_novos": int(row.leads_novos or 0),
                "leads_perdidos": 0,  # Calculado a partir dos dados
                "leads_em_andamento": total_leads - leads_convertidos,
                "taxa_conversao": taxa_conversao or 0,
                "valor_potencial_total": float(row.valor_potencial_total or 0),
                "valor_potencial_medio": round(float(row.valor_potencial_medio or 0), 2),
                "probabilidade_media": round(float(row.probabilidade_media or 0), 1),
                "campanhas_ativas": int(row.campanhas_ativas or 0),
                "leads_campanhas": int(row.leads_campanhas or 0),
                "conversoes_campanhas": int(row.conversoes_campanhas or 0),
                "custo_total": custo_total,
                "receita_campanhas": receita_campanhas,
                "roi_total": roi_total or 0.0,
                "roi_medio": round(float(row.roi_medio or 0), 2),
                "crescimento_leads": crescimento_leads,
                "crescimento_conversao": crescimento_conversao,
                "periodo": {
                    "inicio": start_date.isoformat(),
                    "fim": end_date.isoformat(),
                    "tipo": period_type
                },
                "source": "postgresql_real_data"
            }
            
        except Exception as e:
            logger.error(f"Erro ao buscar overview de marketing: {e}")
            return self._get_empty_overview()
    
    def get_leads_funnel_analysis(self, period_type: str = 'last_90_days') -> List[Dict[str, Any]]:
        """
        Análise do funil de vendas
        
        Args:
            period_type: Período para análise
        
        Returns:
            Lista com dados do funil
        """
        try:
            start_date, end_date = self.date_utils.get_period_range(period_type)
            
            funnel_query = text("""
                SELECT 
                    status,
                    origem,
                    COUNT(*) as quantidade,
                    SUM(valor_potencial) as valor_potencial,
                    AVG(valor_potencial) as valor_medio,
                    AVG(probabilidade) as probabilidade_media
                FROM fato_lead
                WHERE data_criacao BETWEEN :start_date AND :end_date
                GROUP BY status, origem
                ORDER BY 
                    CASE status
                        WHEN 'Novo' THEN 1
                        WHEN 'Qualificado' THEN 2
                        WHEN 'Proposta' THEN 3
                        WHEN 'Negociação' THEN 4
                        WHEN 'Convertido' THEN 5
                        WHEN 'Perdido' THEN 6
                    END,
                    origem
            """)
            
            result = self.db.execute(funnel_query, {
                'start_date': start_date,
                'end_date': end_date
            })
            rows = result.fetchall()
            
            # Organiza dados por status e origem
            funnel_data = {}
            total_leads = 0
            total_valor = 0
            
            for row in rows:
                status = row.status
                origem = row.origem
                quantidade = int(row.quantidade)
                valor = float(row.valor_potencial or 0)
                
                total_leads += quantidade
                total_valor += valor
                
                if status not in funnel_data:
                    funnel_data[status] = {
                        "status": status,
                        "total_quantidade": 0,
                        "total_valor": 0,
                        "origens": []
                    }
                
                funnel_data[status]["total_quantidade"] += quantidade
                funnel_data[status]["total_valor"] += valor
                funnel_data[status]["origens"].append({
                    "origem": origem,
                    "quantidade": quantidade,
                    "valor_potencial": valor,
                    "valor_medio": round(float(row.valor_medio or 0), 2),
                    "probabilidade_media": round(float(row.probabilidade_media or 0), 1)
                })
            
            # Converte para lista ordenada com percentuais
            funnel_list = []
            status_order = ['Novo', 'Qualificado', 'Proposta', 'Negociação', 'Convertido', 'Perdido']
            
            for status in status_order:
                if status in funnel_data:
                    data = funnel_data[status]
                    funnel_list.append({
                        "status": status,
                        "quantidade": data["total_quantidade"],
                        "valor_potencial": round(data["total_valor"], 2),
                        "percentual_quantidade": self.metrics_utils.calculate_percentage(
                            data["total_quantidade"], total_leads
                        ) if total_leads > 0 else 0,
                        "percentual_valor": self.metrics_utils.calculate_percentage(
                            data["total_valor"], total_valor
                        ) if total_valor > 0 else 0,
                        "origens": sorted(data["origens"], key=lambda x: x["quantidade"], reverse=True)
                    })
            
            return funnel_list
            
        except Exception as e:
            logger.error(f"Erro ao buscar análise do funil: {e}")
            return []
    
    def get_campaigns_performance(self, period_type: str = 'last_90_days') -> List[Dict[str, Any]]:
        """
        Performance de campanhas
        
        Args:
            period_type: Período para análise
        
        Returns:
            Lista com performance das campanhas
        """
        try:
            start_date, end_date = self.date_utils.get_period_range(period_type)
            
            campaigns_query = text("""
                SELECT 
                    c.id,
                    c.nome,
                    c.tipo,
                    c.status,
                    c.data_inicio,
                    c.data_fim,
                    c.leads_gerados,
                    c.conversoes,
                    c.custo,
                    c.receita,
                    c.roi,
                    COUNT(l.id) as leads_reais,
                    COUNT(CASE WHEN l.status = 'Convertido' THEN 1 END) as conversoes_reais
                FROM fato_campanha c
                LEFT JOIN fato_lead l ON c.id = l.campanha_id
                WHERE c.data_inicio <= :end_date 
                AND (c.data_fim >= :start_date OR c.data_fim IS NULL)
                GROUP BY c.id, c.nome, c.tipo, c.status, c.data_inicio, c.data_fim, 
                         c.leads_gerados, c.conversoes, c.custo, c.receita, c.roi
                ORDER BY c.roi DESC NULLS LAST
            """)
            
            result = self.db.execute(campaigns_query, {
                'start_date': start_date,
                'end_date': end_date
            })
            rows = result.fetchall()
            
            campaigns_data = []
            for row in rows:
                leads_gerados = int(row.leads_gerados or 0)
                conversoes = int(row.conversoes or 0)
                leads_reais = int(row.leads_reais or 0)
                conversoes_reais = int(row.conversoes_reais or 0)
                custo = float(row.custo or 0)
                receita = float(row.receita or 0)
                
                # Calcula métricas
                taxa_conversao = self.metrics_utils.calculate_conversion_rate(
                    conversoes_reais, leads_reais
                ) if leads_reais > 0 else 0
                
                cpl = custo / leads_reais if leads_reais > 0 else 0  # Custo por Lead
                cpa = custo / conversoes_reais if conversoes_reais > 0 else 0  # Custo por Aquisição
                
                roi_calculado = self.metrics_utils.calculate_roi(receita, custo)
                
                # Performance score
                performance_score = self.metrics_utils.calculate_performance_score(
                    {
                        'roi': max(roi_calculado or 0, 0) / 100,  # Normaliza ROI
                        'conversao': taxa_conversao or 0,
                        'leads': min(leads_reais / 100, 1),  # Normaliza leads (max 100)
                        'eficiencia': max(1 - (cpl / 100), 0) if cpl > 0 else 0  # Eficiência de custo
                    },
                    {
                        'roi': 0.4,
                        'conversao': 0.3,
                        'leads': 0.2,
                        'eficiencia': 0.1
                    }
                )
                
                campaigns_data.append({
                    "id": int(row.id),
                    "nome": row.nome,
                    "tipo": row.tipo,
                    "status": row.status,
                    "data_inicio": row.data_inicio.isoformat() if row.data_inicio else None,
                    "data_fim": row.data_fim.isoformat() if row.data_fim else None,
                    "leads_gerados": leads_gerados,
                    "leads_reais": leads_reais,
                    "conversoes": conversoes,
                    "conversoes_reais": conversoes_reais,
                    "taxa_conversao": taxa_conversao or 0,
                    "custo": custo,
                    "receita": receita,
                    "roi": float(row.roi or 0),
                    "roi_calculado": roi_calculado,
                    "cpl": round(cpl, 2),
                    "cpa": round(cpa, 2),
                    "performance_score": performance_score
                })
            
            return campaigns_data
            
        except Exception as e:
            logger.error(f"Erro ao buscar performance de campanhas: {e}")
            return []
    
    def get_lead_sources_analysis(self, period_type: str = 'last_90_days') -> List[Dict[str, Any]]:
        """
        Análise de fontes de leads
        
        Args:
            period_type: Período para análise
        
        Returns:
            Lista com análise de fontes
        """
        try:
            start_date, end_date = self.date_utils.get_period_range(period_type)
            
            sources_query = text("""
                SELECT 
                    origem,
                    COUNT(*) as total_leads,
                    COUNT(CASE WHEN status = 'Convertido' THEN 1 END) as conversoes,
                    SUM(valor_potencial) as valor_potencial_total,
                    AVG(valor_potencial) as valor_potencial_medio,
                    AVG(probabilidade) as probabilidade_media,
                    COUNT(DISTINCT campanha_id) as campanhas_associadas
                FROM fato_lead
                WHERE data_criacao BETWEEN :start_date AND :end_date
                GROUP BY origem
                ORDER BY COUNT(*) DESC
            """)
            
            result = self.db.execute(sources_query, {
                'start_date': start_date,
                'end_date': end_date
            })
            rows = result.fetchall()
            
            sources_data = []
            total_leads = sum(int(row.total_leads) for row in rows)
            total_conversoes = sum(int(row.conversoes) for row in rows)
            total_valor = sum(float(row.valor_potencial_total or 0) for row in rows)
            
            for row in rows:
                leads = int(row.total_leads)
                conversoes = int(row.conversoes)
                valor = float(row.valor_potencial_total or 0)
                
                taxa_conversao = self.metrics_utils.calculate_conversion_rate(
                    conversoes, leads
                ) if leads > 0 else 0
                
                sources_data.append({
                    "origem": row.origem,
                    "total_leads": leads,
                    "conversoes": conversoes,
                    "taxa_conversao": taxa_conversao or 0,
                    "valor_potencial_total": valor,
                    "valor_potencial_medio": round(float(row.valor_potencial_medio or 0), 2),
                    "probabilidade_media": round(float(row.probabilidade_media or 0), 1),
                    "campanhas_associadas": int(row.campanhas_associadas or 0),
                    "participacao_leads": self.metrics_utils.calculate_percentage(
                        leads, total_leads
                    ) if total_leads > 0 else 0,
                    "participacao_conversoes": self.metrics_utils.calculate_percentage(
                        conversoes, total_conversoes
                    ) if total_conversoes > 0 else 0,
                    "participacao_valor": self.metrics_utils.calculate_percentage(
                        valor, total_valor
                    ) if total_valor > 0 else 0
                })
            
            return sources_data
            
        except Exception as e:
            logger.error(f"Erro ao buscar análise de fontes: {e}")
            return []
    
    def get_conversion_timeline(self, period_type: str = 'last_12_months') -> List[Dict[str, Any]]:
        """
        Timeline de conversões
        
        Args:
            period_type: Período para análise
        
        Returns:
            Lista com timeline de conversões
        """
        try:
            start_date, end_date = self.date_utils.get_period_range(period_type)
            
            timeline_query = text("""
                SELECT 
                    EXTRACT(YEAR FROM data_criacao) as ano,
                    EXTRACT(MONTH FROM data_criacao) as mes,
                    COUNT(*) as total_leads,
                    COUNT(CASE WHEN status = 'Convertido' THEN 1 END) as conversoes,
                    SUM(valor_potencial) as valor_potencial,
                    SUM(CASE WHEN status = 'Convertido' THEN valor_potencial ELSE 0 END) as valor_convertido
                FROM fato_lead
                WHERE data_criacao BETWEEN :start_date AND :end_date
                GROUP BY EXTRACT(YEAR FROM data_criacao), EXTRACT(MONTH FROM data_criacao)
                ORDER BY ano, mes
            """)
            
            result = self.db.execute(timeline_query, {
                'start_date': start_date,
                'end_date': end_date
            })
            rows = result.fetchall()
            
            timeline_data = []
            for row in rows:
                total_leads = int(row.total_leads)
                conversoes = int(row.conversoes)
                
                taxa_conversao = self.metrics_utils.calculate_conversion_rate(
                    conversoes, total_leads
                ) if total_leads > 0 else 0
                
                timeline_data.append({
                    "ano": int(row.ano),
                    "mes": int(row.mes),
                    "mes_nome": self.date_utils.get_month_name(int(row.mes)),
                    "periodo": f"{int(row.mes):02d}/{int(row.ano)}",
                    "total_leads": total_leads,
                    "conversoes": conversoes,
                    "taxa_conversao": taxa_conversao or 0,
                    "valor_potencial": float(row.valor_potencial or 0),
                    "valor_convertido": float(row.valor_convertido or 0)
                })
            
            return timeline_data
            
        except Exception as e:
            logger.error(f"Erro ao buscar timeline de conversões: {e}")
            return []
    
    def _get_empty_overview(self) -> Dict[str, Any]:
        """Retorna estrutura vazia de overview"""
        return {
            "total_leads": 0,
            "leads_convertidos": 0,
            "leads_qualificados": 0,
            "leads_novos": 0,
            "taxa_conversao": 0,
            "valor_potencial_total": 0,
            "valor_potencial_medio": 0,
            "probabilidade_media": 0,
            "campanhas_ativas": 0,
            "leads_campanhas": 0,
            "conversoes_campanhas": 0,
            "custo_total": 0,
            "receita_campanhas": 0,
            "roi_total": None,
            "roi_medio": 0,
            "crescimento_leads": None,
            "crescimento_conversao": None,
            "periodo": {},
            "source": "postgresql_real_data"
        }
