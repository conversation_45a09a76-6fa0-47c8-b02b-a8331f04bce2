import React5, { useRef, useCallback, useEffect, useState } from 'react';
import { addons, types, useGlobalTypes, useStorybookApi, useGlobals } from 'storybook/internal/manager-api';
import { Separator, WithTooltip, TooltipLinkList, IconButton, Icons } from 'storybook/internal/components';

var defaultItemValues={type:"item",value:""},normalizeArgType=(key,argType)=>({...argType,name:argType.name||key,description:argType.description||key,toolbar:{...argType.toolbar,items:argType.toolbar.items.map(_item=>{let item=typeof _item=="string"?{value:_item,title:_item}:_item;return item.type==="reset"&&argType.toolbar.icon&&(item.icon=argType.toolbar.icon,item.hideIcon=!0),{...defaultItemValues,...item}})}});var disallowedCycleableItemTypes=["reset"],createCycleValueArray=items=>items.filter(item=>!disallowedCycleableItemTypes.includes(item.type)).map(item=>item.value);var ADDON_ID="addon-toolbars";var registerShortcuts=async(api,id,shortcuts)=>{shortcuts&&shortcuts.next&&await api.setAddonShortcut(ADDON_ID,{label:shortcuts.next.label,defaultShortcut:shortcuts.next.keys,actionName:`${id}:next`,action:shortcuts.next.action}),shortcuts&&shortcuts.previous&&await api.setAddonShortcut(ADDON_ID,{label:shortcuts.previous.label,defaultShortcut:shortcuts.previous.keys,actionName:`${id}:previous`,action:shortcuts.previous.action}),shortcuts&&shortcuts.reset&&await api.setAddonShortcut(ADDON_ID,{label:shortcuts.reset.label,defaultShortcut:shortcuts.reset.keys,actionName:`${id}:reset`,action:shortcuts.reset.action});};var withKeyboardCycle=Component=>props=>{let{id,toolbar:{items,shortcuts}}=props,api=useStorybookApi(),[globals,updateGlobals]=useGlobals(),cycleValues=useRef([]),currentValue=globals[id],reset=useCallback(()=>{updateGlobals({[id]:""});},[updateGlobals]),setNext=useCallback(()=>{let values=cycleValues.current,currentIndex=values.indexOf(currentValue),newCurrentIndex=currentIndex===values.length-1?0:currentIndex+1,newCurrent=cycleValues.current[newCurrentIndex];updateGlobals({[id]:newCurrent});},[cycleValues,currentValue,updateGlobals]),setPrevious=useCallback(()=>{let values=cycleValues.current,indexOf=values.indexOf(currentValue),currentIndex=indexOf>-1?indexOf:0,newCurrentIndex=currentIndex===0?values.length-1:currentIndex-1,newCurrent=cycleValues.current[newCurrentIndex];updateGlobals({[id]:newCurrent});},[cycleValues,currentValue,updateGlobals]);return useEffect(()=>{shortcuts&&registerShortcuts(api,id,{next:{...shortcuts.next,action:setNext},previous:{...shortcuts.previous,action:setPrevious},reset:{...shortcuts.reset,action:reset}});},[api,id,shortcuts,setNext,setPrevious,reset]),useEffect(()=>{cycleValues.current=createCycleValueArray(items);},[]),React5.createElement(Component,{cycleValues:cycleValues.current,...props})};var getSelectedItem=({currentValue,items})=>currentValue!=null&&items.find(item=>item.value===currentValue&&item.type!=="reset"),getSelectedIcon=({currentValue,items})=>{let selectedItem=getSelectedItem({currentValue,items});if(selectedItem)return selectedItem.icon},getSelectedTitle=({currentValue,items})=>{let selectedItem=getSelectedItem({currentValue,items});if(selectedItem)return selectedItem.title};var ToolbarMenuButton=({active,disabled,title,icon,description,onClick})=>React5.createElement(IconButton,{active,title:description,disabled,onClick:disabled?()=>{}:onClick},icon&&React5.createElement(Icons,{icon,__suppressDeprecationWarning:!0}),title?`\xA0${title}`:null);var ToolbarMenuListItem=({right,title,value,icon,hideIcon,onClick,disabled,currentValue})=>{let Icon=icon&&React5.createElement(Icons,{style:{opacity:1},icon,__suppressDeprecationWarning:!0}),Item={id:value??"_reset",active:currentValue===value,right,title,disabled,onClick};return icon&&!hideIcon&&(Item.icon=Icon),Item};var ToolbarMenuList=withKeyboardCycle(({id,name,description,toolbar:{icon:_icon,items,title:_title,preventDynamicIcon,dynamicTitle}})=>{let[globals,updateGlobals,storyGlobals]=useGlobals(),[isTooltipVisible,setIsTooltipVisible]=useState(!1),currentValue=globals[id],hasGlobalValue=!!currentValue,isOverridden=id in storyGlobals,icon=_icon,title=_title;preventDynamicIcon||(icon=getSelectedIcon({currentValue,items})||icon),dynamicTitle&&(title=getSelectedTitle({currentValue,items})||title),!title&&!icon&&console.warn(`Toolbar '${name}' has no title or icon`);let handleItemClick=useCallback(value=>{updateGlobals({[id]:value});},[id,updateGlobals]);return React5.createElement(WithTooltip,{placement:"top",tooltip:({onHide})=>{let links=items.filter(({type})=>{let shouldReturn=!0;return type==="reset"&&!currentValue&&(shouldReturn=!1),shouldReturn}).map(item=>ToolbarMenuListItem({...item,currentValue,disabled:isOverridden,onClick:()=>{handleItemClick(item.value),onHide();}}));return React5.createElement(TooltipLinkList,{links})},closeOnOutsideClick:!0,onVisibleChange:setIsTooltipVisible},React5.createElement(ToolbarMenuButton,{active:isTooltipVisible||hasGlobalValue,disabled:isOverridden,description:description||"",icon,title:title||""}))});var ToolbarManager=()=>{let globalTypes=useGlobalTypes(),globalIds=Object.keys(globalTypes).filter(id=>!!globalTypes[id].toolbar);return globalIds.length?React5.createElement(React5.Fragment,null,React5.createElement(Separator,null),globalIds.map(id=>{let normalizedArgType=normalizeArgType(id,globalTypes[id]);return React5.createElement(ToolbarMenuList,{key:id,id,...normalizedArgType})})):null};addons.register(ADDON_ID,()=>addons.add(ADDON_ID,{title:ADDON_ID,type:types.TOOL,match:({tabId})=>!tabId,render:()=>React5.createElement(ToolbarManager,null)}));
