import * as React9 from 'react';
import React9__default, { memo, useState, useRef, useEffect, useMemo, Fragment, useCallback } from 'react';
import { IconButton, TooltipNote, Button, Separator, P, AddonPanel, EmptyTabContent, Link, WithTooltip, Bar, Spaced, Badge } from 'storybook/internal/components';
import { useAddonState, useChannel, useParameter, addons, types, Consumer, useStorybookApi } from 'storybook/internal/manager-api';
import { STORY_RENDER_PHASE_CHANGED, STORY_THREW_EXCEPTION, PLAY_FUNCTION_THREW_EXCEPTION, UNHANDLED_ERRORS_WHILE_PLAYING, FORCE_REMOUNT } from 'storybook/internal/core-events';
import { global } from '@storybook/global';
import { CallStates, EVENTS } from '@storybook/instrumenter';
import { styled, typography, useTheme } from 'storybook/internal/theming';
import { transparentize } from 'polished';
import { VideoIcon, DocumentIcon, ListUnorderedIcon, RewindIcon, PlayBackIcon, PlayNextIcon, FastForwardIcon, SyncIcon, CircleIcon, PlayIcon, StopAltIcon, CheckIcon } from '@storybook/icons';

var __create=Object.create;var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty;var __commonJS=(cb,mod)=>function(){return mod||(0, cb[__getOwnPropNames(cb)[0]])((mod={exports:{}}).exports,mod),mod.exports};var __copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to};var __toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod));var require_entities=__commonJS({"../../node_modules/ansi-to-html/node_modules/entities/lib/maps/entities.json"(exports,module){module.exports={Aacute:"\xC1",aacute:"\xE1",Abreve:"\u0102",abreve:"\u0103",ac:"\u223E",acd:"\u223F",acE:"\u223E\u0333",Acirc:"\xC2",acirc:"\xE2",acute:"\xB4",Acy:"\u0410",acy:"\u0430",AElig:"\xC6",aelig:"\xE6",af:"\u2061",Afr:"\u{1D504}",afr:"\u{1D51E}",Agrave:"\xC0",agrave:"\xE0",alefsym:"\u2135",aleph:"\u2135",Alpha:"\u0391",alpha:"\u03B1",Amacr:"\u0100",amacr:"\u0101",amalg:"\u2A3F",amp:"&",AMP:"&",andand:"\u2A55",And:"\u2A53",and:"\u2227",andd:"\u2A5C",andslope:"\u2A58",andv:"\u2A5A",ang:"\u2220",ange:"\u29A4",angle:"\u2220",angmsdaa:"\u29A8",angmsdab:"\u29A9",angmsdac:"\u29AA",angmsdad:"\u29AB",angmsdae:"\u29AC",angmsdaf:"\u29AD",angmsdag:"\u29AE",angmsdah:"\u29AF",angmsd:"\u2221",angrt:"\u221F",angrtvb:"\u22BE",angrtvbd:"\u299D",angsph:"\u2222",angst:"\xC5",angzarr:"\u237C",Aogon:"\u0104",aogon:"\u0105",Aopf:"\u{1D538}",aopf:"\u{1D552}",apacir:"\u2A6F",ap:"\u2248",apE:"\u2A70",ape:"\u224A",apid:"\u224B",apos:"'",ApplyFunction:"\u2061",approx:"\u2248",approxeq:"\u224A",Aring:"\xC5",aring:"\xE5",Ascr:"\u{1D49C}",ascr:"\u{1D4B6}",Assign:"\u2254",ast:"*",asymp:"\u2248",asympeq:"\u224D",Atilde:"\xC3",atilde:"\xE3",Auml:"\xC4",auml:"\xE4",awconint:"\u2233",awint:"\u2A11",backcong:"\u224C",backepsilon:"\u03F6",backprime:"\u2035",backsim:"\u223D",backsimeq:"\u22CD",Backslash:"\u2216",Barv:"\u2AE7",barvee:"\u22BD",barwed:"\u2305",Barwed:"\u2306",barwedge:"\u2305",bbrk:"\u23B5",bbrktbrk:"\u23B6",bcong:"\u224C",Bcy:"\u0411",bcy:"\u0431",bdquo:"\u201E",becaus:"\u2235",because:"\u2235",Because:"\u2235",bemptyv:"\u29B0",bepsi:"\u03F6",bernou:"\u212C",Bernoullis:"\u212C",Beta:"\u0392",beta:"\u03B2",beth:"\u2136",between:"\u226C",Bfr:"\u{1D505}",bfr:"\u{1D51F}",bigcap:"\u22C2",bigcirc:"\u25EF",bigcup:"\u22C3",bigodot:"\u2A00",bigoplus:"\u2A01",bigotimes:"\u2A02",bigsqcup:"\u2A06",bigstar:"\u2605",bigtriangledown:"\u25BD",bigtriangleup:"\u25B3",biguplus:"\u2A04",bigvee:"\u22C1",bigwedge:"\u22C0",bkarow:"\u290D",blacklozenge:"\u29EB",blacksquare:"\u25AA",blacktriangle:"\u25B4",blacktriangledown:"\u25BE",blacktriangleleft:"\u25C2",blacktriangleright:"\u25B8",blank:"\u2423",blk12:"\u2592",blk14:"\u2591",blk34:"\u2593",block:"\u2588",bne:"=\u20E5",bnequiv:"\u2261\u20E5",bNot:"\u2AED",bnot:"\u2310",Bopf:"\u{1D539}",bopf:"\u{1D553}",bot:"\u22A5",bottom:"\u22A5",bowtie:"\u22C8",boxbox:"\u29C9",boxdl:"\u2510",boxdL:"\u2555",boxDl:"\u2556",boxDL:"\u2557",boxdr:"\u250C",boxdR:"\u2552",boxDr:"\u2553",boxDR:"\u2554",boxh:"\u2500",boxH:"\u2550",boxhd:"\u252C",boxHd:"\u2564",boxhD:"\u2565",boxHD:"\u2566",boxhu:"\u2534",boxHu:"\u2567",boxhU:"\u2568",boxHU:"\u2569",boxminus:"\u229F",boxplus:"\u229E",boxtimes:"\u22A0",boxul:"\u2518",boxuL:"\u255B",boxUl:"\u255C",boxUL:"\u255D",boxur:"\u2514",boxuR:"\u2558",boxUr:"\u2559",boxUR:"\u255A",boxv:"\u2502",boxV:"\u2551",boxvh:"\u253C",boxvH:"\u256A",boxVh:"\u256B",boxVH:"\u256C",boxvl:"\u2524",boxvL:"\u2561",boxVl:"\u2562",boxVL:"\u2563",boxvr:"\u251C",boxvR:"\u255E",boxVr:"\u255F",boxVR:"\u2560",bprime:"\u2035",breve:"\u02D8",Breve:"\u02D8",brvbar:"\xA6",bscr:"\u{1D4B7}",Bscr:"\u212C",bsemi:"\u204F",bsim:"\u223D",bsime:"\u22CD",bsolb:"\u29C5",bsol:"\\",bsolhsub:"\u27C8",bull:"\u2022",bullet:"\u2022",bump:"\u224E",bumpE:"\u2AAE",bumpe:"\u224F",Bumpeq:"\u224E",bumpeq:"\u224F",Cacute:"\u0106",cacute:"\u0107",capand:"\u2A44",capbrcup:"\u2A49",capcap:"\u2A4B",cap:"\u2229",Cap:"\u22D2",capcup:"\u2A47",capdot:"\u2A40",CapitalDifferentialD:"\u2145",caps:"\u2229\uFE00",caret:"\u2041",caron:"\u02C7",Cayleys:"\u212D",ccaps:"\u2A4D",Ccaron:"\u010C",ccaron:"\u010D",Ccedil:"\xC7",ccedil:"\xE7",Ccirc:"\u0108",ccirc:"\u0109",Cconint:"\u2230",ccups:"\u2A4C",ccupssm:"\u2A50",Cdot:"\u010A",cdot:"\u010B",cedil:"\xB8",Cedilla:"\xB8",cemptyv:"\u29B2",cent:"\xA2",centerdot:"\xB7",CenterDot:"\xB7",cfr:"\u{1D520}",Cfr:"\u212D",CHcy:"\u0427",chcy:"\u0447",check:"\u2713",checkmark:"\u2713",Chi:"\u03A7",chi:"\u03C7",circ:"\u02C6",circeq:"\u2257",circlearrowleft:"\u21BA",circlearrowright:"\u21BB",circledast:"\u229B",circledcirc:"\u229A",circleddash:"\u229D",CircleDot:"\u2299",circledR:"\xAE",circledS:"\u24C8",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",cir:"\u25CB",cirE:"\u29C3",cire:"\u2257",cirfnint:"\u2A10",cirmid:"\u2AEF",cirscir:"\u29C2",ClockwiseContourIntegral:"\u2232",CloseCurlyDoubleQuote:"\u201D",CloseCurlyQuote:"\u2019",clubs:"\u2663",clubsuit:"\u2663",colon:":",Colon:"\u2237",Colone:"\u2A74",colone:"\u2254",coloneq:"\u2254",comma:",",commat:"@",comp:"\u2201",compfn:"\u2218",complement:"\u2201",complexes:"\u2102",cong:"\u2245",congdot:"\u2A6D",Congruent:"\u2261",conint:"\u222E",Conint:"\u222F",ContourIntegral:"\u222E",copf:"\u{1D554}",Copf:"\u2102",coprod:"\u2210",Coproduct:"\u2210",copy:"\xA9",COPY:"\xA9",copysr:"\u2117",CounterClockwiseContourIntegral:"\u2233",crarr:"\u21B5",cross:"\u2717",Cross:"\u2A2F",Cscr:"\u{1D49E}",cscr:"\u{1D4B8}",csub:"\u2ACF",csube:"\u2AD1",csup:"\u2AD0",csupe:"\u2AD2",ctdot:"\u22EF",cudarrl:"\u2938",cudarrr:"\u2935",cuepr:"\u22DE",cuesc:"\u22DF",cularr:"\u21B6",cularrp:"\u293D",cupbrcap:"\u2A48",cupcap:"\u2A46",CupCap:"\u224D",cup:"\u222A",Cup:"\u22D3",cupcup:"\u2A4A",cupdot:"\u228D",cupor:"\u2A45",cups:"\u222A\uFE00",curarr:"\u21B7",curarrm:"\u293C",curlyeqprec:"\u22DE",curlyeqsucc:"\u22DF",curlyvee:"\u22CE",curlywedge:"\u22CF",curren:"\xA4",curvearrowleft:"\u21B6",curvearrowright:"\u21B7",cuvee:"\u22CE",cuwed:"\u22CF",cwconint:"\u2232",cwint:"\u2231",cylcty:"\u232D",dagger:"\u2020",Dagger:"\u2021",daleth:"\u2138",darr:"\u2193",Darr:"\u21A1",dArr:"\u21D3",dash:"\u2010",Dashv:"\u2AE4",dashv:"\u22A3",dbkarow:"\u290F",dblac:"\u02DD",Dcaron:"\u010E",dcaron:"\u010F",Dcy:"\u0414",dcy:"\u0434",ddagger:"\u2021",ddarr:"\u21CA",DD:"\u2145",dd:"\u2146",DDotrahd:"\u2911",ddotseq:"\u2A77",deg:"\xB0",Del:"\u2207",Delta:"\u0394",delta:"\u03B4",demptyv:"\u29B1",dfisht:"\u297F",Dfr:"\u{1D507}",dfr:"\u{1D521}",dHar:"\u2965",dharl:"\u21C3",dharr:"\u21C2",DiacriticalAcute:"\xB4",DiacriticalDot:"\u02D9",DiacriticalDoubleAcute:"\u02DD",DiacriticalGrave:"`",DiacriticalTilde:"\u02DC",diam:"\u22C4",diamond:"\u22C4",Diamond:"\u22C4",diamondsuit:"\u2666",diams:"\u2666",die:"\xA8",DifferentialD:"\u2146",digamma:"\u03DD",disin:"\u22F2",div:"\xF7",divide:"\xF7",divideontimes:"\u22C7",divonx:"\u22C7",DJcy:"\u0402",djcy:"\u0452",dlcorn:"\u231E",dlcrop:"\u230D",dollar:"$",Dopf:"\u{1D53B}",dopf:"\u{1D555}",Dot:"\xA8",dot:"\u02D9",DotDot:"\u20DC",doteq:"\u2250",doteqdot:"\u2251",DotEqual:"\u2250",dotminus:"\u2238",dotplus:"\u2214",dotsquare:"\u22A1",doublebarwedge:"\u2306",DoubleContourIntegral:"\u222F",DoubleDot:"\xA8",DoubleDownArrow:"\u21D3",DoubleLeftArrow:"\u21D0",DoubleLeftRightArrow:"\u21D4",DoubleLeftTee:"\u2AE4",DoubleLongLeftArrow:"\u27F8",DoubleLongLeftRightArrow:"\u27FA",DoubleLongRightArrow:"\u27F9",DoubleRightArrow:"\u21D2",DoubleRightTee:"\u22A8",DoubleUpArrow:"\u21D1",DoubleUpDownArrow:"\u21D5",DoubleVerticalBar:"\u2225",DownArrowBar:"\u2913",downarrow:"\u2193",DownArrow:"\u2193",Downarrow:"\u21D3",DownArrowUpArrow:"\u21F5",DownBreve:"\u0311",downdownarrows:"\u21CA",downharpoonleft:"\u21C3",downharpoonright:"\u21C2",DownLeftRightVector:"\u2950",DownLeftTeeVector:"\u295E",DownLeftVectorBar:"\u2956",DownLeftVector:"\u21BD",DownRightTeeVector:"\u295F",DownRightVectorBar:"\u2957",DownRightVector:"\u21C1",DownTeeArrow:"\u21A7",DownTee:"\u22A4",drbkarow:"\u2910",drcorn:"\u231F",drcrop:"\u230C",Dscr:"\u{1D49F}",dscr:"\u{1D4B9}",DScy:"\u0405",dscy:"\u0455",dsol:"\u29F6",Dstrok:"\u0110",dstrok:"\u0111",dtdot:"\u22F1",dtri:"\u25BF",dtrif:"\u25BE",duarr:"\u21F5",duhar:"\u296F",dwangle:"\u29A6",DZcy:"\u040F",dzcy:"\u045F",dzigrarr:"\u27FF",Eacute:"\xC9",eacute:"\xE9",easter:"\u2A6E",Ecaron:"\u011A",ecaron:"\u011B",Ecirc:"\xCA",ecirc:"\xEA",ecir:"\u2256",ecolon:"\u2255",Ecy:"\u042D",ecy:"\u044D",eDDot:"\u2A77",Edot:"\u0116",edot:"\u0117",eDot:"\u2251",ee:"\u2147",efDot:"\u2252",Efr:"\u{1D508}",efr:"\u{1D522}",eg:"\u2A9A",Egrave:"\xC8",egrave:"\xE8",egs:"\u2A96",egsdot:"\u2A98",el:"\u2A99",Element:"\u2208",elinters:"\u23E7",ell:"\u2113",els:"\u2A95",elsdot:"\u2A97",Emacr:"\u0112",emacr:"\u0113",empty:"\u2205",emptyset:"\u2205",EmptySmallSquare:"\u25FB",emptyv:"\u2205",EmptyVerySmallSquare:"\u25AB",emsp13:"\u2004",emsp14:"\u2005",emsp:"\u2003",ENG:"\u014A",eng:"\u014B",ensp:"\u2002",Eogon:"\u0118",eogon:"\u0119",Eopf:"\u{1D53C}",eopf:"\u{1D556}",epar:"\u22D5",eparsl:"\u29E3",eplus:"\u2A71",epsi:"\u03B5",Epsilon:"\u0395",epsilon:"\u03B5",epsiv:"\u03F5",eqcirc:"\u2256",eqcolon:"\u2255",eqsim:"\u2242",eqslantgtr:"\u2A96",eqslantless:"\u2A95",Equal:"\u2A75",equals:"=",EqualTilde:"\u2242",equest:"\u225F",Equilibrium:"\u21CC",equiv:"\u2261",equivDD:"\u2A78",eqvparsl:"\u29E5",erarr:"\u2971",erDot:"\u2253",escr:"\u212F",Escr:"\u2130",esdot:"\u2250",Esim:"\u2A73",esim:"\u2242",Eta:"\u0397",eta:"\u03B7",ETH:"\xD0",eth:"\xF0",Euml:"\xCB",euml:"\xEB",euro:"\u20AC",excl:"!",exist:"\u2203",Exists:"\u2203",expectation:"\u2130",exponentiale:"\u2147",ExponentialE:"\u2147",fallingdotseq:"\u2252",Fcy:"\u0424",fcy:"\u0444",female:"\u2640",ffilig:"\uFB03",fflig:"\uFB00",ffllig:"\uFB04",Ffr:"\u{1D509}",ffr:"\u{1D523}",filig:"\uFB01",FilledSmallSquare:"\u25FC",FilledVerySmallSquare:"\u25AA",fjlig:"fj",flat:"\u266D",fllig:"\uFB02",fltns:"\u25B1",fnof:"\u0192",Fopf:"\u{1D53D}",fopf:"\u{1D557}",forall:"\u2200",ForAll:"\u2200",fork:"\u22D4",forkv:"\u2AD9",Fouriertrf:"\u2131",fpartint:"\u2A0D",frac12:"\xBD",frac13:"\u2153",frac14:"\xBC",frac15:"\u2155",frac16:"\u2159",frac18:"\u215B",frac23:"\u2154",frac25:"\u2156",frac34:"\xBE",frac35:"\u2157",frac38:"\u215C",frac45:"\u2158",frac56:"\u215A",frac58:"\u215D",frac78:"\u215E",frasl:"\u2044",frown:"\u2322",fscr:"\u{1D4BB}",Fscr:"\u2131",gacute:"\u01F5",Gamma:"\u0393",gamma:"\u03B3",Gammad:"\u03DC",gammad:"\u03DD",gap:"\u2A86",Gbreve:"\u011E",gbreve:"\u011F",Gcedil:"\u0122",Gcirc:"\u011C",gcirc:"\u011D",Gcy:"\u0413",gcy:"\u0433",Gdot:"\u0120",gdot:"\u0121",ge:"\u2265",gE:"\u2267",gEl:"\u2A8C",gel:"\u22DB",geq:"\u2265",geqq:"\u2267",geqslant:"\u2A7E",gescc:"\u2AA9",ges:"\u2A7E",gesdot:"\u2A80",gesdoto:"\u2A82",gesdotol:"\u2A84",gesl:"\u22DB\uFE00",gesles:"\u2A94",Gfr:"\u{1D50A}",gfr:"\u{1D524}",gg:"\u226B",Gg:"\u22D9",ggg:"\u22D9",gimel:"\u2137",GJcy:"\u0403",gjcy:"\u0453",gla:"\u2AA5",gl:"\u2277",glE:"\u2A92",glj:"\u2AA4",gnap:"\u2A8A",gnapprox:"\u2A8A",gne:"\u2A88",gnE:"\u2269",gneq:"\u2A88",gneqq:"\u2269",gnsim:"\u22E7",Gopf:"\u{1D53E}",gopf:"\u{1D558}",grave:"`",GreaterEqual:"\u2265",GreaterEqualLess:"\u22DB",GreaterFullEqual:"\u2267",GreaterGreater:"\u2AA2",GreaterLess:"\u2277",GreaterSlantEqual:"\u2A7E",GreaterTilde:"\u2273",Gscr:"\u{1D4A2}",gscr:"\u210A",gsim:"\u2273",gsime:"\u2A8E",gsiml:"\u2A90",gtcc:"\u2AA7",gtcir:"\u2A7A",gt:">",GT:">",Gt:"\u226B",gtdot:"\u22D7",gtlPar:"\u2995",gtquest:"\u2A7C",gtrapprox:"\u2A86",gtrarr:"\u2978",gtrdot:"\u22D7",gtreqless:"\u22DB",gtreqqless:"\u2A8C",gtrless:"\u2277",gtrsim:"\u2273",gvertneqq:"\u2269\uFE00",gvnE:"\u2269\uFE00",Hacek:"\u02C7",hairsp:"\u200A",half:"\xBD",hamilt:"\u210B",HARDcy:"\u042A",hardcy:"\u044A",harrcir:"\u2948",harr:"\u2194",hArr:"\u21D4",harrw:"\u21AD",Hat:"^",hbar:"\u210F",Hcirc:"\u0124",hcirc:"\u0125",hearts:"\u2665",heartsuit:"\u2665",hellip:"\u2026",hercon:"\u22B9",hfr:"\u{1D525}",Hfr:"\u210C",HilbertSpace:"\u210B",hksearow:"\u2925",hkswarow:"\u2926",hoarr:"\u21FF",homtht:"\u223B",hookleftarrow:"\u21A9",hookrightarrow:"\u21AA",hopf:"\u{1D559}",Hopf:"\u210D",horbar:"\u2015",HorizontalLine:"\u2500",hscr:"\u{1D4BD}",Hscr:"\u210B",hslash:"\u210F",Hstrok:"\u0126",hstrok:"\u0127",HumpDownHump:"\u224E",HumpEqual:"\u224F",hybull:"\u2043",hyphen:"\u2010",Iacute:"\xCD",iacute:"\xED",ic:"\u2063",Icirc:"\xCE",icirc:"\xEE",Icy:"\u0418",icy:"\u0438",Idot:"\u0130",IEcy:"\u0415",iecy:"\u0435",iexcl:"\xA1",iff:"\u21D4",ifr:"\u{1D526}",Ifr:"\u2111",Igrave:"\xCC",igrave:"\xEC",ii:"\u2148",iiiint:"\u2A0C",iiint:"\u222D",iinfin:"\u29DC",iiota:"\u2129",IJlig:"\u0132",ijlig:"\u0133",Imacr:"\u012A",imacr:"\u012B",image:"\u2111",ImaginaryI:"\u2148",imagline:"\u2110",imagpart:"\u2111",imath:"\u0131",Im:"\u2111",imof:"\u22B7",imped:"\u01B5",Implies:"\u21D2",incare:"\u2105",in:"\u2208",infin:"\u221E",infintie:"\u29DD",inodot:"\u0131",intcal:"\u22BA",int:"\u222B",Int:"\u222C",integers:"\u2124",Integral:"\u222B",intercal:"\u22BA",Intersection:"\u22C2",intlarhk:"\u2A17",intprod:"\u2A3C",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",IOcy:"\u0401",iocy:"\u0451",Iogon:"\u012E",iogon:"\u012F",Iopf:"\u{1D540}",iopf:"\u{1D55A}",Iota:"\u0399",iota:"\u03B9",iprod:"\u2A3C",iquest:"\xBF",iscr:"\u{1D4BE}",Iscr:"\u2110",isin:"\u2208",isindot:"\u22F5",isinE:"\u22F9",isins:"\u22F4",isinsv:"\u22F3",isinv:"\u2208",it:"\u2062",Itilde:"\u0128",itilde:"\u0129",Iukcy:"\u0406",iukcy:"\u0456",Iuml:"\xCF",iuml:"\xEF",Jcirc:"\u0134",jcirc:"\u0135",Jcy:"\u0419",jcy:"\u0439",Jfr:"\u{1D50D}",jfr:"\u{1D527}",jmath:"\u0237",Jopf:"\u{1D541}",jopf:"\u{1D55B}",Jscr:"\u{1D4A5}",jscr:"\u{1D4BF}",Jsercy:"\u0408",jsercy:"\u0458",Jukcy:"\u0404",jukcy:"\u0454",Kappa:"\u039A",kappa:"\u03BA",kappav:"\u03F0",Kcedil:"\u0136",kcedil:"\u0137",Kcy:"\u041A",kcy:"\u043A",Kfr:"\u{1D50E}",kfr:"\u{1D528}",kgreen:"\u0138",KHcy:"\u0425",khcy:"\u0445",KJcy:"\u040C",kjcy:"\u045C",Kopf:"\u{1D542}",kopf:"\u{1D55C}",Kscr:"\u{1D4A6}",kscr:"\u{1D4C0}",lAarr:"\u21DA",Lacute:"\u0139",lacute:"\u013A",laemptyv:"\u29B4",lagran:"\u2112",Lambda:"\u039B",lambda:"\u03BB",lang:"\u27E8",Lang:"\u27EA",langd:"\u2991",langle:"\u27E8",lap:"\u2A85",Laplacetrf:"\u2112",laquo:"\xAB",larrb:"\u21E4",larrbfs:"\u291F",larr:"\u2190",Larr:"\u219E",lArr:"\u21D0",larrfs:"\u291D",larrhk:"\u21A9",larrlp:"\u21AB",larrpl:"\u2939",larrsim:"\u2973",larrtl:"\u21A2",latail:"\u2919",lAtail:"\u291B",lat:"\u2AAB",late:"\u2AAD",lates:"\u2AAD\uFE00",lbarr:"\u290C",lBarr:"\u290E",lbbrk:"\u2772",lbrace:"{",lbrack:"[",lbrke:"\u298B",lbrksld:"\u298F",lbrkslu:"\u298D",Lcaron:"\u013D",lcaron:"\u013E",Lcedil:"\u013B",lcedil:"\u013C",lceil:"\u2308",lcub:"{",Lcy:"\u041B",lcy:"\u043B",ldca:"\u2936",ldquo:"\u201C",ldquor:"\u201E",ldrdhar:"\u2967",ldrushar:"\u294B",ldsh:"\u21B2",le:"\u2264",lE:"\u2266",LeftAngleBracket:"\u27E8",LeftArrowBar:"\u21E4",leftarrow:"\u2190",LeftArrow:"\u2190",Leftarrow:"\u21D0",LeftArrowRightArrow:"\u21C6",leftarrowtail:"\u21A2",LeftCeiling:"\u2308",LeftDoubleBracket:"\u27E6",LeftDownTeeVector:"\u2961",LeftDownVectorBar:"\u2959",LeftDownVector:"\u21C3",LeftFloor:"\u230A",leftharpoondown:"\u21BD",leftharpoonup:"\u21BC",leftleftarrows:"\u21C7",leftrightarrow:"\u2194",LeftRightArrow:"\u2194",Leftrightarrow:"\u21D4",leftrightarrows:"\u21C6",leftrightharpoons:"\u21CB",leftrightsquigarrow:"\u21AD",LeftRightVector:"\u294E",LeftTeeArrow:"\u21A4",LeftTee:"\u22A3",LeftTeeVector:"\u295A",leftthreetimes:"\u22CB",LeftTriangleBar:"\u29CF",LeftTriangle:"\u22B2",LeftTriangleEqual:"\u22B4",LeftUpDownVector:"\u2951",LeftUpTeeVector:"\u2960",LeftUpVectorBar:"\u2958",LeftUpVector:"\u21BF",LeftVectorBar:"\u2952",LeftVector:"\u21BC",lEg:"\u2A8B",leg:"\u22DA",leq:"\u2264",leqq:"\u2266",leqslant:"\u2A7D",lescc:"\u2AA8",les:"\u2A7D",lesdot:"\u2A7F",lesdoto:"\u2A81",lesdotor:"\u2A83",lesg:"\u22DA\uFE00",lesges:"\u2A93",lessapprox:"\u2A85",lessdot:"\u22D6",lesseqgtr:"\u22DA",lesseqqgtr:"\u2A8B",LessEqualGreater:"\u22DA",LessFullEqual:"\u2266",LessGreater:"\u2276",lessgtr:"\u2276",LessLess:"\u2AA1",lesssim:"\u2272",LessSlantEqual:"\u2A7D",LessTilde:"\u2272",lfisht:"\u297C",lfloor:"\u230A",Lfr:"\u{1D50F}",lfr:"\u{1D529}",lg:"\u2276",lgE:"\u2A91",lHar:"\u2962",lhard:"\u21BD",lharu:"\u21BC",lharul:"\u296A",lhblk:"\u2584",LJcy:"\u0409",ljcy:"\u0459",llarr:"\u21C7",ll:"\u226A",Ll:"\u22D8",llcorner:"\u231E",Lleftarrow:"\u21DA",llhard:"\u296B",lltri:"\u25FA",Lmidot:"\u013F",lmidot:"\u0140",lmoustache:"\u23B0",lmoust:"\u23B0",lnap:"\u2A89",lnapprox:"\u2A89",lne:"\u2A87",lnE:"\u2268",lneq:"\u2A87",lneqq:"\u2268",lnsim:"\u22E6",loang:"\u27EC",loarr:"\u21FD",lobrk:"\u27E6",longleftarrow:"\u27F5",LongLeftArrow:"\u27F5",Longleftarrow:"\u27F8",longleftrightarrow:"\u27F7",LongLeftRightArrow:"\u27F7",Longleftrightarrow:"\u27FA",longmapsto:"\u27FC",longrightarrow:"\u27F6",LongRightArrow:"\u27F6",Longrightarrow:"\u27F9",looparrowleft:"\u21AB",looparrowright:"\u21AC",lopar:"\u2985",Lopf:"\u{1D543}",lopf:"\u{1D55D}",loplus:"\u2A2D",lotimes:"\u2A34",lowast:"\u2217",lowbar:"_",LowerLeftArrow:"\u2199",LowerRightArrow:"\u2198",loz:"\u25CA",lozenge:"\u25CA",lozf:"\u29EB",lpar:"(",lparlt:"\u2993",lrarr:"\u21C6",lrcorner:"\u231F",lrhar:"\u21CB",lrhard:"\u296D",lrm:"\u200E",lrtri:"\u22BF",lsaquo:"\u2039",lscr:"\u{1D4C1}",Lscr:"\u2112",lsh:"\u21B0",Lsh:"\u21B0",lsim:"\u2272",lsime:"\u2A8D",lsimg:"\u2A8F",lsqb:"[",lsquo:"\u2018",lsquor:"\u201A",Lstrok:"\u0141",lstrok:"\u0142",ltcc:"\u2AA6",ltcir:"\u2A79",lt:"<",LT:"<",Lt:"\u226A",ltdot:"\u22D6",lthree:"\u22CB",ltimes:"\u22C9",ltlarr:"\u2976",ltquest:"\u2A7B",ltri:"\u25C3",ltrie:"\u22B4",ltrif:"\u25C2",ltrPar:"\u2996",lurdshar:"\u294A",luruhar:"\u2966",lvertneqq:"\u2268\uFE00",lvnE:"\u2268\uFE00",macr:"\xAF",male:"\u2642",malt:"\u2720",maltese:"\u2720",Map:"\u2905",map:"\u21A6",mapsto:"\u21A6",mapstodown:"\u21A7",mapstoleft:"\u21A4",mapstoup:"\u21A5",marker:"\u25AE",mcomma:"\u2A29",Mcy:"\u041C",mcy:"\u043C",mdash:"\u2014",mDDot:"\u223A",measuredangle:"\u2221",MediumSpace:"\u205F",Mellintrf:"\u2133",Mfr:"\u{1D510}",mfr:"\u{1D52A}",mho:"\u2127",micro:"\xB5",midast:"*",midcir:"\u2AF0",mid:"\u2223",middot:"\xB7",minusb:"\u229F",minus:"\u2212",minusd:"\u2238",minusdu:"\u2A2A",MinusPlus:"\u2213",mlcp:"\u2ADB",mldr:"\u2026",mnplus:"\u2213",models:"\u22A7",Mopf:"\u{1D544}",mopf:"\u{1D55E}",mp:"\u2213",mscr:"\u{1D4C2}",Mscr:"\u2133",mstpos:"\u223E",Mu:"\u039C",mu:"\u03BC",multimap:"\u22B8",mumap:"\u22B8",nabla:"\u2207",Nacute:"\u0143",nacute:"\u0144",nang:"\u2220\u20D2",nap:"\u2249",napE:"\u2A70\u0338",napid:"\u224B\u0338",napos:"\u0149",napprox:"\u2249",natural:"\u266E",naturals:"\u2115",natur:"\u266E",nbsp:"\xA0",nbump:"\u224E\u0338",nbumpe:"\u224F\u0338",ncap:"\u2A43",Ncaron:"\u0147",ncaron:"\u0148",Ncedil:"\u0145",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2A6D\u0338",ncup:"\u2A42",Ncy:"\u041D",ncy:"\u043D",ndash:"\u2013",nearhk:"\u2924",nearr:"\u2197",neArr:"\u21D7",nearrow:"\u2197",ne:"\u2260",nedot:"\u2250\u0338",NegativeMediumSpace:"\u200B",NegativeThickSpace:"\u200B",NegativeThinSpace:"\u200B",NegativeVeryThinSpace:"\u200B",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",NestedGreaterGreater:"\u226B",NestedLessLess:"\u226A",NewLine:`
`,nexist:"\u2204",nexists:"\u2204",Nfr:"\u{1D511}",nfr:"\u{1D52B}",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2A7E\u0338",nges:"\u2A7E\u0338",nGg:"\u22D9\u0338",ngsim:"\u2275",nGt:"\u226B\u20D2",ngt:"\u226F",ngtr:"\u226F",nGtv:"\u226B\u0338",nharr:"\u21AE",nhArr:"\u21CE",nhpar:"\u2AF2",ni:"\u220B",nis:"\u22FC",nisd:"\u22FA",niv:"\u220B",NJcy:"\u040A",njcy:"\u045A",nlarr:"\u219A",nlArr:"\u21CD",nldr:"\u2025",nlE:"\u2266\u0338",nle:"\u2270",nleftarrow:"\u219A",nLeftarrow:"\u21CD",nleftrightarrow:"\u21AE",nLeftrightarrow:"\u21CE",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2A7D\u0338",nles:"\u2A7D\u0338",nless:"\u226E",nLl:"\u22D8\u0338",nlsim:"\u2274",nLt:"\u226A\u20D2",nlt:"\u226E",nltri:"\u22EA",nltrie:"\u22EC",nLtv:"\u226A\u0338",nmid:"\u2224",NoBreak:"\u2060",NonBreakingSpace:"\xA0",nopf:"\u{1D55F}",Nopf:"\u2115",Not:"\u2AEC",not:"\xAC",NotCongruent:"\u2262",NotCupCap:"\u226D",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotEqualTilde:"\u2242\u0338",NotExists:"\u2204",NotGreater:"\u226F",NotGreaterEqual:"\u2271",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226B\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2A7E\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224E\u0338",NotHumpEqual:"\u224F\u0338",notin:"\u2209",notindot:"\u22F5\u0338",notinE:"\u22F9\u0338",notinva:"\u2209",notinvb:"\u22F7",notinvc:"\u22F6",NotLeftTriangleBar:"\u29CF\u0338",NotLeftTriangle:"\u22EA",NotLeftTriangleEqual:"\u22EC",NotLess:"\u226E",NotLessEqual:"\u2270",NotLessGreater:"\u2278",NotLessLess:"\u226A\u0338",NotLessSlantEqual:"\u2A7D\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2AA2\u0338",NotNestedLessLess:"\u2AA1\u0338",notni:"\u220C",notniva:"\u220C",notnivb:"\u22FE",notnivc:"\u22FD",NotPrecedes:"\u2280",NotPrecedesEqual:"\u2AAF\u0338",NotPrecedesSlantEqual:"\u22E0",NotReverseElement:"\u220C",NotRightTriangleBar:"\u29D0\u0338",NotRightTriangle:"\u22EB",NotRightTriangleEqual:"\u22ED",NotSquareSubset:"\u228F\u0338",NotSquareSubsetEqual:"\u22E2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22E3",NotSubset:"\u2282\u20D2",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsEqual:"\u2AB0\u0338",NotSucceedsSlantEqual:"\u22E1",NotSucceedsTilde:"\u227F\u0338",NotSuperset:"\u2283\u20D2",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",NotVerticalBar:"\u2224",nparallel:"\u2226",npar:"\u2226",nparsl:"\u2AFD\u20E5",npart:"\u2202\u0338",npolint:"\u2A14",npr:"\u2280",nprcue:"\u22E0",nprec:"\u2280",npreceq:"\u2AAF\u0338",npre:"\u2AAF\u0338",nrarrc:"\u2933\u0338",nrarr:"\u219B",nrArr:"\u21CF",nrarrw:"\u219D\u0338",nrightarrow:"\u219B",nRightarrow:"\u21CF",nrtri:"\u22EB",nrtrie:"\u22ED",nsc:"\u2281",nsccue:"\u22E1",nsce:"\u2AB0\u0338",Nscr:"\u{1D4A9}",nscr:"\u{1D4C3}",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22E2",nsqsupe:"\u22E3",nsub:"\u2284",nsubE:"\u2AC5\u0338",nsube:"\u2288",nsubset:"\u2282\u20D2",nsubseteq:"\u2288",nsubseteqq:"\u2AC5\u0338",nsucc:"\u2281",nsucceq:"\u2AB0\u0338",nsup:"\u2285",nsupE:"\u2AC6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20D2",nsupseteq:"\u2289",nsupseteqq:"\u2AC6\u0338",ntgl:"\u2279",Ntilde:"\xD1",ntilde:"\xF1",ntlg:"\u2278",ntriangleleft:"\u22EA",ntrianglelefteq:"\u22EC",ntriangleright:"\u22EB",ntrianglerighteq:"\u22ED",Nu:"\u039D",nu:"\u03BD",num:"#",numero:"\u2116",numsp:"\u2007",nvap:"\u224D\u20D2",nvdash:"\u22AC",nvDash:"\u22AD",nVdash:"\u22AE",nVDash:"\u22AF",nvge:"\u2265\u20D2",nvgt:">\u20D2",nvHarr:"\u2904",nvinfin:"\u29DE",nvlArr:"\u2902",nvle:"\u2264\u20D2",nvlt:"<\u20D2",nvltrie:"\u22B4\u20D2",nvrArr:"\u2903",nvrtrie:"\u22B5\u20D2",nvsim:"\u223C\u20D2",nwarhk:"\u2923",nwarr:"\u2196",nwArr:"\u21D6",nwarrow:"\u2196",nwnear:"\u2927",Oacute:"\xD3",oacute:"\xF3",oast:"\u229B",Ocirc:"\xD4",ocirc:"\xF4",ocir:"\u229A",Ocy:"\u041E",ocy:"\u043E",odash:"\u229D",Odblac:"\u0150",odblac:"\u0151",odiv:"\u2A38",odot:"\u2299",odsold:"\u29BC",OElig:"\u0152",oelig:"\u0153",ofcir:"\u29BF",Ofr:"\u{1D512}",ofr:"\u{1D52C}",ogon:"\u02DB",Ograve:"\xD2",ograve:"\xF2",ogt:"\u29C1",ohbar:"\u29B5",ohm:"\u03A9",oint:"\u222E",olarr:"\u21BA",olcir:"\u29BE",olcross:"\u29BB",oline:"\u203E",olt:"\u29C0",Omacr:"\u014C",omacr:"\u014D",Omega:"\u03A9",omega:"\u03C9",Omicron:"\u039F",omicron:"\u03BF",omid:"\u29B6",ominus:"\u2296",Oopf:"\u{1D546}",oopf:"\u{1D560}",opar:"\u29B7",OpenCurlyDoubleQuote:"\u201C",OpenCurlyQuote:"\u2018",operp:"\u29B9",oplus:"\u2295",orarr:"\u21BB",Or:"\u2A54",or:"\u2228",ord:"\u2A5D",order:"\u2134",orderof:"\u2134",ordf:"\xAA",ordm:"\xBA",origof:"\u22B6",oror:"\u2A56",orslope:"\u2A57",orv:"\u2A5B",oS:"\u24C8",Oscr:"\u{1D4AA}",oscr:"\u2134",Oslash:"\xD8",oslash:"\xF8",osol:"\u2298",Otilde:"\xD5",otilde:"\xF5",otimesas:"\u2A36",Otimes:"\u2A37",otimes:"\u2297",Ouml:"\xD6",ouml:"\xF6",ovbar:"\u233D",OverBar:"\u203E",OverBrace:"\u23DE",OverBracket:"\u23B4",OverParenthesis:"\u23DC",para:"\xB6",parallel:"\u2225",par:"\u2225",parsim:"\u2AF3",parsl:"\u2AFD",part:"\u2202",PartialD:"\u2202",Pcy:"\u041F",pcy:"\u043F",percnt:"%",period:".",permil:"\u2030",perp:"\u22A5",pertenk:"\u2031",Pfr:"\u{1D513}",pfr:"\u{1D52D}",Phi:"\u03A6",phi:"\u03C6",phiv:"\u03D5",phmmat:"\u2133",phone:"\u260E",Pi:"\u03A0",pi:"\u03C0",pitchfork:"\u22D4",piv:"\u03D6",planck:"\u210F",planckh:"\u210E",plankv:"\u210F",plusacir:"\u2A23",plusb:"\u229E",pluscir:"\u2A22",plus:"+",plusdo:"\u2214",plusdu:"\u2A25",pluse:"\u2A72",PlusMinus:"\xB1",plusmn:"\xB1",plussim:"\u2A26",plustwo:"\u2A27",pm:"\xB1",Poincareplane:"\u210C",pointint:"\u2A15",popf:"\u{1D561}",Popf:"\u2119",pound:"\xA3",prap:"\u2AB7",Pr:"\u2ABB",pr:"\u227A",prcue:"\u227C",precapprox:"\u2AB7",prec:"\u227A",preccurlyeq:"\u227C",Precedes:"\u227A",PrecedesEqual:"\u2AAF",PrecedesSlantEqual:"\u227C",PrecedesTilde:"\u227E",preceq:"\u2AAF",precnapprox:"\u2AB9",precneqq:"\u2AB5",precnsim:"\u22E8",pre:"\u2AAF",prE:"\u2AB3",precsim:"\u227E",prime:"\u2032",Prime:"\u2033",primes:"\u2119",prnap:"\u2AB9",prnE:"\u2AB5",prnsim:"\u22E8",prod:"\u220F",Product:"\u220F",profalar:"\u232E",profline:"\u2312",profsurf:"\u2313",prop:"\u221D",Proportional:"\u221D",Proportion:"\u2237",propto:"\u221D",prsim:"\u227E",prurel:"\u22B0",Pscr:"\u{1D4AB}",pscr:"\u{1D4C5}",Psi:"\u03A8",psi:"\u03C8",puncsp:"\u2008",Qfr:"\u{1D514}",qfr:"\u{1D52E}",qint:"\u2A0C",qopf:"\u{1D562}",Qopf:"\u211A",qprime:"\u2057",Qscr:"\u{1D4AC}",qscr:"\u{1D4C6}",quaternions:"\u210D",quatint:"\u2A16",quest:"?",questeq:"\u225F",quot:'"',QUOT:'"',rAarr:"\u21DB",race:"\u223D\u0331",Racute:"\u0154",racute:"\u0155",radic:"\u221A",raemptyv:"\u29B3",rang:"\u27E9",Rang:"\u27EB",rangd:"\u2992",range:"\u29A5",rangle:"\u27E9",raquo:"\xBB",rarrap:"\u2975",rarrb:"\u21E5",rarrbfs:"\u2920",rarrc:"\u2933",rarr:"\u2192",Rarr:"\u21A0",rArr:"\u21D2",rarrfs:"\u291E",rarrhk:"\u21AA",rarrlp:"\u21AC",rarrpl:"\u2945",rarrsim:"\u2974",Rarrtl:"\u2916",rarrtl:"\u21A3",rarrw:"\u219D",ratail:"\u291A",rAtail:"\u291C",ratio:"\u2236",rationals:"\u211A",rbarr:"\u290D",rBarr:"\u290F",RBarr:"\u2910",rbbrk:"\u2773",rbrace:"}",rbrack:"]",rbrke:"\u298C",rbrksld:"\u298E",rbrkslu:"\u2990",Rcaron:"\u0158",rcaron:"\u0159",Rcedil:"\u0156",rcedil:"\u0157",rceil:"\u2309",rcub:"}",Rcy:"\u0420",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201D",rdquor:"\u201D",rdsh:"\u21B3",real:"\u211C",realine:"\u211B",realpart:"\u211C",reals:"\u211D",Re:"\u211C",rect:"\u25AD",reg:"\xAE",REG:"\xAE",ReverseElement:"\u220B",ReverseEquilibrium:"\u21CB",ReverseUpEquilibrium:"\u296F",rfisht:"\u297D",rfloor:"\u230B",rfr:"\u{1D52F}",Rfr:"\u211C",rHar:"\u2964",rhard:"\u21C1",rharu:"\u21C0",rharul:"\u296C",Rho:"\u03A1",rho:"\u03C1",rhov:"\u03F1",RightAngleBracket:"\u27E9",RightArrowBar:"\u21E5",rightarrow:"\u2192",RightArrow:"\u2192",Rightarrow:"\u21D2",RightArrowLeftArrow:"\u21C4",rightarrowtail:"\u21A3",RightCeiling:"\u2309",RightDoubleBracket:"\u27E7",RightDownTeeVector:"\u295D",RightDownVectorBar:"\u2955",RightDownVector:"\u21C2",RightFloor:"\u230B",rightharpoondown:"\u21C1",rightharpoonup:"\u21C0",rightleftarrows:"\u21C4",rightleftharpoons:"\u21CC",rightrightarrows:"\u21C9",rightsquigarrow:"\u219D",RightTeeArrow:"\u21A6",RightTee:"\u22A2",RightTeeVector:"\u295B",rightthreetimes:"\u22CC",RightTriangleBar:"\u29D0",RightTriangle:"\u22B3",RightTriangleEqual:"\u22B5",RightUpDownVector:"\u294F",RightUpTeeVector:"\u295C",RightUpVectorBar:"\u2954",RightUpVector:"\u21BE",RightVectorBar:"\u2953",RightVector:"\u21C0",ring:"\u02DA",risingdotseq:"\u2253",rlarr:"\u21C4",rlhar:"\u21CC",rlm:"\u200F",rmoustache:"\u23B1",rmoust:"\u23B1",rnmid:"\u2AEE",roang:"\u27ED",roarr:"\u21FE",robrk:"\u27E7",ropar:"\u2986",ropf:"\u{1D563}",Ropf:"\u211D",roplus:"\u2A2E",rotimes:"\u2A35",RoundImplies:"\u2970",rpar:")",rpargt:"\u2994",rppolint:"\u2A12",rrarr:"\u21C9",Rrightarrow:"\u21DB",rsaquo:"\u203A",rscr:"\u{1D4C7}",Rscr:"\u211B",rsh:"\u21B1",Rsh:"\u21B1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22CC",rtimes:"\u22CA",rtri:"\u25B9",rtrie:"\u22B5",rtrif:"\u25B8",rtriltri:"\u29CE",RuleDelayed:"\u29F4",ruluhar:"\u2968",rx:"\u211E",Sacute:"\u015A",sacute:"\u015B",sbquo:"\u201A",scap:"\u2AB8",Scaron:"\u0160",scaron:"\u0161",Sc:"\u2ABC",sc:"\u227B",sccue:"\u227D",sce:"\u2AB0",scE:"\u2AB4",Scedil:"\u015E",scedil:"\u015F",Scirc:"\u015C",scirc:"\u015D",scnap:"\u2ABA",scnE:"\u2AB6",scnsim:"\u22E9",scpolint:"\u2A13",scsim:"\u227F",Scy:"\u0421",scy:"\u0441",sdotb:"\u22A1",sdot:"\u22C5",sdote:"\u2A66",searhk:"\u2925",searr:"\u2198",seArr:"\u21D8",searrow:"\u2198",sect:"\xA7",semi:";",seswar:"\u2929",setminus:"\u2216",setmn:"\u2216",sext:"\u2736",Sfr:"\u{1D516}",sfr:"\u{1D530}",sfrown:"\u2322",sharp:"\u266F",SHCHcy:"\u0429",shchcy:"\u0449",SHcy:"\u0428",shcy:"\u0448",ShortDownArrow:"\u2193",ShortLeftArrow:"\u2190",shortmid:"\u2223",shortparallel:"\u2225",ShortRightArrow:"\u2192",ShortUpArrow:"\u2191",shy:"\xAD",Sigma:"\u03A3",sigma:"\u03C3",sigmaf:"\u03C2",sigmav:"\u03C2",sim:"\u223C",simdot:"\u2A6A",sime:"\u2243",simeq:"\u2243",simg:"\u2A9E",simgE:"\u2AA0",siml:"\u2A9D",simlE:"\u2A9F",simne:"\u2246",simplus:"\u2A24",simrarr:"\u2972",slarr:"\u2190",SmallCircle:"\u2218",smallsetminus:"\u2216",smashp:"\u2A33",smeparsl:"\u29E4",smid:"\u2223",smile:"\u2323",smt:"\u2AAA",smte:"\u2AAC",smtes:"\u2AAC\uFE00",SOFTcy:"\u042C",softcy:"\u044C",solbar:"\u233F",solb:"\u29C4",sol:"/",Sopf:"\u{1D54A}",sopf:"\u{1D564}",spades:"\u2660",spadesuit:"\u2660",spar:"\u2225",sqcap:"\u2293",sqcaps:"\u2293\uFE00",sqcup:"\u2294",sqcups:"\u2294\uFE00",Sqrt:"\u221A",sqsub:"\u228F",sqsube:"\u2291",sqsubset:"\u228F",sqsubseteq:"\u2291",sqsup:"\u2290",sqsupe:"\u2292",sqsupset:"\u2290",sqsupseteq:"\u2292",square:"\u25A1",Square:"\u25A1",SquareIntersection:"\u2293",SquareSubset:"\u228F",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",squarf:"\u25AA",squ:"\u25A1",squf:"\u25AA",srarr:"\u2192",Sscr:"\u{1D4AE}",sscr:"\u{1D4C8}",ssetmn:"\u2216",ssmile:"\u2323",sstarf:"\u22C6",Star:"\u22C6",star:"\u2606",starf:"\u2605",straightepsilon:"\u03F5",straightphi:"\u03D5",strns:"\xAF",sub:"\u2282",Sub:"\u22D0",subdot:"\u2ABD",subE:"\u2AC5",sube:"\u2286",subedot:"\u2AC3",submult:"\u2AC1",subnE:"\u2ACB",subne:"\u228A",subplus:"\u2ABF",subrarr:"\u2979",subset:"\u2282",Subset:"\u22D0",subseteq:"\u2286",subseteqq:"\u2AC5",SubsetEqual:"\u2286",subsetneq:"\u228A",subsetneqq:"\u2ACB",subsim:"\u2AC7",subsub:"\u2AD5",subsup:"\u2AD3",succapprox:"\u2AB8",succ:"\u227B",succcurlyeq:"\u227D",Succeeds:"\u227B",SucceedsEqual:"\u2AB0",SucceedsSlantEqual:"\u227D",SucceedsTilde:"\u227F",succeq:"\u2AB0",succnapprox:"\u2ABA",succneqq:"\u2AB6",succnsim:"\u22E9",succsim:"\u227F",SuchThat:"\u220B",sum:"\u2211",Sum:"\u2211",sung:"\u266A",sup1:"\xB9",sup2:"\xB2",sup3:"\xB3",sup:"\u2283",Sup:"\u22D1",supdot:"\u2ABE",supdsub:"\u2AD8",supE:"\u2AC6",supe:"\u2287",supedot:"\u2AC4",Superset:"\u2283",SupersetEqual:"\u2287",suphsol:"\u27C9",suphsub:"\u2AD7",suplarr:"\u297B",supmult:"\u2AC2",supnE:"\u2ACC",supne:"\u228B",supplus:"\u2AC0",supset:"\u2283",Supset:"\u22D1",supseteq:"\u2287",supseteqq:"\u2AC6",supsetneq:"\u228B",supsetneqq:"\u2ACC",supsim:"\u2AC8",supsub:"\u2AD4",supsup:"\u2AD6",swarhk:"\u2926",swarr:"\u2199",swArr:"\u21D9",swarrow:"\u2199",swnwar:"\u292A",szlig:"\xDF",Tab:"	",target:"\u2316",Tau:"\u03A4",tau:"\u03C4",tbrk:"\u23B4",Tcaron:"\u0164",tcaron:"\u0165",Tcedil:"\u0162",tcedil:"\u0163",Tcy:"\u0422",tcy:"\u0442",tdot:"\u20DB",telrec:"\u2315",Tfr:"\u{1D517}",tfr:"\u{1D531}",there4:"\u2234",therefore:"\u2234",Therefore:"\u2234",Theta:"\u0398",theta:"\u03B8",thetasym:"\u03D1",thetav:"\u03D1",thickapprox:"\u2248",thicksim:"\u223C",ThickSpace:"\u205F\u200A",ThinSpace:"\u2009",thinsp:"\u2009",thkap:"\u2248",thksim:"\u223C",THORN:"\xDE",thorn:"\xFE",tilde:"\u02DC",Tilde:"\u223C",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",timesbar:"\u2A31",timesb:"\u22A0",times:"\xD7",timesd:"\u2A30",tint:"\u222D",toea:"\u2928",topbot:"\u2336",topcir:"\u2AF1",top:"\u22A4",Topf:"\u{1D54B}",topf:"\u{1D565}",topfork:"\u2ADA",tosa:"\u2929",tprime:"\u2034",trade:"\u2122",TRADE:"\u2122",triangle:"\u25B5",triangledown:"\u25BF",triangleleft:"\u25C3",trianglelefteq:"\u22B4",triangleq:"\u225C",triangleright:"\u25B9",trianglerighteq:"\u22B5",tridot:"\u25EC",trie:"\u225C",triminus:"\u2A3A",TripleDot:"\u20DB",triplus:"\u2A39",trisb:"\u29CD",tritime:"\u2A3B",trpezium:"\u23E2",Tscr:"\u{1D4AF}",tscr:"\u{1D4C9}",TScy:"\u0426",tscy:"\u0446",TSHcy:"\u040B",tshcy:"\u045B",Tstrok:"\u0166",tstrok:"\u0167",twixt:"\u226C",twoheadleftarrow:"\u219E",twoheadrightarrow:"\u21A0",Uacute:"\xDA",uacute:"\xFA",uarr:"\u2191",Uarr:"\u219F",uArr:"\u21D1",Uarrocir:"\u2949",Ubrcy:"\u040E",ubrcy:"\u045E",Ubreve:"\u016C",ubreve:"\u016D",Ucirc:"\xDB",ucirc:"\xFB",Ucy:"\u0423",ucy:"\u0443",udarr:"\u21C5",Udblac:"\u0170",udblac:"\u0171",udhar:"\u296E",ufisht:"\u297E",Ufr:"\u{1D518}",ufr:"\u{1D532}",Ugrave:"\xD9",ugrave:"\xF9",uHar:"\u2963",uharl:"\u21BF",uharr:"\u21BE",uhblk:"\u2580",ulcorn:"\u231C",ulcorner:"\u231C",ulcrop:"\u230F",ultri:"\u25F8",Umacr:"\u016A",umacr:"\u016B",uml:"\xA8",UnderBar:"_",UnderBrace:"\u23DF",UnderBracket:"\u23B5",UnderParenthesis:"\u23DD",Union:"\u22C3",UnionPlus:"\u228E",Uogon:"\u0172",uogon:"\u0173",Uopf:"\u{1D54C}",uopf:"\u{1D566}",UpArrowBar:"\u2912",uparrow:"\u2191",UpArrow:"\u2191",Uparrow:"\u21D1",UpArrowDownArrow:"\u21C5",updownarrow:"\u2195",UpDownArrow:"\u2195",Updownarrow:"\u21D5",UpEquilibrium:"\u296E",upharpoonleft:"\u21BF",upharpoonright:"\u21BE",uplus:"\u228E",UpperLeftArrow:"\u2196",UpperRightArrow:"\u2197",upsi:"\u03C5",Upsi:"\u03D2",upsih:"\u03D2",Upsilon:"\u03A5",upsilon:"\u03C5",UpTeeArrow:"\u21A5",UpTee:"\u22A5",upuparrows:"\u21C8",urcorn:"\u231D",urcorner:"\u231D",urcrop:"\u230E",Uring:"\u016E",uring:"\u016F",urtri:"\u25F9",Uscr:"\u{1D4B0}",uscr:"\u{1D4CA}",utdot:"\u22F0",Utilde:"\u0168",utilde:"\u0169",utri:"\u25B5",utrif:"\u25B4",uuarr:"\u21C8",Uuml:"\xDC",uuml:"\xFC",uwangle:"\u29A7",vangrt:"\u299C",varepsilon:"\u03F5",varkappa:"\u03F0",varnothing:"\u2205",varphi:"\u03D5",varpi:"\u03D6",varpropto:"\u221D",varr:"\u2195",vArr:"\u21D5",varrho:"\u03F1",varsigma:"\u03C2",varsubsetneq:"\u228A\uFE00",varsubsetneqq:"\u2ACB\uFE00",varsupsetneq:"\u228B\uFE00",varsupsetneqq:"\u2ACC\uFE00",vartheta:"\u03D1",vartriangleleft:"\u22B2",vartriangleright:"\u22B3",vBar:"\u2AE8",Vbar:"\u2AEB",vBarv:"\u2AE9",Vcy:"\u0412",vcy:"\u0432",vdash:"\u22A2",vDash:"\u22A8",Vdash:"\u22A9",VDash:"\u22AB",Vdashl:"\u2AE6",veebar:"\u22BB",vee:"\u2228",Vee:"\u22C1",veeeq:"\u225A",vellip:"\u22EE",verbar:"|",Verbar:"\u2016",vert:"|",Vert:"\u2016",VerticalBar:"\u2223",VerticalLine:"|",VerticalSeparator:"\u2758",VerticalTilde:"\u2240",VeryThinSpace:"\u200A",Vfr:"\u{1D519}",vfr:"\u{1D533}",vltri:"\u22B2",vnsub:"\u2282\u20D2",vnsup:"\u2283\u20D2",Vopf:"\u{1D54D}",vopf:"\u{1D567}",vprop:"\u221D",vrtri:"\u22B3",Vscr:"\u{1D4B1}",vscr:"\u{1D4CB}",vsubnE:"\u2ACB\uFE00",vsubne:"\u228A\uFE00",vsupnE:"\u2ACC\uFE00",vsupne:"\u228B\uFE00",Vvdash:"\u22AA",vzigzag:"\u299A",Wcirc:"\u0174",wcirc:"\u0175",wedbar:"\u2A5F",wedge:"\u2227",Wedge:"\u22C0",wedgeq:"\u2259",weierp:"\u2118",Wfr:"\u{1D51A}",wfr:"\u{1D534}",Wopf:"\u{1D54E}",wopf:"\u{1D568}",wp:"\u2118",wr:"\u2240",wreath:"\u2240",Wscr:"\u{1D4B2}",wscr:"\u{1D4CC}",xcap:"\u22C2",xcirc:"\u25EF",xcup:"\u22C3",xdtri:"\u25BD",Xfr:"\u{1D51B}",xfr:"\u{1D535}",xharr:"\u27F7",xhArr:"\u27FA",Xi:"\u039E",xi:"\u03BE",xlarr:"\u27F5",xlArr:"\u27F8",xmap:"\u27FC",xnis:"\u22FB",xodot:"\u2A00",Xopf:"\u{1D54F}",xopf:"\u{1D569}",xoplus:"\u2A01",xotime:"\u2A02",xrarr:"\u27F6",xrArr:"\u27F9",Xscr:"\u{1D4B3}",xscr:"\u{1D4CD}",xsqcup:"\u2A06",xuplus:"\u2A04",xutri:"\u25B3",xvee:"\u22C1",xwedge:"\u22C0",Yacute:"\xDD",yacute:"\xFD",YAcy:"\u042F",yacy:"\u044F",Ycirc:"\u0176",ycirc:"\u0177",Ycy:"\u042B",ycy:"\u044B",yen:"\xA5",Yfr:"\u{1D51C}",yfr:"\u{1D536}",YIcy:"\u0407",yicy:"\u0457",Yopf:"\u{1D550}",yopf:"\u{1D56A}",Yscr:"\u{1D4B4}",yscr:"\u{1D4CE}",YUcy:"\u042E",yucy:"\u044E",yuml:"\xFF",Yuml:"\u0178",Zacute:"\u0179",zacute:"\u017A",Zcaron:"\u017D",zcaron:"\u017E",Zcy:"\u0417",zcy:"\u0437",Zdot:"\u017B",zdot:"\u017C",zeetrf:"\u2128",ZeroWidthSpace:"\u200B",Zeta:"\u0396",zeta:"\u03B6",zfr:"\u{1D537}",Zfr:"\u2128",ZHcy:"\u0416",zhcy:"\u0436",zigrarr:"\u21DD",zopf:"\u{1D56B}",Zopf:"\u2124",Zscr:"\u{1D4B5}",zscr:"\u{1D4CF}",zwj:"\u200D",zwnj:"\u200C"};}});var require_legacy=__commonJS({"../../node_modules/ansi-to-html/node_modules/entities/lib/maps/legacy.json"(exports,module){module.exports={Aacute:"\xC1",aacute:"\xE1",Acirc:"\xC2",acirc:"\xE2",acute:"\xB4",AElig:"\xC6",aelig:"\xE6",Agrave:"\xC0",agrave:"\xE0",amp:"&",AMP:"&",Aring:"\xC5",aring:"\xE5",Atilde:"\xC3",atilde:"\xE3",Auml:"\xC4",auml:"\xE4",brvbar:"\xA6",Ccedil:"\xC7",ccedil:"\xE7",cedil:"\xB8",cent:"\xA2",copy:"\xA9",COPY:"\xA9",curren:"\xA4",deg:"\xB0",divide:"\xF7",Eacute:"\xC9",eacute:"\xE9",Ecirc:"\xCA",ecirc:"\xEA",Egrave:"\xC8",egrave:"\xE8",ETH:"\xD0",eth:"\xF0",Euml:"\xCB",euml:"\xEB",frac12:"\xBD",frac14:"\xBC",frac34:"\xBE",gt:">",GT:">",Iacute:"\xCD",iacute:"\xED",Icirc:"\xCE",icirc:"\xEE",iexcl:"\xA1",Igrave:"\xCC",igrave:"\xEC",iquest:"\xBF",Iuml:"\xCF",iuml:"\xEF",laquo:"\xAB",lt:"<",LT:"<",macr:"\xAF",micro:"\xB5",middot:"\xB7",nbsp:"\xA0",not:"\xAC",Ntilde:"\xD1",ntilde:"\xF1",Oacute:"\xD3",oacute:"\xF3",Ocirc:"\xD4",ocirc:"\xF4",Ograve:"\xD2",ograve:"\xF2",ordf:"\xAA",ordm:"\xBA",Oslash:"\xD8",oslash:"\xF8",Otilde:"\xD5",otilde:"\xF5",Ouml:"\xD6",ouml:"\xF6",para:"\xB6",plusmn:"\xB1",pound:"\xA3",quot:'"',QUOT:'"',raquo:"\xBB",reg:"\xAE",REG:"\xAE",sect:"\xA7",shy:"\xAD",sup1:"\xB9",sup2:"\xB2",sup3:"\xB3",szlig:"\xDF",THORN:"\xDE",thorn:"\xFE",times:"\xD7",Uacute:"\xDA",uacute:"\xFA",Ucirc:"\xDB",ucirc:"\xFB",Ugrave:"\xD9",ugrave:"\xF9",uml:"\xA8",Uuml:"\xDC",uuml:"\xFC",Yacute:"\xDD",yacute:"\xFD",yen:"\xA5",yuml:"\xFF"};}});var require_xml=__commonJS({"../../node_modules/ansi-to-html/node_modules/entities/lib/maps/xml.json"(exports,module){module.exports={amp:"&",apos:"'",gt:">",lt:"<",quot:'"'};}});var require_decode=__commonJS({"../../node_modules/ansi-to-html/node_modules/entities/lib/maps/decode.json"(exports,module){module.exports={"0":65533,"128":8364,"130":8218,"131":402,"132":8222,"133":8230,"134":8224,"135":8225,"136":710,"137":8240,"138":352,"139":8249,"140":338,"142":381,"145":8216,"146":8217,"147":8220,"148":8221,"149":8226,"150":8211,"151":8212,"152":732,"153":8482,"154":353,"155":8250,"156":339,"158":382,"159":376};}});var require_decode_codepoint=__commonJS({"../../node_modules/ansi-to-html/node_modules/entities/lib/decode_codepoint.js"(exports){var __importDefault=exports&&exports.__importDefault||function(mod){return mod&&mod.__esModule?mod:{default:mod}};Object.defineProperty(exports,"__esModule",{value:!0});var decode_json_1=__importDefault(require_decode()),fromCodePoint=String.fromCodePoint||function(codePoint){var output="";return codePoint>65535&&(codePoint-=65536,output+=String.fromCharCode(codePoint>>>10&1023|55296),codePoint=56320|codePoint&1023),output+=String.fromCharCode(codePoint),output};function decodeCodePoint(codePoint){return codePoint>=55296&&codePoint<=57343||codePoint>1114111?"\uFFFD":(codePoint in decode_json_1.default&&(codePoint=decode_json_1.default[codePoint]),fromCodePoint(codePoint))}exports.default=decodeCodePoint;}});var require_decode2=__commonJS({"../../node_modules/ansi-to-html/node_modules/entities/lib/decode.js"(exports){var __importDefault=exports&&exports.__importDefault||function(mod){return mod&&mod.__esModule?mod:{default:mod}};Object.defineProperty(exports,"__esModule",{value:!0});exports.decodeHTML=exports.decodeHTMLStrict=exports.decodeXML=void 0;var entities_json_1=__importDefault(require_entities()),legacy_json_1=__importDefault(require_legacy()),xml_json_1=__importDefault(require_xml()),decode_codepoint_1=__importDefault(require_decode_codepoint()),strictEntityRe=/&(?:[a-zA-Z0-9]+|#[xX][\da-fA-F]+|#\d+);/g;exports.decodeXML=getStrictDecoder(xml_json_1.default);exports.decodeHTMLStrict=getStrictDecoder(entities_json_1.default);function getStrictDecoder(map){var replace=getReplacer(map);return function(str){return String(str).replace(strictEntityRe,replace)}}var sorter=function(a,b){return a<b?1:-1};exports.decodeHTML=function(){for(var legacy=Object.keys(legacy_json_1.default).sort(sorter),keys=Object.keys(entities_json_1.default).sort(sorter),i=0,j=0;i<keys.length;i++)legacy[j]===keys[i]?(keys[i]+=";?",j++):keys[i]+=";";var re=new RegExp("&(?:"+keys.join("|")+"|#[xX][\\da-fA-F]+;?|#\\d+;?)","g"),replace=getReplacer(entities_json_1.default);function replacer(str){return str.substr(-1)!==";"&&(str+=";"),replace(str)}return function(str){return String(str).replace(re,replacer)}}();function getReplacer(map){return function(str){if(str.charAt(1)==="#"){var secondChar=str.charAt(2);return secondChar==="X"||secondChar==="x"?decode_codepoint_1.default(parseInt(str.substr(3),16)):decode_codepoint_1.default(parseInt(str.substr(2),10))}return map[str.slice(1,-1)]||str}}}});var require_encode=__commonJS({"../../node_modules/ansi-to-html/node_modules/entities/lib/encode.js"(exports){var __importDefault=exports&&exports.__importDefault||function(mod){return mod&&mod.__esModule?mod:{default:mod}};Object.defineProperty(exports,"__esModule",{value:!0});exports.escapeUTF8=exports.escape=exports.encodeNonAsciiHTML=exports.encodeHTML=exports.encodeXML=void 0;var xml_json_1=__importDefault(require_xml()),inverseXML=getInverseObj(xml_json_1.default),xmlReplacer=getInverseReplacer(inverseXML);exports.encodeXML=getASCIIEncoder(inverseXML);var entities_json_1=__importDefault(require_entities()),inverseHTML=getInverseObj(entities_json_1.default),htmlReplacer=getInverseReplacer(inverseHTML);exports.encodeHTML=getInverse(inverseHTML,htmlReplacer);exports.encodeNonAsciiHTML=getASCIIEncoder(inverseHTML);function getInverseObj(obj){return Object.keys(obj).sort().reduce(function(inverse,name){return inverse[obj[name]]="&"+name+";",inverse},{})}function getInverseReplacer(inverse){for(var single=[],multiple=[],_i=0,_a=Object.keys(inverse);_i<_a.length;_i++){var k=_a[_i];k.length===1?single.push("\\"+k):multiple.push(k);}single.sort();for(var start=0;start<single.length-1;start++){for(var end=start;end<single.length-1&&single[end].charCodeAt(1)+1===single[end+1].charCodeAt(1);)end+=1;var count=1+end-start;count<3||single.splice(start,count,single[start]+"-"+single[end]);}return multiple.unshift("["+single.join("")+"]"),new RegExp(multiple.join("|"),"g")}var reNonASCII=/(?:[\x80-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/g,getCodePoint=String.prototype.codePointAt!=null?function(str){return str.codePointAt(0)}:function(c){return (c.charCodeAt(0)-55296)*1024+c.charCodeAt(1)-56320+65536};function singleCharReplacer(c){return "&#x"+(c.length>1?getCodePoint(c):c.charCodeAt(0)).toString(16).toUpperCase()+";"}function getInverse(inverse,re){return function(data){return data.replace(re,function(name){return inverse[name]}).replace(reNonASCII,singleCharReplacer)}}var reEscapeChars=new RegExp(xmlReplacer.source+"|"+reNonASCII.source,"g");function escape(data){return data.replace(reEscapeChars,singleCharReplacer)}exports.escape=escape;function escapeUTF8(data){return data.replace(xmlReplacer,singleCharReplacer)}exports.escapeUTF8=escapeUTF8;function getASCIIEncoder(obj){return function(data){return data.replace(reEscapeChars,function(c){return obj[c]||singleCharReplacer(c)})}}}});var require_lib=__commonJS({"../../node_modules/ansi-to-html/node_modules/entities/lib/index.js"(exports){Object.defineProperty(exports,"__esModule",{value:!0});exports.decodeXMLStrict=exports.decodeHTML5Strict=exports.decodeHTML4Strict=exports.decodeHTML5=exports.decodeHTML4=exports.decodeHTMLStrict=exports.decodeHTML=exports.decodeXML=exports.encodeHTML5=exports.encodeHTML4=exports.escapeUTF8=exports.escape=exports.encodeNonAsciiHTML=exports.encodeHTML=exports.encodeXML=exports.encode=exports.decodeStrict=exports.decode=void 0;var decode_1=require_decode2(),encode_1=require_encode();function decode(data,level){return (!level||level<=0?decode_1.decodeXML:decode_1.decodeHTML)(data)}exports.decode=decode;function decodeStrict(data,level){return (!level||level<=0?decode_1.decodeXML:decode_1.decodeHTMLStrict)(data)}exports.decodeStrict=decodeStrict;function encode(data,level){return (!level||level<=0?encode_1.encodeXML:encode_1.encodeHTML)(data)}exports.encode=encode;var encode_2=require_encode();Object.defineProperty(exports,"encodeXML",{enumerable:!0,get:function(){return encode_2.encodeXML}});Object.defineProperty(exports,"encodeHTML",{enumerable:!0,get:function(){return encode_2.encodeHTML}});Object.defineProperty(exports,"encodeNonAsciiHTML",{enumerable:!0,get:function(){return encode_2.encodeNonAsciiHTML}});Object.defineProperty(exports,"escape",{enumerable:!0,get:function(){return encode_2.escape}});Object.defineProperty(exports,"escapeUTF8",{enumerable:!0,get:function(){return encode_2.escapeUTF8}});Object.defineProperty(exports,"encodeHTML4",{enumerable:!0,get:function(){return encode_2.encodeHTML}});Object.defineProperty(exports,"encodeHTML5",{enumerable:!0,get:function(){return encode_2.encodeHTML}});var decode_2=require_decode2();Object.defineProperty(exports,"decodeXML",{enumerable:!0,get:function(){return decode_2.decodeXML}});Object.defineProperty(exports,"decodeHTML",{enumerable:!0,get:function(){return decode_2.decodeHTML}});Object.defineProperty(exports,"decodeHTMLStrict",{enumerable:!0,get:function(){return decode_2.decodeHTMLStrict}});Object.defineProperty(exports,"decodeHTML4",{enumerable:!0,get:function(){return decode_2.decodeHTML}});Object.defineProperty(exports,"decodeHTML5",{enumerable:!0,get:function(){return decode_2.decodeHTML}});Object.defineProperty(exports,"decodeHTML4Strict",{enumerable:!0,get:function(){return decode_2.decodeHTMLStrict}});Object.defineProperty(exports,"decodeHTML5Strict",{enumerable:!0,get:function(){return decode_2.decodeHTMLStrict}});Object.defineProperty(exports,"decodeXMLStrict",{enumerable:!0,get:function(){return decode_2.decodeXML}});}});var require_ansi_to_html=__commonJS({"../../node_modules/ansi-to-html/lib/ansi_to_html.js"(exports,module){function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor))throw new TypeError("Cannot call a class as a function")}function _defineProperties(target,props){for(var i=0;i<props.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||!1,descriptor.configurable=!0,"value"in descriptor&&(descriptor.writable=!0),Object.defineProperty(target,descriptor.key,descriptor);}}function _createClass(Constructor,protoProps,staticProps){return protoProps&&_defineProperties(Constructor.prototype,protoProps),staticProps&&_defineProperties(Constructor,staticProps),Constructor}function _createForOfIteratorHelper(o,allowArrayLike){var it=typeof Symbol<"u"&&o[Symbol.iterator]||o["@@iterator"];if(!it){if(Array.isArray(o)||(it=_unsupportedIterableToArray(o))||allowArrayLike&&o&&typeof o.length=="number"){it&&(o=it);var i=0,F=function(){};return {s:F,n:function(){return i>=o.length?{done:!0}:{done:!1,value:o[i++]}},e:function(_e){throw _e},f:F}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var normalCompletion=!0,didErr=!1,err;return {s:function(){it=it.call(o);},n:function(){var step=it.next();return normalCompletion=step.done,step},e:function(_e2){didErr=!0,err=_e2;},f:function(){try{!normalCompletion&&it.return!=null&&it.return();}finally{if(didErr)throw err}}}}function _unsupportedIterableToArray(o,minLen){if(o){if(typeof o=="string")return _arrayLikeToArray(o,minLen);var n=Object.prototype.toString.call(o).slice(8,-1);if(n==="Object"&&o.constructor&&(n=o.constructor.name),n==="Map"||n==="Set")return Array.from(o);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(o,minLen)}}function _arrayLikeToArray(arr,len){(len==null||len>arr.length)&&(len=arr.length);for(var i=0,arr2=new Array(len);i<len;i++)arr2[i]=arr[i];return arr2}var entities=require_lib(),defaults={fg:"#FFF",bg:"#000",newline:!1,escapeXML:!1,stream:!1,colors:getDefaultColors()};function getDefaultColors(){var colors={0:"#000",1:"#A00",2:"#0A0",3:"#A50",4:"#00A",5:"#A0A",6:"#0AA",7:"#AAA",8:"#555",9:"#F55",10:"#5F5",11:"#FF5",12:"#55F",13:"#F5F",14:"#5FF",15:"#FFF"};return range(0,5).forEach(function(red){range(0,5).forEach(function(green){range(0,5).forEach(function(blue){return setStyleColor(red,green,blue,colors)});});}),range(0,23).forEach(function(gray){var c=gray+232,l=toHexString(gray*10+8);colors[c]="#"+l+l+l;}),colors}function setStyleColor(red,green,blue,colors){var c=16+red*36+green*6+blue,r=red>0?red*40+55:0,g=green>0?green*40+55:0,b=blue>0?blue*40+55:0;colors[c]=toColorHexString([r,g,b]);}function toHexString(num){for(var str=num.toString(16);str.length<2;)str="0"+str;return str}function toColorHexString(ref){var results=[],_iterator=_createForOfIteratorHelper(ref),_step;try{for(_iterator.s();!(_step=_iterator.n()).done;){var r=_step.value;results.push(toHexString(r));}}catch(err){_iterator.e(err);}finally{_iterator.f();}return "#"+results.join("")}function generateOutput(stack,token,data,options){var result;return token==="text"?result=pushText(data,options):token==="display"?result=handleDisplay(stack,data,options):token==="xterm256Foreground"?result=pushForegroundColor(stack,options.colors[data]):token==="xterm256Background"?result=pushBackgroundColor(stack,options.colors[data]):token==="rgb"&&(result=handleRgb(stack,data)),result}function handleRgb(stack,data){data=data.substring(2).slice(0,-1);var operation=+data.substr(0,2),color=data.substring(5).split(";"),rgb=color.map(function(value){return ("0"+Number(value).toString(16)).substr(-2)}).join("");return pushStyle(stack,(operation===38?"color:#":"background-color:#")+rgb)}function handleDisplay(stack,code,options){code=parseInt(code,10);var codeMap={"-1":function(){return "<br/>"},0:function(){return stack.length&&resetStyles(stack)},1:function(){return pushTag(stack,"b")},3:function(){return pushTag(stack,"i")},4:function(){return pushTag(stack,"u")},8:function(){return pushStyle(stack,"display:none")},9:function(){return pushTag(stack,"strike")},22:function(){return pushStyle(stack,"font-weight:normal;text-decoration:none;font-style:normal")},23:function(){return closeTag(stack,"i")},24:function(){return closeTag(stack,"u")},39:function(){return pushForegroundColor(stack,options.fg)},49:function(){return pushBackgroundColor(stack,options.bg)},53:function(){return pushStyle(stack,"text-decoration:overline")}},result;return codeMap[code]?result=codeMap[code]():4<code&&code<7?result=pushTag(stack,"blink"):29<code&&code<38?result=pushForegroundColor(stack,options.colors[code-30]):39<code&&code<48?result=pushBackgroundColor(stack,options.colors[code-40]):89<code&&code<98?result=pushForegroundColor(stack,options.colors[8+(code-90)]):99<code&&code<108&&(result=pushBackgroundColor(stack,options.colors[8+(code-100)])),result}function resetStyles(stack){var stackClone=stack.slice(0);return stack.length=0,stackClone.reverse().map(function(tag){return "</"+tag+">"}).join("")}function range(low,high){for(var results=[],j=low;j<=high;j++)results.push(j);return results}function notCategory(category){return function(e){return (category===null||e.category!==category)&&category!=="all"}}function categoryForCode(code){code=parseInt(code,10);var result=null;return code===0?result="all":code===1?result="bold":2<code&&code<5?result="underline":4<code&&code<7?result="blink":code===8?result="hide":code===9?result="strike":29<code&&code<38||code===39||89<code&&code<98?result="foreground-color":(39<code&&code<48||code===49||99<code&&code<108)&&(result="background-color"),result}function pushText(text,options){return options.escapeXML?entities.encodeXML(text):text}function pushTag(stack,tag,style){return style||(style=""),stack.push(tag),"<".concat(tag).concat(style?' style="'.concat(style,'"'):"",">")}function pushStyle(stack,style){return pushTag(stack,"span",style)}function pushForegroundColor(stack,color){return pushTag(stack,"span","color:"+color)}function pushBackgroundColor(stack,color){return pushTag(stack,"span","background-color:"+color)}function closeTag(stack,style){var last;if(stack.slice(-1)[0]===style&&(last=stack.pop()),last)return "</"+style+">"}function tokenize(text,options,callback){var ansiMatch=!1,ansiHandler=3;function remove(){return ""}function removeXterm256Foreground(m,g1){return callback("xterm256Foreground",g1),""}function removeXterm256Background(m,g1){return callback("xterm256Background",g1),""}function newline(m){return options.newline?callback("display",-1):callback("text",m),""}function ansiMess(m,g1){ansiMatch=!0,g1.trim().length===0&&(g1="0"),g1=g1.trimRight(";").split(";");var _iterator2=_createForOfIteratorHelper(g1),_step2;try{for(_iterator2.s();!(_step2=_iterator2.n()).done;){var g=_step2.value;callback("display",g);}}catch(err){_iterator2.e(err);}finally{_iterator2.f();}return ""}function realText(m){return callback("text",m),""}function rgb(m){return callback("rgb",m),""}var tokens=[{pattern:/^\x08+/,sub:remove},{pattern:/^\x1b\[[012]?K/,sub:remove},{pattern:/^\x1b\[\(B/,sub:remove},{pattern:/^\x1b\[[34]8;2;\d+;\d+;\d+m/,sub:rgb},{pattern:/^\x1b\[38;5;(\d+)m/,sub:removeXterm256Foreground},{pattern:/^\x1b\[48;5;(\d+)m/,sub:removeXterm256Background},{pattern:/^\n/,sub:newline},{pattern:/^\r+\n/,sub:newline},{pattern:/^\r/,sub:newline},{pattern:/^\x1b\[((?:\d{1,3};?)+|)m/,sub:ansiMess},{pattern:/^\x1b\[\d?J/,sub:remove},{pattern:/^\x1b\[\d{0,3};\d{0,3}f/,sub:remove},{pattern:/^\x1b\[?[\d;]{0,3}/,sub:remove},{pattern:/^(([^\x1b\x08\r\n])+)/,sub:realText}];function process(handler2,i2){i2>ansiHandler&&ansiMatch||(ansiMatch=!1,text=text.replace(handler2.pattern,handler2.sub));}var results1=[],_text=text,length=_text.length;outer:for(;length>0;){for(var i=0,o=0,len=tokens.length;o<len;i=++o){var handler=tokens[i];if(process(handler,i),text.length!==length){length=text.length;continue outer}}if(text.length===length)break;results1.push(0),length=text.length;}return results1}function updateStickyStack(stickyStack,token,data){return token!=="text"&&(stickyStack=stickyStack.filter(notCategory(categoryForCode(data))),stickyStack.push({token,data,category:categoryForCode(data)})),stickyStack}var Filter2=function(){function Filter3(options){_classCallCheck(this,Filter3),options=options||{},options.colors&&(options.colors=Object.assign({},defaults.colors,options.colors)),this.options=Object.assign({},defaults,options),this.stack=[],this.stickyStack=[];}return _createClass(Filter3,[{key:"toHtml",value:function(input){var _this=this;input=typeof input=="string"?[input]:input;var stack=this.stack,options=this.options,buf=[];return this.stickyStack.forEach(function(element){var output=generateOutput(stack,element.token,element.data,options);output&&buf.push(output);}),tokenize(input.join(""),options,function(token,data){var output=generateOutput(stack,token,data,options);output&&buf.push(output),options.stream&&(_this.stickyStack=updateStickyStack(_this.stickyStack,token,data));}),stack.length&&buf.push(resetStyles(stack)),buf.join("")}}]),Filter3}();module.exports=Filter2;}});var require_extends=__commonJS({"../../node_modules/@devtools-ds/object-inspector/node_modules/@babel/runtime/helpers/extends.js"(exports,module){function _extends5(){return module.exports=_extends5=Object.assign||function(target){for(var i=1;i<arguments.length;i++){var source=arguments[i];for(var key in source)Object.prototype.hasOwnProperty.call(source,key)&&(target[key]=source[key]);}return target},_extends5.apply(this,arguments)}module.exports=_extends5;}});var require_objectWithoutPropertiesLoose=__commonJS({"../../node_modules/@devtools-ds/object-inspector/node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js"(exports,module){function _objectWithoutPropertiesLoose(source,excluded){if(source==null)return {};var target={},sourceKeys=Object.keys(source),key,i;for(i=0;i<sourceKeys.length;i++)key=sourceKeys[i],!(excluded.indexOf(key)>=0)&&(target[key]=source[key]);return target}module.exports=_objectWithoutPropertiesLoose;}});var require_objectWithoutProperties=__commonJS({"../../node_modules/@devtools-ds/object-inspector/node_modules/@babel/runtime/helpers/objectWithoutProperties.js"(exports,module){var objectWithoutPropertiesLoose=require_objectWithoutPropertiesLoose();function _objectWithoutProperties6(source,excluded){if(source==null)return {};var target=objectWithoutPropertiesLoose(source,excluded),key,i;if(Object.getOwnPropertySymbols){var sourceSymbolKeys=Object.getOwnPropertySymbols(source);for(i=0;i<sourceSymbolKeys.length;i++)key=sourceSymbolKeys[i],!(excluded.indexOf(key)>=0)&&Object.prototype.propertyIsEnumerable.call(source,key)&&(target[key]=source[key]);}return target}module.exports=_objectWithoutProperties6;}});var require_defineProperty=__commonJS({"../../node_modules/@devtools-ds/themes/node_modules/@babel/runtime/helpers/defineProperty.js"(exports,module){function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}module.exports=_defineProperty;}});var require_objectSpread2=__commonJS({"../../node_modules/@devtools-ds/themes/node_modules/@babel/runtime/helpers/objectSpread2.js"(exports,module){var defineProperty=require_defineProperty();function ownKeys(object,enumerableOnly){var keys=Object.keys(object);if(Object.getOwnPropertySymbols){var symbols=Object.getOwnPropertySymbols(object);enumerableOnly&&(symbols=symbols.filter(function(sym){return Object.getOwnPropertyDescriptor(object,sym).enumerable})),keys.push.apply(keys,symbols);}return keys}function _objectSpread22(target){for(var i=1;i<arguments.length;i++){var source=arguments[i]!=null?arguments[i]:{};i%2?ownKeys(source,!0).forEach(function(key){defineProperty(target,key,source[key]);}):Object.getOwnPropertyDescriptors?Object.defineProperties(target,Object.getOwnPropertyDescriptors(source)):ownKeys(source).forEach(function(key){Object.defineProperty(target,key,Object.getOwnPropertyDescriptor(source,key));});}return target}module.exports=_objectSpread22;}});var require_objectWithoutPropertiesLoose2=__commonJS({"../../node_modules/@devtools-ds/themes/node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js"(exports,module){function _objectWithoutPropertiesLoose(source,excluded){if(source==null)return {};var target={},sourceKeys=Object.keys(source),key,i;for(i=0;i<sourceKeys.length;i++)key=sourceKeys[i],!(excluded.indexOf(key)>=0)&&(target[key]=source[key]);return target}module.exports=_objectWithoutPropertiesLoose;}});var require_objectWithoutProperties2=__commonJS({"../../node_modules/@devtools-ds/themes/node_modules/@babel/runtime/helpers/objectWithoutProperties.js"(exports,module){var objectWithoutPropertiesLoose=require_objectWithoutPropertiesLoose2();function _objectWithoutProperties6(source,excluded){if(source==null)return {};var target=objectWithoutPropertiesLoose(source,excluded),key,i;if(Object.getOwnPropertySymbols){var sourceSymbolKeys=Object.getOwnPropertySymbols(source);for(i=0;i<sourceSymbolKeys.length;i++)key=sourceSymbolKeys[i],!(excluded.indexOf(key)>=0)&&Object.prototype.propertyIsEnumerable.call(source,key)&&(target[key]=source[key]);}return target}module.exports=_objectWithoutProperties6;}});var require_defineProperty2=__commonJS({"../../node_modules/@devtools-ds/object-inspector/node_modules/@babel/runtime/helpers/defineProperty.js"(exports,module){function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}module.exports=_defineProperty;}});var require_objectSpread22=__commonJS({"../../node_modules/@devtools-ds/object-inspector/node_modules/@babel/runtime/helpers/objectSpread2.js"(exports,module){var defineProperty=require_defineProperty2();function ownKeys(object,enumerableOnly){var keys=Object.keys(object);if(Object.getOwnPropertySymbols){var symbols=Object.getOwnPropertySymbols(object);enumerableOnly&&(symbols=symbols.filter(function(sym){return Object.getOwnPropertyDescriptor(object,sym).enumerable})),keys.push.apply(keys,symbols);}return keys}function _objectSpread22(target){for(var i=1;i<arguments.length;i++){var source=arguments[i]!=null?arguments[i]:{};i%2?ownKeys(source,!0).forEach(function(key){defineProperty(target,key,source[key]);}):Object.getOwnPropertyDescriptors?Object.defineProperties(target,Object.getOwnPropertyDescriptors(source)):ownKeys(source).forEach(function(key){Object.defineProperty(target,key,Object.getOwnPropertyDescriptor(source,key));});}return target}module.exports=_objectSpread22;}});var require_extends2=__commonJS({"../../node_modules/@devtools-ds/tree/node_modules/@babel/runtime/helpers/extends.js"(exports,module){function _extends5(){return module.exports=_extends5=Object.assign||function(target){for(var i=1;i<arguments.length;i++){var source=arguments[i];for(var key in source)Object.prototype.hasOwnProperty.call(source,key)&&(target[key]=source[key]);}return target},_extends5.apply(this,arguments)}module.exports=_extends5;}});var require_objectWithoutPropertiesLoose3=__commonJS({"../../node_modules/@devtools-ds/tree/node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js"(exports,module){function _objectWithoutPropertiesLoose(source,excluded){if(source==null)return {};var target={},sourceKeys=Object.keys(source),key,i;for(i=0;i<sourceKeys.length;i++)key=sourceKeys[i],!(excluded.indexOf(key)>=0)&&(target[key]=source[key]);return target}module.exports=_objectWithoutPropertiesLoose;}});var require_objectWithoutProperties3=__commonJS({"../../node_modules/@devtools-ds/tree/node_modules/@babel/runtime/helpers/objectWithoutProperties.js"(exports,module){var objectWithoutPropertiesLoose=require_objectWithoutPropertiesLoose3();function _objectWithoutProperties6(source,excluded){if(source==null)return {};var target=objectWithoutPropertiesLoose(source,excluded),key,i;if(Object.getOwnPropertySymbols){var sourceSymbolKeys=Object.getOwnPropertySymbols(source);for(i=0;i<sourceSymbolKeys.length;i++)key=sourceSymbolKeys[i],!(excluded.indexOf(key)>=0)&&Object.prototype.propertyIsEnumerable.call(source,key)&&(target[key]=source[key]);}return target}module.exports=_objectWithoutProperties6;}});var import_ansi_to_html=__toESM(require_ansi_to_html());function isTestAssertionError(error){return isChaiError(error)||isJestError(error)}function isChaiError(error){return error&&typeof error=="object"&&"name"in error&&typeof error.name=="string"&&error.name==="AssertionError"}function isJestError(error){return error&&typeof error=="object"&&"message"in error&&typeof error.message=="string"&&error.message.startsWith("expect(")}function createAnsiToHtmlFilter(theme){return new import_ansi_to_html.default({fg:theme.color.defaultText,bg:theme.background.content,escapeXML:!0})}function useAnsiToHtmlFilter(){let theme=useTheme();return createAnsiToHtmlFilter(theme)}var ADDON_ID="storybook/interactions",PANEL_ID=`${ADDON_ID}/panel`,TUTORIAL_VIDEO_LINK="https://youtu.be/Waht9qq7AoA",DOCUMENTATION_LINK="writing-tests/interaction-testing";var Links=styled.div(({theme})=>({display:"flex",fontSize:theme.typography.size.s2-1,gap:25})),Divider=styled.div(({theme})=>({width:1,height:16,backgroundColor:theme.appBorderColor})),Empty=()=>{let[isLoading,setIsLoading]=useState(!0),docsUrl=useStorybookApi().getDocsUrl({subpath:DOCUMENTATION_LINK,versioned:!0,renderer:!0});return useEffect(()=>{let load=setTimeout(()=>{setIsLoading(!1);},100);return ()=>clearTimeout(load)},[]),isLoading?null:React9__default.createElement(EmptyTabContent,{title:"Interaction testing",description:React9__default.createElement(React9__default.Fragment,null,"Interaction tests allow you to verify the functional aspects of UIs. Write a play function for your story and you'll see it run here."),footer:React9__default.createElement(Links,null,React9__default.createElement(Link,{href:TUTORIAL_VIDEO_LINK,target:"_blank",withArrow:!0},React9__default.createElement(VideoIcon,null)," Watch 8m video"),React9__default.createElement(Divider,null),React9__default.createElement(Link,{href:docsUrl,target:"_blank",withArrow:!0},React9__default.createElement(DocumentIcon,null)," Read docs"))})};var import_extends4=__toESM(require_extends()),import_objectWithoutProperties5=__toESM(require_objectWithoutProperties());function toVal(mix){var k,y,str="";if(mix)if(typeof mix=="object")if(Array.isArray(mix))for(k=0;k<mix.length;k++)mix[k]&&(y=toVal(mix[k]))&&(str&&(str+=" "),str+=y);else for(k in mix)mix[k]&&(y=toVal(k))&&(str&&(str+=" "),str+=y);else typeof mix!="boolean"&&!mix.call&&(str&&(str+=" "),str+=mix);return str}function clsx_m_default(){for(var i=0,x,str="";i<arguments.length;)(x=toVal(arguments[i++]))&&(str&&(str+=" "),str+=x);return str}var isArray=val=>Array.isArray(val)||ArrayBuffer.isView(val)&&!(val instanceof DataView),isObject=val=>val!==null&&typeof val=="object"&&!isArray(val)&&!(val instanceof Date)&&!(val instanceof RegExp)&&!(val instanceof Error)&&!(val instanceof WeakMap)&&!(val instanceof WeakSet),isKnownObject=val=>isObject(val)||isArray(val)||typeof val=="function"||val instanceof Promise,getPromiseState=promise=>{let unique=/unique/;return Promise.race([promise,unique]).then(result=>result===unique?["pending"]:["fulfilled",result],e=>["rejected",e])},buildAST=async(key,value,depth,sortKeys,isPrototype,showPrototype)=>{let astNode={key,depth,value,type:"value",parent:void 0};if(value&&isKnownObject(value)&&depth<100){let children=[],t="object";if(isArray(value)){for(let i=0;i<value.length;i++)children.push(async()=>{let child=await buildAST(i.toString(),value[i],depth+1,sortKeys);return child.parent=astNode,child});t="array";}else {let keys=Object.getOwnPropertyNames(value);sortKeys&&keys.sort();for(let i=0;i<keys.length;i++){let safeValue;try{safeValue=value[keys[i]];}catch{}children.push(async()=>{let child=await buildAST(keys[i],safeValue,depth+1,sortKeys);return child.parent=astNode,child});}if(typeof value=="function"&&(t="function"),value instanceof Promise){let[status,result]=await getPromiseState(value);children.push(async()=>{let child=await buildAST("<state>",status,depth+1,sortKeys);return child.parent=astNode,child}),status!=="pending"&&children.push(async()=>{let child=await buildAST("<value>",result,depth+1,sortKeys);return child.parent=astNode,child}),t="promise";}if(value instanceof Map){let parsedEntries=Array.from(value.entries()).map(entry=>{let[entryKey,entryValue]=entry;return {"<key>":entryKey,"<value>":entryValue}});children.push(async()=>{let child=await buildAST("<entries>",parsedEntries,depth+1,sortKeys);return child.parent=astNode,child}),children.push(async()=>{let child=await buildAST("size",value.size,depth+1,sortKeys);return child.parent=astNode,child}),t="map";}if(value instanceof Set){let parsedEntries=Array.from(value.entries()).map(entry=>entry[1]);children.push(async()=>{let child=await buildAST("<entries>",parsedEntries,depth+1,sortKeys);return child.parent=astNode,child}),children.push(async()=>{let child=await buildAST("size",value.size,depth+1,sortKeys);return child.parent=astNode,child}),t="set";}}value!==Object.prototype&&showPrototype&&children.push(async()=>{let child=await buildAST("<prototype>",Object.getPrototypeOf(value),depth+1,sortKeys,!0);return child.parent=astNode,child}),astNode.type=t,astNode.children=children,astNode.isPrototype=isPrototype;}return astNode},parse=(data,sortKeys,includePrototypes)=>buildAST("root",data,0,sortKeys===!1?sortKeys:!0,void 0,includePrototypes===!1?includePrototypes:!0);var import_objectSpread2=__toESM(require_objectSpread2()),import_objectWithoutProperties=__toESM(require_objectWithoutProperties2());var _excluded=["children"];var ThemeContext=React9__default.createContext({theme:"chrome",colorScheme:"light"});var ThemeProvider=_ref=>{let{children}=_ref,value=(0, import_objectWithoutProperties.default)(_ref,_excluded),wrappedTheme=React9__default.useContext(ThemeContext);return React9__default.createElement(ThemeContext.Provider,{value:(0, import_objectSpread2.default)((0, import_objectSpread2.default)({},wrappedTheme),value)},children)},useTheme2=(props,styles={})=>{let themeContext=React9__default.useContext(ThemeContext),currentTheme=props.theme||themeContext.theme||"chrome",currentColorScheme=props.colorScheme||themeContext.colorScheme||"light",themeClass=clsx_m_default(styles[currentTheme],styles[currentColorScheme]);return {currentColorScheme,currentTheme,themeClass}};var import_objectSpread22=__toESM(require_objectSpread22());var import_extends=__toESM(require_extends2()),import_objectWithoutProperties2=__toESM(require_objectWithoutProperties3());var TreeContext=React9__default.createContext({isChild:!1,depth:0,hasHover:!0}),TreeContext_default=TreeContext;var Tree_css_default={tree:"Tree-tree-fbbbe38",item:"Tree-item-353d6f3",group:"Tree-group-d3c3d8a",label:"Tree-label-d819155",focusWhite:"Tree-focusWhite-f1e00c2",arrow:"Tree-arrow-03ab2e7",hover:"Tree-hover-3cc4e5d",open:"Tree-open-3f1a336",dark:"Tree-dark-1b4aa00",chrome:"Tree-chrome-bcbcac6",light:"Tree-light-09174ee"};var _excluded2=["theme","hover","colorScheme","children","label","className","onUpdate","onSelect","open"],Tree=props=>{let{theme,hover,colorScheme,children,label,className,onUpdate,onSelect,open}=props,html=(0, import_objectWithoutProperties2.default)(props,_excluded2),{themeClass,currentTheme}=useTheme2({theme,colorScheme},Tree_css_default),[isOpen,setOpen]=useState(open);useEffect(()=>{setOpen(open);},[open]);let updateState=value=>{setOpen(value),onUpdate&&onUpdate(value);},hasChildren=React9__default.Children.count(children)>0,updateFocus=(newNode,previousNode)=>{if(newNode.isSameNode(previousNode||null))return;let focusableNode=newNode.querySelector('[tabindex="-1"]');focusableNode?.focus(),newNode.setAttribute("aria-selected","true"),previousNode?.removeAttribute("aria-selected");},getParent=(node,role)=>{let parent=node;for(;parent&&parent.parentElement;){if(parent.getAttribute("role")===role)return parent;parent=parent.parentElement;}return null},getListElements=node=>{let tree=getParent(node,"tree");return tree?Array.from(tree.querySelectorAll("li")):[]},moveBack=node=>{let group=getParent(node,"group"),toggle=group?.previousElementSibling;if(toggle&&toggle.getAttribute("tabindex")==="-1"){let toggleParent=toggle.parentElement,nodeParent=node.parentElement;updateFocus(toggleParent,nodeParent);}},moveHome=(node,direction)=>{let elements=getListElements(node);elements.forEach(element=>{element.removeAttribute("aria-selected");}),direction==="start"&&elements[0]&&updateFocus(elements[0]),direction==="end"&&elements[elements.length-1]&&updateFocus(elements[elements.length-1]);},moveFocusAdjacent=(node,direction)=>{let elements=getListElements(node)||[];for(let i=0;i<elements.length;i++){let currentNode=elements[i];if(currentNode.getAttribute("aria-selected")==="true"){direction==="up"&&elements[i-1]?updateFocus(elements[i-1],currentNode):direction==="down"&&elements[i+1]&&updateFocus(elements[i+1],currentNode);return}}updateFocus(elements[0]);},handleKeypress=(event,isChild2)=>{let node=event.target;(event.key==="Enter"||event.key===" ")&&updateState(!isOpen),event.key==="ArrowRight"&&isOpen&&!isChild2?moveFocusAdjacent(node,"down"):event.key==="ArrowRight"&&updateState(!0),event.key==="ArrowLeft"&&(!isOpen||isChild2)?moveBack(node):event.key==="ArrowLeft"&&updateState(!1),event.key==="ArrowDown"&&moveFocusAdjacent(node,"down"),event.key==="ArrowUp"&&moveFocusAdjacent(node,"up"),event.key==="Home"&&moveHome(node,"start"),event.key==="End"&&moveHome(node,"end");},handleClick=(event,isChild2)=>{let node=event.target,parent=getParent(node,"treeitem"),elements=getListElements(node)||[],found=!1;for(let i=0;i<elements.length;i++){let currentNode=elements[i];if(currentNode.getAttribute("aria-selected")==="true"){parent&&(found=!0,updateFocus(parent,currentNode));break}}!found&&parent&&updateFocus(parent),isChild2||updateState(!isOpen);},handleBlur=event=>{let node=event.currentTarget;!node.contains(document.activeElement)&&node.getAttribute("role")==="tree"&&node.setAttribute("tabindex","0");},handleFocus=event=>{let node=event.target;if(node.getAttribute("role")==="tree"){let selected=node.querySelector('[aria-selected="true"]');selected?updateFocus(selected):moveFocusAdjacent(node,"down"),node.setAttribute("tabindex","-1");}},handleButtonFocus=()=>{onSelect?.();},getPaddingStyles=depth2=>{let space=depth2*.9+.3;return {paddingLeft:`${space}em`,width:`calc(100% - ${space}em)`}},{isChild,depth,hasHover}=React9__default.useContext(TreeContext_default),showHover=hasHover?hover:!1;if(!isChild)return React9__default.createElement("ul",(0, import_extends.default)({role:"tree",tabIndex:0,className:clsx_m_default(Tree_css_default.tree,Tree_css_default.group,themeClass,className),onFocus:handleFocus,onBlur:handleBlur},html),React9__default.createElement(TreeContext_default.Provider,{value:{isChild:!0,depth:0,hasHover:showHover}},React9__default.createElement(Tree,props)));if(!hasChildren)return React9__default.createElement("li",(0, import_extends.default)({role:"treeitem",className:Tree_css_default.item},html),React9__default.createElement("div",{role:"button",className:clsx_m_default(Tree_css_default.label,{[Tree_css_default.hover]:showHover,[Tree_css_default.focusWhite]:currentTheme==="firefox"}),tabIndex:-1,style:getPaddingStyles(depth),onKeyDown:e=>{handleKeypress(e,isChild);},onClick:e=>handleClick(e,!0),onFocus:handleButtonFocus},React9__default.createElement("span",null,label)));let arrowClass=clsx_m_default(Tree_css_default.arrow,{[Tree_css_default.open]:isOpen});return React9__default.createElement("li",{role:"treeitem","aria-expanded":isOpen,className:Tree_css_default.item},React9__default.createElement("div",{role:"button",tabIndex:-1,className:clsx_m_default(Tree_css_default.label,{[Tree_css_default.hover]:showHover,[Tree_css_default.focusWhite]:currentTheme==="firefox"}),style:getPaddingStyles(depth),onClick:e=>handleClick(e),onKeyDown:e=>handleKeypress(e),onFocus:handleButtonFocus},React9__default.createElement("span",null,React9__default.createElement("span",{"aria-hidden":!0,className:arrowClass}),React9__default.createElement("span",null,label))),React9__default.createElement("ul",(0, import_extends.default)({role:"group",className:clsx_m_default(className,Tree_css_default.group)},html),isOpen&&React9__default.Children.map(children,child=>React9__default.createElement(TreeContext_default.Provider,{value:{isChild:!0,depth:depth+1,hasHover:showHover}},child))))};Tree.defaultProps={open:!1,hover:!0};var import_extends2=__toESM(require_extends()),import_objectWithoutProperties3=__toESM(require_objectWithoutProperties());var ObjectInspector_css_default={"object-inspector":"ObjectInspector-object-inspector-0c33e82",objectInspector:"ObjectInspector-object-inspector-0c33e82","object-label":"ObjectInspector-object-label-b81482b",objectLabel:"ObjectInspector-object-label-b81482b",text:"ObjectInspector-text-25f57f3",key:"ObjectInspector-key-4f712bb",value:"ObjectInspector-value-f7ec2e5",string:"ObjectInspector-string-c496000",regex:"ObjectInspector-regex-59d45a3",error:"ObjectInspector-error-b818698",boolean:"ObjectInspector-boolean-2dd1642",number:"ObjectInspector-number-a6daabb",undefined:"ObjectInspector-undefined-3a68263",null:"ObjectInspector-null-74acb50",function:"ObjectInspector-function-07bbdcd","function-decorator":"ObjectInspector-function-decorator-3d22c24",functionDecorator:"ObjectInspector-function-decorator-3d22c24",prototype:"ObjectInspector-prototype-f2449ee",dark:"ObjectInspector-dark-0c96c97",chrome:"ObjectInspector-chrome-2f3ca98",light:"ObjectInspector-light-78bef54"};var _excluded3=["ast","theme","showKey","colorScheme","className"],buildValue=(key,value,valueClass,showKey,depth)=>{let computedKey=key.includes("-")?`"${key}"`:key,isRoot=depth<=0;return React9__default.createElement("span",{className:ObjectInspector_css_default.text},!isRoot&&showKey&&React9__default.createElement(React9__default.Fragment,null,React9__default.createElement("span",{className:ObjectInspector_css_default.key},computedKey),React9__default.createElement("span",null,":\xA0")),React9__default.createElement("span",{className:valueClass},value))},ObjectValue=props=>{let{ast,theme,showKey,colorScheme,className}=props,html=(0, import_objectWithoutProperties3.default)(props,_excluded3),{themeClass}=useTheme2({theme,colorScheme},ObjectInspector_css_default),[asyncValue,setAsyncValue]=useState(React9__default.createElement("span",null)),value=React9__default.createElement("span",null);return useEffect(()=>{ast.value instanceof Promise&&(async promise=>{setAsyncValue(buildValue(ast.key,`Promise { "${await getPromiseState(promise)}" }`,ObjectInspector_css_default.key,showKey,ast.depth));})(ast.value);},[ast,showKey]),typeof ast.value=="number"||typeof ast.value=="bigint"?value=buildValue(ast.key,String(ast.value),ObjectInspector_css_default.number,showKey,ast.depth):typeof ast.value=="boolean"?value=buildValue(ast.key,String(ast.value),ObjectInspector_css_default.boolean,showKey,ast.depth):typeof ast.value=="string"?value=buildValue(ast.key,`"${ast.value}"`,ObjectInspector_css_default.string,showKey,ast.depth):typeof ast.value>"u"?value=buildValue(ast.key,"undefined",ObjectInspector_css_default.undefined,showKey,ast.depth):typeof ast.value=="symbol"?value=buildValue(ast.key,ast.value.toString(),ObjectInspector_css_default.string,showKey,ast.depth):typeof ast.value=="function"?value=buildValue(ast.key,`${ast.value.name}()`,ObjectInspector_css_default.key,showKey,ast.depth):typeof ast.value=="object"&&(ast.value===null?value=buildValue(ast.key,"null",ObjectInspector_css_default.null,showKey,ast.depth):Array.isArray(ast.value)?value=buildValue(ast.key,`Array(${ast.value.length})`,ObjectInspector_css_default.key,showKey,ast.depth):ast.value instanceof Date?value=buildValue(ast.key,`Date ${ast.value.toString()}`,ObjectInspector_css_default.value,showKey,ast.depth):ast.value instanceof RegExp?value=buildValue(ast.key,ast.value.toString(),ObjectInspector_css_default.regex,showKey,ast.depth):ast.value instanceof Error?value=buildValue(ast.key,ast.value.toString(),ObjectInspector_css_default.error,showKey,ast.depth):isObject(ast.value)?value=buildValue(ast.key,"{\u2026}",ObjectInspector_css_default.key,showKey,ast.depth):value=buildValue(ast.key,ast.value.constructor.name,ObjectInspector_css_default.key,showKey,ast.depth)),React9__default.createElement("span",(0, import_extends2.default)({className:clsx_m_default(themeClass,className)},html),asyncValue,value)};ObjectValue.defaultProps={showKey:!0};var ObjectValue_default=ObjectValue;var import_extends3=__toESM(require_extends()),import_objectWithoutProperties4=__toESM(require_objectWithoutProperties());var _excluded4=["ast","theme","previewMax","open","colorScheme","className"],buildPreview=(children,previewMax,showKey)=>{let previews=[];for(let i=0;i<children.length;i++){let child=children[i];if(child.isPrototype||(previews.push(React9__default.createElement(ObjectValue_default,{key:child.key,ast:child,showKey})),i<children.length-1?previews.push(", "):previews.push(" ")),child.isPrototype&&i===children.length-1&&(previews.pop(),previews.push(" ")),i===previewMax-1&&children.length>previewMax){previews.push("\u2026 ");break}}return previews},getArrayLabel=(ast,open,previewMax,theme)=>{let l=ast.value.length;return open?React9__default.createElement("span",null,"Array(",l,")"):React9__default.createElement(React9__default.Fragment,null,React9__default.createElement("span",null,`${theme==="firefox"?"Array":""}(${l}) [ `),buildPreview(ast.children,previewMax,!1),React9__default.createElement("span",null,"]"))},getObjectLabel=(ast,open,previewMax,theme)=>ast.isPrototype?React9__default.createElement("span",null,`Object ${theme==="firefox"?"{ \u2026 }":""}`):open?React9__default.createElement("span",null,"{\u2026}"):React9__default.createElement(React9__default.Fragment,null,React9__default.createElement("span",null,`${theme==="firefox"?"Object ":""}{ `),buildPreview(ast.children,previewMax,!0),React9__default.createElement("span",null,"}")),getPromiseLabel=(ast,open,previewMax)=>open?React9__default.createElement("span",null,`Promise { "${String(ast.children[0].value)}" }`):React9__default.createElement(React9__default.Fragment,null,React9__default.createElement("span",null,"Promise { "),buildPreview(ast.children,previewMax,!0),React9__default.createElement("span",null,"}")),getMapLabel=(ast,open,previewMax,theme)=>{let{size}=ast.value;return open?React9__default.createElement("span",null,`Map(${size})`):React9__default.createElement(React9__default.Fragment,null,React9__default.createElement("span",null,`Map${theme==="chrome"?`(${size})`:""} { `),buildPreview(ast.children,previewMax,!0),React9__default.createElement("span",null,"}"))},getSetLabel=(ast,open,previewMax)=>{let{size}=ast.value;return open?React9__default.createElement("span",null,"Set(",size,")"):React9__default.createElement(React9__default.Fragment,null,React9__default.createElement("span",null,`Set(${ast.value.size}) {`),buildPreview(ast.children,previewMax,!0),React9__default.createElement("span",null,"}"))},ObjectLabel=props=>{let{ast,theme,previewMax,open,colorScheme,className}=props,html=(0, import_objectWithoutProperties4.default)(props,_excluded4),{themeClass,currentTheme}=useTheme2({theme,colorScheme},ObjectInspector_css_default),isPrototype=ast.isPrototype||!1,classes=clsx_m_default(ObjectInspector_css_default.objectLabel,themeClass,className,{[ObjectInspector_css_default.prototype]:isPrototype}),isRoot=ast.depth<=0,Key=()=>React9__default.createElement("span",{className:isPrototype?ObjectInspector_css_default.prototype:ObjectInspector_css_default.key},isRoot?"":`${ast.key}: `);return ast.type==="array"?React9__default.createElement("span",(0, import_extends3.default)({className:classes},html),React9__default.createElement(Key,null),getArrayLabel(ast,open,previewMax,currentTheme)):ast.type==="function"?React9__default.createElement("span",(0, import_extends3.default)({className:classes},html),React9__default.createElement(Key,null),currentTheme==="chrome"&&React9__default.createElement("span",{className:ObjectInspector_css_default.functionDecorator},"\u0192 "),React9__default.createElement("span",{className:clsx_m_default({[ObjectInspector_css_default.function]:!isPrototype})},`${ast.value.name}()`)):ast.type==="promise"?React9__default.createElement("span",(0, import_extends3.default)({className:classes},html),React9__default.createElement(Key,null),getPromiseLabel(ast,open,previewMax)):ast.type==="map"?React9__default.createElement("span",(0, import_extends3.default)({className:classes},html),React9__default.createElement(Key,null),getMapLabel(ast,open,previewMax,currentTheme)):ast.type==="set"?React9__default.createElement("span",(0, import_extends3.default)({className:classes},html),React9__default.createElement(Key,null),getSetLabel(ast,open,previewMax)):React9__default.createElement("span",(0, import_extends3.default)({className:classes},html),React9__default.createElement(Key,null),getObjectLabel(ast,open,previewMax,currentTheme))};ObjectLabel.defaultProps={previewMax:8,open:!1};var ObjectLabel_default=ObjectLabel;var ObjectInspectorItem=props=>{let{ast,expandLevel,depth}=props,[resolved,setResolved]=useState(),[open,setOpen]=useState(depth<expandLevel);return useEffect(()=>{(async()=>{if(ast.type!=="value"){let promises=ast.children.map(f=>f()),children=await Promise.all(promises),r=(0, import_objectSpread22.default)((0, import_objectSpread22.default)({},ast),{},{children});setResolved(r);}})();},[ast]),resolved?React9__default.createElement(Tree,{hover:!1,open,label:React9__default.createElement(ObjectLabel_default,{open,ast:resolved}),onSelect:()=>{var _props$onSelect;(_props$onSelect=props.onSelect)===null||_props$onSelect===void 0||_props$onSelect.call(props,ast);},onUpdate:value=>{setOpen(value);}},resolved.children.map(child=>React9__default.createElement(ObjectInspectorItem,{key:child.key,ast:child,depth:depth+1,expandLevel,onSelect:props.onSelect}))):React9__default.createElement(Tree,{hover:!1,label:React9__default.createElement(ObjectValue_default,{ast}),onSelect:()=>{var _props$onSelect2;(_props$onSelect2=props.onSelect)===null||_props$onSelect2===void 0||_props$onSelect2.call(props,ast);}})};ObjectInspectorItem.defaultProps={expandLevel:0,depth:0};var ObjectInspectorItem_default=ObjectInspectorItem;var _excluded5=["data","expandLevel","sortKeys","includePrototypes","className","theme","colorScheme","onSelect"],ObjectInspector=props=>{let{data,expandLevel,sortKeys,includePrototypes,className,theme,colorScheme,onSelect}=props,html=(0, import_objectWithoutProperties5.default)(props,_excluded5),[ast,setAST]=useState(void 0),{themeClass,currentTheme,currentColorScheme}=useTheme2({theme,colorScheme},ObjectInspector_css_default);return useEffect(()=>{(async()=>{setAST(await parse(data,sortKeys,includePrototypes));})();},[data,sortKeys,includePrototypes]),React9__default.createElement("div",(0, import_extends4.default)({className:clsx_m_default(ObjectInspector_css_default.objectInspector,className,themeClass)},html),ast&&React9__default.createElement(ThemeProvider,{theme:currentTheme,colorScheme:currentColorScheme},React9__default.createElement(ObjectInspectorItem_default,{ast,expandLevel,onSelect})))};ObjectInspector.defaultProps={expandLevel:0,sortKeys:!0,includePrototypes:!0};var colorsLight={base:"#444",nullish:"#7D99AA",string:"#16B242",number:"#5D40D0",boolean:"#f41840",objectkey:"#698394",instance:"#A15C20",function:"#EA7509",muted:"#7D99AA",tag:{name:"#6F2CAC",suffix:"#1F99E5"},date:"#459D9C",error:{name:"#D43900",message:"#444"},regex:{source:"#A15C20",flags:"#EA7509"},meta:"#EA7509",method:"#0271B6"},colorsDark={base:"#eee",nullish:"#aaa",string:"#5FE584",number:"#6ba5ff",boolean:"#ff4191",objectkey:"#accfe6",instance:"#E3B551",function:"#E3B551",muted:"#aaa",tag:{name:"#f57bff",suffix:"#8EB5FF"},date:"#70D4D3",error:{name:"#f40",message:"#eee"},regex:{source:"#FAD483",flags:"#E3B551"},meta:"#FAD483",method:"#5EC1FF"},useThemeColors=()=>{let{base}=useTheme();return base==="dark"?colorsDark:colorsLight},special=/[^A-Z0-9]/i,trimEnd=/[\s.,…]+$/gm,ellipsize=(string,maxlength)=>{if(string.length<=maxlength)return string;for(let i=maxlength-1;i>=0;i-=1)if(special.test(string[i])&&i>10)return `${string.slice(0,i).replace(trimEnd,"")}\u2026`;return `${string.slice(0,maxlength).replace(trimEnd,"")}\u2026`},stringify=value=>{try{return JSON.stringify(value,null,1)}catch{return String(value)}},interleave=(nodes,separator)=>nodes.flatMap((node,index)=>index===nodes.length-1?[node]:[node,React9__default.cloneElement(separator,{key:`sep${index}`})]),Node=({value,nested,showObjectInspector,callsById,...props})=>{switch(!0){case value===null:return React9__default.createElement(NullNode,{...props});case value===void 0:return React9__default.createElement(UndefinedNode,{...props});case Array.isArray(value):return React9__default.createElement(ArrayNode,{...props,value,callsById});case typeof value=="string":return React9__default.createElement(StringNode,{...props,value});case typeof value=="number":return React9__default.createElement(NumberNode,{...props,value});case typeof value=="boolean":return React9__default.createElement(BooleanNode,{...props,value});case Object.prototype.hasOwnProperty.call(value,"__date__"):return React9__default.createElement(DateNode,{...props,...value.__date__});case Object.prototype.hasOwnProperty.call(value,"__error__"):return React9__default.createElement(ErrorNode,{...props,...value.__error__});case Object.prototype.hasOwnProperty.call(value,"__regexp__"):return React9__default.createElement(RegExpNode,{...props,...value.__regexp__});case Object.prototype.hasOwnProperty.call(value,"__function__"):return React9__default.createElement(FunctionNode,{...props,...value.__function__});case Object.prototype.hasOwnProperty.call(value,"__symbol__"):return React9__default.createElement(SymbolNode,{...props,...value.__symbol__});case Object.prototype.hasOwnProperty.call(value,"__element__"):return React9__default.createElement(ElementNode,{...props,...value.__element__});case Object.prototype.hasOwnProperty.call(value,"__class__"):return React9__default.createElement(ClassNode,{...props,...value.__class__});case Object.prototype.hasOwnProperty.call(value,"__callId__"):return React9__default.createElement(MethodCall,{call:callsById.get(value.__callId__),callsById});case Object.prototype.toString.call(value)==="[object Object]":return React9__default.createElement(ObjectNode,{value,showInspector:showObjectInspector,callsById,...props});default:return React9__default.createElement(OtherNode,{value,...props})}},NullNode=props=>{let colors=useThemeColors();return React9__default.createElement("span",{style:{color:colors.nullish},...props},"null")},UndefinedNode=props=>{let colors=useThemeColors();return React9__default.createElement("span",{style:{color:colors.nullish},...props},"undefined")},StringNode=({value,...props})=>{let colors=useThemeColors();return React9__default.createElement("span",{style:{color:colors.string},...props},JSON.stringify(ellipsize(value,50)))},NumberNode=({value,...props})=>{let colors=useThemeColors();return React9__default.createElement("span",{style:{color:colors.number},...props},value)},BooleanNode=({value,...props})=>{let colors=useThemeColors();return React9__default.createElement("span",{style:{color:colors.boolean},...props},String(value))},ArrayNode=({value,nested=!1,callsById})=>{let colors=useThemeColors();if(nested)return React9__default.createElement("span",{style:{color:colors.base}},"[\u2026]");let nodes=value.slice(0,3).map((v,index)=>React9__default.createElement(Node,{key:`${index}--${JSON.stringify(v)}`,value:v,nested:!0,callsById})),nodelist=interleave(nodes,React9__default.createElement("span",null,", "));return value.length<=3?React9__default.createElement("span",{style:{color:colors.base}},"[",nodelist,"]"):React9__default.createElement("span",{style:{color:colors.base}},"(",value.length,") [",nodelist,", \u2026]")},ObjectNode=({showInspector,value,callsById,nested=!1})=>{let isDarkMode=useTheme().base==="dark",colors=useThemeColors();if(showInspector)return React9__default.createElement(React9__default.Fragment,null,React9__default.createElement(ObjectInspector,{id:"interactions-object-inspector",data:value,includePrototypes:!1,colorScheme:isDarkMode?"dark":"light"}));if(nested)return React9__default.createElement("span",{style:{color:colors.base}},"{\u2026}");let nodelist=interleave(Object.entries(value).slice(0,2).map(([k,v])=>React9__default.createElement(Fragment,{key:k},React9__default.createElement("span",{style:{color:colors.objectkey}},k,": "),React9__default.createElement(Node,{value:v,callsById,nested:!0}))),React9__default.createElement("span",null,", "));return Object.keys(value).length<=2?React9__default.createElement("span",{style:{color:colors.base}},"{ ",nodelist," }"):React9__default.createElement("span",{style:{color:colors.base}},"(",Object.keys(value).length,") ","{ ",nodelist,", \u2026 }")},ClassNode=({name})=>{let colors=useThemeColors();return React9__default.createElement("span",{style:{color:colors.instance}},name)},FunctionNode=({name})=>{let colors=useThemeColors();return name?React9__default.createElement("span",{style:{color:colors.function}},name):React9__default.createElement("span",{style:{color:colors.nullish,fontStyle:"italic"}},"anonymous")},ElementNode=({prefix,localName,id,classNames=[],innerText})=>{let name=prefix?`${prefix}:${localName}`:localName,colors=useThemeColors();return React9__default.createElement("span",{style:{wordBreak:"keep-all"}},React9__default.createElement("span",{key:`${name}_lt`,style:{color:colors.muted}},"<"),React9__default.createElement("span",{key:`${name}_tag`,style:{color:colors.tag.name}},name),React9__default.createElement("span",{key:`${name}_suffix`,style:{color:colors.tag.suffix}},id?`#${id}`:classNames.reduce((acc,className)=>`${acc}.${className}`,"")),React9__default.createElement("span",{key:`${name}_gt`,style:{color:colors.muted}},">"),!id&&classNames.length===0&&innerText&&React9__default.createElement(React9__default.Fragment,null,React9__default.createElement("span",{key:`${name}_text`},innerText),React9__default.createElement("span",{key:`${name}_close_lt`,style:{color:colors.muted}},"<"),React9__default.createElement("span",{key:`${name}_close_tag`,style:{color:colors.tag.name}},"/",name),React9__default.createElement("span",{key:`${name}_close_gt`,style:{color:colors.muted}},">")))},DateNode=({value})=>{let string=value instanceof Date?value.toISOString():value,[date,time,ms]=string.split(/[T.Z]/),colors=useThemeColors();return React9__default.createElement("span",{style:{whiteSpace:"nowrap",color:colors.date}},date,React9__default.createElement("span",{style:{opacity:.7}},"T"),time==="00:00:00"?React9__default.createElement("span",{style:{opacity:.7}},time):time,ms==="000"?React9__default.createElement("span",{style:{opacity:.7}},".",ms):`.${ms}`,React9__default.createElement("span",{style:{opacity:.7}},"Z"))},ErrorNode=({name,message})=>{let colors=useThemeColors();return React9__default.createElement("span",{style:{color:colors.error.name}},name,message&&": ",message&&React9__default.createElement("span",{style:{color:colors.error.message},title:message.length>50?message:""},ellipsize(message,50)))},RegExpNode=({flags,source})=>{let colors=useThemeColors();return React9__default.createElement("span",{style:{whiteSpace:"nowrap",color:colors.regex.flags}},"/",React9__default.createElement("span",{style:{color:colors.regex.source}},source),"/",flags)},SymbolNode=({description})=>{let colors=useThemeColors();return React9__default.createElement("span",{style:{whiteSpace:"nowrap",color:colors.instance}},"Symbol(",description&&React9__default.createElement("span",{style:{color:colors.meta}},'"',description,'"'),")")},OtherNode=({value})=>{let colors=useThemeColors();return React9__default.createElement("span",{style:{color:colors.meta}},stringify(value))},StepNode=({label})=>{let colors=useThemeColors(),{typography:typography4}=useTheme();return React9__default.createElement("span",{style:{color:colors.base,fontFamily:typography4.fonts.base,fontSize:typography4.size.s2-1}},label)},MethodCall=({call,callsById})=>{if(!call)return null;if(call.method==="step"&&call.path.length===0)return React9__default.createElement(StepNode,{label:call.args[0]});let path=call.path?.flatMap((elem,index)=>{let callId=elem.__callId__;return [callId?React9__default.createElement(MethodCall,{key:`elem${index}`,call:callsById.get(callId),callsById}):React9__default.createElement("span",{key:`elem${index}`},elem),React9__default.createElement("wbr",{key:`wbr${index}`}),React9__default.createElement("span",{key:`dot${index}`},".")]}),args=call.args?.flatMap((arg,index,array)=>{let node=React9__default.createElement(Node,{key:`node${index}`,value:arg,callsById});return index<array.length-1?[node,React9__default.createElement("span",{key:`comma${index}`},",\xA0"),React9__default.createElement("wbr",{key:`wbr${index}`})]:[node]}),colors=useThemeColors();return React9__default.createElement(React9__default.Fragment,null,React9__default.createElement("span",{style:{color:colors.base}},path),React9__default.createElement("span",{style:{color:colors.method}},call.method),React9__default.createElement("span",{style:{color:colors.base}},"(",React9__default.createElement("wbr",null),args,React9__default.createElement("wbr",null),")"))};var getParams=(line,fromIndex=0)=>{for(let i=fromIndex,depth=1;i<line.length;i+=1)if(line[i]==="("?depth+=1:line[i]===")"&&(depth-=1),depth===0)return line.slice(fromIndex,i);return ""},parseValue=value=>{try{return value==="undefined"?void 0:JSON.parse(value)}catch{return value}},StyledExpected=styled.span(({theme})=>({color:theme.base==="light"?theme.color.positiveText:theme.color.positive})),StyledReceived=styled.span(({theme})=>({color:theme.base==="light"?theme.color.negativeText:theme.color.negative})),Received=({value,parsed})=>parsed?React9__default.createElement(Node,{showObjectInspector:!0,value,style:{color:"#D43900"}}):React9__default.createElement(StyledReceived,null,value),Expected=({value,parsed})=>parsed?typeof value=="string"&&value.startsWith("called with")?React9__default.createElement(React9__default.Fragment,null,value):React9__default.createElement(Node,{showObjectInspector:!0,value,style:{color:"#16B242"}}):React9__default.createElement(StyledExpected,null,value),MatcherResult=({message,style={}})=>{let filter=useAnsiToHtmlFilter(),lines=message.split(`
`);return React9__default.createElement("pre",{style:{margin:0,padding:"8px 10px 8px 36px",fontSize:typography.size.s1,...style}},lines.flatMap((line,index)=>{if(line.startsWith("expect(")){let received=getParams(line,7),remainderIndex=received&&7+received.length,matcher=received&&line.slice(remainderIndex).match(/\.(to|last|nth)[A-Z]\w+\(/);if(matcher){let expectedIndex=remainderIndex+matcher.index+matcher[0].length,expected=getParams(line,expectedIndex);if(expected)return ["expect(",React9__default.createElement(Received,{key:`received_${received}`,value:received}),line.slice(remainderIndex,expectedIndex),React9__default.createElement(Expected,{key:`expected_${expected}`,value:expected}),line.slice(expectedIndex+expected.length),React9__default.createElement("br",{key:`br${index}`})]}}if(line.match(/^\s*- /))return [React9__default.createElement(Expected,{key:line+index,value:line}),React9__default.createElement("br",{key:`br${index}`})];if(line.match(/^\s*\+ /)||line.match(/^Received: $/))return [React9__default.createElement(Received,{key:line+index,value:line}),React9__default.createElement("br",{key:`br${index}`})];let[,assertionLabel,assertionValue]=line.match(/^(Expected|Received): (.*)$/)||[];if(assertionLabel&&assertionValue)return assertionLabel==="Expected"?["Expected: ",React9__default.createElement(Expected,{key:line+index,value:parseValue(assertionValue),parsed:!0}),React9__default.createElement("br",{key:`br${index}`})]:["Received: ",React9__default.createElement(Received,{key:line+index,value:parseValue(assertionValue),parsed:!0}),React9__default.createElement("br",{key:`br${index}`})];let[,prefix,numberOfCalls]=line.match(/(Expected number|Received number|Number) of calls: (\d+)$/i)||[];if(prefix&&numberOfCalls)return [`${prefix} of calls: `,React9__default.createElement(Node,{key:line+index,value:Number(numberOfCalls)}),React9__default.createElement("br",{key:`br${index}`})];let[,receivedValue]=line.match(/^Received has value: (.+)$/)||[];return receivedValue?["Received has value: ",React9__default.createElement(Node,{key:line+index,value:parseValue(receivedValue)}),React9__default.createElement("br",{key:`br${index}`})]:[React9__default.createElement("span",{key:line+index,dangerouslySetInnerHTML:{__html:filter.toHtml(line)}}),React9__default.createElement("br",{key:`br${index}`})]}))};var WarningContainer=styled.div({width:14,height:14,display:"flex",alignItems:"center",justifyContent:"center"}),StatusIcon=({status})=>{let theme=useTheme();switch(status){case CallStates.DONE:return React9__default.createElement(CheckIcon,{color:theme.color.positive,"data-testid":"icon-done"});case CallStates.ERROR:return React9__default.createElement(StopAltIcon,{color:theme.color.negative,"data-testid":"icon-error"});case CallStates.ACTIVE:return React9__default.createElement(PlayIcon,{color:theme.color.secondary,"data-testid":"icon-active"});case CallStates.WAITING:return React9__default.createElement(WarningContainer,{"data-testid":"icon-waiting"},React9__default.createElement(CircleIcon,{color:transparentize(.5,"#CCCCCC"),size:6}));default:return null}};var MethodCallWrapper=styled.div({fontFamily:typography.fonts.mono,fontSize:typography.size.s1,overflowWrap:"break-word",inlineSize:"calc( 100% - 40px )"}),RowContainer=styled("div",{shouldForwardProp:prop=>!["call","pausedAt"].includes(prop.toString())})(({theme,call})=>({position:"relative",display:"flex",flexDirection:"column",borderBottom:`1px solid ${theme.appBorderColor}`,fontFamily:typography.fonts.base,fontSize:13,...call.status===CallStates.ERROR&&{backgroundColor:theme.base==="dark"?transparentize(.93,theme.color.negative):theme.background.warning},paddingLeft:(call.ancestors?.length??0)*20}),({theme,call,pausedAt})=>pausedAt===call.id&&{"&::before":{content:'""',position:"absolute",top:-5,zIndex:1,borderTop:"4.5px solid transparent",borderLeft:`7px solid ${theme.color.warning}`,borderBottom:"4.5px solid transparent"},"&::after":{content:'""',position:"absolute",top:-1,zIndex:1,width:"100%",borderTop:`1.5px solid ${theme.color.warning}`}}),RowHeader=styled.div(({theme,isInteractive})=>({display:"flex","&:hover":isInteractive?{}:{background:theme.background.hoverable}})),RowLabel=styled("button",{shouldForwardProp:prop=>!["call"].includes(prop.toString())})(({theme,disabled,call})=>({flex:1,display:"grid",background:"none",border:0,gridTemplateColumns:"15px 1fr",alignItems:"center",minHeight:40,margin:0,padding:"8px 15px",textAlign:"start",cursor:disabled||call.status===CallStates.ERROR?"default":"pointer","&:focus-visible":{outline:0,boxShadow:`inset 3px 0 0 0 ${call.status===CallStates.ERROR?theme.color.warning:theme.color.secondary}`,background:call.status===CallStates.ERROR?"transparent":theme.background.hoverable},"& > div":{opacity:call.status===CallStates.WAITING?.5:1}})),RowActions=styled.div({padding:6}),StyledIconButton=styled(IconButton)(({theme})=>({color:theme.textMutedColor,margin:"0 3px"})),Note=styled(TooltipNote)(({theme})=>({fontFamily:theme.typography.fonts.base})),RowMessage=styled("div")(({theme})=>({padding:"8px 10px 8px 36px",fontSize:typography.size.s1,color:theme.color.defaultText,pre:{margin:0,padding:0}})),Exception=({exception})=>{let filter=useAnsiToHtmlFilter();if(isJestError(exception))return React9.createElement(MatcherResult,{...exception});if(isChaiError(exception))return React9.createElement(RowMessage,null,React9.createElement(MatcherResult,{message:`${exception.message}${exception.diff?`

${exception.diff}`:""}`,style:{padding:0}}),React9.createElement("p",null,"See the full stack trace in the browser console."));let paragraphs=exception.message.split(`

`),more=paragraphs.length>1;return React9.createElement(RowMessage,null,React9.createElement("pre",{dangerouslySetInnerHTML:{__html:filter.toHtml(paragraphs[0])}}),more&&React9.createElement("p",null,"See the full stack trace in the browser console."))},Interaction=({call,callsById,controls,controlStates,childCallIds,isHidden,isCollapsed,toggleCollapsed,pausedAt})=>{let[isHovered,setIsHovered]=React9.useState(!1),isInteractive=!controlStates.goto||!call.interceptable||!!call.ancestors?.length;return isHidden?null:React9.createElement(RowContainer,{call,pausedAt},React9.createElement(RowHeader,{isInteractive},React9.createElement(RowLabel,{"aria-label":"Interaction step",call,onClick:()=>controls.goto(call.id),disabled:isInteractive,onMouseEnter:()=>controlStates.goto&&setIsHovered(!0),onMouseLeave:()=>controlStates.goto&&setIsHovered(!1)},React9.createElement(StatusIcon,{status:isHovered?CallStates.ACTIVE:call.status}),React9.createElement(MethodCallWrapper,{style:{marginLeft:6,marginBottom:1}},React9.createElement(MethodCall,{call,callsById}))),React9.createElement(RowActions,null,childCallIds?.length>0&&React9.createElement(WithTooltip,{hasChrome:!1,tooltip:React9.createElement(Note,{note:`${isCollapsed?"Show":"Hide"} interactions`})},React9.createElement(StyledIconButton,{onClick:toggleCollapsed},React9.createElement(ListUnorderedIcon,null))))),call.status===CallStates.ERROR&&call.exception?.callId===call.id&&React9.createElement(Exception,{exception:call.exception}))};var StyledBadge=styled.div(({theme,status})=>({padding:"4px 6px 4px 8px;",borderRadius:"4px",backgroundColor:{[CallStates.DONE]:theme.color.positive,[CallStates.ERROR]:theme.color.negative,[CallStates.ACTIVE]:theme.color.warning,[CallStates.WAITING]:theme.color.warning}[status],color:"white",fontFamily:typography.fonts.base,textTransform:"uppercase",fontSize:typography.size.s1,letterSpacing:3,fontWeight:typography.weight.bold,width:65,textAlign:"center"})),StatusBadge=({status})=>{let badgeText={[CallStates.DONE]:"Pass",[CallStates.ERROR]:"Fail",[CallStates.ACTIVE]:"Runs",[CallStates.WAITING]:"Runs"}[status];return React9__default.createElement(StyledBadge,{"aria-label":"Status of the test run",status},badgeText)};var SubnavWrapper=styled.div(({theme})=>({background:theme.background.app,borderBottom:`1px solid ${theme.appBorderColor}`,position:"sticky",top:0,zIndex:1})),StyledSubnav=styled.nav(({theme})=>({height:40,display:"flex",alignItems:"center",justifyContent:"space-between",paddingLeft:15})),StyledButton=styled(Button)(({theme})=>({borderRadius:4,padding:6,color:theme.textMutedColor,"&:not(:disabled)":{"&:hover,&:focus-visible":{color:theme.color.secondary}}})),Note2=styled(TooltipNote)(({theme})=>({fontFamily:theme.typography.fonts.base})),StyledIconButton2=styled(IconButton)(({theme})=>({color:theme.textMutedColor,margin:"0 3px"})),StyledSeparator=styled(Separator)({marginTop:0}),StyledLocation=styled(P)(({theme})=>({color:theme.textMutedColor,justifyContent:"flex-end",textAlign:"right",whiteSpace:"nowrap",marginTop:"auto",marginBottom:1,paddingRight:15,fontSize:13})),Group=styled.div({display:"flex",alignItems:"center"}),RewindButton=styled(StyledIconButton2)({marginLeft:9}),JumpToEndButton=styled(StyledButton)({marginLeft:9,marginRight:9,marginBottom:1,lineHeight:"12px"}),RerunButton=styled(StyledIconButton2)(({theme,animating,disabled})=>({opacity:disabled?.5:1,svg:{animation:animating&&`${theme.animation.rotate360} 200ms ease-out`}})),Subnav=({controls,controlStates,status,storyFileName,onScrollToEnd})=>{let buttonText=status===CallStates.ERROR?"Scroll to error":"Scroll to end";return React9__default.createElement(SubnavWrapper,null,React9__default.createElement(Bar,null,React9__default.createElement(StyledSubnav,null,React9__default.createElement(Group,null,React9__default.createElement(StatusBadge,{status}),React9__default.createElement(JumpToEndButton,{onClick:onScrollToEnd,disabled:!onScrollToEnd},buttonText),React9__default.createElement(StyledSeparator,null),React9__default.createElement(WithTooltip,{trigger:"hover",hasChrome:!1,tooltip:React9__default.createElement(Note2,{note:"Go to start"})},React9__default.createElement(RewindButton,{"aria-label":"Go to start",onClick:controls.start,disabled:!controlStates.start},React9__default.createElement(RewindIcon,null))),React9__default.createElement(WithTooltip,{trigger:"hover",hasChrome:!1,tooltip:React9__default.createElement(Note2,{note:"Go back"})},React9__default.createElement(StyledIconButton2,{"aria-label":"Go back",onClick:controls.back,disabled:!controlStates.back},React9__default.createElement(PlayBackIcon,null))),React9__default.createElement(WithTooltip,{trigger:"hover",hasChrome:!1,tooltip:React9__default.createElement(Note2,{note:"Go forward"})},React9__default.createElement(StyledIconButton2,{"aria-label":"Go forward",onClick:controls.next,disabled:!controlStates.next},React9__default.createElement(PlayNextIcon,null))),React9__default.createElement(WithTooltip,{trigger:"hover",hasChrome:!1,tooltip:React9__default.createElement(Note2,{note:"Go to end"})},React9__default.createElement(StyledIconButton2,{"aria-label":"Go to end",onClick:controls.end,disabled:!controlStates.end},React9__default.createElement(FastForwardIcon,null))),React9__default.createElement(WithTooltip,{trigger:"hover",hasChrome:!1,tooltip:React9__default.createElement(Note2,{note:"Rerun"})},React9__default.createElement(RerunButton,{"aria-label":"Rerun",onClick:controls.rerun},React9__default.createElement(SyncIcon,null)))),storyFileName&&React9__default.createElement(Group,null,React9__default.createElement(StyledLocation,null,storyFileName)))))};var Container=styled.div(({theme})=>({height:"100%",background:theme.background.content})),CaughtException=styled.div(({theme})=>({borderBottom:`1px solid ${theme.appBorderColor}`,backgroundColor:theme.base==="dark"?transparentize(.93,theme.color.negative):theme.background.warning,padding:15,fontSize:theme.typography.size.s2-1,lineHeight:"19px"})),CaughtExceptionCode=styled.code(({theme})=>({margin:"0 1px",padding:3,fontSize:theme.typography.size.s1-1,lineHeight:1,verticalAlign:"top",background:"rgba(0, 0, 0, 0.05)",border:`1px solid ${theme.appBorderColor}`,borderRadius:3})),CaughtExceptionTitle=styled.div({paddingBottom:4,fontWeight:"bold"}),CaughtExceptionDescription=styled.p({margin:0,padding:"0 0 20px"}),CaughtExceptionStack=styled.pre(({theme})=>({margin:0,padding:0,"&:not(:last-child)":{paddingBottom:16},fontSize:theme.typography.size.s1-1})),InteractionsPanel=React9.memo(function({calls,controls,controlStates,interactions,fileName,hasException,caughtException,unhandledErrors,isPlaying,pausedAt,onScrollToEnd,endRef}){let filter=useAnsiToHtmlFilter();return React9.createElement(Container,null,(interactions.length>0||hasException)&&React9.createElement(Subnav,{controls,controlStates,status:isPlaying?CallStates.ACTIVE:hasException?CallStates.ERROR:CallStates.DONE,storyFileName:fileName,onScrollToEnd}),React9.createElement("div",{"aria-label":"Interactions list"},interactions.map(call=>React9.createElement(Interaction,{key:call.id,call,callsById:calls,controls,controlStates,childCallIds:call.childCallIds,isHidden:call.isHidden,isCollapsed:call.isCollapsed,toggleCollapsed:call.toggleCollapsed,pausedAt}))),caughtException&&!isTestAssertionError(caughtException)&&React9.createElement(CaughtException,null,React9.createElement(CaughtExceptionTitle,null,"Caught exception in ",React9.createElement(CaughtExceptionCode,null,"play")," function"),React9.createElement(CaughtExceptionStack,{"data-chromatic":"ignore",dangerouslySetInnerHTML:{__html:filter.toHtml(printSerializedError(caughtException))}})),unhandledErrors&&React9.createElement(CaughtException,null,React9.createElement(CaughtExceptionTitle,null,"Unhandled Errors"),React9.createElement(CaughtExceptionDescription,null,"Found ",unhandledErrors.length," unhandled error",unhandledErrors.length>1?"s":""," ","while running the play function. This might cause false positive assertions. Resolve unhandled errors or ignore unhandled errors with setting the",React9.createElement(CaughtExceptionCode,null,"test.dangerouslyIgnoreUnhandledErrors")," ","parameter to ",React9.createElement(CaughtExceptionCode,null,"true"),"."),unhandledErrors.map((error,i)=>React9.createElement(CaughtExceptionStack,{key:i,"data-chromatic":"ignore"},printSerializedError(error)))),React9.createElement("div",{ref:endRef}),!isPlaying&&!caughtException&&interactions.length===0&&React9.createElement(Empty,null))});function printSerializedError(error){return error.stack||`${error.name}: ${error.message}`}var INITIAL_CONTROL_STATES={start:!1,back:!1,goto:!1,next:!1,end:!1},getInteractions=({log,calls,collapsed,setCollapsed})=>{let callsById=new Map,childCallMap=new Map;return log.map(({callId,ancestors,status})=>{let isHidden=!1;return ancestors.forEach(ancestor=>{collapsed.has(ancestor)&&(isHidden=!0),childCallMap.set(ancestor,(childCallMap.get(ancestor)||[]).concat(callId));}),{...calls.get(callId),status,isHidden}}).map(call=>{let status=call.status===CallStates.ERROR&&callsById.get(call.ancestors.slice(-1)[0])?.status===CallStates.ACTIVE?CallStates.ACTIVE:call.status;return callsById.set(call.id,{...call,status}),{...call,status,childCallIds:childCallMap.get(call.id),isCollapsed:collapsed.has(call.id),toggleCollapsed:()=>setCollapsed(ids=>(ids.has(call.id)?ids.delete(call.id):ids.add(call.id),new Set(ids)))}})},Panel=memo(function({storyId}){let[addonState,set]=useAddonState(ADDON_ID,{controlStates:INITIAL_CONTROL_STATES,isErrored:!1,pausedAt:void 0,interactions:[],isPlaying:!1,hasException:!1,caughtException:void 0,interactionsCount:0,unhandledErrors:void 0}),[scrollTarget,setScrollTarget]=useState(void 0),[collapsed,setCollapsed]=useState(new Set),{controlStates=INITIAL_CONTROL_STATES,isErrored=!1,pausedAt=void 0,interactions=[],isPlaying=!1,caughtException=void 0,unhandledErrors=void 0}=addonState,log=useRef([]),calls=useRef(new Map),setCall=({status,...call})=>calls.current.set(call.id,call),endRef=useRef();useEffect(()=>{let observer;return global.IntersectionObserver&&(observer=new global.IntersectionObserver(([end])=>setScrollTarget(end.isIntersecting?void 0:end.target),{root:global.document.querySelector("#panel-tab-content")}),endRef.current&&observer.observe(endRef.current)),()=>observer?.disconnect()},[]);let emit=useChannel({[EVENTS.CALL]:setCall,[EVENTS.SYNC]:payload=>{set(s=>{let list=getInteractions({log:payload.logItems,calls:calls.current,collapsed,setCollapsed});return {...s,controlStates:payload.controlStates,pausedAt:payload.pausedAt,interactions:list,interactionsCount:list.filter(({method})=>method!=="step").length}}),log.current=payload.logItems;},[STORY_RENDER_PHASE_CHANGED]:event=>{if(event.newPhase==="preparing"){set({controlStates:INITIAL_CONTROL_STATES,isErrored:!1,pausedAt:void 0,interactions:[],isPlaying:!1,hasException:!1,caughtException:void 0,interactionsCount:0,unhandledErrors:void 0});return}set(s=>({...s,isPlaying:event.newPhase==="playing",pausedAt:void 0,...event.newPhase==="rendering"?{isErrored:!1,caughtException:void 0}:{}}));},[STORY_THREW_EXCEPTION]:()=>{set(s=>({...s,isErrored:!0,hasException:!0}));},[PLAY_FUNCTION_THREW_EXCEPTION]:e=>{set(s=>({...s,caughtException:e,hasException:!0}));},[UNHANDLED_ERRORS_WHILE_PLAYING]:e=>{set(s=>({...s,unhandledErrors:e,hasException:!0}));}},[collapsed]);useEffect(()=>{set(s=>{let list=getInteractions({log:log.current,calls:calls.current,collapsed,setCollapsed});return {...s,interactions:list,interactionsCount:list.filter(({method})=>method!=="step").length}});},[collapsed]);let controls=useMemo(()=>({start:()=>emit(EVENTS.START,{storyId}),back:()=>emit(EVENTS.BACK,{storyId}),goto:callId=>emit(EVENTS.GOTO,{storyId,callId}),next:()=>emit(EVENTS.NEXT,{storyId}),end:()=>emit(EVENTS.END,{storyId}),rerun:()=>{emit(FORCE_REMOUNT,{storyId});}}),[storyId]),storyFilePath=useParameter("fileName",""),[fileName]=storyFilePath.toString().split("/").slice(-1),scrollToTarget=()=>scrollTarget?.scrollIntoView({behavior:"smooth",block:"end"}),hasException=!!caughtException||!!unhandledErrors||interactions.some(v=>v.status===CallStates.ERROR);return isErrored?React9__default.createElement(Fragment,{key:"interactions"}):React9__default.createElement(Fragment,{key:"interactions"},React9__default.createElement(InteractionsPanel,{calls:calls.current,controls,controlStates,interactions,fileName,hasException,caughtException,unhandledErrors,isPlaying,pausedAt,endRef,onScrollToEnd:scrollTarget&&scrollToTarget}))});function Title(){let[addonState={}]=useAddonState(ADDON_ID),{hasException,interactionsCount}=addonState;return React9__default.createElement("div",null,React9__default.createElement(Spaced,{col:1},React9__default.createElement("span",{style:{display:"inline-block",verticalAlign:"middle"}},"Interactions"),interactionsCount&&!hasException?React9__default.createElement(Badge,{status:"neutral"},interactionsCount):null,hasException?React9__default.createElement(Badge,{status:"negative"},interactionsCount):null))}addons.register(ADDON_ID,api=>{addons.add(PANEL_ID,{type:types.PANEL,title:Title,match:({viewMode})=>viewMode==="story",render:({active})=>{let newLocal=useCallback(({state})=>({storyId:state.storyId}),[]);return React9__default.createElement(AddonPanel,{active},React9__default.createElement(Consumer,{filter:newLocal},({storyId})=>React9__default.createElement(Panel,{storyId})))}});});
