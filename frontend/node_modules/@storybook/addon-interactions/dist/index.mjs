import { definePreview } from 'storybook/internal/preview-api';
import { instrument } from '@storybook/instrumenter';
import '@storybook/test';

var __defProp=Object.defineProperty;var __export=(target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0});};var preview_exports={};__export(preview_exports,{parameters:()=>parameters,runStep:()=>runStep});var runStep=instrument({step:(label,play,context)=>play(context)},{intercept:!0}).step,parameters={throwPlayFunctionExceptions:!1};var index_default=()=>definePreview(preview_exports);

export { index_default as default };
