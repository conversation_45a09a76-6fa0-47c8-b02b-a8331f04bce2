{"name": "@storybook/addon-controls", "version": "8.6.14", "description": "Interact with component inputs dynamically in the Storybook UI", "keywords": ["addon", "storybook", "knobs", "controls", "properties", "essentials", "data-state"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/controls", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/controls"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./manager": "./dist/manager.js", "./register": "./dist/manager.js", "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/addon-bundle.ts"}, "dependencies": {"@storybook/global": "^5.0.0", "dequal": "^2.0.2", "ts-dedent": "^2.0.0"}, "devDependencies": {"@storybook/blocks": "8.6.14", "@storybook/icons": "^1.2.12", "react": "^18.2.0", "react-dom": "^18.2.0"}, "peerDependencies": {"storybook": "^8.6.14"}, "publishConfig": {"access": "public"}, "bundler": {"exportEntries": ["./src/index.ts"], "managerEntries": ["./src/manager.tsx"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae16", "storybook": {"displayName": "Controls", "icon": "https://user-images.githubusercontent.com/263385/101991669-479cc600-3c7c-11eb-93d9-38b67e8371f2.png", "supportedFrameworks": ["react", "vue", "angular", "web-components", "ember"]}}