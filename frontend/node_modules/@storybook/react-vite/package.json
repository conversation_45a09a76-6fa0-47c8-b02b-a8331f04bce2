{"name": "@storybook/react-vite", "version": "8.6.14", "description": "Storybook for React and Vite: Develop React components in isolation with Hot Reloading.", "keywords": ["storybook"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/frameworks/react-vite", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/frameworks/react-vite"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./preset": {"types": "./dist/preset.d.ts", "require": "./dist/preset.js"}, "./node": {"types": "./dist/node/index.d.ts", "node": "./dist/node/index.js", "import": "./dist/node/index.mjs", "require": "./dist/node/index.js"}, "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["dist/index.d.ts"], "preset": ["dist/preset.d.ts"], "node": ["dist/node/index.d.ts"]}}, "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/bundle.ts"}, "dependencies": {"@joshwooding/vite-plugin-react-docgen-typescript": "0.5.0", "@rollup/pluginutils": "^5.0.2", "@storybook/builder-vite": "8.6.14", "@storybook/react": "8.6.14", "find-up": "^5.0.0", "magic-string": "^0.30.0", "react-docgen": "^7.0.0", "resolve": "^1.22.8", "tsconfig-paths": "^4.2.0"}, "devDependencies": {"@types/node": "^22.0.0", "typescript": "^5.7.3", "vite": "^4.0.0"}, "peerDependencies": {"@storybook/test": "8.6.14", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0-beta", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0-beta", "storybook": "^8.6.14", "vite": "^4.0.0 || ^5.0.0 || ^6.0.0"}, "peerDependenciesMeta": {"@storybook/test": {"optional": true}}, "engines": {"node": ">=18.0.0"}, "publishConfig": {"access": "public"}, "bundler": {"entries": ["./src/index.ts", "./src/preset.ts", "./src/node/index.ts"], "platform": "node"}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae16"}