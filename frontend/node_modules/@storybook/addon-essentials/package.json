{"name": "@storybook/addon-essentials", "version": "8.6.14", "description": "Curated addons to bring out the best of Storybook", "keywords": ["addon", "essentials", "storybook"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/essentials", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/essentials"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./entry-preview": {"types": "./dist/preview.d.ts", "import": "./dist/preview.mjs", "require": "./dist/preview.js"}, "./actions/preview": {"types": "./dist/actions/preview.d.ts", "import": "./dist/actions/preview.mjs", "require": "./dist/actions/preview.js"}, "./actions/manager": "./dist/actions/manager.js", "./backgrounds/preview": {"types": "./dist/backgrounds/preview.d.ts", "import": "./dist/backgrounds/preview.mjs", "require": "./dist/backgrounds/preview.js"}, "./backgrounds/manager": "./dist/backgrounds/manager.js", "./controls/manager": "./dist/controls/manager.js", "./docs/manager": "./dist/docs/manager.js", "./docs/preview": {"types": "./dist/docs/preview.d.ts", "import": "./dist/docs/preview.mjs", "require": "./dist/docs/preview.js"}, "./docs/preset": "./dist/docs/preset.js", "./docs/mdx-react-shim": "./dist/docs/mdx-react-shim.js", "./highlight/preview": {"types": "./dist/highlight/preview.d.ts", "import": "./dist/highlight/preview.mjs", "require": "./dist/highlight/preview.js"}, "./measure/preview": {"types": "./dist/measure/preview.d.ts", "import": "./dist/measure/preview.mjs", "require": "./dist/measure/preview.js"}, "./measure/manager": "./dist/measure/manager.js", "./outline/preview": {"types": "./dist/outline/preview.d.ts", "import": "./dist/outline/preview.mjs", "require": "./dist/outline/preview.js"}, "./outline/manager": "./dist/outline/manager.js", "./toolbars/manager": "./dist/toolbars/manager.js", "./viewport/manager": "./dist/viewport/manager.js", "./viewport/preview": {"types": "./dist/viewport/preview.d.ts", "import": "./dist/viewport/preview.mjs", "require": "./dist/viewport/preview.js"}, "./preset": "./dist/preset.js", "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["dist/index.d.ts"], "entry-preview": ["dist/preview.d.ts"]}}, "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/addon-bundle.ts"}, "dependencies": {"@storybook/addon-actions": "8.6.14", "@storybook/addon-backgrounds": "8.6.14", "@storybook/addon-controls": "8.6.14", "@storybook/addon-docs": "8.6.14", "@storybook/addon-highlight": "8.6.14", "@storybook/addon-measure": "8.6.14", "@storybook/addon-outline": "8.6.14", "@storybook/addon-toolbars": "8.6.14", "@storybook/addon-viewport": "8.6.14", "ts-dedent": "^2.0.0"}, "devDependencies": {"typescript": "^5.7.3"}, "peerDependencies": {"storybook": "^8.6.14"}, "publishConfig": {"access": "public"}, "bundler": {"nodeEntries": ["./src/preset.ts", "./src/docs/preset.ts", "./src/docs/mdx-react-shim.ts"], "exportEntries": ["./src/index.ts"], "entries": ["./src/docs/manager.ts"], "managerEntries": ["./src/actions/manager.ts", "./src/backgrounds/manager.ts", "./src/controls/manager.ts", "./src/docs/manager.ts", "./src/measure/manager.ts", "./src/outline/manager.ts", "./src/toolbars/manager.ts", "./src/viewport/manager.ts"], "previewEntries": ["./src/preview.ts", "./src/actions/preview.ts", "./src/backgrounds/preview.ts", "./src/docs/preview.ts", "./src/highlight/preview.ts", "./src/measure/preview.ts", "./src/outline/preview.ts", "./src/viewport/preview.ts"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae16"}