declare const globalsNameReferenceMap: {
    readonly react: "__REACT__";
    readonly 'react-dom': "__REACT_DOM__";
    readonly 'react-dom/client': "__REACT_DOM_CLIENT__";
    readonly '@storybook/icons': "__STORYBOOK_ICONS__";
    readonly 'storybook/internal/manager-api': "__STORYBOOK_API__";
    readonly '@storybook/manager-api': "__STORYBOOK_API__";
    readonly '@storybook/core/manager-api': "__STORYBOOK_API__";
    readonly 'storybook/internal/components': "__STORYBOOK_COMPONENTS__";
    readonly '@storybook/components': "__STORYBOOK_COMPONENTS__";
    readonly '@storybook/core/components': "__STORYBOOK_COMPONENTS__";
    readonly 'storybook/internal/channels': "__STORYBOOK_CHANNELS__";
    readonly '@storybook/channels': "__STORYBOOK_CHANNELS__";
    readonly '@storybook/core/channels': "__STORYBOOK_CHANNELS__";
    readonly 'storybook/internal/core-errors': "__STORYBOOK_CORE_EVENTS__";
    readonly '@storybook/core-events': "__STORYBOOK_CORE_EVENTS__";
    readonly '@storybook/core/core-events': "__STORYBOOK_CORE_EVENTS__";
    readonly 'storybook/internal/manager-errors': "__STORYBOOK_CORE_EVENTS_MANAGER_ERRORS__";
    readonly '@storybook/core-events/manager-errors': "__STORYBOOK_CORE_EVENTS_MANAGER_ERRORS__";
    readonly '@storybook/core/manager-errors': "__STORYBOOK_CORE_EVENTS_MANAGER_ERRORS__";
    readonly 'storybook/internal/router': "__STORYBOOK_ROUTER__";
    readonly '@storybook/router': "__STORYBOOK_ROUTER__";
    readonly '@storybook/core/router': "__STORYBOOK_ROUTER__";
    readonly 'storybook/internal/theming': "__STORYBOOK_THEMING__";
    readonly '@storybook/theming': "__STORYBOOK_THEMING__";
    readonly '@storybook/core/theming': "__STORYBOOK_THEMING__";
    readonly 'storybook/internal/theming/create': "__STORYBOOK_THEMING_CREATE__";
    readonly '@storybook/theming/create': "__STORYBOOK_THEMING_CREATE__";
    readonly '@storybook/core/theming/create': "__STORYBOOK_THEMING_CREATE__";
    readonly 'storybook/internal/client-logger': "__STORYBOOK_CLIENT_LOGGER__";
    readonly '@storybook/client-logger': "__STORYBOOK_CLIENT_LOGGER__";
    readonly '@storybook/core/client-logger': "__STORYBOOK_CLIENT_LOGGER__";
    readonly 'storybook/internal/types': "__STORYBOOK_TYPES__";
    readonly '@storybook/types': "__STORYBOOK_TYPES__";
    readonly '@storybook/core/types': "__STORYBOOK_TYPES__";
};
declare const globalPackages: Array<keyof typeof globalsNameReferenceMap>;

export { globalPackages, globalsNameReferenceMap };
