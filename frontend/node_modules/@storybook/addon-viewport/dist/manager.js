import * as React2 from 'react';
import React2__default, { Fragment, useCallback, memo, useState, useEffect, useRef } from 'react';
import { useGlobals, useParameter, useStorybookApi, addons, types } from 'storybook/internal/manager-api';
import { Icon<PERSON>utton, WithTooltip, TooltipLinkList } from 'storybook/internal/components';
import { styled, Global } from 'storybook/internal/theming';
import { BrowserIcon, MobileIcon, TabletIcon, RefreshIcon, GrowIcon, TransferIcon } from '@storybook/icons';
import memoize from 'memoizerific';

var ADDON_ID="storybook/viewport",PARAM_KEY="viewport";var MINIMAL_VIEWPORTS={mobile1:{name:"Small mobile",styles:{height:"568px",width:"320px"},type:"mobile"},mobile2:{name:"Large mobile",styles:{height:"896px",width:"414px"},type:"mobile"},tablet:{name:"Tablet",styles:{height:"1112px",width:"834px"},type:"tablet"}};var responsiveViewport={name:"Reset viewport",styles:{height:"100%",width:"100%"},type:"desktop"};var modern={[PARAM_KEY]:{value:void 0,isRotated:!1}},legacy={viewport:"reset",viewportRotated:!1},initialGlobals=globalThis.FEATURES?.viewportStoryGlobals?modern:legacy;var getCurrentViewportIndex=(viewportsKeys,current)=>viewportsKeys.indexOf(current),getNextViewport=(viewportsKeys,current)=>{let currentViewportIndex=getCurrentViewportIndex(viewportsKeys,current);return currentViewportIndex===viewportsKeys.length-1?viewportsKeys[0]:viewportsKeys[currentViewportIndex+1]},getPreviousViewport=(viewportsKeys,current)=>{let currentViewportIndex=getCurrentViewportIndex(viewportsKeys,current);return currentViewportIndex<1?viewportsKeys[viewportsKeys.length-1]:viewportsKeys[currentViewportIndex-1]},registerShortcuts=async(api,viewport,updateGlobals,viewportsKeys)=>{await api.setAddonShortcut(ADDON_ID,{label:"Previous viewport",defaultShortcut:["alt","shift","V"],actionName:"previous",action:()=>{updateGlobals({viewport:getPreviousViewport(viewportsKeys,viewport)});}}),await api.setAddonShortcut(ADDON_ID,{label:"Next viewport",defaultShortcut:["alt","V"],actionName:"next",action:()=>{updateGlobals({viewport:getNextViewport(viewportsKeys,viewport)});}}),await api.setAddonShortcut(ADDON_ID,{label:"Reset viewport",defaultShortcut:["alt","control","V"],actionName:"reset",action:()=>{updateGlobals(initialGlobals);}});};var ActiveViewportSize=styled.div({display:"inline-flex",alignItems:"center"}),ActiveViewportLabel=styled.div(({theme})=>({display:"inline-block",textDecoration:"none",padding:10,fontWeight:theme.typography.weight.bold,fontSize:theme.typography.size.s2-1,lineHeight:"1",height:40,border:"none",borderTop:"3px solid transparent",borderBottom:"3px solid transparent",background:"transparent"})),IconButtonWithLabel=styled(IconButton)(()=>({display:"inline-flex",alignItems:"center"})),IconButtonLabel=styled.div(({theme})=>({fontSize:theme.typography.size.s2-1,marginLeft:10})),iconsMap={desktop:React2__default.createElement(BrowserIcon,null),mobile:React2__default.createElement(MobileIcon,null),tablet:React2__default.createElement(TabletIcon,null),other:React2__default.createElement(Fragment,null)};var ViewportTool=({api})=>{let config=useParameter(PARAM_KEY),[globals,updateGlobals,storyGlobals]=useGlobals(),[isTooltipVisible,setIsTooltipVisible]=useState(!1),{options=MINIMAL_VIEWPORTS,disable}=config||{},data=globals?.[PARAM_KEY]||{},viewportName=data.value,isRotated=data.isRotated,item=options[viewportName]||responsiveViewport,isActive=isTooltipVisible||item!==responsiveViewport,isLocked=PARAM_KEY in storyGlobals,length2=Object.keys(options).length;if(useEffect(()=>{registerShortcuts(api,viewportName,updateGlobals,Object.keys(options));},[options,viewportName,updateGlobals,api]),item.styles===null||!options||length2<1)return null;if(typeof item.styles=="function")return console.warn("Addon Viewport no longer supports dynamic styles using a function, use css calc() instead"),null;let width=isRotated?item.styles.height:item.styles.width,height=isRotated?item.styles.width:item.styles.height;return disable?null:React2__default.createElement(Pure,{item,updateGlobals,viewportMap:options,viewportName,isRotated,setIsTooltipVisible,isLocked,isActive,width,height})},Pure=React2__default.memo(function(props){let{item,viewportMap,viewportName,isRotated,updateGlobals,setIsTooltipVisible,isLocked,isActive,width,height}=props,update=useCallback(input=>updateGlobals({[PARAM_KEY]:input}),[updateGlobals]);return React2__default.createElement(Fragment,null,React2__default.createElement(WithTooltip,{placement:"bottom",tooltip:({onHide})=>React2__default.createElement(TooltipLinkList,{links:[...length>0&&item!==responsiveViewport?[{id:"reset",title:"Reset viewport",icon:React2__default.createElement(RefreshIcon,null),onClick:()=>{update({value:void 0,isRotated:!1}),onHide();}}]:[],...Object.entries(viewportMap).map(([k,value])=>({id:k,title:value.name,icon:iconsMap[value.type],active:k===viewportName,onClick:()=>{update({value:k,isRotated:!1}),onHide();}}))].flat()}),closeOnOutsideClick:!0,onVisibleChange:setIsTooltipVisible},React2__default.createElement(IconButtonWithLabel,{disabled:isLocked,key:"viewport",title:"Change the size of the preview",active:isActive,onDoubleClick:()=>{update({value:void 0,isRotated:!1});}},React2__default.createElement(GrowIcon,null),item!==responsiveViewport?React2__default.createElement(IconButtonLabel,null,item.name," ",isRotated?"(L)":"(P)"):null)),React2__default.createElement(Global,{styles:{'iframe[data-is-storybook="true"]':{width,height}}}),item!==responsiveViewport?React2__default.createElement(ActiveViewportSize,null,React2__default.createElement(ActiveViewportLabel,{title:"Viewport width"},width.replace("px","")),isLocked?"/":React2__default.createElement(IconButton,{key:"viewport-rotate",title:"Rotate viewport",onClick:()=>{update({value:viewportName,isRotated:!isRotated});}},React2__default.createElement(TransferIcon,null)),React2__default.createElement(ActiveViewportLabel,{title:"Viewport height"},height.replace("px",""))):null)});var toList=memoize(50)(items=>[...baseViewports,...Object.entries(items).map(([id,{name,...rest}])=>({...rest,id,title:name}))]),responsiveViewport2={id:"reset",title:"Reset viewport",styles:null,type:"other"},baseViewports=[responsiveViewport2],toLinks=memoize(50)((list,active,updateGlobals,close)=>list.filter(i=>i.id!==responsiveViewport2.id||active.id!==i.id).map(i=>({...i,onClick:()=>{updateGlobals({viewport:i.id}),close();}}))),flip=({width,height,...styles})=>({...styles,height:width,width:height}),ActiveViewportSize2=styled.div({display:"inline-flex",alignItems:"center"}),ActiveViewportLabel2=styled.div(({theme})=>({display:"inline-block",textDecoration:"none",padding:10,fontWeight:theme.typography.weight.bold,fontSize:theme.typography.size.s2-1,lineHeight:"1",height:40,border:"none",borderTop:"3px solid transparent",borderBottom:"3px solid transparent",background:"transparent"})),IconButtonWithLabel2=styled(IconButton)(()=>({display:"inline-flex",alignItems:"center"})),IconButtonLabel2=styled.div(({theme})=>({fontSize:theme.typography.size.s2-1,marginLeft:10})),getStyles=(prevStyles,styles,isRotated)=>{if(styles===null)return;let result=typeof styles=="function"?styles(prevStyles):styles;return isRotated?flip(result):result},ViewportToolLegacy=memo(function(){let[globals,updateGlobals]=useGlobals(),{viewports=MINIMAL_VIEWPORTS,defaultOrientation,defaultViewport,disable}=useParameter(PARAM_KEY,{}),list=toList(viewports),api=useStorybookApi(),[isTooltipVisible,setIsTooltipVisible]=useState(!1);defaultViewport&&!list.find(i=>i.id===defaultViewport)&&console.warn(`Cannot find "defaultViewport" of "${defaultViewport}" in addon-viewport configs, please check the "viewports" setting in the configuration.`),useEffect(()=>{registerShortcuts(api,globals,updateGlobals,Object.keys(viewports));},[viewports,globals,globals.viewport,updateGlobals,api]),useEffect(()=>{let defaultRotated=defaultOrientation==="landscape";(defaultViewport&&globals.viewport!==defaultViewport||defaultOrientation&&globals.viewportRotated!==defaultRotated)&&updateGlobals({viewport:defaultViewport,viewportRotated:defaultRotated});},[defaultOrientation,defaultViewport,updateGlobals]);let item=list.find(i=>i.id===globals.viewport)||list.find(i=>i.id===defaultViewport)||list.find(i=>i.default)||responsiveViewport2,ref=useRef(),styles=getStyles(ref.current,item.styles,globals.viewportRotated);return useEffect(()=>{ref.current=styles;},[item]),disable||Object.entries(viewports).length===0?null:React2__default.createElement(Fragment,null,React2__default.createElement(WithTooltip,{placement:"top",tooltip:({onHide})=>React2__default.createElement(TooltipLinkList,{links:toLinks(list,item,updateGlobals,onHide)}),closeOnOutsideClick:!0,onVisibleChange:setIsTooltipVisible},React2__default.createElement(IconButtonWithLabel2,{key:"viewport",title:"Change the size of the preview",active:isTooltipVisible||!!styles,onDoubleClick:()=>{updateGlobals({viewport:responsiveViewport2.id});}},React2__default.createElement(GrowIcon,null),styles?React2__default.createElement(IconButtonLabel2,null,globals.viewportRotated?`${item.title} (L)`:`${item.title} (P)`):null)),styles?React2__default.createElement(ActiveViewportSize2,null,React2__default.createElement(Global,{styles:{'iframe[data-is-storybook="true"]':{...styles||{width:"100%",height:"100%"}}}}),React2__default.createElement(ActiveViewportLabel2,{title:"Viewport width"},styles.width.replace("px","")),React2__default.createElement(IconButton,{key:"viewport-rotate",title:"Rotate viewport",onClick:()=>{updateGlobals({viewportRotated:!globals.viewportRotated});}},React2__default.createElement(TransferIcon,null)),React2__default.createElement(ActiveViewportLabel2,{title:"Viewport height"},styles.height.replace("px",""))):null)});addons.register(ADDON_ID,api=>{addons.add(ADDON_ID,{title:"viewport / media-queries",type:types.TOOL,match:({viewMode,tabId})=>viewMode==="story"&&!tabId,render:()=>FEATURES?.viewportStoryGlobals?React2.createElement(ViewportTool,{api}):React2.createElement(ViewportToolLegacy,null)});});
