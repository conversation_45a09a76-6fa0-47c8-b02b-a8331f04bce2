import React14, { createContext, memo, Children, useContext, useCallback, useState, useLayoutEffect, forwardRef, Component, useRef, useEffect, Fragment, useMemo } from 'react';
import { ScrollArea, Spaced, Badge, ActionBar } from 'storybook/internal/components';
import { STORY_CHANGED } from 'storybook/internal/core-events';
import { addons, types, useAddonState, useChannel } from 'storybook/internal/manager-api';
import { dequal } from 'dequal';
import { styled, withTheme } from 'storybook/internal/theming';
import { opacify } from 'polished';

var PARAM_KEY="actions",ADDON_ID="storybook/actions",PANEL_ID=`${ADDON_ID}/panel`,EVENT_ID=`${ADDON_ID}/action-event`,CLEAR_ID=`${ADDON_ID}/action-clear`;var __create=Object.create,__defProp=Object.defineProperty,__getOwnPropDesc=Object.getOwnPropertyDescriptor,__getOwnPropNames=Object.getOwnPropertyNames,__getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty,__commonJS=(cb,mod)=>function(){return mod||(0, cb[__getOwnPropNames(cb)[0]])((mod={exports:{}}).exports,mod),mod.exports},__export=(target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0});},__copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to},__toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod)),require_is_object=__commonJS({"node_modules/is-object/index.js"(exports,module){module.exports=function(x){return typeof x=="object"&&x!==null};}}),require_is_window=__commonJS({"node_modules/is-window/index.js"(exports,module){module.exports=function(obj){if(obj==null)return !1;var o=Object(obj);return o===o.window};}}),require_is_dom=__commonJS({"node_modules/is-dom/index.js"(exports,module){var isObject=require_is_object(),isWindow=require_is_window();function isNode(val){return !isObject(val)||!isWindow(window)||typeof window.Node!="function"?!1:typeof val.nodeType=="number"&&typeof val.nodeName=="string"}module.exports=isNode;}}),themes_exports={};__export(themes_exports,{chromeDark:()=>theme,chromeLight:()=>theme2});var theme={BASE_FONT_FAMILY:"Menlo, monospace",BASE_FONT_SIZE:"11px",BASE_LINE_HEIGHT:1.2,BASE_BACKGROUND_COLOR:"rgb(36, 36, 36)",BASE_COLOR:"rgb(213, 213, 213)",OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES:10,OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES:5,OBJECT_NAME_COLOR:"rgb(227, 110, 236)",OBJECT_VALUE_NULL_COLOR:"rgb(127, 127, 127)",OBJECT_VALUE_UNDEFINED_COLOR:"rgb(127, 127, 127)",OBJECT_VALUE_REGEXP_COLOR:"rgb(233, 63, 59)",OBJECT_VALUE_STRING_COLOR:"rgb(233, 63, 59)",OBJECT_VALUE_SYMBOL_COLOR:"rgb(233, 63, 59)",OBJECT_VALUE_NUMBER_COLOR:"hsl(252, 100%, 75%)",OBJECT_VALUE_BOOLEAN_COLOR:"hsl(252, 100%, 75%)",OBJECT_VALUE_FUNCTION_PREFIX_COLOR:"rgb(85, 106, 242)",HTML_TAG_COLOR:"rgb(93, 176, 215)",HTML_TAGNAME_COLOR:"rgb(93, 176, 215)",HTML_TAGNAME_TEXT_TRANSFORM:"lowercase",HTML_ATTRIBUTE_NAME_COLOR:"rgb(155, 187, 220)",HTML_ATTRIBUTE_VALUE_COLOR:"rgb(242, 151, 102)",HTML_COMMENT_COLOR:"rgb(137, 137, 137)",HTML_DOCTYPE_COLOR:"rgb(192, 192, 192)",ARROW_COLOR:"rgb(145, 145, 145)",ARROW_MARGIN_RIGHT:3,ARROW_FONT_SIZE:12,ARROW_ANIMATION_DURATION:"0",TREENODE_FONT_FAMILY:"Menlo, monospace",TREENODE_FONT_SIZE:"11px",TREENODE_LINE_HEIGHT:1.2,TREENODE_PADDING_LEFT:12,TABLE_BORDER_COLOR:"rgb(85, 85, 85)",TABLE_TH_BACKGROUND_COLOR:"rgb(44, 44, 44)",TABLE_TH_HOVER_COLOR:"rgb(48, 48, 48)",TABLE_SORT_ICON_COLOR:"black",TABLE_DATA_BACKGROUND_IMAGE:"linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 0) 50%, rgba(51, 139, 255, 0.0980392) 50%, rgba(51, 139, 255, 0.0980392))",TABLE_DATA_BACKGROUND_SIZE:"128px 32px"},theme2={BASE_FONT_FAMILY:"Menlo, monospace",BASE_FONT_SIZE:"11px",BASE_LINE_HEIGHT:1.2,BASE_BACKGROUND_COLOR:"white",BASE_COLOR:"black",OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES:10,OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES:5,OBJECT_NAME_COLOR:"rgb(136, 19, 145)",OBJECT_VALUE_NULL_COLOR:"rgb(128, 128, 128)",OBJECT_VALUE_UNDEFINED_COLOR:"rgb(128, 128, 128)",OBJECT_VALUE_REGEXP_COLOR:"rgb(196, 26, 22)",OBJECT_VALUE_STRING_COLOR:"rgb(196, 26, 22)",OBJECT_VALUE_SYMBOL_COLOR:"rgb(196, 26, 22)",OBJECT_VALUE_NUMBER_COLOR:"rgb(28, 0, 207)",OBJECT_VALUE_BOOLEAN_COLOR:"rgb(28, 0, 207)",OBJECT_VALUE_FUNCTION_PREFIX_COLOR:"rgb(13, 34, 170)",HTML_TAG_COLOR:"rgb(168, 148, 166)",HTML_TAGNAME_COLOR:"rgb(136, 18, 128)",HTML_TAGNAME_TEXT_TRANSFORM:"lowercase",HTML_ATTRIBUTE_NAME_COLOR:"rgb(153, 69, 0)",HTML_ATTRIBUTE_VALUE_COLOR:"rgb(26, 26, 166)",HTML_COMMENT_COLOR:"rgb(35, 110, 37)",HTML_DOCTYPE_COLOR:"rgb(192, 192, 192)",ARROW_COLOR:"#6e6e6e",ARROW_MARGIN_RIGHT:3,ARROW_FONT_SIZE:12,ARROW_ANIMATION_DURATION:"0",TREENODE_FONT_FAMILY:"Menlo, monospace",TREENODE_FONT_SIZE:"11px",TREENODE_LINE_HEIGHT:1.2,TREENODE_PADDING_LEFT:12,TABLE_BORDER_COLOR:"#aaa",TABLE_TH_BACKGROUND_COLOR:"#eee",TABLE_TH_HOVER_COLOR:"hsla(0, 0%, 90%, 1)",TABLE_SORT_ICON_COLOR:"#6e6e6e",TABLE_DATA_BACKGROUND_IMAGE:"linear-gradient(to bottom, white, white 50%, rgb(234, 243, 255) 50%, rgb(234, 243, 255))",TABLE_DATA_BACKGROUND_SIZE:"128px 32px"},ExpandedPathsContext=createContext([{},()=>{}]),unselectable={WebkitTouchCallout:"none",WebkitUserSelect:"none",KhtmlUserSelect:"none",MozUserSelect:"none",msUserSelect:"none",OUserSelect:"none",userSelect:"none"},createTheme=theme3=>({DOMNodePreview:{htmlOpenTag:{base:{color:theme3.HTML_TAG_COLOR},tagName:{color:theme3.HTML_TAGNAME_COLOR,textTransform:theme3.HTML_TAGNAME_TEXT_TRANSFORM},htmlAttributeName:{color:theme3.HTML_ATTRIBUTE_NAME_COLOR},htmlAttributeValue:{color:theme3.HTML_ATTRIBUTE_VALUE_COLOR}},htmlCloseTag:{base:{color:theme3.HTML_TAG_COLOR},offsetLeft:{marginLeft:-theme3.TREENODE_PADDING_LEFT},tagName:{color:theme3.HTML_TAGNAME_COLOR,textTransform:theme3.HTML_TAGNAME_TEXT_TRANSFORM}},htmlComment:{color:theme3.HTML_COMMENT_COLOR},htmlDoctype:{color:theme3.HTML_DOCTYPE_COLOR}},ObjectPreview:{objectDescription:{fontStyle:"italic"},preview:{fontStyle:"italic"},arrayMaxProperties:theme3.OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES,objectMaxProperties:theme3.OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES},ObjectName:{base:{color:theme3.OBJECT_NAME_COLOR},dimmed:{opacity:.6}},ObjectValue:{objectValueNull:{color:theme3.OBJECT_VALUE_NULL_COLOR},objectValueUndefined:{color:theme3.OBJECT_VALUE_UNDEFINED_COLOR},objectValueRegExp:{color:theme3.OBJECT_VALUE_REGEXP_COLOR},objectValueString:{color:theme3.OBJECT_VALUE_STRING_COLOR},objectValueSymbol:{color:theme3.OBJECT_VALUE_SYMBOL_COLOR},objectValueNumber:{color:theme3.OBJECT_VALUE_NUMBER_COLOR},objectValueBoolean:{color:theme3.OBJECT_VALUE_BOOLEAN_COLOR},objectValueFunctionPrefix:{color:theme3.OBJECT_VALUE_FUNCTION_PREFIX_COLOR,fontStyle:"italic"},objectValueFunctionName:{fontStyle:"italic"}},TreeView:{treeViewOutline:{padding:0,margin:0,listStyleType:"none"}},TreeNode:{treeNodeBase:{color:theme3.BASE_COLOR,backgroundColor:theme3.BASE_BACKGROUND_COLOR,lineHeight:theme3.TREENODE_LINE_HEIGHT,cursor:"default",boxSizing:"border-box",listStyle:"none",fontFamily:theme3.TREENODE_FONT_FAMILY,fontSize:theme3.TREENODE_FONT_SIZE},treeNodePreviewContainer:{},treeNodePlaceholder:{whiteSpace:"pre",fontSize:theme3.ARROW_FONT_SIZE,marginRight:theme3.ARROW_MARGIN_RIGHT,...unselectable},treeNodeArrow:{base:{color:theme3.ARROW_COLOR,display:"inline-block",fontSize:theme3.ARROW_FONT_SIZE,marginRight:theme3.ARROW_MARGIN_RIGHT,...parseFloat(theme3.ARROW_ANIMATION_DURATION)>0?{transition:`transform ${theme3.ARROW_ANIMATION_DURATION} ease 0s`}:{},...unselectable},expanded:{WebkitTransform:"rotateZ(90deg)",MozTransform:"rotateZ(90deg)",transform:"rotateZ(90deg)"},collapsed:{WebkitTransform:"rotateZ(0deg)",MozTransform:"rotateZ(0deg)",transform:"rotateZ(0deg)"}},treeNodeChildNodesContainer:{margin:0,paddingLeft:theme3.TREENODE_PADDING_LEFT}},TableInspector:{base:{color:theme3.BASE_COLOR,position:"relative",border:`1px solid ${theme3.TABLE_BORDER_COLOR}`,fontFamily:theme3.BASE_FONT_FAMILY,fontSize:theme3.BASE_FONT_SIZE,lineHeight:"120%",boxSizing:"border-box",cursor:"default"}},TableInspectorHeaderContainer:{base:{top:0,height:"17px",left:0,right:0,overflowX:"hidden"},table:{tableLayout:"fixed",borderSpacing:0,borderCollapse:"separate",height:"100%",width:"100%",margin:0}},TableInspectorDataContainer:{tr:{display:"table-row"},td:{boxSizing:"border-box",border:"none",height:"16px",verticalAlign:"top",padding:"1px 4px",WebkitUserSelect:"text",whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",lineHeight:"14px"},div:{position:"static",top:"17px",bottom:0,overflowY:"overlay",transform:"translateZ(0)",left:0,right:0,overflowX:"hidden"},table:{positon:"static",left:0,top:0,right:0,bottom:0,borderTop:"0 none transparent",margin:0,backgroundImage:theme3.TABLE_DATA_BACKGROUND_IMAGE,backgroundSize:theme3.TABLE_DATA_BACKGROUND_SIZE,tableLayout:"fixed",borderSpacing:0,borderCollapse:"separate",width:"100%",fontSize:theme3.BASE_FONT_SIZE,lineHeight:"120%"}},TableInspectorTH:{base:{position:"relative",height:"auto",textAlign:"left",backgroundColor:theme3.TABLE_TH_BACKGROUND_COLOR,borderBottom:`1px solid ${theme3.TABLE_BORDER_COLOR}`,fontWeight:"normal",verticalAlign:"middle",padding:"0 4px",whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",lineHeight:"14px",":hover":{backgroundColor:theme3.TABLE_TH_HOVER_COLOR}},div:{whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",fontSize:theme3.BASE_FONT_SIZE,lineHeight:"120%"}},TableInspectorLeftBorder:{none:{borderLeft:"none"},solid:{borderLeft:`1px solid ${theme3.TABLE_BORDER_COLOR}`}},TableInspectorSortIcon:{display:"block",marginRight:3,width:8,height:7,marginTop:-7,color:theme3.TABLE_SORT_ICON_COLOR,fontSize:12,...unselectable}}),DEFAULT_THEME_NAME="chromeLight",ThemeContext=createContext(createTheme(themes_exports[DEFAULT_THEME_NAME])),useStyles=baseStylesKey=>useContext(ThemeContext)[baseStylesKey],themeAcceptor=WrappedComponent=>({theme:theme3=DEFAULT_THEME_NAME,...restProps})=>{let themeStyles=useMemo(()=>{switch(Object.prototype.toString.call(theme3)){case"[object String]":return createTheme(themes_exports[theme3]);case"[object Object]":return createTheme(theme3);default:return createTheme(themes_exports[DEFAULT_THEME_NAME])}},[theme3]);return React14.createElement(ThemeContext.Provider,{value:themeStyles},React14.createElement(WrappedComponent,{...restProps}))},Arrow=({expanded,styles})=>React14.createElement("span",{style:{...styles.base,...expanded?styles.expanded:styles.collapsed}},"\u25B6"),TreeNode=memo(props=>{props={expanded:!0,nodeRenderer:({name})=>React14.createElement("span",null,name),onClick:()=>{},shouldShowArrow:!1,shouldShowPlaceholder:!0,...props};let{expanded,onClick,children,nodeRenderer,title,shouldShowArrow,shouldShowPlaceholder}=props,styles=useStyles("TreeNode"),NodeRenderer=nodeRenderer;return React14.createElement("li",{"aria-expanded":expanded,role:"treeitem",style:styles.treeNodeBase,title},React14.createElement("div",{style:styles.treeNodePreviewContainer,onClick},shouldShowArrow||Children.count(children)>0?React14.createElement(Arrow,{expanded,styles:styles.treeNodeArrow}):shouldShowPlaceholder&&React14.createElement("span",{style:styles.treeNodePlaceholder},"\xA0"),React14.createElement(NodeRenderer,{...props})),React14.createElement("ol",{role:"group",style:styles.treeNodeChildNodesContainer},expanded?children:void 0))}),DEFAULT_ROOT_PATH="$",WILDCARD="*";function hasChildNodes(data,dataIterator){return !dataIterator(data).next().done}var wildcardPathsFromLevel=level=>Array.from({length:level},(_,i)=>[DEFAULT_ROOT_PATH].concat(Array.from({length:i},()=>"*")).join(".")),getExpandedPaths=(data,dataIterator,expandPaths,expandLevel,prevExpandedPaths)=>{let wildcardPaths=[].concat(wildcardPathsFromLevel(expandLevel)).concat(expandPaths).filter(path=>typeof path=="string"),expandedPaths=[];return wildcardPaths.forEach(wildcardPath=>{let keyPaths=wildcardPath.split("."),populatePaths=(curData,curPath,depth)=>{if(depth===keyPaths.length){expandedPaths.push(curPath);return}let key=keyPaths[depth];if(depth===0)hasChildNodes(curData,dataIterator)&&(key===DEFAULT_ROOT_PATH||key===WILDCARD)&&populatePaths(curData,DEFAULT_ROOT_PATH,depth+1);else if(key===WILDCARD)for(let{name,data:data2}of dataIterator(curData))hasChildNodes(data2,dataIterator)&&populatePaths(data2,`${curPath}.${name}`,depth+1);else {let value=curData[key];hasChildNodes(value,dataIterator)&&populatePaths(value,`${curPath}.${key}`,depth+1);}};populatePaths(data,"",0);}),expandedPaths.reduce((obj,path)=>(obj[path]=!0,obj),{...prevExpandedPaths})},ConnectedTreeNode=memo(props=>{let{data,dataIterator,path,depth,nodeRenderer}=props,[expandedPaths,setExpandedPaths]=useContext(ExpandedPathsContext),nodeHasChildNodes=hasChildNodes(data,dataIterator),expanded=!!expandedPaths[path],handleClick=useCallback(()=>nodeHasChildNodes&&setExpandedPaths(prevExpandedPaths=>({...prevExpandedPaths,[path]:!expanded})),[nodeHasChildNodes,setExpandedPaths,path,expanded]);return React14.createElement(TreeNode,{expanded,onClick:handleClick,shouldShowArrow:nodeHasChildNodes,shouldShowPlaceholder:depth>0,nodeRenderer,...props},expanded?[...dataIterator(data)].map(({name,data:data2,...renderNodeProps})=>React14.createElement(ConnectedTreeNode,{name,data:data2,depth:depth+1,path:`${path}.${name}`,key:name,dataIterator,nodeRenderer,...renderNodeProps})):null)}),TreeView=memo(({name,data,dataIterator,nodeRenderer,expandPaths,expandLevel})=>{let styles=useStyles("TreeView"),stateAndSetter=useState({}),[,setExpandedPaths]=stateAndSetter;return useLayoutEffect(()=>setExpandedPaths(prevExpandedPaths=>getExpandedPaths(data,dataIterator,expandPaths,expandLevel,prevExpandedPaths)),[data,dataIterator,expandPaths,expandLevel]),React14.createElement(ExpandedPathsContext.Provider,{value:stateAndSetter},React14.createElement("ol",{role:"tree",style:styles.treeViewOutline},React14.createElement(ConnectedTreeNode,{name,data,dataIterator,depth:0,path:DEFAULT_ROOT_PATH,nodeRenderer})))}),ObjectName=({name,dimmed=!1,styles={}})=>{let themeStyles=useStyles("ObjectName"),appliedStyles={...themeStyles.base,...dimmed?themeStyles.dimmed:{},...styles};return React14.createElement("span",{style:appliedStyles},name)},ObjectValue=({object,styles})=>{let themeStyles=useStyles("ObjectValue"),mkStyle=key=>({...themeStyles[key],...styles});switch(typeof object){case"bigint":return React14.createElement("span",{style:mkStyle("objectValueNumber")},String(object),"n");case"number":return React14.createElement("span",{style:mkStyle("objectValueNumber")},String(object));case"string":return React14.createElement("span",{style:mkStyle("objectValueString")},'"',object,'"');case"boolean":return React14.createElement("span",{style:mkStyle("objectValueBoolean")},String(object));case"undefined":return React14.createElement("span",{style:mkStyle("objectValueUndefined")},"undefined");case"object":return object===null?React14.createElement("span",{style:mkStyle("objectValueNull")},"null"):object instanceof Date?React14.createElement("span",null,object.toString()):object instanceof RegExp?React14.createElement("span",{style:mkStyle("objectValueRegExp")},object.toString()):Array.isArray(object)?React14.createElement("span",null,`Array(${object.length})`):object.constructor?typeof object.constructor.isBuffer=="function"&&object.constructor.isBuffer(object)?React14.createElement("span",null,`Buffer[${object.length}]`):React14.createElement("span",null,object.constructor.name):React14.createElement("span",null,"Object");case"function":return React14.createElement("span",null,React14.createElement("span",{style:mkStyle("objectValueFunctionPrefix")},"\u0192\xA0"),React14.createElement("span",{style:mkStyle("objectValueFunctionName")},object.name,"()"));case"symbol":return React14.createElement("span",{style:mkStyle("objectValueSymbol")},object.toString());default:return React14.createElement("span",null)}},hasOwnProperty=Object.prototype.hasOwnProperty,propertyIsEnumerable=Object.prototype.propertyIsEnumerable;function getPropertyValue(object,propertyName){let propertyDescriptor=Object.getOwnPropertyDescriptor(object,propertyName);if(propertyDescriptor.get)try{return propertyDescriptor.get()}catch{return propertyDescriptor.get}return object[propertyName]}function intersperse(arr,sep){return arr.length===0?[]:arr.slice(1).reduce((xs,x)=>xs.concat([sep,x]),[arr[0]])}var ObjectPreview=({data})=>{let styles=useStyles("ObjectPreview"),object=data;if(typeof object!="object"||object===null||object instanceof Date||object instanceof RegExp)return React14.createElement(ObjectValue,{object});if(Array.isArray(object)){let maxProperties=styles.arrayMaxProperties,previewArray=object.slice(0,maxProperties).map((element,index)=>React14.createElement(ObjectValue,{key:index,object:element}));object.length>maxProperties&&previewArray.push(React14.createElement("span",{key:"ellipsis"},"\u2026"));let arrayLength=object.length;return React14.createElement(React14.Fragment,null,React14.createElement("span",{style:styles.objectDescription},arrayLength===0?"":`(${arrayLength})\xA0`),React14.createElement("span",{style:styles.preview},"[",intersperse(previewArray,", "),"]"))}else {let maxProperties=styles.objectMaxProperties,propertyNodes=[];for(let propertyName in object)if(hasOwnProperty.call(object,propertyName)){let ellipsis;propertyNodes.length===maxProperties-1&&Object.keys(object).length>maxProperties&&(ellipsis=React14.createElement("span",{key:"ellipsis"},"\u2026"));let propertyValue=getPropertyValue(object,propertyName);if(propertyNodes.push(React14.createElement("span",{key:propertyName},React14.createElement(ObjectName,{name:propertyName||'""'}),":\xA0",React14.createElement(ObjectValue,{object:propertyValue}),ellipsis)),ellipsis)break}let objectConstructorName=object.constructor?object.constructor.name:"Object";return React14.createElement(React14.Fragment,null,React14.createElement("span",{style:styles.objectDescription},objectConstructorName==="Object"?"":`${objectConstructorName} `),React14.createElement("span",{style:styles.preview},"{",intersperse(propertyNodes,", "),"}"))}},ObjectRootLabel=({name,data})=>typeof name=="string"?React14.createElement("span",null,React14.createElement(ObjectName,{name}),React14.createElement("span",null,": "),React14.createElement(ObjectPreview,{data})):React14.createElement(ObjectPreview,{data}),ObjectLabel=({name,data,isNonenumerable=!1})=>{let object=data;return React14.createElement("span",null,typeof name=="string"?React14.createElement(ObjectName,{name,dimmed:isNonenumerable}):React14.createElement(ObjectPreview,{data:name}),React14.createElement("span",null,": "),React14.createElement(ObjectValue,{object}))},createIterator=(showNonenumerable,sortObjectKeys)=>function*(data){if(!(typeof data=="object"&&data!==null||typeof data=="function"))return;let dataIsArray=Array.isArray(data);if(!dataIsArray&&data[Symbol.iterator]){let i=0;for(let entry of data){if(Array.isArray(entry)&&entry.length===2){let[k,v]=entry;yield {name:k,data:v};}else yield {name:i.toString(),data:entry};i++;}}else {let keys=Object.getOwnPropertyNames(data);sortObjectKeys===!0&&!dataIsArray?keys.sort():typeof sortObjectKeys=="function"&&keys.sort(sortObjectKeys);for(let propertyName of keys)if(propertyIsEnumerable.call(data,propertyName)){let propertyValue=getPropertyValue(data,propertyName);yield {name:propertyName||'""',data:propertyValue};}else if(showNonenumerable){let propertyValue;try{propertyValue=getPropertyValue(data,propertyName);}catch{}propertyValue!==void 0&&(yield {name:propertyName,data:propertyValue,isNonenumerable:!0});}showNonenumerable&&data!==Object.prototype&&(yield {name:"__proto__",data:Object.getPrototypeOf(data),isNonenumerable:!0});}},defaultNodeRenderer=({depth,name,data,isNonenumerable})=>depth===0?React14.createElement(ObjectRootLabel,{name,data}):React14.createElement(ObjectLabel,{name,data,isNonenumerable}),ObjectInspector=({showNonenumerable=!1,sortObjectKeys,nodeRenderer,...treeViewProps})=>{let dataIterator=createIterator(showNonenumerable,sortObjectKeys),renderer=nodeRenderer||defaultNodeRenderer;return React14.createElement(TreeView,{nodeRenderer:renderer,dataIterator,...treeViewProps})},themedObjectInspector=themeAcceptor(ObjectInspector);function getHeaders(data){if(typeof data=="object"){let rowHeaders=[];if(Array.isArray(data)){let nRows=data.length;rowHeaders=[...Array(nRows).keys()];}else data!==null&&(rowHeaders=Object.keys(data));let colHeaders=rowHeaders.reduce((colHeaders2,rowHeader)=>{let row=data[rowHeader];return typeof row=="object"&&row!==null&&Object.keys(row).reduce((xs,x)=>(xs.includes(x)||xs.push(x),xs),colHeaders2),colHeaders2},[]);return {rowHeaders,colHeaders}}}var DataContainer=({rows,columns,rowsData})=>{let styles=useStyles("TableInspectorDataContainer"),borderStyles=useStyles("TableInspectorLeftBorder");return React14.createElement("div",{style:styles.div},React14.createElement("table",{style:styles.table},React14.createElement("colgroup",null),React14.createElement("tbody",null,rows.map((row,i)=>React14.createElement("tr",{key:row,style:styles.tr},React14.createElement("td",{style:{...styles.td,...borderStyles.none}},row),columns.map(column=>{let rowData=rowsData[i];return typeof rowData=="object"&&rowData!==null&&hasOwnProperty.call(rowData,column)?React14.createElement("td",{key:column,style:{...styles.td,...borderStyles.solid}},React14.createElement(ObjectValue,{object:rowData[column]})):React14.createElement("td",{key:column,style:{...styles.td,...borderStyles.solid}})}))))))},SortIconContainer=props=>React14.createElement("div",{style:{position:"absolute",top:1,right:0,bottom:1,display:"flex",alignItems:"center"}},props.children),SortIcon=({sortAscending})=>{let styles=useStyles("TableInspectorSortIcon"),glyph=sortAscending?"\u25B2":"\u25BC";return React14.createElement("div",{style:styles},glyph)},TH=({sortAscending=!1,sorted=!1,onClick=void 0,borderStyle={},children,...thProps})=>{let styles=useStyles("TableInspectorTH"),[hovered,setHovered]=useState(!1),handleMouseEnter=useCallback(()=>setHovered(!0),[]),handleMouseLeave=useCallback(()=>setHovered(!1),[]);return React14.createElement("th",{...thProps,style:{...styles.base,...borderStyle,...hovered?styles.base[":hover"]:{}},onMouseEnter:handleMouseEnter,onMouseLeave:handleMouseLeave,onClick},React14.createElement("div",{style:styles.div},children),sorted&&React14.createElement(SortIconContainer,null,React14.createElement(SortIcon,{sortAscending})))},HeaderContainer=({indexColumnText="(index)",columns=[],sorted,sortIndexColumn,sortColumn,sortAscending,onTHClick,onIndexTHClick})=>{let styles=useStyles("TableInspectorHeaderContainer"),borderStyles=useStyles("TableInspectorLeftBorder");return React14.createElement("div",{style:styles.base},React14.createElement("table",{style:styles.table},React14.createElement("tbody",null,React14.createElement("tr",null,React14.createElement(TH,{borderStyle:borderStyles.none,sorted:sorted&&sortIndexColumn,sortAscending,onClick:onIndexTHClick},indexColumnText),columns.map(column=>React14.createElement(TH,{borderStyle:borderStyles.solid,key:column,sorted:sorted&&sortColumn===column,sortAscending,onClick:onTHClick.bind(null,column)},column))))))},TableInspector=({data,columns})=>{let styles=useStyles("TableInspector"),[{sorted,sortIndexColumn,sortColumn,sortAscending},setState]=useState({sorted:!1,sortIndexColumn:!1,sortColumn:void 0,sortAscending:!1}),handleIndexTHClick=useCallback(()=>{setState(({sortIndexColumn:sortIndexColumn2,sortAscending:sortAscending2})=>({sorted:!0,sortIndexColumn:!0,sortColumn:void 0,sortAscending:sortIndexColumn2?!sortAscending2:!0}));},[]),handleTHClick=useCallback(col=>{setState(({sortColumn:sortColumn2,sortAscending:sortAscending2})=>({sorted:!0,sortIndexColumn:!1,sortColumn:col,sortAscending:col===sortColumn2?!sortAscending2:!0}));},[]);if(typeof data!="object"||data===null)return React14.createElement("div",null);let{rowHeaders,colHeaders}=getHeaders(data);columns!==void 0&&(colHeaders=columns);let rowsData=rowHeaders.map(rowHeader=>data[rowHeader]),columnDataWithRowIndexes;if(sortColumn!==void 0?columnDataWithRowIndexes=rowsData.map((rowData,index)=>typeof rowData=="object"&&rowData!==null?[rowData[sortColumn],index]:[void 0,index]):sortIndexColumn&&(columnDataWithRowIndexes=rowHeaders.map((rowData,index)=>[rowHeaders[index],index])),columnDataWithRowIndexes!==void 0){let comparator=(mapper,ascending)=>(a,b)=>{let v1=mapper(a),v2=mapper(b),type1=typeof v1,type2=typeof v2,lt=(v12,v22)=>v12<v22?-1:v12>v22?1:0,result;if(type1===type2)result=lt(v1,v2);else {let order={string:0,number:1,object:2,symbol:3,boolean:4,undefined:5,function:6};result=lt(order[type1],order[type2]);}return ascending||(result=-result),result},sortedRowIndexes=columnDataWithRowIndexes.sort(comparator(item=>item[0],sortAscending)).map(item=>item[1]);rowHeaders=sortedRowIndexes.map(i=>rowHeaders[i]),rowsData=sortedRowIndexes.map(i=>rowsData[i]);}return React14.createElement("div",{style:styles.base},React14.createElement(HeaderContainer,{columns:colHeaders,sorted,sortIndexColumn,sortColumn,sortAscending,onTHClick:handleTHClick,onIndexTHClick:handleIndexTHClick}),React14.createElement(DataContainer,{rows:rowHeaders,columns:colHeaders,rowsData}))},themedTableInspector=themeAcceptor(TableInspector),TEXT_NODE_MAX_INLINE_CHARS=80,shouldInline=data=>data.childNodes.length===0||data.childNodes.length===1&&data.childNodes[0].nodeType===Node.TEXT_NODE&&data.textContent.length<TEXT_NODE_MAX_INLINE_CHARS,OpenTag=({tagName,attributes,styles})=>React14.createElement("span",{style:styles.base},"<",React14.createElement("span",{style:styles.tagName},tagName),(()=>{if(attributes){let attributeNodes=[];for(let i=0;i<attributes.length;i++){let attribute=attributes[i];attributeNodes.push(React14.createElement("span",{key:i}," ",React14.createElement("span",{style:styles.htmlAttributeName},attribute.name),'="',React14.createElement("span",{style:styles.htmlAttributeValue},attribute.value),'"'));}return attributeNodes}})(),">"),CloseTag=({tagName,isChildNode=!1,styles})=>React14.createElement("span",{style:Object.assign({},styles.base,isChildNode&&styles.offsetLeft)},"</",React14.createElement("span",{style:styles.tagName},tagName),">"),nameByNodeType={1:"ELEMENT_NODE",3:"TEXT_NODE",7:"PROCESSING_INSTRUCTION_NODE",8:"COMMENT_NODE",9:"DOCUMENT_NODE",10:"DOCUMENT_TYPE_NODE",11:"DOCUMENT_FRAGMENT_NODE"},DOMNodePreview=({isCloseTag,data,expanded})=>{let styles=useStyles("DOMNodePreview");if(isCloseTag)return React14.createElement(CloseTag,{styles:styles.htmlCloseTag,isChildNode:!0,tagName:data.tagName});switch(data.nodeType){case Node.ELEMENT_NODE:return React14.createElement("span",null,React14.createElement(OpenTag,{tagName:data.tagName,attributes:data.attributes,styles:styles.htmlOpenTag}),shouldInline(data)?data.textContent:!expanded&&"\u2026",!expanded&&React14.createElement(CloseTag,{tagName:data.tagName,styles:styles.htmlCloseTag}));case Node.TEXT_NODE:return React14.createElement("span",null,data.textContent);case Node.CDATA_SECTION_NODE:return React14.createElement("span",null,"<![CDATA["+data.textContent+"]]>");case Node.COMMENT_NODE:return React14.createElement("span",{style:styles.htmlComment},"<!--",data.textContent,"-->");case Node.PROCESSING_INSTRUCTION_NODE:return React14.createElement("span",null,data.nodeName);case Node.DOCUMENT_TYPE_NODE:return React14.createElement("span",{style:styles.htmlDoctype},"<!DOCTYPE ",data.name,data.publicId?` PUBLIC "${data.publicId}"`:"",!data.publicId&&data.systemId?" SYSTEM":"",data.systemId?` "${data.systemId}"`:"",">");case Node.DOCUMENT_NODE:return React14.createElement("span",null,data.nodeName);case Node.DOCUMENT_FRAGMENT_NODE:return React14.createElement("span",null,data.nodeName);default:return React14.createElement("span",null,nameByNodeType[data.nodeType])}},domIterator=function*(data){if(data&&data.childNodes){if(shouldInline(data))return;for(let i=0;i<data.childNodes.length;i++){let node=data.childNodes[i];node.nodeType===Node.TEXT_NODE&&node.textContent.trim().length===0||(yield {name:`${node.tagName}[${i}]`,data:node});}data.tagName&&(yield {name:"CLOSE_TAG",data:{tagName:data.tagName},isCloseTag:!0});}},DOMInspector=props=>React14.createElement(TreeView,{nodeRenderer:DOMNodePreview,dataIterator:domIterator,...props}),themedDOMInspector=themeAcceptor(DOMInspector),import_is_dom=__toESM(require_is_dom()),Inspector=({table=!1,data,...rest})=>table?React14.createElement(themedTableInspector,{data,...rest}):(0, import_is_dom.default)(data)?React14.createElement(themedDOMInspector,{data,...rest}):React14.createElement(themedObjectInspector,{data,...rest});var Action=styled.div({display:"flex",padding:0,borderLeft:"5px solid transparent",borderBottom:"1px solid transparent",transition:"all 0.1s",alignItems:"flex-start",whiteSpace:"pre"}),Counter=styled.div(({theme:theme3})=>({backgroundColor:opacify(.5,theme3.appBorderColor),color:theme3.color.inverseText,fontSize:theme3.typography.size.s1,fontWeight:theme3.typography.weight.bold,lineHeight:1,padding:"1px 5px",borderRadius:20,margin:"2px 0px"})),InspectorContainer=styled.div({flex:1,padding:"0 0 0 5px"});var UnstyledWrapped=forwardRef(({children,className},ref)=>React14.createElement(ScrollArea,{ref,horizontal:!0,vertical:!0,className},children));UnstyledWrapped.displayName="UnstyledWrapped";var Wrapper=styled(UnstyledWrapped)({margin:0,padding:"10px 5px 20px"}),ThemedInspector=withTheme(({theme:theme3,...props})=>React14.createElement(Inspector,{theme:theme3.addonActionsTheme||"chromeLight",table:!1,...props})),ActionLogger=({actions,onClear})=>{let wrapperRef=useRef(null),wrapper=wrapperRef.current,wasAtBottom=wrapper&&wrapper.scrollHeight-wrapper.scrollTop===wrapper.clientHeight;return useEffect(()=>{wasAtBottom&&(wrapperRef.current.scrollTop=wrapperRef.current.scrollHeight);},[wasAtBottom,actions.length]),React14.createElement(Fragment,null,React14.createElement(Wrapper,{ref:wrapperRef},actions.map(action=>React14.createElement(Action,{key:action.id},action.count>1&&React14.createElement(Counter,null,action.count),React14.createElement(InspectorContainer,null,React14.createElement(ThemedInspector,{sortObjectKeys:!0,showNonenumerable:!1,name:action.data.name,data:action.data.args??action.data}))))),React14.createElement(ActionBar,{actionItems:[{title:"Clear",onClick:onClear}]}))};var safeDeepEqual=(a,b)=>{try{return dequal(a,b)}catch{return !1}},ActionLogger2=class extends Component{constructor(props){super(props);this.handleStoryChange=()=>{let{actions}=this.state;actions.length>0&&actions[0].options.clearOnStoryChange&&this.clearActions();};this.addAction=action=>{this.setState(prevState=>{let actions=[...prevState.actions],previous=actions.length&&actions[actions.length-1];return previous&&safeDeepEqual(previous.data,action.data)?previous.count++:(action.count=1,actions.push(action)),{actions:actions.slice(0,action.options.limit)}});};this.clearActions=()=>{let{api}=this.props;api.emit(CLEAR_ID),this.setState({actions:[]});};this.mounted=!1,this.state={actions:[]};}componentDidMount(){this.mounted=!0;let{api}=this.props;api.on(EVENT_ID,this.addAction),api.on(STORY_CHANGED,this.handleStoryChange);}componentWillUnmount(){this.mounted=!1;let{api}=this.props;api.off(STORY_CHANGED,this.handleStoryChange),api.off(EVENT_ID,this.addAction);}render(){let{actions=[]}=this.state,{active}=this.props,props={actions,onClear:this.clearActions};return active?React14.createElement(ActionLogger,{...props}):null}};function Title(){let[{count},setCount]=useAddonState(ADDON_ID,{count:0});return useChannel({[EVENT_ID]:()=>{setCount(c=>({...c,count:c.count+1}));},[STORY_CHANGED]:()=>{setCount(c=>({...c,count:0}));},[CLEAR_ID]:()=>{setCount(c=>({...c,count:0}));}}),React14.createElement("div",null,React14.createElement(Spaced,{col:1},React14.createElement("span",{style:{display:"inline-block",verticalAlign:"middle"}},"Actions"),count===0?"":React14.createElement(Badge,{status:"neutral"},count)))}addons.register(ADDON_ID,api=>{addons.add(PANEL_ID,{title:Title,type:types.PANEL,render:({active})=>React14.createElement(ActionLogger2,{api,active:!!active}),paramKey:PARAM_KEY});});
