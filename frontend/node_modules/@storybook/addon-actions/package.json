{"name": "@storybook/addon-actions", "version": "8.6.14", "description": "Get UI feedback when an action is performed on an interactive element", "keywords": ["storybook", "essentials", "data-state"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/actions", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/actions"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./decorator": {"types": "./dist/decorator.d.ts", "import": "./dist/decorator.mjs", "require": "./dist/decorator.js"}, "./preview": {"types": "./dist/preview.d.ts", "import": "./dist/preview.mjs", "require": "./dist/preview.js"}, "./manager": "./dist/manager.js", "./register.js": "./dist/manager.js", "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "react-native": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["dist/index.d.ts"], "decorator": ["dist/decorator.d.ts"]}}, "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/addon-bundle.ts"}, "dependencies": {"@storybook/global": "^5.0.0", "@types/uuid": "^9.0.1", "dequal": "^2.0.2", "polished": "^4.2.2", "uuid": "^9.0.0"}, "devDependencies": {"@storybook/test": "8.6.14", "react": "^18.2.0", "react-dom": "^18.2.0", "react-inspector": "^6.0.0", "telejson": "^7.2.0", "typescript": "^5.7.3"}, "peerDependencies": {"storybook": "^8.6.14"}, "publishConfig": {"access": "public"}, "bundler": {"exportEntries": ["./src/decorator.ts", "./src/index.ts"], "managerEntries": ["./src/manager.tsx"], "previewEntries": ["./src/preview.ts"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae16", "storybook": {"displayName": "Actions", "unsupportedFrameworks": ["react-native"], "icon": "https://user-images.githubusercontent.com/263385/101991666-479cc600-3c7c-11eb-837b-be4e5ffa1bb8.png"}}