import {
  require_isObjectLike
} from "./chunk-URS5MDWH.js";
import {
  require_baseGetTag
} from "./chunk-LCZ7HEDH.js";
import {
  __commonJS
} from "./chunk-2LSFTFF7.js";

// node_modules/lodash/isSymbol.js
var require_isSymbol = __commonJS({
  "node_modules/lodash/isSymbol.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isObjectLike = require_isObjectLike();
    var symbolTag = "[object Symbol]";
    function isSymbol(value) {
      return typeof value == "symbol" || isObjectLike(value) && baseGetTag(value) == symbolTag;
    }
    module.exports = isSymbol;
  }
});

export {
  require_isSymbol
};
//# sourceMappingURL=chunk-Y3FQZQPT.js.map
