{"version": 3, "sources": ["../../../../../util-deprecate/browser.js"], "sourcesContent": ["\n/**\n * Module exports.\n */\n\nmodule.exports = deprecate;\n\n/**\n * Mark that a method should not be used.\n * Returns a modified function which warns once by default.\n *\n * If `localStorage.noDeprecation = true` is set, then it is a no-op.\n *\n * If `localStorage.throwDeprecation = true` is set, then deprecated functions\n * will throw an Error when invoked.\n *\n * If `localStorage.traceDeprecation = true` is set, then deprecated functions\n * will invoke `console.trace()` instead of `console.error()`.\n *\n * @param {Function} fn - the function to deprecate\n * @param {String} msg - the string to print to the console when `fn` is invoked\n * @returns {Function} a new \"deprecated\" version of `fn`\n * @api public\n */\n\nfunction deprecate (fn, msg) {\n  if (config('noDeprecation')) {\n    return fn;\n  }\n\n  var warned = false;\n  function deprecated() {\n    if (!warned) {\n      if (config('throwDeprecation')) {\n        throw new Error(msg);\n      } else if (config('traceDeprecation')) {\n        console.trace(msg);\n      } else {\n        console.warn(msg);\n      }\n      warned = true;\n    }\n    return fn.apply(this, arguments);\n  }\n\n  return deprecated;\n}\n\n/**\n * Checks `localStorage` for boolean values for the given `name`.\n *\n * @param {String} name\n * @returns {Boolean}\n * @api private\n */\n\nfunction config (name) {\n  // accessing global.localStorage can trigger a DOMException in sandboxed iframes\n  try {\n    if (!global.localStorage) return false;\n  } catch (_) {\n    return false;\n  }\n  var val = global.localStorage[name];\n  if (null == val) return false;\n  return String(val).toLowerCase() === 'true';\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAKA,WAAO,UAAU;AAoBjB,aAAS,UAAW,IAAI,KAAK;AAC3B,UAAI,OAAO,eAAe,GAAG;AAC3B,eAAO;AAAA,MACT;AAEA,UAAI,SAAS;AACb,eAAS,aAAa;AACpB,YAAI,CAAC,QAAQ;AACX,cAAI,OAAO,kBAAkB,GAAG;AAC9B,kBAAM,IAAI,MAAM,GAAG;AAAA,UACrB,WAAW,OAAO,kBAAkB,GAAG;AACrC,oBAAQ,MAAM,GAAG;AAAA,UACnB,OAAO;AACL,oBAAQ,KAAK,GAAG;AAAA,UAClB;AACA,mBAAS;AAAA,QACX;AACA,eAAO,GAAG,MAAM,MAAM,SAAS;AAAA,MACjC;AAEA,aAAO;AAAA,IACT;AAUA,aAAS,OAAQ,MAAM;AAErB,UAAI;AACF,YAAI,CAAC,OAAO;AAAc,iBAAO;AAAA,MACnC,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AACA,UAAI,MAAM,OAAO,aAAa,IAAI;AAClC,UAAI,QAAQ;AAAK,eAAO;AACxB,aAAO,OAAO,GAAG,EAAE,YAAY,MAAM;AAAA,IACvC;AAAA;AAAA;", "names": []}