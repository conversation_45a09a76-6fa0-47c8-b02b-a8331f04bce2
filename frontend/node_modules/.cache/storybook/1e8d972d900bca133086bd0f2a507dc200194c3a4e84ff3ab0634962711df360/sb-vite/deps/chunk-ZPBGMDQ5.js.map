{"version": 3, "sources": ["../../../../../lodash/mapKeys.js"], "sourcesContent": ["var baseAssignValue = require('./_baseAssignValue'),\n    baseForOwn = require('./_baseForOwn'),\n    baseIteratee = require('./_baseIteratee');\n\n/**\n * The opposite of `_.mapValues`; this method creates an object with the\n * same values as `object` and keys generated by running each own enumerable\n * string keyed property of `object` thru `iteratee`. The iteratee is invoked\n * with three arguments: (value, key, object).\n *\n * @static\n * @memberOf _\n * @since 3.8.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns the new mapped object.\n * @see _.mapValues\n * @example\n *\n * _.mapKeys({ 'a': 1, 'b': 2 }, function(value, key) {\n *   return key + value;\n * });\n * // => { 'a1': 1, 'b2': 2 }\n */\nfunction mapKeys(object, iteratee) {\n  var result = {};\n  iteratee = baseIteratee(iteratee, 3);\n\n  baseForOwn(object, function(value, key, object) {\n    baseAssignValue(result, iteratee(value, key, object), value);\n  });\n  return result;\n}\n\nmodule.exports = mapKeys;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,kBAAkB;AAAtB,QACI,aAAa;AADjB,QAEI,eAAe;AAuBnB,aAAS,QAAQ,QAAQ,UAAU;AACjC,UAAI,SAAS,CAAC;AACd,iBAAW,aAAa,UAAU,CAAC;AAEnC,iBAAW,QAAQ,SAAS,OAAO,KAAKA,SAAQ;AAC9C,wBAAgB,QAAQ,SAAS,OAAO,KAAKA,OAAM,GAAG,KAAK;AAAA,MAC7D,CAAC;AACD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": ["object"]}