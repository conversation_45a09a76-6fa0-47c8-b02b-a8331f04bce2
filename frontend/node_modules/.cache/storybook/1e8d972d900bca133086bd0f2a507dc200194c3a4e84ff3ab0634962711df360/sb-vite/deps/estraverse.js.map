{"version": 3, "sources": ["../../../../../estraverse/estraverse.js"], "sourcesContent": ["/*\n  Copyright (C) 2012-2013 <PERSON><PERSON> <<EMAIL>>\n  Copyright (C) 2012 <PERSON>ya Hidayat <<EMAIL>>\n\n  Redistribution and use in source and binary forms, with or without\n  modification, are permitted provided that the following conditions are met:\n\n    * Redistributions of source code must retain the above copyright\n      notice, this list of conditions and the following disclaimer.\n    * Redistributions in binary form must reproduce the above copyright\n      notice, this list of conditions and the following disclaimer in the\n      documentation and/or other materials provided with the distribution.\n\n  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n  ARE DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\n  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n  THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n*/\n/*jslint vars:false, bitwise:true*/\n/*jshint indent:4*/\n/*global exports:true*/\n(function clone(exports) {\n    'use strict';\n\n    var Syntax,\n        VisitorOption,\n        VisitorKeys,\n        BREAK,\n        SKIP,\n        REMOVE;\n\n    function deepCopy(obj) {\n        var ret = {}, key, val;\n        for (key in obj) {\n            if (obj.hasOwnProperty(key)) {\n                val = obj[key];\n                if (typeof val === 'object' && val !== null) {\n                    ret[key] = deepCopy(val);\n                } else {\n                    ret[key] = val;\n                }\n            }\n        }\n        return ret;\n    }\n\n    // based on LLVM libc++ upper_bound / lower_bound\n    // MIT License\n\n    function upperBound(array, func) {\n        var diff, len, i, current;\n\n        len = array.length;\n        i = 0;\n\n        while (len) {\n            diff = len >>> 1;\n            current = i + diff;\n            if (func(array[current])) {\n                len = diff;\n            } else {\n                i = current + 1;\n                len -= diff + 1;\n            }\n        }\n        return i;\n    }\n\n    Syntax = {\n        AssignmentExpression: 'AssignmentExpression',\n        AssignmentPattern: 'AssignmentPattern',\n        ArrayExpression: 'ArrayExpression',\n        ArrayPattern: 'ArrayPattern',\n        ArrowFunctionExpression: 'ArrowFunctionExpression',\n        AwaitExpression: 'AwaitExpression', // CAUTION: It's deferred to ES7.\n        BlockStatement: 'BlockStatement',\n        BinaryExpression: 'BinaryExpression',\n        BreakStatement: 'BreakStatement',\n        CallExpression: 'CallExpression',\n        CatchClause: 'CatchClause',\n        ChainExpression: 'ChainExpression',\n        ClassBody: 'ClassBody',\n        ClassDeclaration: 'ClassDeclaration',\n        ClassExpression: 'ClassExpression',\n        ComprehensionBlock: 'ComprehensionBlock',  // CAUTION: It's deferred to ES7.\n        ComprehensionExpression: 'ComprehensionExpression',  // CAUTION: It's deferred to ES7.\n        ConditionalExpression: 'ConditionalExpression',\n        ContinueStatement: 'ContinueStatement',\n        DebuggerStatement: 'DebuggerStatement',\n        DirectiveStatement: 'DirectiveStatement',\n        DoWhileStatement: 'DoWhileStatement',\n        EmptyStatement: 'EmptyStatement',\n        ExportAllDeclaration: 'ExportAllDeclaration',\n        ExportDefaultDeclaration: 'ExportDefaultDeclaration',\n        ExportNamedDeclaration: 'ExportNamedDeclaration',\n        ExportSpecifier: 'ExportSpecifier',\n        ExpressionStatement: 'ExpressionStatement',\n        ForStatement: 'ForStatement',\n        ForInStatement: 'ForInStatement',\n        ForOfStatement: 'ForOfStatement',\n        FunctionDeclaration: 'FunctionDeclaration',\n        FunctionExpression: 'FunctionExpression',\n        GeneratorExpression: 'GeneratorExpression',  // CAUTION: It's deferred to ES7.\n        Identifier: 'Identifier',\n        IfStatement: 'IfStatement',\n        ImportExpression: 'ImportExpression',\n        ImportDeclaration: 'ImportDeclaration',\n        ImportDefaultSpecifier: 'ImportDefaultSpecifier',\n        ImportNamespaceSpecifier: 'ImportNamespaceSpecifier',\n        ImportSpecifier: 'ImportSpecifier',\n        Literal: 'Literal',\n        LabeledStatement: 'LabeledStatement',\n        LogicalExpression: 'LogicalExpression',\n        MemberExpression: 'MemberExpression',\n        MetaProperty: 'MetaProperty',\n        MethodDefinition: 'MethodDefinition',\n        ModuleSpecifier: 'ModuleSpecifier',\n        NewExpression: 'NewExpression',\n        ObjectExpression: 'ObjectExpression',\n        ObjectPattern: 'ObjectPattern',\n        PrivateIdentifier: 'PrivateIdentifier',\n        Program: 'Program',\n        Property: 'Property',\n        PropertyDefinition: 'PropertyDefinition',\n        RestElement: 'RestElement',\n        ReturnStatement: 'ReturnStatement',\n        SequenceExpression: 'SequenceExpression',\n        SpreadElement: 'SpreadElement',\n        Super: 'Super',\n        SwitchStatement: 'SwitchStatement',\n        SwitchCase: 'SwitchCase',\n        TaggedTemplateExpression: 'TaggedTemplateExpression',\n        TemplateElement: 'TemplateElement',\n        TemplateLiteral: 'TemplateLiteral',\n        ThisExpression: 'ThisExpression',\n        ThrowStatement: 'ThrowStatement',\n        TryStatement: 'TryStatement',\n        UnaryExpression: 'UnaryExpression',\n        UpdateExpression: 'UpdateExpression',\n        VariableDeclaration: 'VariableDeclaration',\n        VariableDeclarator: 'VariableDeclarator',\n        WhileStatement: 'WhileStatement',\n        WithStatement: 'WithStatement',\n        YieldExpression: 'YieldExpression'\n    };\n\n    VisitorKeys = {\n        AssignmentExpression: ['left', 'right'],\n        AssignmentPattern: ['left', 'right'],\n        ArrayExpression: ['elements'],\n        ArrayPattern: ['elements'],\n        ArrowFunctionExpression: ['params', 'body'],\n        AwaitExpression: ['argument'], // CAUTION: It's deferred to ES7.\n        BlockStatement: ['body'],\n        BinaryExpression: ['left', 'right'],\n        BreakStatement: ['label'],\n        CallExpression: ['callee', 'arguments'],\n        CatchClause: ['param', 'body'],\n        ChainExpression: ['expression'],\n        ClassBody: ['body'],\n        ClassDeclaration: ['id', 'superClass', 'body'],\n        ClassExpression: ['id', 'superClass', 'body'],\n        ComprehensionBlock: ['left', 'right'],  // CAUTION: It's deferred to ES7.\n        ComprehensionExpression: ['blocks', 'filter', 'body'],  // CAUTION: It's deferred to ES7.\n        ConditionalExpression: ['test', 'consequent', 'alternate'],\n        ContinueStatement: ['label'],\n        DebuggerStatement: [],\n        DirectiveStatement: [],\n        DoWhileStatement: ['body', 'test'],\n        EmptyStatement: [],\n        ExportAllDeclaration: ['source'],\n        ExportDefaultDeclaration: ['declaration'],\n        ExportNamedDeclaration: ['declaration', 'specifiers', 'source'],\n        ExportSpecifier: ['exported', 'local'],\n        ExpressionStatement: ['expression'],\n        ForStatement: ['init', 'test', 'update', 'body'],\n        ForInStatement: ['left', 'right', 'body'],\n        ForOfStatement: ['left', 'right', 'body'],\n        FunctionDeclaration: ['id', 'params', 'body'],\n        FunctionExpression: ['id', 'params', 'body'],\n        GeneratorExpression: ['blocks', 'filter', 'body'],  // CAUTION: It's deferred to ES7.\n        Identifier: [],\n        IfStatement: ['test', 'consequent', 'alternate'],\n        ImportExpression: ['source'],\n        ImportDeclaration: ['specifiers', 'source'],\n        ImportDefaultSpecifier: ['local'],\n        ImportNamespaceSpecifier: ['local'],\n        ImportSpecifier: ['imported', 'local'],\n        Literal: [],\n        LabeledStatement: ['label', 'body'],\n        LogicalExpression: ['left', 'right'],\n        MemberExpression: ['object', 'property'],\n        MetaProperty: ['meta', 'property'],\n        MethodDefinition: ['key', 'value'],\n        ModuleSpecifier: [],\n        NewExpression: ['callee', 'arguments'],\n        ObjectExpression: ['properties'],\n        ObjectPattern: ['properties'],\n        PrivateIdentifier: [],\n        Program: ['body'],\n        Property: ['key', 'value'],\n        PropertyDefinition: ['key', 'value'],\n        RestElement: [ 'argument' ],\n        ReturnStatement: ['argument'],\n        SequenceExpression: ['expressions'],\n        SpreadElement: ['argument'],\n        Super: [],\n        SwitchStatement: ['discriminant', 'cases'],\n        SwitchCase: ['test', 'consequent'],\n        TaggedTemplateExpression: ['tag', 'quasi'],\n        TemplateElement: [],\n        TemplateLiteral: ['quasis', 'expressions'],\n        ThisExpression: [],\n        ThrowStatement: ['argument'],\n        TryStatement: ['block', 'handler', 'finalizer'],\n        UnaryExpression: ['argument'],\n        UpdateExpression: ['argument'],\n        VariableDeclaration: ['declarations'],\n        VariableDeclarator: ['id', 'init'],\n        WhileStatement: ['test', 'body'],\n        WithStatement: ['object', 'body'],\n        YieldExpression: ['argument']\n    };\n\n    // unique id\n    BREAK = {};\n    SKIP = {};\n    REMOVE = {};\n\n    VisitorOption = {\n        Break: BREAK,\n        Skip: SKIP,\n        Remove: REMOVE\n    };\n\n    function Reference(parent, key) {\n        this.parent = parent;\n        this.key = key;\n    }\n\n    Reference.prototype.replace = function replace(node) {\n        this.parent[this.key] = node;\n    };\n\n    Reference.prototype.remove = function remove() {\n        if (Array.isArray(this.parent)) {\n            this.parent.splice(this.key, 1);\n            return true;\n        } else {\n            this.replace(null);\n            return false;\n        }\n    };\n\n    function Element(node, path, wrap, ref) {\n        this.node = node;\n        this.path = path;\n        this.wrap = wrap;\n        this.ref = ref;\n    }\n\n    function Controller() { }\n\n    // API:\n    // return property path array from root to current node\n    Controller.prototype.path = function path() {\n        var i, iz, j, jz, result, element;\n\n        function addToPath(result, path) {\n            if (Array.isArray(path)) {\n                for (j = 0, jz = path.length; j < jz; ++j) {\n                    result.push(path[j]);\n                }\n            } else {\n                result.push(path);\n            }\n        }\n\n        // root node\n        if (!this.__current.path) {\n            return null;\n        }\n\n        // first node is sentinel, second node is root element\n        result = [];\n        for (i = 2, iz = this.__leavelist.length; i < iz; ++i) {\n            element = this.__leavelist[i];\n            addToPath(result, element.path);\n        }\n        addToPath(result, this.__current.path);\n        return result;\n    };\n\n    // API:\n    // return type of current node\n    Controller.prototype.type = function () {\n        var node = this.current();\n        return node.type || this.__current.wrap;\n    };\n\n    // API:\n    // return array of parent elements\n    Controller.prototype.parents = function parents() {\n        var i, iz, result;\n\n        // first node is sentinel\n        result = [];\n        for (i = 1, iz = this.__leavelist.length; i < iz; ++i) {\n            result.push(this.__leavelist[i].node);\n        }\n\n        return result;\n    };\n\n    // API:\n    // return current node\n    Controller.prototype.current = function current() {\n        return this.__current.node;\n    };\n\n    Controller.prototype.__execute = function __execute(callback, element) {\n        var previous, result;\n\n        result = undefined;\n\n        previous  = this.__current;\n        this.__current = element;\n        this.__state = null;\n        if (callback) {\n            result = callback.call(this, element.node, this.__leavelist[this.__leavelist.length - 1].node);\n        }\n        this.__current = previous;\n\n        return result;\n    };\n\n    // API:\n    // notify control skip / break\n    Controller.prototype.notify = function notify(flag) {\n        this.__state = flag;\n    };\n\n    // API:\n    // skip child nodes of current node\n    Controller.prototype.skip = function () {\n        this.notify(SKIP);\n    };\n\n    // API:\n    // break traversals\n    Controller.prototype['break'] = function () {\n        this.notify(BREAK);\n    };\n\n    // API:\n    // remove node\n    Controller.prototype.remove = function () {\n        this.notify(REMOVE);\n    };\n\n    Controller.prototype.__initialize = function(root, visitor) {\n        this.visitor = visitor;\n        this.root = root;\n        this.__worklist = [];\n        this.__leavelist = [];\n        this.__current = null;\n        this.__state = null;\n        this.__fallback = null;\n        if (visitor.fallback === 'iteration') {\n            this.__fallback = Object.keys;\n        } else if (typeof visitor.fallback === 'function') {\n            this.__fallback = visitor.fallback;\n        }\n\n        this.__keys = VisitorKeys;\n        if (visitor.keys) {\n            this.__keys = Object.assign(Object.create(this.__keys), visitor.keys);\n        }\n    };\n\n    function isNode(node) {\n        if (node == null) {\n            return false;\n        }\n        return typeof node === 'object' && typeof node.type === 'string';\n    }\n\n    function isProperty(nodeType, key) {\n        return (nodeType === Syntax.ObjectExpression || nodeType === Syntax.ObjectPattern) && 'properties' === key;\n    }\n  \n    function candidateExistsInLeaveList(leavelist, candidate) {\n        for (var i = leavelist.length - 1; i >= 0; --i) {\n            if (leavelist[i].node === candidate) {\n                return true;\n            }\n        }\n        return false;\n    }\n\n    Controller.prototype.traverse = function traverse(root, visitor) {\n        var worklist,\n            leavelist,\n            element,\n            node,\n            nodeType,\n            ret,\n            key,\n            current,\n            current2,\n            candidates,\n            candidate,\n            sentinel;\n\n        this.__initialize(root, visitor);\n\n        sentinel = {};\n\n        // reference\n        worklist = this.__worklist;\n        leavelist = this.__leavelist;\n\n        // initialize\n        worklist.push(new Element(root, null, null, null));\n        leavelist.push(new Element(null, null, null, null));\n\n        while (worklist.length) {\n            element = worklist.pop();\n\n            if (element === sentinel) {\n                element = leavelist.pop();\n\n                ret = this.__execute(visitor.leave, element);\n\n                if (this.__state === BREAK || ret === BREAK) {\n                    return;\n                }\n                continue;\n            }\n\n            if (element.node) {\n\n                ret = this.__execute(visitor.enter, element);\n\n                if (this.__state === BREAK || ret === BREAK) {\n                    return;\n                }\n\n                worklist.push(sentinel);\n                leavelist.push(element);\n\n                if (this.__state === SKIP || ret === SKIP) {\n                    continue;\n                }\n\n                node = element.node;\n                nodeType = node.type || element.wrap;\n                candidates = this.__keys[nodeType];\n                if (!candidates) {\n                    if (this.__fallback) {\n                        candidates = this.__fallback(node);\n                    } else {\n                        throw new Error('Unknown node type ' + nodeType + '.');\n                    }\n                }\n\n                current = candidates.length;\n                while ((current -= 1) >= 0) {\n                    key = candidates[current];\n                    candidate = node[key];\n                    if (!candidate) {\n                        continue;\n                    }\n\n                    if (Array.isArray(candidate)) {\n                        current2 = candidate.length;\n                        while ((current2 -= 1) >= 0) {\n                            if (!candidate[current2]) {\n                                continue;\n                            }\n\n                            if (candidateExistsInLeaveList(leavelist, candidate[current2])) {\n                              continue;\n                            }\n\n                            if (isProperty(nodeType, candidates[current])) {\n                                element = new Element(candidate[current2], [key, current2], 'Property', null);\n                            } else if (isNode(candidate[current2])) {\n                                element = new Element(candidate[current2], [key, current2], null, null);\n                            } else {\n                                continue;\n                            }\n                            worklist.push(element);\n                        }\n                    } else if (isNode(candidate)) {\n                        if (candidateExistsInLeaveList(leavelist, candidate)) {\n                          continue;\n                        }\n\n                        worklist.push(new Element(candidate, key, null, null));\n                    }\n                }\n            }\n        }\n    };\n\n    Controller.prototype.replace = function replace(root, visitor) {\n        var worklist,\n            leavelist,\n            node,\n            nodeType,\n            target,\n            element,\n            current,\n            current2,\n            candidates,\n            candidate,\n            sentinel,\n            outer,\n            key;\n\n        function removeElem(element) {\n            var i,\n                key,\n                nextElem,\n                parent;\n\n            if (element.ref.remove()) {\n                // When the reference is an element of an array.\n                key = element.ref.key;\n                parent = element.ref.parent;\n\n                // If removed from array, then decrease following items' keys.\n                i = worklist.length;\n                while (i--) {\n                    nextElem = worklist[i];\n                    if (nextElem.ref && nextElem.ref.parent === parent) {\n                        if  (nextElem.ref.key < key) {\n                            break;\n                        }\n                        --nextElem.ref.key;\n                    }\n                }\n            }\n        }\n\n        this.__initialize(root, visitor);\n\n        sentinel = {};\n\n        // reference\n        worklist = this.__worklist;\n        leavelist = this.__leavelist;\n\n        // initialize\n        outer = {\n            root: root\n        };\n        element = new Element(root, null, null, new Reference(outer, 'root'));\n        worklist.push(element);\n        leavelist.push(element);\n\n        while (worklist.length) {\n            element = worklist.pop();\n\n            if (element === sentinel) {\n                element = leavelist.pop();\n\n                target = this.__execute(visitor.leave, element);\n\n                // node may be replaced with null,\n                // so distinguish between undefined and null in this place\n                if (target !== undefined && target !== BREAK && target !== SKIP && target !== REMOVE) {\n                    // replace\n                    element.ref.replace(target);\n                }\n\n                if (this.__state === REMOVE || target === REMOVE) {\n                    removeElem(element);\n                }\n\n                if (this.__state === BREAK || target === BREAK) {\n                    return outer.root;\n                }\n                continue;\n            }\n\n            target = this.__execute(visitor.enter, element);\n\n            // node may be replaced with null,\n            // so distinguish between undefined and null in this place\n            if (target !== undefined && target !== BREAK && target !== SKIP && target !== REMOVE) {\n                // replace\n                element.ref.replace(target);\n                element.node = target;\n            }\n\n            if (this.__state === REMOVE || target === REMOVE) {\n                removeElem(element);\n                element.node = null;\n            }\n\n            if (this.__state === BREAK || target === BREAK) {\n                return outer.root;\n            }\n\n            // node may be null\n            node = element.node;\n            if (!node) {\n                continue;\n            }\n\n            worklist.push(sentinel);\n            leavelist.push(element);\n\n            if (this.__state === SKIP || target === SKIP) {\n                continue;\n            }\n\n            nodeType = node.type || element.wrap;\n            candidates = this.__keys[nodeType];\n            if (!candidates) {\n                if (this.__fallback) {\n                    candidates = this.__fallback(node);\n                } else {\n                    throw new Error('Unknown node type ' + nodeType + '.');\n                }\n            }\n\n            current = candidates.length;\n            while ((current -= 1) >= 0) {\n                key = candidates[current];\n                candidate = node[key];\n                if (!candidate) {\n                    continue;\n                }\n\n                if (Array.isArray(candidate)) {\n                    current2 = candidate.length;\n                    while ((current2 -= 1) >= 0) {\n                        if (!candidate[current2]) {\n                            continue;\n                        }\n                        if (isProperty(nodeType, candidates[current])) {\n                            element = new Element(candidate[current2], [key, current2], 'Property', new Reference(candidate, current2));\n                        } else if (isNode(candidate[current2])) {\n                            element = new Element(candidate[current2], [key, current2], null, new Reference(candidate, current2));\n                        } else {\n                            continue;\n                        }\n                        worklist.push(element);\n                    }\n                } else if (isNode(candidate)) {\n                    worklist.push(new Element(candidate, key, null, new Reference(node, key)));\n                }\n            }\n        }\n\n        return outer.root;\n    };\n\n    function traverse(root, visitor) {\n        var controller = new Controller();\n        return controller.traverse(root, visitor);\n    }\n\n    function replace(root, visitor) {\n        var controller = new Controller();\n        return controller.replace(root, visitor);\n    }\n\n    function extendCommentRange(comment, tokens) {\n        var target;\n\n        target = upperBound(tokens, function search(token) {\n            return token.range[0] > comment.range[0];\n        });\n\n        comment.extendedRange = [comment.range[0], comment.range[1]];\n\n        if (target !== tokens.length) {\n            comment.extendedRange[1] = tokens[target].range[0];\n        }\n\n        target -= 1;\n        if (target >= 0) {\n            comment.extendedRange[0] = tokens[target].range[1];\n        }\n\n        return comment;\n    }\n\n    function attachComments(tree, providedComments, tokens) {\n        // At first, we should calculate extended comment ranges.\n        var comments = [], comment, len, i, cursor;\n\n        if (!tree.range) {\n            throw new Error('attachComments needs range information');\n        }\n\n        // tokens array is empty, we attach comments to tree as 'leadingComments'\n        if (!tokens.length) {\n            if (providedComments.length) {\n                for (i = 0, len = providedComments.length; i < len; i += 1) {\n                    comment = deepCopy(providedComments[i]);\n                    comment.extendedRange = [0, tree.range[0]];\n                    comments.push(comment);\n                }\n                tree.leadingComments = comments;\n            }\n            return tree;\n        }\n\n        for (i = 0, len = providedComments.length; i < len; i += 1) {\n            comments.push(extendCommentRange(deepCopy(providedComments[i]), tokens));\n        }\n\n        // This is based on John Freeman's implementation.\n        cursor = 0;\n        traverse(tree, {\n            enter: function (node) {\n                var comment;\n\n                while (cursor < comments.length) {\n                    comment = comments[cursor];\n                    if (comment.extendedRange[1] > node.range[0]) {\n                        break;\n                    }\n\n                    if (comment.extendedRange[1] === node.range[0]) {\n                        if (!node.leadingComments) {\n                            node.leadingComments = [];\n                        }\n                        node.leadingComments.push(comment);\n                        comments.splice(cursor, 1);\n                    } else {\n                        cursor += 1;\n                    }\n                }\n\n                // already out of owned node\n                if (cursor === comments.length) {\n                    return VisitorOption.Break;\n                }\n\n                if (comments[cursor].extendedRange[0] > node.range[1]) {\n                    return VisitorOption.Skip;\n                }\n            }\n        });\n\n        cursor = 0;\n        traverse(tree, {\n            leave: function (node) {\n                var comment;\n\n                while (cursor < comments.length) {\n                    comment = comments[cursor];\n                    if (node.range[1] < comment.extendedRange[0]) {\n                        break;\n                    }\n\n                    if (node.range[1] === comment.extendedRange[0]) {\n                        if (!node.trailingComments) {\n                            node.trailingComments = [];\n                        }\n                        node.trailingComments.push(comment);\n                        comments.splice(cursor, 1);\n                    } else {\n                        cursor += 1;\n                    }\n                }\n\n                // already out of owned node\n                if (cursor === comments.length) {\n                    return VisitorOption.Break;\n                }\n\n                if (comments[cursor].extendedRange[0] > node.range[1]) {\n                    return VisitorOption.Skip;\n                }\n            }\n        });\n\n        return tree;\n    }\n\n    exports.Syntax = Syntax;\n    exports.traverse = traverse;\n    exports.replace = replace;\n    exports.attachComments = attachComments;\n    exports.VisitorKeys = VisitorKeys;\n    exports.VisitorOption = VisitorOption;\n    exports.Controller = Controller;\n    exports.cloneEnvironment = function () { return clone({}); };\n\n    return exports;\n}(exports));\n/* vim: set sw=4 ts=4 et tw=80 : */\n"], "mappings": ";;;;;AAAA;AAAA;AA2BA,KAAC,SAAS,MAAMA,UAAS;AACrB;AAEA,UAAI,QACA,eACA,aACA,OACA,MACA;AAEJ,eAAS,SAAS,KAAK;AACnB,YAAI,MAAM,CAAC,GAAG,KAAK;AACnB,aAAK,OAAO,KAAK;AACb,cAAI,IAAI,eAAe,GAAG,GAAG;AACzB,kBAAM,IAAI,GAAG;AACb,gBAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AACzC,kBAAI,GAAG,IAAI,SAAS,GAAG;AAAA,YAC3B,OAAO;AACH,kBAAI,GAAG,IAAI;AAAA,YACf;AAAA,UACJ;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAKA,eAAS,WAAW,OAAO,MAAM;AAC7B,YAAI,MAAM,KAAK,GAAG;AAElB,cAAM,MAAM;AACZ,YAAI;AAEJ,eAAO,KAAK;AACR,iBAAO,QAAQ;AACf,oBAAU,IAAI;AACd,cAAI,KAAK,MAAM,OAAO,CAAC,GAAG;AACtB,kBAAM;AAAA,UACV,OAAO;AACH,gBAAI,UAAU;AACd,mBAAO,OAAO;AAAA,UAClB;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAEA,eAAS;AAAA,QACL,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,QACnB,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,yBAAyB;AAAA,QACzB,iBAAiB;AAAA;AAAA,QACjB,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,kBAAkB;AAAA,QAClB,iBAAiB;AAAA,QACjB,oBAAoB;AAAA;AAAA,QACpB,yBAAyB;AAAA;AAAA,QACzB,uBAAuB;AAAA,QACvB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QACpB,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,QAChB,sBAAsB;AAAA,QACtB,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,qBAAqB;AAAA;AAAA,QACrB,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,0BAA0B;AAAA,QAC1B,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,kBAAkB;AAAA,QAClB,cAAc;AAAA,QACd,kBAAkB;AAAA,QAClB,iBAAiB;AAAA,QACjB,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,oBAAoB;AAAA,QACpB,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,QACP,iBAAiB;AAAA,QACjB,YAAY;AAAA,QACZ,0BAA0B;AAAA,QAC1B,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,iBAAiB;AAAA,MACrB;AAEA,oBAAc;AAAA,QACV,sBAAsB,CAAC,QAAQ,OAAO;AAAA,QACtC,mBAAmB,CAAC,QAAQ,OAAO;AAAA,QACnC,iBAAiB,CAAC,UAAU;AAAA,QAC5B,cAAc,CAAC,UAAU;AAAA,QACzB,yBAAyB,CAAC,UAAU,MAAM;AAAA,QAC1C,iBAAiB,CAAC,UAAU;AAAA;AAAA,QAC5B,gBAAgB,CAAC,MAAM;AAAA,QACvB,kBAAkB,CAAC,QAAQ,OAAO;AAAA,QAClC,gBAAgB,CAAC,OAAO;AAAA,QACxB,gBAAgB,CAAC,UAAU,WAAW;AAAA,QACtC,aAAa,CAAC,SAAS,MAAM;AAAA,QAC7B,iBAAiB,CAAC,YAAY;AAAA,QAC9B,WAAW,CAAC,MAAM;AAAA,QAClB,kBAAkB,CAAC,MAAM,cAAc,MAAM;AAAA,QAC7C,iBAAiB,CAAC,MAAM,cAAc,MAAM;AAAA,QAC5C,oBAAoB,CAAC,QAAQ,OAAO;AAAA;AAAA,QACpC,yBAAyB,CAAC,UAAU,UAAU,MAAM;AAAA;AAAA,QACpD,uBAAuB,CAAC,QAAQ,cAAc,WAAW;AAAA,QACzD,mBAAmB,CAAC,OAAO;AAAA,QAC3B,mBAAmB,CAAC;AAAA,QACpB,oBAAoB,CAAC;AAAA,QACrB,kBAAkB,CAAC,QAAQ,MAAM;AAAA,QACjC,gBAAgB,CAAC;AAAA,QACjB,sBAAsB,CAAC,QAAQ;AAAA,QAC/B,0BAA0B,CAAC,aAAa;AAAA,QACxC,wBAAwB,CAAC,eAAe,cAAc,QAAQ;AAAA,QAC9D,iBAAiB,CAAC,YAAY,OAAO;AAAA,QACrC,qBAAqB,CAAC,YAAY;AAAA,QAClC,cAAc,CAAC,QAAQ,QAAQ,UAAU,MAAM;AAAA,QAC/C,gBAAgB,CAAC,QAAQ,SAAS,MAAM;AAAA,QACxC,gBAAgB,CAAC,QAAQ,SAAS,MAAM;AAAA,QACxC,qBAAqB,CAAC,MAAM,UAAU,MAAM;AAAA,QAC5C,oBAAoB,CAAC,MAAM,UAAU,MAAM;AAAA,QAC3C,qBAAqB,CAAC,UAAU,UAAU,MAAM;AAAA;AAAA,QAChD,YAAY,CAAC;AAAA,QACb,aAAa,CAAC,QAAQ,cAAc,WAAW;AAAA,QAC/C,kBAAkB,CAAC,QAAQ;AAAA,QAC3B,mBAAmB,CAAC,cAAc,QAAQ;AAAA,QAC1C,wBAAwB,CAAC,OAAO;AAAA,QAChC,0BAA0B,CAAC,OAAO;AAAA,QAClC,iBAAiB,CAAC,YAAY,OAAO;AAAA,QACrC,SAAS,CAAC;AAAA,QACV,kBAAkB,CAAC,SAAS,MAAM;AAAA,QAClC,mBAAmB,CAAC,QAAQ,OAAO;AAAA,QACnC,kBAAkB,CAAC,UAAU,UAAU;AAAA,QACvC,cAAc,CAAC,QAAQ,UAAU;AAAA,QACjC,kBAAkB,CAAC,OAAO,OAAO;AAAA,QACjC,iBAAiB,CAAC;AAAA,QAClB,eAAe,CAAC,UAAU,WAAW;AAAA,QACrC,kBAAkB,CAAC,YAAY;AAAA,QAC/B,eAAe,CAAC,YAAY;AAAA,QAC5B,mBAAmB,CAAC;AAAA,QACpB,SAAS,CAAC,MAAM;AAAA,QAChB,UAAU,CAAC,OAAO,OAAO;AAAA,QACzB,oBAAoB,CAAC,OAAO,OAAO;AAAA,QACnC,aAAa,CAAE,UAAW;AAAA,QAC1B,iBAAiB,CAAC,UAAU;AAAA,QAC5B,oBAAoB,CAAC,aAAa;AAAA,QAClC,eAAe,CAAC,UAAU;AAAA,QAC1B,OAAO,CAAC;AAAA,QACR,iBAAiB,CAAC,gBAAgB,OAAO;AAAA,QACzC,YAAY,CAAC,QAAQ,YAAY;AAAA,QACjC,0BAA0B,CAAC,OAAO,OAAO;AAAA,QACzC,iBAAiB,CAAC;AAAA,QAClB,iBAAiB,CAAC,UAAU,aAAa;AAAA,QACzC,gBAAgB,CAAC;AAAA,QACjB,gBAAgB,CAAC,UAAU;AAAA,QAC3B,cAAc,CAAC,SAAS,WAAW,WAAW;AAAA,QAC9C,iBAAiB,CAAC,UAAU;AAAA,QAC5B,kBAAkB,CAAC,UAAU;AAAA,QAC7B,qBAAqB,CAAC,cAAc;AAAA,QACpC,oBAAoB,CAAC,MAAM,MAAM;AAAA,QACjC,gBAAgB,CAAC,QAAQ,MAAM;AAAA,QAC/B,eAAe,CAAC,UAAU,MAAM;AAAA,QAChC,iBAAiB,CAAC,UAAU;AAAA,MAChC;AAGA,cAAQ,CAAC;AACT,aAAO,CAAC;AACR,eAAS,CAAC;AAEV,sBAAgB;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,MACZ;AAEA,eAAS,UAAU,QAAQ,KAAK;AAC5B,aAAK,SAAS;AACd,aAAK,MAAM;AAAA,MACf;AAEA,gBAAU,UAAU,UAAU,SAASC,SAAQ,MAAM;AACjD,aAAK,OAAO,KAAK,GAAG,IAAI;AAAA,MAC5B;AAEA,gBAAU,UAAU,SAAS,SAAS,SAAS;AAC3C,YAAI,MAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,eAAK,OAAO,OAAO,KAAK,KAAK,CAAC;AAC9B,iBAAO;AAAA,QACX,OAAO;AACH,eAAK,QAAQ,IAAI;AACjB,iBAAO;AAAA,QACX;AAAA,MACJ;AAEA,eAAS,QAAQ,MAAM,MAAM,MAAM,KAAK;AACpC,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,aAAK,MAAM;AAAA,MACf;AAEA,eAAS,aAAa;AAAA,MAAE;AAIxB,iBAAW,UAAU,OAAO,SAAS,OAAO;AACxC,YAAI,GAAG,IAAI,GAAG,IAAI,QAAQ;AAE1B,iBAAS,UAAUC,SAAQC,OAAM;AAC7B,cAAI,MAAM,QAAQA,KAAI,GAAG;AACrB,iBAAK,IAAI,GAAG,KAAKA,MAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AACvC,cAAAD,QAAO,KAAKC,MAAK,CAAC,CAAC;AAAA,YACvB;AAAA,UACJ,OAAO;AACH,YAAAD,QAAO,KAAKC,KAAI;AAAA,UACpB;AAAA,QACJ;AAGA,YAAI,CAAC,KAAK,UAAU,MAAM;AACtB,iBAAO;AAAA,QACX;AAGA,iBAAS,CAAC;AACV,aAAK,IAAI,GAAG,KAAK,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,oBAAU,KAAK,YAAY,CAAC;AAC5B,oBAAU,QAAQ,QAAQ,IAAI;AAAA,QAClC;AACA,kBAAU,QAAQ,KAAK,UAAU,IAAI;AACrC,eAAO;AAAA,MACX;AAIA,iBAAW,UAAU,OAAO,WAAY;AACpC,YAAI,OAAO,KAAK,QAAQ;AACxB,eAAO,KAAK,QAAQ,KAAK,UAAU;AAAA,MACvC;AAIA,iBAAW,UAAU,UAAU,SAAS,UAAU;AAC9C,YAAI,GAAG,IAAI;AAGX,iBAAS,CAAC;AACV,aAAK,IAAI,GAAG,KAAK,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,iBAAO,KAAK,KAAK,YAAY,CAAC,EAAE,IAAI;AAAA,QACxC;AAEA,eAAO;AAAA,MACX;AAIA,iBAAW,UAAU,UAAU,SAAS,UAAU;AAC9C,eAAO,KAAK,UAAU;AAAA,MAC1B;AAEA,iBAAW,UAAU,YAAY,SAAS,UAAU,UAAU,SAAS;AACnE,YAAI,UAAU;AAEd,iBAAS;AAET,mBAAY,KAAK;AACjB,aAAK,YAAY;AACjB,aAAK,UAAU;AACf,YAAI,UAAU;AACV,mBAAS,SAAS,KAAK,MAAM,QAAQ,MAAM,KAAK,YAAY,KAAK,YAAY,SAAS,CAAC,EAAE,IAAI;AAAA,QACjG;AACA,aAAK,YAAY;AAEjB,eAAO;AAAA,MACX;AAIA,iBAAW,UAAU,SAAS,SAAS,OAAO,MAAM;AAChD,aAAK,UAAU;AAAA,MACnB;AAIA,iBAAW,UAAU,OAAO,WAAY;AACpC,aAAK,OAAO,IAAI;AAAA,MACpB;AAIA,iBAAW,UAAU,OAAO,IAAI,WAAY;AACxC,aAAK,OAAO,KAAK;AAAA,MACrB;AAIA,iBAAW,UAAU,SAAS,WAAY;AACtC,aAAK,OAAO,MAAM;AAAA,MACtB;AAEA,iBAAW,UAAU,eAAe,SAAS,MAAM,SAAS;AACxD,aAAK,UAAU;AACf,aAAK,OAAO;AACZ,aAAK,aAAa,CAAC;AACnB,aAAK,cAAc,CAAC;AACpB,aAAK,YAAY;AACjB,aAAK,UAAU;AACf,aAAK,aAAa;AAClB,YAAI,QAAQ,aAAa,aAAa;AAClC,eAAK,aAAa,OAAO;AAAA,QAC7B,WAAW,OAAO,QAAQ,aAAa,YAAY;AAC/C,eAAK,aAAa,QAAQ;AAAA,QAC9B;AAEA,aAAK,SAAS;AACd,YAAI,QAAQ,MAAM;AACd,eAAK,SAAS,OAAO,OAAO,OAAO,OAAO,KAAK,MAAM,GAAG,QAAQ,IAAI;AAAA,QACxE;AAAA,MACJ;AAEA,eAAS,OAAO,MAAM;AAClB,YAAI,QAAQ,MAAM;AACd,iBAAO;AAAA,QACX;AACA,eAAO,OAAO,SAAS,YAAY,OAAO,KAAK,SAAS;AAAA,MAC5D;AAEA,eAAS,WAAW,UAAU,KAAK;AAC/B,gBAAQ,aAAa,OAAO,oBAAoB,aAAa,OAAO,kBAAkB,iBAAiB;AAAA,MAC3G;AAEA,eAAS,2BAA2B,WAAW,WAAW;AACtD,iBAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAC5C,cAAI,UAAU,CAAC,EAAE,SAAS,WAAW;AACjC,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAEA,iBAAW,UAAU,WAAW,SAASC,UAAS,MAAM,SAAS;AAC7D,YAAI,UACA,WACA,SACA,MACA,UACA,KACA,KACA,SACA,UACA,YACA,WACA;AAEJ,aAAK,aAAa,MAAM,OAAO;AAE/B,mBAAW,CAAC;AAGZ,mBAAW,KAAK;AAChB,oBAAY,KAAK;AAGjB,iBAAS,KAAK,IAAI,QAAQ,MAAM,MAAM,MAAM,IAAI,CAAC;AACjD,kBAAU,KAAK,IAAI,QAAQ,MAAM,MAAM,MAAM,IAAI,CAAC;AAElD,eAAO,SAAS,QAAQ;AACpB,oBAAU,SAAS,IAAI;AAEvB,cAAI,YAAY,UAAU;AACtB,sBAAU,UAAU,IAAI;AAExB,kBAAM,KAAK,UAAU,QAAQ,OAAO,OAAO;AAE3C,gBAAI,KAAK,YAAY,SAAS,QAAQ,OAAO;AACzC;AAAA,YACJ;AACA;AAAA,UACJ;AAEA,cAAI,QAAQ,MAAM;AAEd,kBAAM,KAAK,UAAU,QAAQ,OAAO,OAAO;AAE3C,gBAAI,KAAK,YAAY,SAAS,QAAQ,OAAO;AACzC;AAAA,YACJ;AAEA,qBAAS,KAAK,QAAQ;AACtB,sBAAU,KAAK,OAAO;AAEtB,gBAAI,KAAK,YAAY,QAAQ,QAAQ,MAAM;AACvC;AAAA,YACJ;AAEA,mBAAO,QAAQ;AACf,uBAAW,KAAK,QAAQ,QAAQ;AAChC,yBAAa,KAAK,OAAO,QAAQ;AACjC,gBAAI,CAAC,YAAY;AACb,kBAAI,KAAK,YAAY;AACjB,6BAAa,KAAK,WAAW,IAAI;AAAA,cACrC,OAAO;AACH,sBAAM,IAAI,MAAM,uBAAuB,WAAW,GAAG;AAAA,cACzD;AAAA,YACJ;AAEA,sBAAU,WAAW;AACrB,oBAAQ,WAAW,MAAM,GAAG;AACxB,oBAAM,WAAW,OAAO;AACxB,0BAAY,KAAK,GAAG;AACpB,kBAAI,CAAC,WAAW;AACZ;AAAA,cACJ;AAEA,kBAAI,MAAM,QAAQ,SAAS,GAAG;AAC1B,2BAAW,UAAU;AACrB,wBAAQ,YAAY,MAAM,GAAG;AACzB,sBAAI,CAAC,UAAU,QAAQ,GAAG;AACtB;AAAA,kBACJ;AAEA,sBAAI,2BAA2B,WAAW,UAAU,QAAQ,CAAC,GAAG;AAC9D;AAAA,kBACF;AAEA,sBAAI,WAAW,UAAU,WAAW,OAAO,CAAC,GAAG;AAC3C,8BAAU,IAAI,QAAQ,UAAU,QAAQ,GAAG,CAAC,KAAK,QAAQ,GAAG,YAAY,IAAI;AAAA,kBAChF,WAAW,OAAO,UAAU,QAAQ,CAAC,GAAG;AACpC,8BAAU,IAAI,QAAQ,UAAU,QAAQ,GAAG,CAAC,KAAK,QAAQ,GAAG,MAAM,IAAI;AAAA,kBAC1E,OAAO;AACH;AAAA,kBACJ;AACA,2BAAS,KAAK,OAAO;AAAA,gBACzB;AAAA,cACJ,WAAW,OAAO,SAAS,GAAG;AAC1B,oBAAI,2BAA2B,WAAW,SAAS,GAAG;AACpD;AAAA,gBACF;AAEA,yBAAS,KAAK,IAAI,QAAQ,WAAW,KAAK,MAAM,IAAI,CAAC;AAAA,cACzD;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAEA,iBAAW,UAAU,UAAU,SAASH,SAAQ,MAAM,SAAS;AAC3D,YAAI,UACA,WACA,MACA,UACA,QACA,SACA,SACA,UACA,YACA,WACA,UACA,OACA;AAEJ,iBAAS,WAAWI,UAAS;AACzB,cAAI,GACAC,MACA,UACA;AAEJ,cAAID,SAAQ,IAAI,OAAO,GAAG;AAEtB,YAAAC,OAAMD,SAAQ,IAAI;AAClB,qBAASA,SAAQ,IAAI;AAGrB,gBAAI,SAAS;AACb,mBAAO,KAAK;AACR,yBAAW,SAAS,CAAC;AACrB,kBAAI,SAAS,OAAO,SAAS,IAAI,WAAW,QAAQ;AAChD,oBAAK,SAAS,IAAI,MAAMC,MAAK;AACzB;AAAA,gBACJ;AACA,kBAAE,SAAS,IAAI;AAAA,cACnB;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAEA,aAAK,aAAa,MAAM,OAAO;AAE/B,mBAAW,CAAC;AAGZ,mBAAW,KAAK;AAChB,oBAAY,KAAK;AAGjB,gBAAQ;AAAA,UACJ;AAAA,QACJ;AACA,kBAAU,IAAI,QAAQ,MAAM,MAAM,MAAM,IAAI,UAAU,OAAO,MAAM,CAAC;AACpE,iBAAS,KAAK,OAAO;AACrB,kBAAU,KAAK,OAAO;AAEtB,eAAO,SAAS,QAAQ;AACpB,oBAAU,SAAS,IAAI;AAEvB,cAAI,YAAY,UAAU;AACtB,sBAAU,UAAU,IAAI;AAExB,qBAAS,KAAK,UAAU,QAAQ,OAAO,OAAO;AAI9C,gBAAI,WAAW,UAAa,WAAW,SAAS,WAAW,QAAQ,WAAW,QAAQ;AAElF,sBAAQ,IAAI,QAAQ,MAAM;AAAA,YAC9B;AAEA,gBAAI,KAAK,YAAY,UAAU,WAAW,QAAQ;AAC9C,yBAAW,OAAO;AAAA,YACtB;AAEA,gBAAI,KAAK,YAAY,SAAS,WAAW,OAAO;AAC5C,qBAAO,MAAM;AAAA,YACjB;AACA;AAAA,UACJ;AAEA,mBAAS,KAAK,UAAU,QAAQ,OAAO,OAAO;AAI9C,cAAI,WAAW,UAAa,WAAW,SAAS,WAAW,QAAQ,WAAW,QAAQ;AAElF,oBAAQ,IAAI,QAAQ,MAAM;AAC1B,oBAAQ,OAAO;AAAA,UACnB;AAEA,cAAI,KAAK,YAAY,UAAU,WAAW,QAAQ;AAC9C,uBAAW,OAAO;AAClB,oBAAQ,OAAO;AAAA,UACnB;AAEA,cAAI,KAAK,YAAY,SAAS,WAAW,OAAO;AAC5C,mBAAO,MAAM;AAAA,UACjB;AAGA,iBAAO,QAAQ;AACf,cAAI,CAAC,MAAM;AACP;AAAA,UACJ;AAEA,mBAAS,KAAK,QAAQ;AACtB,oBAAU,KAAK,OAAO;AAEtB,cAAI,KAAK,YAAY,QAAQ,WAAW,MAAM;AAC1C;AAAA,UACJ;AAEA,qBAAW,KAAK,QAAQ,QAAQ;AAChC,uBAAa,KAAK,OAAO,QAAQ;AACjC,cAAI,CAAC,YAAY;AACb,gBAAI,KAAK,YAAY;AACjB,2BAAa,KAAK,WAAW,IAAI;AAAA,YACrC,OAAO;AACH,oBAAM,IAAI,MAAM,uBAAuB,WAAW,GAAG;AAAA,YACzD;AAAA,UACJ;AAEA,oBAAU,WAAW;AACrB,kBAAQ,WAAW,MAAM,GAAG;AACxB,kBAAM,WAAW,OAAO;AACxB,wBAAY,KAAK,GAAG;AACpB,gBAAI,CAAC,WAAW;AACZ;AAAA,YACJ;AAEA,gBAAI,MAAM,QAAQ,SAAS,GAAG;AAC1B,yBAAW,UAAU;AACrB,sBAAQ,YAAY,MAAM,GAAG;AACzB,oBAAI,CAAC,UAAU,QAAQ,GAAG;AACtB;AAAA,gBACJ;AACA,oBAAI,WAAW,UAAU,WAAW,OAAO,CAAC,GAAG;AAC3C,4BAAU,IAAI,QAAQ,UAAU,QAAQ,GAAG,CAAC,KAAK,QAAQ,GAAG,YAAY,IAAI,UAAU,WAAW,QAAQ,CAAC;AAAA,gBAC9G,WAAW,OAAO,UAAU,QAAQ,CAAC,GAAG;AACpC,4BAAU,IAAI,QAAQ,UAAU,QAAQ,GAAG,CAAC,KAAK,QAAQ,GAAG,MAAM,IAAI,UAAU,WAAW,QAAQ,CAAC;AAAA,gBACxG,OAAO;AACH;AAAA,gBACJ;AACA,yBAAS,KAAK,OAAO;AAAA,cACzB;AAAA,YACJ,WAAW,OAAO,SAAS,GAAG;AAC1B,uBAAS,KAAK,IAAI,QAAQ,WAAW,KAAK,MAAM,IAAI,UAAU,MAAM,GAAG,CAAC,CAAC;AAAA,YAC7E;AAAA,UACJ;AAAA,QACJ;AAEA,eAAO,MAAM;AAAA,MACjB;AAEA,eAAS,SAAS,MAAM,SAAS;AAC7B,YAAI,aAAa,IAAI,WAAW;AAChC,eAAO,WAAW,SAAS,MAAM,OAAO;AAAA,MAC5C;AAEA,eAAS,QAAQ,MAAM,SAAS;AAC5B,YAAI,aAAa,IAAI,WAAW;AAChC,eAAO,WAAW,QAAQ,MAAM,OAAO;AAAA,MAC3C;AAEA,eAAS,mBAAmB,SAAS,QAAQ;AACzC,YAAI;AAEJ,iBAAS,WAAW,QAAQ,SAAS,OAAO,OAAO;AAC/C,iBAAO,MAAM,MAAM,CAAC,IAAI,QAAQ,MAAM,CAAC;AAAA,QAC3C,CAAC;AAED,gBAAQ,gBAAgB,CAAC,QAAQ,MAAM,CAAC,GAAG,QAAQ,MAAM,CAAC,CAAC;AAE3D,YAAI,WAAW,OAAO,QAAQ;AAC1B,kBAAQ,cAAc,CAAC,IAAI,OAAO,MAAM,EAAE,MAAM,CAAC;AAAA,QACrD;AAEA,kBAAU;AACV,YAAI,UAAU,GAAG;AACb,kBAAQ,cAAc,CAAC,IAAI,OAAO,MAAM,EAAE,MAAM,CAAC;AAAA,QACrD;AAEA,eAAO;AAAA,MACX;AAEA,eAAS,eAAe,MAAM,kBAAkB,QAAQ;AAEpD,YAAI,WAAW,CAAC,GAAG,SAAS,KAAK,GAAG;AAEpC,YAAI,CAAC,KAAK,OAAO;AACb,gBAAM,IAAI,MAAM,wCAAwC;AAAA,QAC5D;AAGA,YAAI,CAAC,OAAO,QAAQ;AAChB,cAAI,iBAAiB,QAAQ;AACzB,iBAAK,IAAI,GAAG,MAAM,iBAAiB,QAAQ,IAAI,KAAK,KAAK,GAAG;AACxD,wBAAU,SAAS,iBAAiB,CAAC,CAAC;AACtC,sBAAQ,gBAAgB,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC;AACzC,uBAAS,KAAK,OAAO;AAAA,YACzB;AACA,iBAAK,kBAAkB;AAAA,UAC3B;AACA,iBAAO;AAAA,QACX;AAEA,aAAK,IAAI,GAAG,MAAM,iBAAiB,QAAQ,IAAI,KAAK,KAAK,GAAG;AACxD,mBAAS,KAAK,mBAAmB,SAAS,iBAAiB,CAAC,CAAC,GAAG,MAAM,CAAC;AAAA,QAC3E;AAGA,iBAAS;AACT,iBAAS,MAAM;AAAA,UACX,OAAO,SAAU,MAAM;AACnB,gBAAIC;AAEJ,mBAAO,SAAS,SAAS,QAAQ;AAC7B,cAAAA,WAAU,SAAS,MAAM;AACzB,kBAAIA,SAAQ,cAAc,CAAC,IAAI,KAAK,MAAM,CAAC,GAAG;AAC1C;AAAA,cACJ;AAEA,kBAAIA,SAAQ,cAAc,CAAC,MAAM,KAAK,MAAM,CAAC,GAAG;AAC5C,oBAAI,CAAC,KAAK,iBAAiB;AACvB,uBAAK,kBAAkB,CAAC;AAAA,gBAC5B;AACA,qBAAK,gBAAgB,KAAKA,QAAO;AACjC,yBAAS,OAAO,QAAQ,CAAC;AAAA,cAC7B,OAAO;AACH,0BAAU;AAAA,cACd;AAAA,YACJ;AAGA,gBAAI,WAAW,SAAS,QAAQ;AAC5B,qBAAO,cAAc;AAAA,YACzB;AAEA,gBAAI,SAAS,MAAM,EAAE,cAAc,CAAC,IAAI,KAAK,MAAM,CAAC,GAAG;AACnD,qBAAO,cAAc;AAAA,YACzB;AAAA,UACJ;AAAA,QACJ,CAAC;AAED,iBAAS;AACT,iBAAS,MAAM;AAAA,UACX,OAAO,SAAU,MAAM;AACnB,gBAAIA;AAEJ,mBAAO,SAAS,SAAS,QAAQ;AAC7B,cAAAA,WAAU,SAAS,MAAM;AACzB,kBAAI,KAAK,MAAM,CAAC,IAAIA,SAAQ,cAAc,CAAC,GAAG;AAC1C;AAAA,cACJ;AAEA,kBAAI,KAAK,MAAM,CAAC,MAAMA,SAAQ,cAAc,CAAC,GAAG;AAC5C,oBAAI,CAAC,KAAK,kBAAkB;AACxB,uBAAK,mBAAmB,CAAC;AAAA,gBAC7B;AACA,qBAAK,iBAAiB,KAAKA,QAAO;AAClC,yBAAS,OAAO,QAAQ,CAAC;AAAA,cAC7B,OAAO;AACH,0BAAU;AAAA,cACd;AAAA,YACJ;AAGA,gBAAI,WAAW,SAAS,QAAQ;AAC5B,qBAAO,cAAc;AAAA,YACzB;AAEA,gBAAI,SAAS,MAAM,EAAE,cAAc,CAAC,IAAI,KAAK,MAAM,CAAC,GAAG;AACnD,qBAAO,cAAc;AAAA,YACzB;AAAA,UACJ;AAAA,QACJ,CAAC;AAED,eAAO;AAAA,MACX;AAEA,MAAAP,SAAQ,SAAS;AACjB,MAAAA,SAAQ,WAAW;AACnB,MAAAA,SAAQ,UAAU;AAClB,MAAAA,SAAQ,iBAAiB;AACzB,MAAAA,SAAQ,cAAc;AACtB,MAAAA,SAAQ,gBAAgB;AACxB,MAAAA,SAAQ,aAAa;AACrB,MAAAA,SAAQ,mBAAmB,WAAY;AAAE,eAAO,MAAM,CAAC,CAAC;AAAA,MAAG;AAE3D,aAAOA;AAAA,IACX,GAAE,OAAO;AAAA;AAAA;", "names": ["exports", "replace", "result", "path", "traverse", "element", "key", "comment"]}