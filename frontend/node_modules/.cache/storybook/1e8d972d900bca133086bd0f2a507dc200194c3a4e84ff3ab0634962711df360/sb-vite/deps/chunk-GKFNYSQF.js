import {
  require_baseMerge,
  require_createAssigner
} from "./chunk-D3GWGFQY.js";
import {
  __commonJS
} from "./chunk-2LSFTFF7.js";

// node_modules/lodash/mergeWith.js
var require_mergeWith = __commonJS({
  "node_modules/lodash/mergeWith.js"(exports, module) {
    var baseMerge = require_baseMerge();
    var createAssigner = require_createAssigner();
    var mergeWith = createAssigner(function(object, source, srcIndex, customizer) {
      baseMerge(object, source, srcIndex, customizer);
    });
    module.exports = mergeWith;
  }
});

export {
  require_mergeWith
};
//# sourceMappingURL=chunk-GKFNYSQF.js.map
