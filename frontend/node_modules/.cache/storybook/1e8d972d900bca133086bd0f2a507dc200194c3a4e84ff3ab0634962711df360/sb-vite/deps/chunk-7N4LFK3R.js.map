{"version": 3, "sources": ["../../../../../jsdoc-type-pratt-parser/dist/index.js"], "sourcesContent": ["(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :\n    typeof define === 'function' && define.amd ? define(['exports'], factory) :\n    (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.jtpp = {}));\n})(this, (function (exports) { 'use strict';\n\n    function tokenToString(token) {\n        if (token.text !== undefined && token.text !== '') {\n            return `'${token.type}' with value '${token.text}'`;\n        }\n        else {\n            return `'${token.type}'`;\n        }\n    }\n    class NoParsletFoundError extends Error {\n        constructor(token) {\n            super(`No parslet found for token: ${tokenToString(token)}`);\n            this.token = token;\n            Object.setPrototypeOf(this, NoParsletFoundError.prototype);\n        }\n        getToken() {\n            return this.token;\n        }\n    }\n    class EarlyEndOfParseError extends Error {\n        constructor(token) {\n            super(`The parsing ended early. The next token was: ${tokenToString(token)}`);\n            this.token = token;\n            Object.setPrototypeOf(this, EarlyEndOfParseError.prototype);\n        }\n        getToken() {\n            return this.token;\n        }\n    }\n    class UnexpectedTypeError extends Error {\n        constructor(result, message) {\n            let error = `Unexpected type: '${result.type}'.`;\n            if (message !== undefined) {\n                error += ` Message: ${message}`;\n            }\n            super(error);\n            Object.setPrototypeOf(this, UnexpectedTypeError.prototype);\n        }\n    }\n    // export class UnexpectedTokenError extends Error {\n    //   private expected: Token\n    //   private found: Token\n    //\n    //   constructor (expected: Token, found: Token) {\n    //     super(`The parsing ended early. The next token was: ${tokenToString(token)}`)\n    //\n    //     this.token = token\n    //\n    //     Object.setPrototypeOf(this, EarlyEndOfParseError.prototype)\n    //   }\n    //\n    //   getToken() {\n    //     return this.token\n    //   }\n    // }\n\n    function makePunctuationRule(type) {\n        return text => {\n            if (text.startsWith(type)) {\n                return { type, text: type };\n            }\n            else {\n                return null;\n            }\n        };\n    }\n    function getQuoted(text) {\n        let position = 0;\n        let char;\n        const mark = text[0];\n        let escaped = false;\n        if (mark !== '\\'' && mark !== '\"') {\n            return null;\n        }\n        while (position < text.length) {\n            position++;\n            char = text[position];\n            if (!escaped && char === mark) {\n                position++;\n                break;\n            }\n            escaped = !escaped && char === '\\\\';\n        }\n        if (char !== mark) {\n            throw new Error('Unterminated String');\n        }\n        return text.slice(0, position);\n    }\n    const identifierStartRegex = /[$_\\p{ID_Start}]|\\\\u\\p{Hex_Digit}{4}|\\\\u\\{0*(?:\\p{Hex_Digit}{1,5}|10\\p{Hex_Digit}{4})\\}/u;\n    // A hyphen is not technically allowed, but to keep it liberal for now,\n    //  adding it here\n    const identifierContinueRegex = /[$\\-\\p{ID_Continue}\\u200C\\u200D]|\\\\u\\p{Hex_Digit}{4}|\\\\u\\{0*(?:\\p{Hex_Digit}{1,5}|10\\p{Hex_Digit}{4})\\}/u;\n    function getIdentifier(text) {\n        let char = text[0];\n        if (!identifierStartRegex.test(char)) {\n            return null;\n        }\n        let position = 1;\n        do {\n            char = text[position];\n            if (!identifierContinueRegex.test(char)) {\n                break;\n            }\n            position++;\n        } while (position < text.length);\n        return text.slice(0, position);\n    }\n    // we are a bit more liberal than TypeScript here and allow `NaN`, `Infinity` and `-Infinity`\n    const numberRegex = /^(NaN|-?((\\d*\\.\\d+|\\d+)([Ee][+-]?\\d+)?|Infinity))/;\n    function getNumber(text) {\n        var _a, _b;\n        return (_b = (_a = numberRegex.exec(text)) === null || _a === void 0 ? void 0 : _a[0]) !== null && _b !== void 0 ? _b : null;\n    }\n    const identifierRule = text => {\n        const value = getIdentifier(text);\n        if (value == null) {\n            return null;\n        }\n        return {\n            type: 'Identifier',\n            text: value\n        };\n    };\n    function makeKeyWordRule(type) {\n        return text => {\n            if (!text.startsWith(type)) {\n                return null;\n            }\n            const prepends = text[type.length];\n            if (prepends !== undefined && identifierContinueRegex.test(prepends)) {\n                return null;\n            }\n            return {\n                type,\n                text: type\n            };\n        };\n    }\n    const stringValueRule = text => {\n        const value = getQuoted(text);\n        if (value == null) {\n            return null;\n        }\n        return {\n            type: 'StringValue',\n            text: value\n        };\n    };\n    const eofRule = text => {\n        if (text.length > 0) {\n            return null;\n        }\n        return {\n            type: 'EOF',\n            text: ''\n        };\n    };\n    const numberRule = text => {\n        const value = getNumber(text);\n        if (value === null) {\n            return null;\n        }\n        return {\n            type: 'Number',\n            text: value\n        };\n    };\n    const rules = [\n        eofRule,\n        makePunctuationRule('=>'),\n        makePunctuationRule('('),\n        makePunctuationRule(')'),\n        makePunctuationRule('{'),\n        makePunctuationRule('}'),\n        makePunctuationRule('['),\n        makePunctuationRule(']'),\n        makePunctuationRule('|'),\n        makePunctuationRule('&'),\n        makePunctuationRule('<'),\n        makePunctuationRule('>'),\n        makePunctuationRule(','),\n        makePunctuationRule(';'),\n        makePunctuationRule('*'),\n        makePunctuationRule('?'),\n        makePunctuationRule('!'),\n        makePunctuationRule('='),\n        makePunctuationRule(':'),\n        makePunctuationRule('...'),\n        makePunctuationRule('.'),\n        makePunctuationRule('#'),\n        makePunctuationRule('~'),\n        makePunctuationRule('/'),\n        makePunctuationRule('@'),\n        makeKeyWordRule('undefined'),\n        makeKeyWordRule('null'),\n        makeKeyWordRule('function'),\n        makeKeyWordRule('this'),\n        makeKeyWordRule('new'),\n        makeKeyWordRule('module'),\n        makeKeyWordRule('event'),\n        makeKeyWordRule('external'),\n        makeKeyWordRule('typeof'),\n        makeKeyWordRule('keyof'),\n        makeKeyWordRule('readonly'),\n        makeKeyWordRule('import'),\n        makeKeyWordRule('is'),\n        makeKeyWordRule('in'),\n        makeKeyWordRule('asserts'),\n        numberRule,\n        identifierRule,\n        stringValueRule\n    ];\n    const breakingWhitespaceRegex = /^\\s*\\n\\s*/;\n    class Lexer {\n        static create(text) {\n            const current = this.read(text);\n            text = current.text;\n            const next = this.read(text);\n            text = next.text;\n            return new Lexer(text, undefined, current.token, next.token);\n        }\n        constructor(text, previous, current, next) {\n            this.text = '';\n            this.text = text;\n            this.previous = previous;\n            this.current = current;\n            this.next = next;\n        }\n        static read(text, startOfLine = false) {\n            startOfLine = startOfLine || breakingWhitespaceRegex.test(text);\n            text = text.trim();\n            for (const rule of rules) {\n                const partial = rule(text);\n                if (partial !== null) {\n                    const token = Object.assign(Object.assign({}, partial), { startOfLine });\n                    text = text.slice(token.text.length);\n                    return { text, token };\n                }\n            }\n            throw new Error('Unexpected Token ' + text);\n        }\n        advance() {\n            const next = Lexer.read(this.text);\n            return new Lexer(next.text, this.current, this.next, next.token);\n        }\n    }\n\n    /**\n     * Throws an error if the provided result is not a {@link RootResult}\n     */\n    function assertRootResult(result) {\n        if (result === undefined) {\n            throw new Error('Unexpected undefined');\n        }\n        if (result.type === 'JsdocTypeKeyValue' || result.type === 'JsdocTypeParameterList' ||\n            result.type === 'JsdocTypeProperty' || result.type === 'JsdocTypeReadonlyProperty' ||\n            result.type === 'JsdocTypeObjectField' || result.type === 'JsdocTypeJsdocObjectField' ||\n            result.type === 'JsdocTypeIndexSignature' || result.type === 'JsdocTypeMappedType') {\n            throw new UnexpectedTypeError(result);\n        }\n        return result;\n    }\n    function assertPlainKeyValueOrRootResult(result) {\n        if (result.type === 'JsdocTypeKeyValue') {\n            return assertPlainKeyValueResult(result);\n        }\n        return assertRootResult(result);\n    }\n    function assertPlainKeyValueOrNameResult(result) {\n        if (result.type === 'JsdocTypeName') {\n            return result;\n        }\n        return assertPlainKeyValueResult(result);\n    }\n    function assertPlainKeyValueResult(result) {\n        if (result.type !== 'JsdocTypeKeyValue') {\n            throw new UnexpectedTypeError(result);\n        }\n        return result;\n    }\n    function assertNumberOrVariadicNameResult(result) {\n        var _a;\n        if (result.type === 'JsdocTypeVariadic') {\n            if (((_a = result.element) === null || _a === void 0 ? void 0 : _a.type) === 'JsdocTypeName') {\n                return result;\n            }\n            throw new UnexpectedTypeError(result);\n        }\n        if (result.type !== 'JsdocTypeNumber' && result.type !== 'JsdocTypeName') {\n            throw new UnexpectedTypeError(result);\n        }\n        return result;\n    }\n    function isSquaredProperty(result) {\n        return result.type === 'JsdocTypeIndexSignature' || result.type === 'JsdocTypeMappedType';\n    }\n\n    // higher precedence = higher importance\n    var Precedence;\n    (function (Precedence) {\n        Precedence[Precedence[\"ALL\"] = 0] = \"ALL\";\n        Precedence[Precedence[\"PARAMETER_LIST\"] = 1] = \"PARAMETER_LIST\";\n        Precedence[Precedence[\"OBJECT\"] = 2] = \"OBJECT\";\n        Precedence[Precedence[\"KEY_VALUE\"] = 3] = \"KEY_VALUE\";\n        Precedence[Precedence[\"INDEX_BRACKETS\"] = 4] = \"INDEX_BRACKETS\";\n        Precedence[Precedence[\"UNION\"] = 5] = \"UNION\";\n        Precedence[Precedence[\"INTERSECTION\"] = 6] = \"INTERSECTION\";\n        Precedence[Precedence[\"PREFIX\"] = 7] = \"PREFIX\";\n        Precedence[Precedence[\"INFIX\"] = 8] = \"INFIX\";\n        Precedence[Precedence[\"TUPLE\"] = 9] = \"TUPLE\";\n        Precedence[Precedence[\"SYMBOL\"] = 10] = \"SYMBOL\";\n        Precedence[Precedence[\"OPTIONAL\"] = 11] = \"OPTIONAL\";\n        Precedence[Precedence[\"NULLABLE\"] = 12] = \"NULLABLE\";\n        Precedence[Precedence[\"KEY_OF_TYPE_OF\"] = 13] = \"KEY_OF_TYPE_OF\";\n        Precedence[Precedence[\"FUNCTION\"] = 14] = \"FUNCTION\";\n        Precedence[Precedence[\"ARROW\"] = 15] = \"ARROW\";\n        Precedence[Precedence[\"ARRAY_BRACKETS\"] = 16] = \"ARRAY_BRACKETS\";\n        Precedence[Precedence[\"GENERIC\"] = 17] = \"GENERIC\";\n        Precedence[Precedence[\"NAME_PATH\"] = 18] = \"NAME_PATH\";\n        Precedence[Precedence[\"PARENTHESIS\"] = 19] = \"PARENTHESIS\";\n        Precedence[Precedence[\"SPECIAL_TYPES\"] = 20] = \"SPECIAL_TYPES\";\n    })(Precedence || (Precedence = {}));\n\n    class Parser {\n        constructor(grammar, textOrLexer, baseParser) {\n            this.grammar = grammar;\n            if (typeof textOrLexer === 'string') {\n                this._lexer = Lexer.create(textOrLexer);\n            }\n            else {\n                this._lexer = textOrLexer;\n            }\n            this.baseParser = baseParser;\n        }\n        get lexer() {\n            return this._lexer;\n        }\n        /**\n         * Parses a given string and throws an error if the parse ended before the end of the string.\n         */\n        parse() {\n            const result = this.parseType(Precedence.ALL);\n            if (this.lexer.current.type !== 'EOF') {\n                throw new EarlyEndOfParseError(this.lexer.current);\n            }\n            return result;\n        }\n        /**\n         * Parses with the current lexer and asserts that the result is a {@link RootResult}.\n         */\n        parseType(precedence) {\n            return assertRootResult(this.parseIntermediateType(precedence));\n        }\n        /**\n         * The main parsing function. First it tries to parse the current state in the prefix step, and then it continues\n         * to parse the state in the infix step.\n         */\n        parseIntermediateType(precedence) {\n            const result = this.tryParslets(null, precedence);\n            if (result === null) {\n                throw new NoParsletFoundError(this.lexer.current);\n            }\n            return this.parseInfixIntermediateType(result, precedence);\n        }\n        /**\n         * In the infix parsing step the parser continues to parse the current state with all parslets until none returns\n         * a result.\n         */\n        parseInfixIntermediateType(left, precedence) {\n            let result = this.tryParslets(left, precedence);\n            while (result !== null) {\n                left = result;\n                result = this.tryParslets(left, precedence);\n            }\n            return left;\n        }\n        /**\n         * Tries to parse the current state with all parslets in the grammar and returns the first non null result.\n         */\n        tryParslets(left, precedence) {\n            for (const parslet of this.grammar) {\n                const result = parslet(this, precedence, left);\n                if (result !== null) {\n                    return result;\n                }\n            }\n            return null;\n        }\n        /**\n         * If the given type equals the current type of the {@link Lexer} advance the lexer. Return true if the lexer was\n         * advanced.\n         */\n        consume(types) {\n            if (!Array.isArray(types)) {\n                types = [types];\n            }\n            if (types.includes(this.lexer.current.type)) {\n                this._lexer = this.lexer.advance();\n                return true;\n            }\n            else {\n                return false;\n            }\n        }\n        acceptLexerState(parser) {\n            this._lexer = parser.lexer;\n        }\n    }\n\n    function isQuestionMarkUnknownType(next) {\n        return next === 'EOF' || next === '|' || next === ',' || next === ')' || next === '>';\n    }\n\n    const nullableParslet = (parser, precedence, left) => {\n        const type = parser.lexer.current.type;\n        const next = parser.lexer.next.type;\n        const accept = ((left == null) && type === '?' && !isQuestionMarkUnknownType(next)) ||\n            ((left != null) && type === '?');\n        if (!accept) {\n            return null;\n        }\n        parser.consume('?');\n        if (left == null) {\n            return {\n                type: 'JsdocTypeNullable',\n                element: parser.parseType(Precedence.NULLABLE),\n                meta: {\n                    position: 'prefix'\n                }\n            };\n        }\n        else {\n            return {\n                type: 'JsdocTypeNullable',\n                element: assertRootResult(left),\n                meta: {\n                    position: 'suffix'\n                }\n            };\n        }\n    };\n\n    function composeParslet(options) {\n        const parslet = (parser, curPrecedence, left) => {\n            const type = parser.lexer.current.type;\n            const next = parser.lexer.next.type;\n            if (left === null) {\n                if ('parsePrefix' in options) {\n                    if (options.accept(type, next)) {\n                        return options.parsePrefix(parser);\n                    }\n                }\n            }\n            else {\n                if ('parseInfix' in options) {\n                    if (options.precedence > curPrecedence && options.accept(type, next)) {\n                        return options.parseInfix(parser, left);\n                    }\n                }\n            }\n            return null;\n        };\n        // for debugging\n        Object.defineProperty(parslet, 'name', {\n            value: options.name\n        });\n        return parslet;\n    }\n\n    const optionalParslet = composeParslet({\n        name: 'optionalParslet',\n        accept: type => type === '=',\n        precedence: Precedence.OPTIONAL,\n        parsePrefix: parser => {\n            parser.consume('=');\n            return {\n                type: 'JsdocTypeOptional',\n                element: parser.parseType(Precedence.OPTIONAL),\n                meta: {\n                    position: 'prefix'\n                }\n            };\n        },\n        parseInfix: (parser, left) => {\n            parser.consume('=');\n            return {\n                type: 'JsdocTypeOptional',\n                element: assertRootResult(left),\n                meta: {\n                    position: 'suffix'\n                }\n            };\n        }\n    });\n\n    const numberParslet = composeParslet({\n        name: 'numberParslet',\n        accept: type => type === 'Number',\n        parsePrefix: parser => {\n            const value = parseFloat(parser.lexer.current.text);\n            parser.consume('Number');\n            return {\n                type: 'JsdocTypeNumber',\n                value\n            };\n        }\n    });\n\n    const parenthesisParslet = composeParslet({\n        name: 'parenthesisParslet',\n        accept: type => type === '(',\n        parsePrefix: parser => {\n            parser.consume('(');\n            if (parser.consume(')')) {\n                return {\n                    type: 'JsdocTypeParameterList',\n                    elements: []\n                };\n            }\n            const result = parser.parseIntermediateType(Precedence.ALL);\n            if (!parser.consume(')')) {\n                throw new Error('Unterminated parenthesis');\n            }\n            if (result.type === 'JsdocTypeParameterList') {\n                return result;\n            }\n            else if (result.type === 'JsdocTypeKeyValue') {\n                return {\n                    type: 'JsdocTypeParameterList',\n                    elements: [result]\n                };\n            }\n            return {\n                type: 'JsdocTypeParenthesis',\n                element: assertRootResult(result)\n            };\n        }\n    });\n\n    const specialTypesParslet = composeParslet({\n        name: 'specialTypesParslet',\n        accept: (type, next) => (type === '?' && isQuestionMarkUnknownType(next)) ||\n            type === 'null' || type === 'undefined' || type === '*',\n        parsePrefix: parser => {\n            if (parser.consume('null')) {\n                return {\n                    type: 'JsdocTypeNull'\n                };\n            }\n            if (parser.consume('undefined')) {\n                return {\n                    type: 'JsdocTypeUndefined'\n                };\n            }\n            if (parser.consume('*')) {\n                return {\n                    type: 'JsdocTypeAny'\n                };\n            }\n            if (parser.consume('?')) {\n                return {\n                    type: 'JsdocTypeUnknown'\n                };\n            }\n            throw new Error('Unacceptable token: ' + parser.lexer.current.text);\n        }\n    });\n\n    const notNullableParslet = composeParslet({\n        name: 'notNullableParslet',\n        accept: type => type === '!',\n        precedence: Precedence.NULLABLE,\n        parsePrefix: parser => {\n            parser.consume('!');\n            return {\n                type: 'JsdocTypeNotNullable',\n                element: parser.parseType(Precedence.NULLABLE),\n                meta: {\n                    position: 'prefix'\n                }\n            };\n        },\n        parseInfix: (parser, left) => {\n            parser.consume('!');\n            return {\n                type: 'JsdocTypeNotNullable',\n                element: assertRootResult(left),\n                meta: {\n                    position: 'suffix'\n                }\n            };\n        }\n    });\n\n    function createParameterListParslet({ allowTrailingComma }) {\n        return composeParslet({\n            name: 'parameterListParslet',\n            accept: type => type === ',',\n            precedence: Precedence.PARAMETER_LIST,\n            parseInfix: (parser, left) => {\n                const elements = [\n                    assertPlainKeyValueOrRootResult(left)\n                ];\n                parser.consume(',');\n                do {\n                    try {\n                        const next = parser.parseIntermediateType(Precedence.PARAMETER_LIST);\n                        elements.push(assertPlainKeyValueOrRootResult(next));\n                    }\n                    catch (e) {\n                        if (allowTrailingComma && e instanceof NoParsletFoundError) {\n                            break;\n                        }\n                        else {\n                            throw e;\n                        }\n                    }\n                } while (parser.consume(','));\n                if (elements.length > 0 && elements.slice(0, -1).some(e => e.type === 'JsdocTypeVariadic')) {\n                    throw new Error('Only the last parameter may be a rest parameter');\n                }\n                return {\n                    type: 'JsdocTypeParameterList',\n                    elements\n                };\n            }\n        });\n    }\n\n    const genericParslet = composeParslet({\n        name: 'genericParslet',\n        accept: (type, next) => type === '<' || (type === '.' && next === '<'),\n        precedence: Precedence.GENERIC,\n        parseInfix: (parser, left) => {\n            const dot = parser.consume('.');\n            parser.consume('<');\n            const objects = [];\n            do {\n                objects.push(parser.parseType(Precedence.PARAMETER_LIST));\n            } while (parser.consume(','));\n            if (!parser.consume('>')) {\n                throw new Error('Unterminated generic parameter list');\n            }\n            return {\n                type: 'JsdocTypeGeneric',\n                left: assertRootResult(left),\n                elements: objects,\n                meta: {\n                    brackets: 'angle',\n                    dot\n                }\n            };\n        }\n    });\n\n    const unionParslet = composeParslet({\n        name: 'unionParslet',\n        accept: type => type === '|',\n        precedence: Precedence.UNION,\n        parseInfix: (parser, left) => {\n            parser.consume('|');\n            const elements = [];\n            do {\n                elements.push(parser.parseType(Precedence.UNION));\n            } while (parser.consume('|'));\n            return {\n                type: 'JsdocTypeUnion',\n                elements: [assertRootResult(left), ...elements]\n            };\n        }\n    });\n\n    const baseGrammar = [\n        nullableParslet,\n        optionalParslet,\n        numberParslet,\n        parenthesisParslet,\n        specialTypesParslet,\n        notNullableParslet,\n        createParameterListParslet({\n            allowTrailingComma: true\n        }),\n        genericParslet,\n        unionParslet,\n        optionalParslet\n    ];\n\n    function createNamePathParslet({ allowSquareBracketsOnAnyType, allowJsdocNamePaths, pathGrammar }) {\n        return function namePathParslet(parser, precedence, left) {\n            if ((left == null) || precedence >= Precedence.NAME_PATH) {\n                return null;\n            }\n            const type = parser.lexer.current.type;\n            const next = parser.lexer.next.type;\n            const accept = (type === '.' && next !== '<') ||\n                (type === '[' && (allowSquareBracketsOnAnyType || left.type === 'JsdocTypeName')) ||\n                (allowJsdocNamePaths && (type === '~' || type === '#'));\n            if (!accept) {\n                return null;\n            }\n            let pathType;\n            let brackets = false;\n            if (parser.consume('.')) {\n                pathType = 'property';\n            }\n            else if (parser.consume('[')) {\n                pathType = 'property-brackets';\n                brackets = true;\n            }\n            else if (parser.consume('~')) {\n                pathType = 'inner';\n            }\n            else {\n                parser.consume('#');\n                pathType = 'instance';\n            }\n            const pathParser = pathGrammar !== null\n                ? new Parser(pathGrammar, parser.lexer, parser)\n                : parser;\n            const parsed = pathParser.parseIntermediateType(Precedence.NAME_PATH);\n            parser.acceptLexerState(pathParser);\n            let right;\n            switch (parsed.type) {\n                case 'JsdocTypeName':\n                    right = {\n                        type: 'JsdocTypeProperty',\n                        value: parsed.value,\n                        meta: {\n                            quote: undefined\n                        }\n                    };\n                    break;\n                case 'JsdocTypeNumber':\n                    right = {\n                        type: 'JsdocTypeProperty',\n                        value: parsed.value.toString(10),\n                        meta: {\n                            quote: undefined\n                        }\n                    };\n                    break;\n                case 'JsdocTypeStringValue':\n                    right = {\n                        type: 'JsdocTypeProperty',\n                        value: parsed.value,\n                        meta: {\n                            quote: parsed.meta.quote\n                        }\n                    };\n                    break;\n                case 'JsdocTypeSpecialNamePath':\n                    if (parsed.specialType === 'event') {\n                        right = parsed;\n                    }\n                    else {\n                        throw new UnexpectedTypeError(parsed, 'Type \\'JsdocTypeSpecialNamePath\\' is only allowed with specialType \\'event\\'');\n                    }\n                    break;\n                default:\n                    throw new UnexpectedTypeError(parsed, 'Expecting \\'JsdocTypeName\\', \\'JsdocTypeNumber\\', \\'JsdocStringValue\\' or \\'JsdocTypeSpecialNamePath\\'');\n            }\n            if (brackets && !parser.consume(']')) {\n                const token = parser.lexer.current;\n                throw new Error(`Unterminated square brackets. Next token is '${token.type}' ` +\n                    `with text '${token.text}'`);\n            }\n            return {\n                type: 'JsdocTypeNamePath',\n                left: assertRootResult(left),\n                right,\n                pathType\n            };\n        };\n    }\n\n    function createNameParslet({ allowedAdditionalTokens }) {\n        return composeParslet({\n            name: 'nameParslet',\n            accept: type => type === 'Identifier' || type === 'this' || type === 'new' || allowedAdditionalTokens.includes(type),\n            parsePrefix: parser => {\n                const { type, text } = parser.lexer.current;\n                parser.consume(type);\n                return {\n                    type: 'JsdocTypeName',\n                    value: text\n                };\n            }\n        });\n    }\n\n    const stringValueParslet = composeParslet({\n        name: 'stringValueParslet',\n        accept: type => type === 'StringValue',\n        parsePrefix: parser => {\n            const text = parser.lexer.current.text;\n            parser.consume('StringValue');\n            return {\n                type: 'JsdocTypeStringValue',\n                value: text.slice(1, -1),\n                meta: {\n                    quote: text[0] === '\\'' ? 'single' : 'double'\n                }\n            };\n        }\n    });\n\n    function createSpecialNamePathParslet({ pathGrammar, allowedTypes }) {\n        return composeParslet({\n            name: 'specialNamePathParslet',\n            accept: type => allowedTypes.includes(type),\n            parsePrefix: parser => {\n                const type = parser.lexer.current.type;\n                parser.consume(type);\n                if (!parser.consume(':')) {\n                    return {\n                        type: 'JsdocTypeName',\n                        value: type\n                    };\n                }\n                let result;\n                let token = parser.lexer.current;\n                if (parser.consume('StringValue')) {\n                    result = {\n                        type: 'JsdocTypeSpecialNamePath',\n                        value: token.text.slice(1, -1),\n                        specialType: type,\n                        meta: {\n                            quote: token.text[0] === '\\'' ? 'single' : 'double'\n                        }\n                    };\n                }\n                else {\n                    let value = '';\n                    const allowed = ['Identifier', '@', '/'];\n                    while (allowed.some(type => parser.consume(type))) {\n                        value += token.text;\n                        token = parser.lexer.current;\n                    }\n                    result = {\n                        type: 'JsdocTypeSpecialNamePath',\n                        value,\n                        specialType: type,\n                        meta: {\n                            quote: undefined\n                        }\n                    };\n                }\n                const moduleParser = new Parser(pathGrammar, parser.lexer, parser);\n                const moduleResult = moduleParser.parseInfixIntermediateType(result, Precedence.ALL);\n                parser.acceptLexerState(moduleParser);\n                return assertRootResult(moduleResult);\n            }\n        });\n    }\n\n    const basePathGrammar = [\n        createNameParslet({\n            allowedAdditionalTokens: ['external', 'module']\n        }),\n        stringValueParslet,\n        numberParslet,\n        createNamePathParslet({\n            allowSquareBracketsOnAnyType: false,\n            allowJsdocNamePaths: true,\n            pathGrammar: null\n        })\n    ];\n    const pathGrammar = [\n        ...basePathGrammar,\n        createSpecialNamePathParslet({\n            allowedTypes: ['event'],\n            pathGrammar: basePathGrammar\n        })\n    ];\n\n    function getParameters(value) {\n        let parameters;\n        if (value.type === 'JsdocTypeParameterList') {\n            parameters = value.elements;\n        }\n        else if (value.type === 'JsdocTypeParenthesis') {\n            parameters = [value.element];\n        }\n        else {\n            throw new UnexpectedTypeError(value);\n        }\n        return parameters.map(p => assertPlainKeyValueOrRootResult(p));\n    }\n    function getUnnamedParameters(value) {\n        const parameters = getParameters(value);\n        if (parameters.some(p => p.type === 'JsdocTypeKeyValue')) {\n            throw new Error('No parameter should be named');\n        }\n        return parameters;\n    }\n    function createFunctionParslet({ allowNamedParameters, allowNoReturnType, allowWithoutParenthesis, allowNewAsFunctionKeyword }) {\n        return composeParslet({\n            name: 'functionParslet',\n            accept: (type, next) => type === 'function' || (allowNewAsFunctionKeyword && type === 'new' && next === '('),\n            parsePrefix: parser => {\n                const newKeyword = parser.consume('new');\n                parser.consume('function');\n                const hasParenthesis = parser.lexer.current.type === '(';\n                if (!hasParenthesis) {\n                    if (!allowWithoutParenthesis) {\n                        throw new Error('function is missing parameter list');\n                    }\n                    return {\n                        type: 'JsdocTypeName',\n                        value: 'function'\n                    };\n                }\n                let result = {\n                    type: 'JsdocTypeFunction',\n                    parameters: [],\n                    arrow: false,\n                    constructor: newKeyword,\n                    parenthesis: hasParenthesis\n                };\n                const value = parser.parseIntermediateType(Precedence.FUNCTION);\n                if (allowNamedParameters === undefined) {\n                    result.parameters = getUnnamedParameters(value);\n                }\n                else if (newKeyword && value.type === 'JsdocTypeFunction' && value.arrow) {\n                    result = value;\n                    result.constructor = true;\n                    return result;\n                }\n                else {\n                    result.parameters = getParameters(value);\n                    for (const p of result.parameters) {\n                        if (p.type === 'JsdocTypeKeyValue' && (!allowNamedParameters.includes(p.key))) {\n                            throw new Error(`only allowed named parameters are ${allowNamedParameters.join(', ')} but got ${p.type}`);\n                        }\n                    }\n                }\n                if (parser.consume(':')) {\n                    result.returnType = parser.parseType(Precedence.PREFIX);\n                }\n                else {\n                    if (!allowNoReturnType) {\n                        throw new Error('function is missing return type');\n                    }\n                }\n                return result;\n            }\n        });\n    }\n\n    function createVariadicParslet({ allowPostfix, allowEnclosingBrackets }) {\n        return composeParslet({\n            name: 'variadicParslet',\n            accept: type => type === '...',\n            precedence: Precedence.PREFIX,\n            parsePrefix: parser => {\n                parser.consume('...');\n                const brackets = allowEnclosingBrackets && parser.consume('[');\n                try {\n                    const element = parser.parseType(Precedence.PREFIX);\n                    if (brackets && !parser.consume(']')) {\n                        throw new Error('Unterminated variadic type. Missing \\']\\'');\n                    }\n                    return {\n                        type: 'JsdocTypeVariadic',\n                        element: assertRootResult(element),\n                        meta: {\n                            position: 'prefix',\n                            squareBrackets: brackets\n                        }\n                    };\n                }\n                catch (e) {\n                    if (e instanceof NoParsletFoundError) {\n                        if (brackets) {\n                            throw new Error('Empty square brackets for variadic are not allowed.');\n                        }\n                        return {\n                            type: 'JsdocTypeVariadic',\n                            meta: {\n                                position: undefined,\n                                squareBrackets: false\n                            }\n                        };\n                    }\n                    else {\n                        throw e;\n                    }\n                }\n            },\n            parseInfix: allowPostfix\n                ? (parser, left) => {\n                    parser.consume('...');\n                    return {\n                        type: 'JsdocTypeVariadic',\n                        element: assertRootResult(left),\n                        meta: {\n                            position: 'suffix',\n                            squareBrackets: false\n                        }\n                    };\n                }\n                : undefined\n        });\n    }\n\n    const symbolParslet = composeParslet({\n        name: 'symbolParslet',\n        accept: type => type === '(',\n        precedence: Precedence.SYMBOL,\n        parseInfix: (parser, left) => {\n            if (left.type !== 'JsdocTypeName') {\n                throw new Error('Symbol expects a name on the left side. (Reacting on \\'(\\')');\n            }\n            parser.consume('(');\n            const result = {\n                type: 'JsdocTypeSymbol',\n                value: left.value\n            };\n            if (!parser.consume(')')) {\n                const next = parser.parseIntermediateType(Precedence.SYMBOL);\n                result.element = assertNumberOrVariadicNameResult(next);\n                if (!parser.consume(')')) {\n                    throw new Error('Symbol does not end after value');\n                }\n            }\n            return result;\n        }\n    });\n\n    const arrayBracketsParslet = composeParslet({\n        name: 'arrayBracketsParslet',\n        precedence: Precedence.ARRAY_BRACKETS,\n        accept: (type, next) => type === '[' && next === ']',\n        parseInfix: (parser, left) => {\n            parser.consume('[');\n            parser.consume(']');\n            return {\n                type: 'JsdocTypeGeneric',\n                left: {\n                    type: 'JsdocTypeName',\n                    value: 'Array'\n                },\n                elements: [\n                    assertRootResult(left)\n                ],\n                meta: {\n                    brackets: 'square',\n                    dot: false\n                }\n            };\n        }\n    });\n\n    function createObjectParslet({ objectFieldGrammar, allowKeyTypes }) {\n        return composeParslet({\n            name: 'objectParslet',\n            accept: type => type === '{',\n            parsePrefix: parser => {\n                parser.consume('{');\n                const result = {\n                    type: 'JsdocTypeObject',\n                    meta: {\n                        separator: 'comma'\n                    },\n                    elements: []\n                };\n                if (!parser.consume('}')) {\n                    let separator;\n                    const fieldParser = new Parser(objectFieldGrammar, parser.lexer, parser);\n                    while (true) {\n                        fieldParser.acceptLexerState(parser);\n                        let field = fieldParser.parseIntermediateType(Precedence.OBJECT);\n                        parser.acceptLexerState(fieldParser);\n                        if (field === undefined && allowKeyTypes) {\n                            field = parser.parseIntermediateType(Precedence.OBJECT);\n                        }\n                        let optional = false;\n                        if (field.type === 'JsdocTypeNullable') {\n                            optional = true;\n                            field = field.element;\n                        }\n                        if (field.type === 'JsdocTypeNumber' || field.type === 'JsdocTypeName' || field.type === 'JsdocTypeStringValue') {\n                            let quote;\n                            if (field.type === 'JsdocTypeStringValue') {\n                                quote = field.meta.quote;\n                            }\n                            result.elements.push({\n                                type: 'JsdocTypeObjectField',\n                                key: field.value.toString(),\n                                right: undefined,\n                                optional,\n                                readonly: false,\n                                meta: {\n                                    quote\n                                }\n                            });\n                        }\n                        else if (field.type === 'JsdocTypeObjectField' || field.type === 'JsdocTypeJsdocObjectField') {\n                            result.elements.push(field);\n                        }\n                        else {\n                            throw new UnexpectedTypeError(field);\n                        }\n                        if (parser.lexer.current.startOfLine) {\n                            separator = 'linebreak';\n                        }\n                        else if (parser.consume(',')) {\n                            separator = 'comma';\n                        }\n                        else if (parser.consume(';')) {\n                            separator = 'semicolon';\n                        }\n                        else {\n                            break;\n                        }\n                        const type = parser.lexer.current.type;\n                        if (type === '}') {\n                            break;\n                        }\n                    }\n                    result.meta.separator = separator !== null && separator !== void 0 ? separator : 'comma'; // TODO: use undefined here\n                    if (!parser.consume('}')) {\n                        throw new Error('Unterminated record type. Missing \\'}\\'');\n                    }\n                }\n                return result;\n            }\n        });\n    }\n\n    function createObjectFieldParslet({ allowSquaredProperties, allowKeyTypes, allowReadonly, allowOptional }) {\n        return composeParslet({\n            name: 'objectFieldParslet',\n            precedence: Precedence.KEY_VALUE,\n            accept: type => type === ':',\n            parseInfix: (parser, left) => {\n                var _a;\n                let optional = false;\n                let readonlyProperty = false;\n                if (allowOptional && left.type === 'JsdocTypeNullable') {\n                    optional = true;\n                    left = left.element;\n                }\n                if (allowReadonly && left.type === 'JsdocTypeReadonlyProperty') {\n                    readonlyProperty = true;\n                    left = left.element;\n                }\n                // object parslet uses a special grammar and for the value we want to switch back to the parent\n                const parentParser = (_a = parser.baseParser) !== null && _a !== void 0 ? _a : parser;\n                parentParser.acceptLexerState(parser);\n                if (left.type === 'JsdocTypeNumber' || left.type === 'JsdocTypeName' || left.type === 'JsdocTypeStringValue' ||\n                    isSquaredProperty(left)) {\n                    if (isSquaredProperty(left) && !allowSquaredProperties) {\n                        throw new UnexpectedTypeError(left);\n                    }\n                    parentParser.consume(':');\n                    let quote;\n                    if (left.type === 'JsdocTypeStringValue') {\n                        quote = left.meta.quote;\n                    }\n                    const right = parentParser.parseType(Precedence.KEY_VALUE);\n                    parser.acceptLexerState(parentParser);\n                    return {\n                        type: 'JsdocTypeObjectField',\n                        key: isSquaredProperty(left) ? left : left.value.toString(),\n                        right,\n                        optional,\n                        readonly: readonlyProperty,\n                        meta: {\n                            quote\n                        }\n                    };\n                }\n                else {\n                    if (!allowKeyTypes) {\n                        throw new UnexpectedTypeError(left);\n                    }\n                    parentParser.consume(':');\n                    const right = parentParser.parseType(Precedence.KEY_VALUE);\n                    parser.acceptLexerState(parentParser);\n                    return {\n                        type: 'JsdocTypeJsdocObjectField',\n                        left: assertRootResult(left),\n                        right\n                    };\n                }\n            }\n        });\n    }\n\n    function createKeyValueParslet({ allowOptional, allowVariadic }) {\n        return composeParslet({\n            name: 'keyValueParslet',\n            precedence: Precedence.KEY_VALUE,\n            accept: type => type === ':',\n            parseInfix: (parser, left) => {\n                let optional = false;\n                let variadic = false;\n                if (allowOptional && left.type === 'JsdocTypeNullable') {\n                    optional = true;\n                    left = left.element;\n                }\n                if (allowVariadic && left.type === 'JsdocTypeVariadic' && left.element !== undefined) {\n                    variadic = true;\n                    left = left.element;\n                }\n                if (left.type !== 'JsdocTypeName') {\n                    throw new UnexpectedTypeError(left);\n                }\n                parser.consume(':');\n                const right = parser.parseType(Precedence.KEY_VALUE);\n                return {\n                    type: 'JsdocTypeKeyValue',\n                    key: left.value,\n                    right,\n                    optional,\n                    variadic\n                };\n            }\n        });\n    }\n\n    const jsdocBaseGrammar = [\n        ...baseGrammar,\n        createFunctionParslet({\n            allowWithoutParenthesis: true,\n            allowNamedParameters: ['this', 'new'],\n            allowNoReturnType: true,\n            allowNewAsFunctionKeyword: false\n        }),\n        stringValueParslet,\n        createSpecialNamePathParslet({\n            allowedTypes: ['module', 'external', 'event'],\n            pathGrammar\n        }),\n        createVariadicParslet({\n            allowEnclosingBrackets: true,\n            allowPostfix: true\n        }),\n        createNameParslet({\n            allowedAdditionalTokens: ['keyof']\n        }),\n        symbolParslet,\n        arrayBracketsParslet,\n        createNamePathParslet({\n            allowSquareBracketsOnAnyType: false,\n            allowJsdocNamePaths: true,\n            pathGrammar\n        })\n    ];\n    const jsdocGrammar = [\n        ...jsdocBaseGrammar,\n        createObjectParslet({\n            // jsdoc syntax allows full types as keys, so we need to pull in the full grammar here\n            // we leave out the object type deliberately\n            objectFieldGrammar: [\n                createNameParslet({\n                    allowedAdditionalTokens: ['module', 'in']\n                }),\n                createObjectFieldParslet({\n                    allowSquaredProperties: false,\n                    allowKeyTypes: true,\n                    allowOptional: false,\n                    allowReadonly: false\n                }),\n                ...jsdocBaseGrammar\n            ],\n            allowKeyTypes: true\n        }),\n        createKeyValueParslet({\n            allowOptional: true,\n            allowVariadic: true\n        })\n    ];\n\n    const typeOfParslet = composeParslet({\n        name: 'typeOfParslet',\n        accept: type => type === 'typeof',\n        parsePrefix: parser => {\n            parser.consume('typeof');\n            return {\n                type: 'JsdocTypeTypeof',\n                element: assertRootResult(parser.parseType(Precedence.KEY_OF_TYPE_OF))\n            };\n        }\n    });\n\n    const objectFieldGrammar$1 = [\n        createNameParslet({\n            allowedAdditionalTokens: ['module', 'keyof', 'event', 'external', 'in']\n        }),\n        nullableParslet,\n        optionalParslet,\n        stringValueParslet,\n        numberParslet,\n        createObjectFieldParslet({\n            allowSquaredProperties: false,\n            allowKeyTypes: false,\n            allowOptional: false,\n            allowReadonly: false\n        })\n    ];\n    const closureGrammar = [\n        ...baseGrammar,\n        createObjectParslet({\n            allowKeyTypes: false,\n            objectFieldGrammar: objectFieldGrammar$1\n        }),\n        createNameParslet({\n            allowedAdditionalTokens: ['event', 'external', 'in']\n        }),\n        typeOfParslet,\n        createFunctionParslet({\n            allowWithoutParenthesis: false,\n            allowNamedParameters: ['this', 'new'],\n            allowNoReturnType: true,\n            allowNewAsFunctionKeyword: false\n        }),\n        createVariadicParslet({\n            allowEnclosingBrackets: false,\n            allowPostfix: false\n        }),\n        // additional name parslet is needed for some special cases\n        createNameParslet({\n            allowedAdditionalTokens: ['keyof']\n        }),\n        createSpecialNamePathParslet({\n            allowedTypes: ['module'],\n            pathGrammar\n        }),\n        createNamePathParslet({\n            allowSquareBracketsOnAnyType: false,\n            allowJsdocNamePaths: true,\n            pathGrammar\n        }),\n        createKeyValueParslet({\n            allowOptional: false,\n            allowVariadic: false\n        }),\n        symbolParslet\n    ];\n\n    const assertsParslet = composeParslet({\n        name: 'assertsParslet',\n        accept: type => type === 'asserts',\n        parsePrefix: (parser) => {\n            parser.consume('asserts');\n            const left = parser.parseIntermediateType(Precedence.SYMBOL);\n            if (left.type !== 'JsdocTypeName') {\n                throw new UnexpectedTypeError(left, 'A typescript asserts always has to have a name on the left side.');\n            }\n            parser.consume('is');\n            return {\n                type: 'JsdocTypeAsserts',\n                left,\n                right: assertRootResult(parser.parseIntermediateType(Precedence.INFIX))\n            };\n        }\n    });\n\n    function createTupleParslet({ allowQuestionMark }) {\n        return composeParslet({\n            name: 'tupleParslet',\n            accept: type => type === '[',\n            parsePrefix: parser => {\n                parser.consume('[');\n                const result = {\n                    type: 'JsdocTypeTuple',\n                    elements: []\n                };\n                if (parser.consume(']')) {\n                    return result;\n                }\n                const typeList = parser.parseIntermediateType(Precedence.ALL);\n                if (typeList.type === 'JsdocTypeParameterList') {\n                    if (typeList.elements[0].type === 'JsdocTypeKeyValue') {\n                        result.elements = typeList.elements.map(assertPlainKeyValueResult);\n                    }\n                    else {\n                        result.elements = typeList.elements.map(assertRootResult);\n                    }\n                }\n                else {\n                    if (typeList.type === 'JsdocTypeKeyValue') {\n                        result.elements = [assertPlainKeyValueResult(typeList)];\n                    }\n                    else {\n                        result.elements = [assertRootResult(typeList)];\n                    }\n                }\n                if (!parser.consume(']')) {\n                    throw new Error('Unterminated \\'[\\'');\n                }\n                if (!allowQuestionMark && result.elements.some((e) => e.type === 'JsdocTypeUnknown')) {\n                    throw new Error('Question mark in tuple not allowed');\n                }\n                return result;\n            }\n        });\n    }\n\n    const keyOfParslet = composeParslet({\n        name: 'keyOfParslet',\n        accept: type => type === 'keyof',\n        parsePrefix: parser => {\n            parser.consume('keyof');\n            return {\n                type: 'JsdocTypeKeyof',\n                element: assertRootResult(parser.parseType(Precedence.KEY_OF_TYPE_OF))\n            };\n        }\n    });\n\n    const importParslet = composeParslet({\n        name: 'importParslet',\n        accept: type => type === 'import',\n        parsePrefix: parser => {\n            parser.consume('import');\n            if (!parser.consume('(')) {\n                throw new Error('Missing parenthesis after import keyword');\n            }\n            const path = parser.parseType(Precedence.PREFIX);\n            if (path.type !== 'JsdocTypeStringValue') {\n                throw new Error('Only string values are allowed as paths for imports');\n            }\n            if (!parser.consume(')')) {\n                throw new Error('Missing closing parenthesis after import keyword');\n            }\n            return {\n                type: 'JsdocTypeImport',\n                element: path\n            };\n        }\n    });\n\n    const readonlyPropertyParslet = composeParslet({\n        name: 'readonlyPropertyParslet',\n        accept: type => type === 'readonly',\n        parsePrefix: parser => {\n            parser.consume('readonly');\n            return {\n                type: 'JsdocTypeReadonlyProperty',\n                element: parser.parseType(Precedence.KEY_VALUE)\n            };\n        }\n    });\n\n    const arrowFunctionParslet = composeParslet({\n        name: 'arrowFunctionParslet',\n        precedence: Precedence.ARROW,\n        accept: type => type === '=>',\n        parseInfix: (parser, left) => {\n            parser.consume('=>');\n            return {\n                type: 'JsdocTypeFunction',\n                parameters: getParameters(left).map(assertPlainKeyValueOrNameResult),\n                arrow: true,\n                constructor: false,\n                parenthesis: true,\n                returnType: parser.parseType(Precedence.OBJECT)\n            };\n        }\n    });\n\n    const intersectionParslet = composeParslet({\n        name: 'intersectionParslet',\n        accept: type => type === '&',\n        precedence: Precedence.INTERSECTION,\n        parseInfix: (parser, left) => {\n            parser.consume('&');\n            const elements = [];\n            do {\n                elements.push(parser.parseType(Precedence.INTERSECTION));\n            } while (parser.consume('&'));\n            return {\n                type: 'JsdocTypeIntersection',\n                elements: [assertRootResult(left), ...elements]\n            };\n        }\n    });\n\n    const predicateParslet = composeParslet({\n        name: 'predicateParslet',\n        precedence: Precedence.INFIX,\n        accept: type => type === 'is',\n        parseInfix: (parser, left) => {\n            if (left.type !== 'JsdocTypeName') {\n                throw new UnexpectedTypeError(left, 'A typescript predicate always has to have a name on the left side.');\n            }\n            parser.consume('is');\n            return {\n                type: 'JsdocTypePredicate',\n                left,\n                right: assertRootResult(parser.parseIntermediateType(Precedence.INFIX))\n            };\n        }\n    });\n\n    const objectSquaredPropertyParslet = composeParslet({\n        name: 'objectSquareBracketPropertyParslet',\n        accept: type => type === '[',\n        parsePrefix: parser => {\n            if (parser.baseParser === undefined) {\n                throw new Error('Only allowed inside object grammar');\n            }\n            parser.consume('[');\n            const key = parser.lexer.current.text;\n            parser.consume('Identifier');\n            let result;\n            if (parser.consume(':')) {\n                const parentParser = parser.baseParser;\n                parentParser.acceptLexerState(parser);\n                result = {\n                    type: 'JsdocTypeIndexSignature',\n                    key,\n                    right: parentParser.parseType(Precedence.INDEX_BRACKETS)\n                };\n                parser.acceptLexerState(parentParser);\n            }\n            else if (parser.consume('in')) {\n                const parentParser = parser.baseParser;\n                parentParser.acceptLexerState(parser);\n                result = {\n                    type: 'JsdocTypeMappedType',\n                    key,\n                    right: parentParser.parseType(Precedence.ARRAY_BRACKETS)\n                };\n                parser.acceptLexerState(parentParser);\n            }\n            else {\n                throw new Error('Missing \\':\\' or \\'in\\' inside square bracketed property.');\n            }\n            if (!parser.consume(']')) {\n                throw new Error('Unterminated square brackets');\n            }\n            return result;\n        }\n    });\n\n    const objectFieldGrammar = [\n        readonlyPropertyParslet,\n        createNameParslet({\n            allowedAdditionalTokens: ['module', 'event', 'keyof', 'event', 'external', 'in']\n        }),\n        nullableParslet,\n        optionalParslet,\n        stringValueParslet,\n        numberParslet,\n        createObjectFieldParslet({\n            allowSquaredProperties: true,\n            allowKeyTypes: false,\n            allowOptional: true,\n            allowReadonly: true\n        }),\n        objectSquaredPropertyParslet\n    ];\n    const typescriptGrammar = [\n        ...baseGrammar,\n        createObjectParslet({\n            allowKeyTypes: false,\n            objectFieldGrammar\n        }),\n        typeOfParslet,\n        keyOfParslet,\n        importParslet,\n        stringValueParslet,\n        createFunctionParslet({\n            allowWithoutParenthesis: true,\n            allowNoReturnType: false,\n            allowNamedParameters: ['this', 'new', 'args'],\n            allowNewAsFunctionKeyword: true\n        }),\n        createTupleParslet({\n            allowQuestionMark: false\n        }),\n        createVariadicParslet({\n            allowEnclosingBrackets: false,\n            allowPostfix: false\n        }),\n        assertsParslet,\n        createNameParslet({\n            allowedAdditionalTokens: ['event', 'external', 'in']\n        }),\n        createSpecialNamePathParslet({\n            allowedTypes: ['module'],\n            pathGrammar\n        }),\n        arrayBracketsParslet,\n        arrowFunctionParslet,\n        createNamePathParslet({\n            allowSquareBracketsOnAnyType: true,\n            allowJsdocNamePaths: false,\n            pathGrammar\n        }),\n        intersectionParslet,\n        predicateParslet,\n        createKeyValueParslet({\n            allowVariadic: true,\n            allowOptional: true\n        })\n    ];\n\n    /**\n     * This function parses the given expression in the given mode and produces a {@link RootResult}.\n     * @param expression\n     * @param mode\n     */\n    function parse(expression, mode) {\n        switch (mode) {\n            case 'closure':\n                return (new Parser(closureGrammar, expression)).parse();\n            case 'jsdoc':\n                return (new Parser(jsdocGrammar, expression)).parse();\n            case 'typescript':\n                return (new Parser(typescriptGrammar, expression)).parse();\n        }\n    }\n    /**\n     * This function tries to parse the given expression in multiple modes and returns the first successful\n     * {@link RootResult}. By default it tries `'typescript'`, `'closure'` and `'jsdoc'` in this order. If\n     * no mode was successful it throws the error that was produced by the last parsing attempt.\n     * @param expression\n     * @param modes\n     */\n    function tryParse(expression, modes = ['typescript', 'closure', 'jsdoc']) {\n        let error;\n        for (const mode of modes) {\n            try {\n                return parse(expression, mode);\n            }\n            catch (e) {\n                error = e;\n            }\n        }\n        throw error;\n    }\n\n    function transform(rules, parseResult) {\n        const rule = rules[parseResult.type];\n        if (rule === undefined) {\n            throw new Error(`In this set of transform rules exists no rule for type ${parseResult.type}.`);\n        }\n        return rule(parseResult, aParseResult => transform(rules, aParseResult));\n    }\n    function notAvailableTransform(parseResult) {\n        throw new Error('This transform is not available. Are you trying the correct parsing mode?');\n    }\n    function extractSpecialParams(source) {\n        const result = {\n            params: []\n        };\n        for (const param of source.parameters) {\n            if (param.type === 'JsdocTypeKeyValue') {\n                if (param.key === 'this') {\n                    result.this = param.right;\n                }\n                else if (param.key === 'new') {\n                    result.new = param.right;\n                }\n                else {\n                    result.params.push(param);\n                }\n            }\n            else {\n                result.params.push(param);\n            }\n        }\n        return result;\n    }\n\n    function applyPosition(position, target, value) {\n        return position === 'prefix' ? value + target : target + value;\n    }\n    function quote(value, quote) {\n        switch (quote) {\n            case 'double':\n                return `\"${value}\"`;\n            case 'single':\n                return `'${value}'`;\n            case undefined:\n                return value;\n        }\n    }\n    function stringifyRules() {\n        return {\n            JsdocTypeParenthesis: (result, transform) => `(${result.element !== undefined ? transform(result.element) : ''})`,\n            JsdocTypeKeyof: (result, transform) => `keyof ${transform(result.element)}`,\n            JsdocTypeFunction: (result, transform) => {\n                if (!result.arrow) {\n                    let stringified = result.constructor ? 'new' : 'function';\n                    if (!result.parenthesis) {\n                        return stringified;\n                    }\n                    stringified += `(${result.parameters.map(transform).join(', ')})`;\n                    if (result.returnType !== undefined) {\n                        stringified += `: ${transform(result.returnType)}`;\n                    }\n                    return stringified;\n                }\n                else {\n                    if (result.returnType === undefined) {\n                        throw new Error('Arrow function needs a return type.');\n                    }\n                    let stringified = `(${result.parameters.map(transform).join(', ')}) => ${transform(result.returnType)}`;\n                    if (result.constructor) {\n                        stringified = 'new ' + stringified;\n                    }\n                    return stringified;\n                }\n            },\n            JsdocTypeName: result => result.value,\n            JsdocTypeTuple: (result, transform) => `[${result.elements.map(transform).join(', ')}]`,\n            JsdocTypeVariadic: (result, transform) => result.meta.position === undefined\n                ? '...'\n                : applyPosition(result.meta.position, transform(result.element), '...'),\n            JsdocTypeNamePath: (result, transform) => {\n                const left = transform(result.left);\n                const right = transform(result.right);\n                switch (result.pathType) {\n                    case 'inner':\n                        return `${left}~${right}`;\n                    case 'instance':\n                        return `${left}#${right}`;\n                    case 'property':\n                        return `${left}.${right}`;\n                    case 'property-brackets':\n                        return `${left}[${right}]`;\n                }\n            },\n            JsdocTypeStringValue: result => quote(result.value, result.meta.quote),\n            JsdocTypeAny: () => '*',\n            JsdocTypeGeneric: (result, transform) => {\n                if (result.meta.brackets === 'square') {\n                    const element = result.elements[0];\n                    const transformed = transform(element);\n                    if (element.type === 'JsdocTypeUnion' || element.type === 'JsdocTypeIntersection') {\n                        return `(${transformed})[]`;\n                    }\n                    else {\n                        return `${transformed}[]`;\n                    }\n                }\n                else {\n                    return `${transform(result.left)}${result.meta.dot ? '.' : ''}<${result.elements.map(transform).join(', ')}>`;\n                }\n            },\n            JsdocTypeImport: (result, transform) => `import(${transform(result.element)})`,\n            JsdocTypeObjectField: (result, transform) => {\n                let text = '';\n                if (result.readonly) {\n                    text += 'readonly ';\n                }\n                if (typeof result.key === 'string') {\n                    text += quote(result.key, result.meta.quote);\n                }\n                else {\n                    text += transform(result.key);\n                }\n                if (result.optional) {\n                    text += '?';\n                }\n                if (result.right === undefined) {\n                    return text;\n                }\n                else {\n                    return text + `: ${transform(result.right)}`;\n                }\n            },\n            JsdocTypeJsdocObjectField: (result, transform) => {\n                return `${transform(result.left)}: ${transform(result.right)}`;\n            },\n            JsdocTypeKeyValue: (result, transform) => {\n                let text = result.key;\n                if (result.optional) {\n                    text += '?';\n                }\n                if (result.variadic) {\n                    text = '...' + text;\n                }\n                if (result.right === undefined) {\n                    return text;\n                }\n                else {\n                    return text + `: ${transform(result.right)}`;\n                }\n            },\n            JsdocTypeSpecialNamePath: result => `${result.specialType}:${quote(result.value, result.meta.quote)}`,\n            JsdocTypeNotNullable: (result, transform) => applyPosition(result.meta.position, transform(result.element), '!'),\n            JsdocTypeNull: () => 'null',\n            JsdocTypeNullable: (result, transform) => applyPosition(result.meta.position, transform(result.element), '?'),\n            JsdocTypeNumber: result => result.value.toString(),\n            JsdocTypeObject: (result, transform) => `{${result.elements.map(transform).join((result.meta.separator === 'comma' ? ',' : ';') + ' ')}}`,\n            JsdocTypeOptional: (result, transform) => applyPosition(result.meta.position, transform(result.element), '='),\n            JsdocTypeSymbol: (result, transform) => `${result.value}(${result.element !== undefined ? transform(result.element) : ''})`,\n            JsdocTypeTypeof: (result, transform) => `typeof ${transform(result.element)}`,\n            JsdocTypeUndefined: () => 'undefined',\n            JsdocTypeUnion: (result, transform) => result.elements.map(transform).join(' | '),\n            JsdocTypeUnknown: () => '?',\n            JsdocTypeIntersection: (result, transform) => result.elements.map(transform).join(' & '),\n            JsdocTypeProperty: result => quote(result.value, result.meta.quote),\n            JsdocTypePredicate: (result, transform) => `${transform(result.left)} is ${transform(result.right)}`,\n            JsdocTypeIndexSignature: (result, transform) => `[${result.key}: ${transform(result.right)}]`,\n            JsdocTypeMappedType: (result, transform) => `[${result.key} in ${transform(result.right)}]`,\n            JsdocTypeAsserts: (result, transform) => `asserts ${transform(result.left)} is ${transform(result.right)}`\n        };\n    }\n    const storedStringifyRules = stringifyRules();\n    function stringify(result) {\n        return transform(storedStringifyRules, result);\n    }\n\n    const reservedWords = [\n        'null',\n        'true',\n        'false',\n        'break',\n        'case',\n        'catch',\n        'class',\n        'const',\n        'continue',\n        'debugger',\n        'default',\n        'delete',\n        'do',\n        'else',\n        'export',\n        'extends',\n        'finally',\n        'for',\n        'function',\n        'if',\n        'import',\n        'in',\n        'instanceof',\n        'new',\n        'return',\n        'super',\n        'switch',\n        'this',\n        'throw',\n        'try',\n        'typeof',\n        'var',\n        'void',\n        'while',\n        'with',\n        'yield'\n    ];\n    function makeName(value) {\n        const result = {\n            type: 'NameExpression',\n            name: value\n        };\n        if (reservedWords.includes(value)) {\n            result.reservedWord = true;\n        }\n        return result;\n    }\n    const catharsisTransformRules = {\n        JsdocTypeOptional: (result, transform) => {\n            const transformed = transform(result.element);\n            transformed.optional = true;\n            return transformed;\n        },\n        JsdocTypeNullable: (result, transform) => {\n            const transformed = transform(result.element);\n            transformed.nullable = true;\n            return transformed;\n        },\n        JsdocTypeNotNullable: (result, transform) => {\n            const transformed = transform(result.element);\n            transformed.nullable = false;\n            return transformed;\n        },\n        JsdocTypeVariadic: (result, transform) => {\n            if (result.element === undefined) {\n                throw new Error('dots without value are not allowed in catharsis mode');\n            }\n            const transformed = transform(result.element);\n            transformed.repeatable = true;\n            return transformed;\n        },\n        JsdocTypeAny: () => ({\n            type: 'AllLiteral'\n        }),\n        JsdocTypeNull: () => ({\n            type: 'NullLiteral'\n        }),\n        JsdocTypeStringValue: result => makeName(quote(result.value, result.meta.quote)),\n        JsdocTypeUndefined: () => ({\n            type: 'UndefinedLiteral'\n        }),\n        JsdocTypeUnknown: () => ({\n            type: 'UnknownLiteral'\n        }),\n        JsdocTypeFunction: (result, transform) => {\n            const params = extractSpecialParams(result);\n            const transformed = {\n                type: 'FunctionType',\n                params: params.params.map(transform)\n            };\n            if (params.this !== undefined) {\n                transformed.this = transform(params.this);\n            }\n            if (params.new !== undefined) {\n                transformed.new = transform(params.new);\n            }\n            if (result.returnType !== undefined) {\n                transformed.result = transform(result.returnType);\n            }\n            return transformed;\n        },\n        JsdocTypeGeneric: (result, transform) => ({\n            type: 'TypeApplication',\n            applications: result.elements.map(o => transform(o)),\n            expression: transform(result.left)\n        }),\n        JsdocTypeSpecialNamePath: result => makeName(result.specialType + ':' + quote(result.value, result.meta.quote)),\n        JsdocTypeName: result => {\n            if (result.value !== 'function') {\n                return makeName(result.value);\n            }\n            else {\n                return {\n                    type: 'FunctionType',\n                    params: []\n                };\n            }\n        },\n        JsdocTypeNumber: result => makeName(result.value.toString()),\n        JsdocTypeObject: (result, transform) => {\n            const transformed = {\n                type: 'RecordType',\n                fields: []\n            };\n            for (const field of result.elements) {\n                if (field.type !== 'JsdocTypeObjectField' && field.type !== 'JsdocTypeJsdocObjectField') {\n                    transformed.fields.push({\n                        type: 'FieldType',\n                        key: transform(field),\n                        value: undefined\n                    });\n                }\n                else {\n                    transformed.fields.push(transform(field));\n                }\n            }\n            return transformed;\n        },\n        JsdocTypeObjectField: (result, transform) => {\n            if (typeof result.key !== 'string') {\n                throw new Error('Index signatures and mapped types are not supported');\n            }\n            return {\n                type: 'FieldType',\n                key: makeName(quote(result.key, result.meta.quote)),\n                value: result.right === undefined ? undefined : transform(result.right)\n            };\n        },\n        JsdocTypeJsdocObjectField: (result, transform) => ({\n            type: 'FieldType',\n            key: transform(result.left),\n            value: transform(result.right)\n        }),\n        JsdocTypeUnion: (result, transform) => ({\n            type: 'TypeUnion',\n            elements: result.elements.map(e => transform(e))\n        }),\n        JsdocTypeKeyValue: (result, transform) => {\n            return {\n                type: 'FieldType',\n                key: makeName(result.key),\n                value: result.right === undefined ? undefined : transform(result.right)\n            };\n        },\n        JsdocTypeNamePath: (result, transform) => {\n            const leftResult = transform(result.left);\n            let rightValue;\n            if (result.right.type === 'JsdocTypeSpecialNamePath') {\n                rightValue = transform(result.right).name;\n            }\n            else {\n                rightValue = quote(result.right.value, result.right.meta.quote);\n            }\n            const joiner = result.pathType === 'inner' ? '~' : result.pathType === 'instance' ? '#' : '.';\n            return makeName(`${leftResult.name}${joiner}${rightValue}`);\n        },\n        JsdocTypeSymbol: result => {\n            let value = '';\n            let element = result.element;\n            let trailingDots = false;\n            if ((element === null || element === void 0 ? void 0 : element.type) === 'JsdocTypeVariadic') {\n                if (element.meta.position === 'prefix') {\n                    value = '...';\n                }\n                else {\n                    trailingDots = true;\n                }\n                element = element.element;\n            }\n            if ((element === null || element === void 0 ? void 0 : element.type) === 'JsdocTypeName') {\n                value += element.value;\n            }\n            else if ((element === null || element === void 0 ? void 0 : element.type) === 'JsdocTypeNumber') {\n                value += element.value.toString();\n            }\n            if (trailingDots) {\n                value += '...';\n            }\n            return makeName(`${result.value}(${value})`);\n        },\n        JsdocTypeParenthesis: (result, transform) => transform(assertRootResult(result.element)),\n        JsdocTypeMappedType: notAvailableTransform,\n        JsdocTypeIndexSignature: notAvailableTransform,\n        JsdocTypeImport: notAvailableTransform,\n        JsdocTypeKeyof: notAvailableTransform,\n        JsdocTypeTuple: notAvailableTransform,\n        JsdocTypeTypeof: notAvailableTransform,\n        JsdocTypeIntersection: notAvailableTransform,\n        JsdocTypeProperty: notAvailableTransform,\n        JsdocTypePredicate: notAvailableTransform,\n        JsdocTypeAsserts: notAvailableTransform\n    };\n    function catharsisTransform(result) {\n        return transform(catharsisTransformRules, result);\n    }\n\n    function getQuoteStyle(quote) {\n        switch (quote) {\n            case undefined:\n                return 'none';\n            case 'single':\n                return 'single';\n            case 'double':\n                return 'double';\n        }\n    }\n    function getMemberType(type) {\n        switch (type) {\n            case 'inner':\n                return 'INNER_MEMBER';\n            case 'instance':\n                return 'INSTANCE_MEMBER';\n            case 'property':\n                return 'MEMBER';\n            case 'property-brackets':\n                return 'MEMBER';\n        }\n    }\n    function nestResults(type, results) {\n        if (results.length === 2) {\n            return {\n                type,\n                left: results[0],\n                right: results[1]\n            };\n        }\n        else {\n            return {\n                type,\n                left: results[0],\n                right: nestResults(type, results.slice(1))\n            };\n        }\n    }\n    const jtpRules = {\n        JsdocTypeOptional: (result, transform) => ({\n            type: 'OPTIONAL',\n            value: transform(result.element),\n            meta: {\n                syntax: result.meta.position === 'prefix' ? 'PREFIX_EQUAL_SIGN' : 'SUFFIX_EQUALS_SIGN'\n            }\n        }),\n        JsdocTypeNullable: (result, transform) => ({\n            type: 'NULLABLE',\n            value: transform(result.element),\n            meta: {\n                syntax: result.meta.position === 'prefix' ? 'PREFIX_QUESTION_MARK' : 'SUFFIX_QUESTION_MARK'\n            }\n        }),\n        JsdocTypeNotNullable: (result, transform) => ({\n            type: 'NOT_NULLABLE',\n            value: transform(result.element),\n            meta: {\n                syntax: result.meta.position === 'prefix' ? 'PREFIX_BANG' : 'SUFFIX_BANG'\n            }\n        }),\n        JsdocTypeVariadic: (result, transform) => {\n            const transformed = {\n                type: 'VARIADIC',\n                meta: {\n                    syntax: result.meta.position === 'prefix'\n                        ? 'PREFIX_DOTS'\n                        : result.meta.position === 'suffix' ? 'SUFFIX_DOTS' : 'ONLY_DOTS'\n                }\n            };\n            if (result.element !== undefined) {\n                transformed.value = transform(result.element);\n            }\n            return transformed;\n        },\n        JsdocTypeName: result => ({\n            type: 'NAME',\n            name: result.value\n        }),\n        JsdocTypeTypeof: (result, transform) => ({\n            type: 'TYPE_QUERY',\n            name: transform(result.element)\n        }),\n        JsdocTypeTuple: (result, transform) => ({\n            type: 'TUPLE',\n            entries: result.elements.map(transform)\n        }),\n        JsdocTypeKeyof: (result, transform) => ({\n            type: 'KEY_QUERY',\n            value: transform(result.element)\n        }),\n        JsdocTypeImport: result => ({\n            type: 'IMPORT',\n            path: {\n                type: 'STRING_VALUE',\n                quoteStyle: getQuoteStyle(result.element.meta.quote),\n                string: result.element.value\n            }\n        }),\n        JsdocTypeUndefined: () => ({\n            type: 'NAME',\n            name: 'undefined'\n        }),\n        JsdocTypeAny: () => ({\n            type: 'ANY'\n        }),\n        JsdocTypeFunction: (result, transform) => {\n            const specialParams = extractSpecialParams(result);\n            const transformed = {\n                type: result.arrow ? 'ARROW' : 'FUNCTION',\n                params: specialParams.params.map(param => {\n                    if (param.type === 'JsdocTypeKeyValue') {\n                        if (param.right === undefined) {\n                            throw new Error('Function parameter without \\':\\' is not expected to be \\'KEY_VALUE\\'');\n                        }\n                        return {\n                            type: 'NAMED_PARAMETER',\n                            name: param.key,\n                            typeName: transform(param.right)\n                        };\n                    }\n                    else {\n                        return transform(param);\n                    }\n                }),\n                new: null,\n                returns: null\n            };\n            if (specialParams.this !== undefined) {\n                transformed.this = transform(specialParams.this);\n            }\n            else if (!result.arrow) {\n                transformed.this = null;\n            }\n            if (specialParams.new !== undefined) {\n                transformed.new = transform(specialParams.new);\n            }\n            if (result.returnType !== undefined) {\n                transformed.returns = transform(result.returnType);\n            }\n            return transformed;\n        },\n        JsdocTypeGeneric: (result, transform) => {\n            const transformed = {\n                type: 'GENERIC',\n                subject: transform(result.left),\n                objects: result.elements.map(transform),\n                meta: {\n                    syntax: result.meta.brackets === 'square' ? 'SQUARE_BRACKET' : result.meta.dot ? 'ANGLE_BRACKET_WITH_DOT' : 'ANGLE_BRACKET'\n                }\n            };\n            if (result.meta.brackets === 'square' && result.elements[0].type === 'JsdocTypeFunction' && !result.elements[0].parenthesis) {\n                transformed.objects[0] = {\n                    type: 'NAME',\n                    name: 'function'\n                };\n            }\n            return transformed;\n        },\n        JsdocTypeObjectField: (result, transform) => {\n            if (typeof result.key !== 'string') {\n                throw new Error('Index signatures and mapped types are not supported');\n            }\n            if (result.right === undefined) {\n                return {\n                    type: 'RECORD_ENTRY',\n                    key: result.key,\n                    quoteStyle: getQuoteStyle(result.meta.quote),\n                    value: null,\n                    readonly: false\n                };\n            }\n            let right = transform(result.right);\n            if (result.optional) {\n                right = {\n                    type: 'OPTIONAL',\n                    value: right,\n                    meta: {\n                        syntax: 'SUFFIX_KEY_QUESTION_MARK'\n                    }\n                };\n            }\n            return {\n                type: 'RECORD_ENTRY',\n                key: result.key.toString(),\n                quoteStyle: getQuoteStyle(result.meta.quote),\n                value: right,\n                readonly: false\n            };\n        },\n        JsdocTypeJsdocObjectField: () => {\n            throw new Error('Keys may not be typed in jsdoctypeparser.');\n        },\n        JsdocTypeKeyValue: (result, transform) => {\n            if (result.right === undefined) {\n                return {\n                    type: 'RECORD_ENTRY',\n                    key: result.key,\n                    quoteStyle: 'none',\n                    value: null,\n                    readonly: false\n                };\n            }\n            let right = transform(result.right);\n            if (result.optional) {\n                right = {\n                    type: 'OPTIONAL',\n                    value: right,\n                    meta: {\n                        syntax: 'SUFFIX_KEY_QUESTION_MARK'\n                    }\n                };\n            }\n            return {\n                type: 'RECORD_ENTRY',\n                key: result.key,\n                quoteStyle: 'none',\n                value: right,\n                readonly: false\n            };\n        },\n        JsdocTypeObject: (result, transform) => {\n            const entries = [];\n            for (const field of result.elements) {\n                if (field.type === 'JsdocTypeObjectField' || field.type === 'JsdocTypeJsdocObjectField') {\n                    entries.push(transform(field));\n                }\n            }\n            return {\n                type: 'RECORD',\n                entries\n            };\n        },\n        JsdocTypeSpecialNamePath: result => {\n            if (result.specialType !== 'module') {\n                throw new Error(`jsdoctypeparser does not support type ${result.specialType} at this point.`);\n            }\n            return {\n                type: 'MODULE',\n                value: {\n                    type: 'FILE_PATH',\n                    quoteStyle: getQuoteStyle(result.meta.quote),\n                    path: result.value\n                }\n            };\n        },\n        JsdocTypeNamePath: (result, transform) => {\n            let hasEventPrefix = false;\n            let name;\n            let quoteStyle;\n            if (result.right.type === 'JsdocTypeSpecialNamePath' && result.right.specialType === 'event') {\n                hasEventPrefix = true;\n                name = result.right.value;\n                quoteStyle = getQuoteStyle(result.right.meta.quote);\n            }\n            else {\n                name = result.right.value;\n                quoteStyle = getQuoteStyle(result.right.meta.quote);\n            }\n            const transformed = {\n                type: getMemberType(result.pathType),\n                owner: transform(result.left),\n                name,\n                quoteStyle,\n                hasEventPrefix\n            };\n            if (transformed.owner.type === 'MODULE') {\n                const tModule = transformed.owner;\n                transformed.owner = transformed.owner.value;\n                tModule.value = transformed;\n                return tModule;\n            }\n            else {\n                return transformed;\n            }\n        },\n        JsdocTypeUnion: (result, transform) => nestResults('UNION', result.elements.map(transform)),\n        JsdocTypeParenthesis: (result, transform) => ({\n            type: 'PARENTHESIS',\n            value: transform(assertRootResult(result.element))\n        }),\n        JsdocTypeNull: () => ({\n            type: 'NAME',\n            name: 'null'\n        }),\n        JsdocTypeUnknown: () => ({\n            type: 'UNKNOWN'\n        }),\n        JsdocTypeStringValue: result => ({\n            type: 'STRING_VALUE',\n            quoteStyle: getQuoteStyle(result.meta.quote),\n            string: result.value\n        }),\n        JsdocTypeIntersection: (result, transform) => nestResults('INTERSECTION', result.elements.map(transform)),\n        JsdocTypeNumber: result => ({\n            type: 'NUMBER_VALUE',\n            number: result.value.toString()\n        }),\n        JsdocTypeSymbol: notAvailableTransform,\n        JsdocTypeProperty: notAvailableTransform,\n        JsdocTypePredicate: notAvailableTransform,\n        JsdocTypeMappedType: notAvailableTransform,\n        JsdocTypeIndexSignature: notAvailableTransform,\n        JsdocTypeAsserts: notAvailableTransform\n    };\n    function jtpTransform(result) {\n        return transform(jtpRules, result);\n    }\n\n    function identityTransformRules() {\n        return {\n            JsdocTypeIntersection: (result, transform) => ({\n                type: 'JsdocTypeIntersection',\n                elements: result.elements.map(transform)\n            }),\n            JsdocTypeGeneric: (result, transform) => ({\n                type: 'JsdocTypeGeneric',\n                left: transform(result.left),\n                elements: result.elements.map(transform),\n                meta: {\n                    dot: result.meta.dot,\n                    brackets: result.meta.brackets\n                }\n            }),\n            JsdocTypeNullable: result => result,\n            JsdocTypeUnion: (result, transform) => ({\n                type: 'JsdocTypeUnion',\n                elements: result.elements.map(transform)\n            }),\n            JsdocTypeUnknown: result => result,\n            JsdocTypeUndefined: result => result,\n            JsdocTypeTypeof: (result, transform) => ({\n                type: 'JsdocTypeTypeof',\n                element: transform(result.element)\n            }),\n            JsdocTypeSymbol: (result, transform) => {\n                const transformed = {\n                    type: 'JsdocTypeSymbol',\n                    value: result.value\n                };\n                if (result.element !== undefined) {\n                    transformed.element = transform(result.element);\n                }\n                return transformed;\n            },\n            JsdocTypeOptional: (result, transform) => ({\n                type: 'JsdocTypeOptional',\n                element: transform(result.element),\n                meta: {\n                    position: result.meta.position\n                }\n            }),\n            JsdocTypeObject: (result, transform) => ({\n                type: 'JsdocTypeObject',\n                meta: {\n                    separator: 'comma'\n                },\n                elements: result.elements.map(transform)\n            }),\n            JsdocTypeNumber: result => result,\n            JsdocTypeNull: result => result,\n            JsdocTypeNotNullable: (result, transform) => ({\n                type: 'JsdocTypeNotNullable',\n                element: transform(result.element),\n                meta: {\n                    position: result.meta.position\n                }\n            }),\n            JsdocTypeSpecialNamePath: result => result,\n            JsdocTypeObjectField: (result, transform) => ({\n                type: 'JsdocTypeObjectField',\n                key: result.key,\n                right: result.right === undefined ? undefined : transform(result.right),\n                optional: result.optional,\n                readonly: result.readonly,\n                meta: result.meta\n            }),\n            JsdocTypeJsdocObjectField: (result, transform) => ({\n                type: 'JsdocTypeJsdocObjectField',\n                left: transform(result.left),\n                right: transform(result.right)\n            }),\n            JsdocTypeKeyValue: (result, transform) => {\n                return {\n                    type: 'JsdocTypeKeyValue',\n                    key: result.key,\n                    right: result.right === undefined ? undefined : transform(result.right),\n                    optional: result.optional,\n                    variadic: result.variadic\n                };\n            },\n            JsdocTypeImport: (result, transform) => ({\n                type: 'JsdocTypeImport',\n                element: transform(result.element)\n            }),\n            JsdocTypeAny: result => result,\n            JsdocTypeStringValue: result => result,\n            JsdocTypeNamePath: result => result,\n            JsdocTypeVariadic: (result, transform) => {\n                const transformed = {\n                    type: 'JsdocTypeVariadic',\n                    meta: {\n                        position: result.meta.position,\n                        squareBrackets: result.meta.squareBrackets\n                    }\n                };\n                if (result.element !== undefined) {\n                    transformed.element = transform(result.element);\n                }\n                return transformed;\n            },\n            JsdocTypeTuple: (result, transform) => ({\n                type: 'JsdocTypeTuple',\n                elements: result.elements.map(transform)\n            }),\n            JsdocTypeName: result => result,\n            JsdocTypeFunction: (result, transform) => {\n                const transformed = {\n                    type: 'JsdocTypeFunction',\n                    arrow: result.arrow,\n                    parameters: result.parameters.map(transform),\n                    constructor: result.constructor,\n                    parenthesis: result.parenthesis\n                };\n                if (result.returnType !== undefined) {\n                    transformed.returnType = transform(result.returnType);\n                }\n                return transformed;\n            },\n            JsdocTypeKeyof: (result, transform) => ({\n                type: 'JsdocTypeKeyof',\n                element: transform(result.element)\n            }),\n            JsdocTypeParenthesis: (result, transform) => ({\n                type: 'JsdocTypeParenthesis',\n                element: transform(result.element)\n            }),\n            JsdocTypeProperty: result => result,\n            JsdocTypePredicate: (result, transform) => ({\n                type: 'JsdocTypePredicate',\n                left: transform(result.left),\n                right: transform(result.right)\n            }),\n            JsdocTypeIndexSignature: (result, transform) => ({\n                type: 'JsdocTypeIndexSignature',\n                key: result.key,\n                right: transform(result.right)\n            }),\n            JsdocTypeMappedType: (result, transform) => ({\n                type: 'JsdocTypeMappedType',\n                key: result.key,\n                right: transform(result.right)\n            }),\n            JsdocTypeAsserts: (result, transform) => ({\n                type: 'JsdocTypeAsserts',\n                left: transform(result.left),\n                right: transform(result.right)\n            })\n        };\n    }\n\n    const visitorKeys = {\n        JsdocTypeAny: [],\n        JsdocTypeFunction: ['parameters', 'returnType'],\n        JsdocTypeGeneric: ['left', 'elements'],\n        JsdocTypeImport: [],\n        JsdocTypeIndexSignature: ['right'],\n        JsdocTypeIntersection: ['elements'],\n        JsdocTypeKeyof: ['element'],\n        JsdocTypeKeyValue: ['right'],\n        JsdocTypeMappedType: ['right'],\n        JsdocTypeName: [],\n        JsdocTypeNamePath: ['left', 'right'],\n        JsdocTypeNotNullable: ['element'],\n        JsdocTypeNull: [],\n        JsdocTypeNullable: ['element'],\n        JsdocTypeNumber: [],\n        JsdocTypeObject: ['elements'],\n        JsdocTypeObjectField: ['right'],\n        JsdocTypeJsdocObjectField: ['left', 'right'],\n        JsdocTypeOptional: ['element'],\n        JsdocTypeParenthesis: ['element'],\n        JsdocTypeSpecialNamePath: [],\n        JsdocTypeStringValue: [],\n        JsdocTypeSymbol: ['element'],\n        JsdocTypeTuple: ['elements'],\n        JsdocTypeTypeof: ['element'],\n        JsdocTypeUndefined: [],\n        JsdocTypeUnion: ['elements'],\n        JsdocTypeUnknown: [],\n        JsdocTypeVariadic: ['element'],\n        JsdocTypeProperty: [],\n        JsdocTypePredicate: ['left', 'right'],\n        JsdocTypeAsserts: ['left', 'right']\n    };\n\n    function _traverse(node, parentNode, property, onEnter, onLeave) {\n        onEnter === null || onEnter === void 0 ? void 0 : onEnter(node, parentNode, property);\n        const keysToVisit = visitorKeys[node.type];\n        for (const key of keysToVisit) {\n            const value = node[key];\n            if (value !== undefined) {\n                if (Array.isArray(value)) {\n                    for (const element of value) {\n                        _traverse(element, node, key, onEnter, onLeave);\n                    }\n                }\n                else {\n                    _traverse(value, node, key, onEnter, onLeave);\n                }\n            }\n        }\n        onLeave === null || onLeave === void 0 ? void 0 : onLeave(node, parentNode, property);\n    }\n    /**\n     * A function to traverse an AST. It traverses it depth first.\n     * @param node the node to start traversing at.\n     * @param onEnter node visitor function that will be called on entering the node. This corresponds to preorder traversing.\n     * @param onLeave node visitor function that will be called on leaving the node. This corresponds to postorder traversing.\n     */\n    function traverse(node, onEnter, onLeave) {\n        _traverse(node, undefined, undefined, onEnter, onLeave);\n    }\n\n    exports.catharsisTransform = catharsisTransform;\n    exports.identityTransformRules = identityTransformRules;\n    exports.jtpTransform = jtpTransform;\n    exports.parse = parse;\n    exports.stringify = stringify;\n    exports.stringifyRules = stringifyRules;\n    exports.transform = transform;\n    exports.traverse = traverse;\n    exports.tryParse = tryParse;\n    exports.visitorKeys = visitorKeys;\n\n}));\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAU,QAAQ,SAAS;AACxB,aAAO,YAAY,YAAY,OAAO,WAAW,cAAc,QAAQ,OAAO,IAC9E,OAAO,WAAW,cAAc,OAAO,MAAM,OAAO,CAAC,SAAS,GAAG,OAAO,KACvE,SAAS,OAAO,eAAe,cAAc,aAAa,UAAU,MAAM,QAAQ,OAAO,OAAO,CAAC,CAAC;AAAA,IACvG,GAAG,SAAO,SAAUA,UAAS;AAAE;AAE3B,eAAS,cAAc,OAAO;AAC1B,YAAI,MAAM,SAAS,UAAa,MAAM,SAAS,IAAI;AAC/C,iBAAO,IAAI,MAAM,IAAI,iBAAiB,MAAM,IAAI;AAAA,QACpD,OACK;AACD,iBAAO,IAAI,MAAM,IAAI;AAAA,QACzB;AAAA,MACJ;AAAA,MACA,MAAM,4BAA4B,MAAM;AAAA,QACpC,YAAY,OAAO;AACf,gBAAM,+BAA+B,cAAc,KAAK,CAAC,EAAE;AAC3D,eAAK,QAAQ;AACb,iBAAO,eAAe,MAAM,oBAAoB,SAAS;AAAA,QAC7D;AAAA,QACA,WAAW;AACP,iBAAO,KAAK;AAAA,QAChB;AAAA,MACJ;AAAA,MACA,MAAM,6BAA6B,MAAM;AAAA,QACrC,YAAY,OAAO;AACf,gBAAM,gDAAgD,cAAc,KAAK,CAAC,EAAE;AAC5E,eAAK,QAAQ;AACb,iBAAO,eAAe,MAAM,qBAAqB,SAAS;AAAA,QAC9D;AAAA,QACA,WAAW;AACP,iBAAO,KAAK;AAAA,QAChB;AAAA,MACJ;AAAA,MACA,MAAM,4BAA4B,MAAM;AAAA,QACpC,YAAY,QAAQ,SAAS;AACzB,cAAI,QAAQ,qBAAqB,OAAO,IAAI;AAC5C,cAAI,YAAY,QAAW;AACvB,qBAAS,aAAa,OAAO;AAAA,UACjC;AACA,gBAAM,KAAK;AACX,iBAAO,eAAe,MAAM,oBAAoB,SAAS;AAAA,QAC7D;AAAA,MACJ;AAkBA,eAAS,oBAAoB,MAAM;AAC/B,eAAO,UAAQ;AACX,cAAI,KAAK,WAAW,IAAI,GAAG;AACvB,mBAAO,EAAE,MAAM,MAAM,KAAK;AAAA,UAC9B,OACK;AACD,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AACA,eAAS,UAAU,MAAM;AACrB,YAAI,WAAW;AACf,YAAI;AACJ,cAAM,OAAO,KAAK,CAAC;AACnB,YAAI,UAAU;AACd,YAAI,SAAS,OAAQ,SAAS,KAAK;AAC/B,iBAAO;AAAA,QACX;AACA,eAAO,WAAW,KAAK,QAAQ;AAC3B;AACA,iBAAO,KAAK,QAAQ;AACpB,cAAI,CAAC,WAAW,SAAS,MAAM;AAC3B;AACA;AAAA,UACJ;AACA,oBAAU,CAAC,WAAW,SAAS;AAAA,QACnC;AACA,YAAI,SAAS,MAAM;AACf,gBAAM,IAAI,MAAM,qBAAqB;AAAA,QACzC;AACA,eAAO,KAAK,MAAM,GAAG,QAAQ;AAAA,MACjC;AACA,YAAM,uBAAuB;AAG7B,YAAM,0BAA0B;AAChC,eAAS,cAAc,MAAM;AACzB,YAAI,OAAO,KAAK,CAAC;AACjB,YAAI,CAAC,qBAAqB,KAAK,IAAI,GAAG;AAClC,iBAAO;AAAA,QACX;AACA,YAAI,WAAW;AACf,WAAG;AACC,iBAAO,KAAK,QAAQ;AACpB,cAAI,CAAC,wBAAwB,KAAK,IAAI,GAAG;AACrC;AAAA,UACJ;AACA;AAAA,QACJ,SAAS,WAAW,KAAK;AACzB,eAAO,KAAK,MAAM,GAAG,QAAQ;AAAA,MACjC;AAEA,YAAM,cAAc;AACpB,eAAS,UAAU,MAAM;AACrB,YAAI,IAAI;AACR,gBAAQ,MAAM,KAAK,YAAY,KAAK,IAAI,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,MAC5H;AACA,YAAM,iBAAiB,UAAQ;AAC3B,cAAM,QAAQ,cAAc,IAAI;AAChC,YAAI,SAAS,MAAM;AACf,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,UACH,MAAM;AAAA,UACN,MAAM;AAAA,QACV;AAAA,MACJ;AACA,eAAS,gBAAgB,MAAM;AAC3B,eAAO,UAAQ;AACX,cAAI,CAAC,KAAK,WAAW,IAAI,GAAG;AACxB,mBAAO;AAAA,UACX;AACA,gBAAM,WAAW,KAAK,KAAK,MAAM;AACjC,cAAI,aAAa,UAAa,wBAAwB,KAAK,QAAQ,GAAG;AAClE,mBAAO;AAAA,UACX;AACA,iBAAO;AAAA,YACH;AAAA,YACA,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,kBAAkB,UAAQ;AAC5B,cAAM,QAAQ,UAAU,IAAI;AAC5B,YAAI,SAAS,MAAM;AACf,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,UACH,MAAM;AAAA,UACN,MAAM;AAAA,QACV;AAAA,MACJ;AACA,YAAM,UAAU,UAAQ;AACpB,YAAI,KAAK,SAAS,GAAG;AACjB,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,UACH,MAAM;AAAA,UACN,MAAM;AAAA,QACV;AAAA,MACJ;AACA,YAAM,aAAa,UAAQ;AACvB,cAAM,QAAQ,UAAU,IAAI;AAC5B,YAAI,UAAU,MAAM;AAChB,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,UACH,MAAM;AAAA,UACN,MAAM;AAAA,QACV;AAAA,MACJ;AACA,YAAM,QAAQ;AAAA,QACV;AAAA,QACA,oBAAoB,IAAI;AAAA,QACxB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,KAAK;AAAA,QACzB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,gBAAgB,WAAW;AAAA,QAC3B,gBAAgB,MAAM;AAAA,QACtB,gBAAgB,UAAU;AAAA,QAC1B,gBAAgB,MAAM;AAAA,QACtB,gBAAgB,KAAK;AAAA,QACrB,gBAAgB,QAAQ;AAAA,QACxB,gBAAgB,OAAO;AAAA,QACvB,gBAAgB,UAAU;AAAA,QAC1B,gBAAgB,QAAQ;AAAA,QACxB,gBAAgB,OAAO;AAAA,QACvB,gBAAgB,UAAU;AAAA,QAC1B,gBAAgB,QAAQ;AAAA,QACxB,gBAAgB,IAAI;AAAA,QACpB,gBAAgB,IAAI;AAAA,QACpB,gBAAgB,SAAS;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AACA,YAAM,0BAA0B;AAAA,MAChC,MAAM,MAAM;AAAA,QACR,OAAO,OAAO,MAAM;AAChB,gBAAM,UAAU,KAAK,KAAK,IAAI;AAC9B,iBAAO,QAAQ;AACf,gBAAM,OAAO,KAAK,KAAK,IAAI;AAC3B,iBAAO,KAAK;AACZ,iBAAO,IAAI,MAAM,MAAM,QAAW,QAAQ,OAAO,KAAK,KAAK;AAAA,QAC/D;AAAA,QACA,YAAY,MAAM,UAAU,SAAS,MAAM;AACvC,eAAK,OAAO;AACZ,eAAK,OAAO;AACZ,eAAK,WAAW;AAChB,eAAK,UAAU;AACf,eAAK,OAAO;AAAA,QAChB;AAAA,QACA,OAAO,KAAK,MAAM,cAAc,OAAO;AACnC,wBAAc,eAAe,wBAAwB,KAAK,IAAI;AAC9D,iBAAO,KAAK,KAAK;AACjB,qBAAW,QAAQ,OAAO;AACtB,kBAAM,UAAU,KAAK,IAAI;AACzB,gBAAI,YAAY,MAAM;AAClB,oBAAM,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,YAAY,CAAC;AACvE,qBAAO,KAAK,MAAM,MAAM,KAAK,MAAM;AACnC,qBAAO,EAAE,MAAM,MAAM;AAAA,YACzB;AAAA,UACJ;AACA,gBAAM,IAAI,MAAM,sBAAsB,IAAI;AAAA,QAC9C;AAAA,QACA,UAAU;AACN,gBAAM,OAAO,MAAM,KAAK,KAAK,IAAI;AACjC,iBAAO,IAAI,MAAM,KAAK,MAAM,KAAK,SAAS,KAAK,MAAM,KAAK,KAAK;AAAA,QACnE;AAAA,MACJ;AAKA,eAAS,iBAAiB,QAAQ;AAC9B,YAAI,WAAW,QAAW;AACtB,gBAAM,IAAI,MAAM,sBAAsB;AAAA,QAC1C;AACA,YAAI,OAAO,SAAS,uBAAuB,OAAO,SAAS,4BACvD,OAAO,SAAS,uBAAuB,OAAO,SAAS,+BACvD,OAAO,SAAS,0BAA0B,OAAO,SAAS,+BAC1D,OAAO,SAAS,6BAA6B,OAAO,SAAS,uBAAuB;AACpF,gBAAM,IAAI,oBAAoB,MAAM;AAAA,QACxC;AACA,eAAO;AAAA,MACX;AACA,eAAS,gCAAgC,QAAQ;AAC7C,YAAI,OAAO,SAAS,qBAAqB;AACrC,iBAAO,0BAA0B,MAAM;AAAA,QAC3C;AACA,eAAO,iBAAiB,MAAM;AAAA,MAClC;AACA,eAAS,gCAAgC,QAAQ;AAC7C,YAAI,OAAO,SAAS,iBAAiB;AACjC,iBAAO;AAAA,QACX;AACA,eAAO,0BAA0B,MAAM;AAAA,MAC3C;AACA,eAAS,0BAA0B,QAAQ;AACvC,YAAI,OAAO,SAAS,qBAAqB;AACrC,gBAAM,IAAI,oBAAoB,MAAM;AAAA,QACxC;AACA,eAAO;AAAA,MACX;AACA,eAAS,iCAAiC,QAAQ;AAC9C,YAAI;AACJ,YAAI,OAAO,SAAS,qBAAqB;AACrC,gBAAM,KAAK,OAAO,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,iBAAiB;AAC1F,mBAAO;AAAA,UACX;AACA,gBAAM,IAAI,oBAAoB,MAAM;AAAA,QACxC;AACA,YAAI,OAAO,SAAS,qBAAqB,OAAO,SAAS,iBAAiB;AACtE,gBAAM,IAAI,oBAAoB,MAAM;AAAA,QACxC;AACA,eAAO;AAAA,MACX;AACA,eAAS,kBAAkB,QAAQ;AAC/B,eAAO,OAAO,SAAS,6BAA6B,OAAO,SAAS;AAAA,MACxE;AAGA,UAAI;AACJ,OAAC,SAAUC,aAAY;AACnB,QAAAA,YAAWA,YAAW,KAAK,IAAI,CAAC,IAAI;AACpC,QAAAA,YAAWA,YAAW,gBAAgB,IAAI,CAAC,IAAI;AAC/C,QAAAA,YAAWA,YAAW,QAAQ,IAAI,CAAC,IAAI;AACvC,QAAAA,YAAWA,YAAW,WAAW,IAAI,CAAC,IAAI;AAC1C,QAAAA,YAAWA,YAAW,gBAAgB,IAAI,CAAC,IAAI;AAC/C,QAAAA,YAAWA,YAAW,OAAO,IAAI,CAAC,IAAI;AACtC,QAAAA,YAAWA,YAAW,cAAc,IAAI,CAAC,IAAI;AAC7C,QAAAA,YAAWA,YAAW,QAAQ,IAAI,CAAC,IAAI;AACvC,QAAAA,YAAWA,YAAW,OAAO,IAAI,CAAC,IAAI;AACtC,QAAAA,YAAWA,YAAW,OAAO,IAAI,CAAC,IAAI;AACtC,QAAAA,YAAWA,YAAW,QAAQ,IAAI,EAAE,IAAI;AACxC,QAAAA,YAAWA,YAAW,UAAU,IAAI,EAAE,IAAI;AAC1C,QAAAA,YAAWA,YAAW,UAAU,IAAI,EAAE,IAAI;AAC1C,QAAAA,YAAWA,YAAW,gBAAgB,IAAI,EAAE,IAAI;AAChD,QAAAA,YAAWA,YAAW,UAAU,IAAI,EAAE,IAAI;AAC1C,QAAAA,YAAWA,YAAW,OAAO,IAAI,EAAE,IAAI;AACvC,QAAAA,YAAWA,YAAW,gBAAgB,IAAI,EAAE,IAAI;AAChD,QAAAA,YAAWA,YAAW,SAAS,IAAI,EAAE,IAAI;AACzC,QAAAA,YAAWA,YAAW,WAAW,IAAI,EAAE,IAAI;AAC3C,QAAAA,YAAWA,YAAW,aAAa,IAAI,EAAE,IAAI;AAC7C,QAAAA,YAAWA,YAAW,eAAe,IAAI,EAAE,IAAI;AAAA,MACnD,GAAG,eAAe,aAAa,CAAC,EAAE;AAAA,MAElC,MAAM,OAAO;AAAA,QACT,YAAY,SAAS,aAAa,YAAY;AAC1C,eAAK,UAAU;AACf,cAAI,OAAO,gBAAgB,UAAU;AACjC,iBAAK,SAAS,MAAM,OAAO,WAAW;AAAA,UAC1C,OACK;AACD,iBAAK,SAAS;AAAA,UAClB;AACA,eAAK,aAAa;AAAA,QACtB;AAAA,QACA,IAAI,QAAQ;AACR,iBAAO,KAAK;AAAA,QAChB;AAAA;AAAA;AAAA;AAAA,QAIA,QAAQ;AACJ,gBAAM,SAAS,KAAK,UAAU,WAAW,GAAG;AAC5C,cAAI,KAAK,MAAM,QAAQ,SAAS,OAAO;AACnC,kBAAM,IAAI,qBAAqB,KAAK,MAAM,OAAO;AAAA,UACrD;AACA,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA,QAIA,UAAU,YAAY;AAClB,iBAAO,iBAAiB,KAAK,sBAAsB,UAAU,CAAC;AAAA,QAClE;AAAA;AAAA;AAAA;AAAA;AAAA,QAKA,sBAAsB,YAAY;AAC9B,gBAAM,SAAS,KAAK,YAAY,MAAM,UAAU;AAChD,cAAI,WAAW,MAAM;AACjB,kBAAM,IAAI,oBAAoB,KAAK,MAAM,OAAO;AAAA,UACpD;AACA,iBAAO,KAAK,2BAA2B,QAAQ,UAAU;AAAA,QAC7D;AAAA;AAAA;AAAA;AAAA;AAAA,QAKA,2BAA2B,MAAM,YAAY;AACzC,cAAI,SAAS,KAAK,YAAY,MAAM,UAAU;AAC9C,iBAAO,WAAW,MAAM;AACpB,mBAAO;AACP,qBAAS,KAAK,YAAY,MAAM,UAAU;AAAA,UAC9C;AACA,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA,QAIA,YAAY,MAAM,YAAY;AAC1B,qBAAW,WAAW,KAAK,SAAS;AAChC,kBAAM,SAAS,QAAQ,MAAM,YAAY,IAAI;AAC7C,gBAAI,WAAW,MAAM;AACjB,qBAAO;AAAA,YACX;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA;AAAA,QAKA,QAAQ,OAAO;AACX,cAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACvB,oBAAQ,CAAC,KAAK;AAAA,UAClB;AACA,cAAI,MAAM,SAAS,KAAK,MAAM,QAAQ,IAAI,GAAG;AACzC,iBAAK,SAAS,KAAK,MAAM,QAAQ;AACjC,mBAAO;AAAA,UACX,OACK;AACD,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,QACA,iBAAiB,QAAQ;AACrB,eAAK,SAAS,OAAO;AAAA,QACzB;AAAA,MACJ;AAEA,eAAS,0BAA0B,MAAM;AACrC,eAAO,SAAS,SAAS,SAAS,OAAO,SAAS,OAAO,SAAS,OAAO,SAAS;AAAA,MACtF;AAEA,YAAM,kBAAkB,CAAC,QAAQ,YAAY,SAAS;AAClD,cAAM,OAAO,OAAO,MAAM,QAAQ;AAClC,cAAM,OAAO,OAAO,MAAM,KAAK;AAC/B,cAAM,SAAW,QAAQ,QAAS,SAAS,OAAO,CAAC,0BAA0B,IAAI,KAC3E,QAAQ,QAAS,SAAS;AAChC,YAAI,CAAC,QAAQ;AACT,iBAAO;AAAA,QACX;AACA,eAAO,QAAQ,GAAG;AAClB,YAAI,QAAQ,MAAM;AACd,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,OAAO,UAAU,WAAW,QAAQ;AAAA,YAC7C,MAAM;AAAA,cACF,UAAU;AAAA,YACd;AAAA,UACJ;AAAA,QACJ,OACK;AACD,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,iBAAiB,IAAI;AAAA,YAC9B,MAAM;AAAA,cACF,UAAU;AAAA,YACd;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAEA,eAAS,eAAe,SAAS;AAC7B,cAAM,UAAU,CAAC,QAAQ,eAAe,SAAS;AAC7C,gBAAM,OAAO,OAAO,MAAM,QAAQ;AAClC,gBAAM,OAAO,OAAO,MAAM,KAAK;AAC/B,cAAI,SAAS,MAAM;AACf,gBAAI,iBAAiB,SAAS;AAC1B,kBAAI,QAAQ,OAAO,MAAM,IAAI,GAAG;AAC5B,uBAAO,QAAQ,YAAY,MAAM;AAAA,cACrC;AAAA,YACJ;AAAA,UACJ,OACK;AACD,gBAAI,gBAAgB,SAAS;AACzB,kBAAI,QAAQ,aAAa,iBAAiB,QAAQ,OAAO,MAAM,IAAI,GAAG;AAClE,uBAAO,QAAQ,WAAW,QAAQ,IAAI;AAAA,cAC1C;AAAA,YACJ;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAEA,eAAO,eAAe,SAAS,QAAQ;AAAA,UACnC,OAAO,QAAQ;AAAA,QACnB,CAAC;AACD,eAAO;AAAA,MACX;AAEA,YAAM,kBAAkB,eAAe;AAAA,QACnC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,YAAY,WAAW;AAAA,QACvB,aAAa,YAAU;AACnB,iBAAO,QAAQ,GAAG;AAClB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,OAAO,UAAU,WAAW,QAAQ;AAAA,YAC7C,MAAM;AAAA,cACF,UAAU;AAAA,YACd;AAAA,UACJ;AAAA,QACJ;AAAA,QACA,YAAY,CAAC,QAAQ,SAAS;AAC1B,iBAAO,QAAQ,GAAG;AAClB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,iBAAiB,IAAI;AAAA,YAC9B,MAAM;AAAA,cACF,UAAU;AAAA,YACd;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,gBAAgB,eAAe;AAAA,QACjC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,YAAU;AACnB,gBAAM,QAAQ,WAAW,OAAO,MAAM,QAAQ,IAAI;AAClD,iBAAO,QAAQ,QAAQ;AACvB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,qBAAqB,eAAe;AAAA,QACtC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,YAAU;AACnB,iBAAO,QAAQ,GAAG;AAClB,cAAI,OAAO,QAAQ,GAAG,GAAG;AACrB,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,UAAU,CAAC;AAAA,YACf;AAAA,UACJ;AACA,gBAAM,SAAS,OAAO,sBAAsB,WAAW,GAAG;AAC1D,cAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,kBAAM,IAAI,MAAM,0BAA0B;AAAA,UAC9C;AACA,cAAI,OAAO,SAAS,0BAA0B;AAC1C,mBAAO;AAAA,UACX,WACS,OAAO,SAAS,qBAAqB;AAC1C,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,UAAU,CAAC,MAAM;AAAA,YACrB;AAAA,UACJ;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,iBAAiB,MAAM;AAAA,UACpC;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,sBAAsB,eAAe;AAAA,QACvC,MAAM;AAAA,QACN,QAAQ,CAAC,MAAM,SAAU,SAAS,OAAO,0BAA0B,IAAI,KACnE,SAAS,UAAU,SAAS,eAAe,SAAS;AAAA,QACxD,aAAa,YAAU;AACnB,cAAI,OAAO,QAAQ,MAAM,GAAG;AACxB,mBAAO;AAAA,cACH,MAAM;AAAA,YACV;AAAA,UACJ;AACA,cAAI,OAAO,QAAQ,WAAW,GAAG;AAC7B,mBAAO;AAAA,cACH,MAAM;AAAA,YACV;AAAA,UACJ;AACA,cAAI,OAAO,QAAQ,GAAG,GAAG;AACrB,mBAAO;AAAA,cACH,MAAM;AAAA,YACV;AAAA,UACJ;AACA,cAAI,OAAO,QAAQ,GAAG,GAAG;AACrB,mBAAO;AAAA,cACH,MAAM;AAAA,YACV;AAAA,UACJ;AACA,gBAAM,IAAI,MAAM,yBAAyB,OAAO,MAAM,QAAQ,IAAI;AAAA,QACtE;AAAA,MACJ,CAAC;AAED,YAAM,qBAAqB,eAAe;AAAA,QACtC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,YAAY,WAAW;AAAA,QACvB,aAAa,YAAU;AACnB,iBAAO,QAAQ,GAAG;AAClB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,OAAO,UAAU,WAAW,QAAQ;AAAA,YAC7C,MAAM;AAAA,cACF,UAAU;AAAA,YACd;AAAA,UACJ;AAAA,QACJ;AAAA,QACA,YAAY,CAAC,QAAQ,SAAS;AAC1B,iBAAO,QAAQ,GAAG;AAClB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,iBAAiB,IAAI;AAAA,YAC9B,MAAM;AAAA,cACF,UAAU;AAAA,YACd;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,eAAS,2BAA2B,EAAE,mBAAmB,GAAG;AACxD,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,QAAQ,UAAQ,SAAS;AAAA,UACzB,YAAY,WAAW;AAAA,UACvB,YAAY,CAAC,QAAQ,SAAS;AAC1B,kBAAM,WAAW;AAAA,cACb,gCAAgC,IAAI;AAAA,YACxC;AACA,mBAAO,QAAQ,GAAG;AAClB,eAAG;AACC,kBAAI;AACA,sBAAM,OAAO,OAAO,sBAAsB,WAAW,cAAc;AACnE,yBAAS,KAAK,gCAAgC,IAAI,CAAC;AAAA,cACvD,SACO,GAAG;AACN,oBAAI,sBAAsB,aAAa,qBAAqB;AACxD;AAAA,gBACJ,OACK;AACD,wBAAM;AAAA,gBACV;AAAA,cACJ;AAAA,YACJ,SAAS,OAAO,QAAQ,GAAG;AAC3B,gBAAI,SAAS,SAAS,KAAK,SAAS,MAAM,GAAG,EAAE,EAAE,KAAK,OAAK,EAAE,SAAS,mBAAmB,GAAG;AACxF,oBAAM,IAAI,MAAM,iDAAiD;AAAA,YACrE;AACA,mBAAO;AAAA,cACH,MAAM;AAAA,cACN;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,YAAM,iBAAiB,eAAe;AAAA,QAClC,MAAM;AAAA,QACN,QAAQ,CAAC,MAAM,SAAS,SAAS,OAAQ,SAAS,OAAO,SAAS;AAAA,QAClE,YAAY,WAAW;AAAA,QACvB,YAAY,CAAC,QAAQ,SAAS;AAC1B,gBAAM,MAAM,OAAO,QAAQ,GAAG;AAC9B,iBAAO,QAAQ,GAAG;AAClB,gBAAM,UAAU,CAAC;AACjB,aAAG;AACC,oBAAQ,KAAK,OAAO,UAAU,WAAW,cAAc,CAAC;AAAA,UAC5D,SAAS,OAAO,QAAQ,GAAG;AAC3B,cAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,kBAAM,IAAI,MAAM,qCAAqC;AAAA,UACzD;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,MAAM,iBAAiB,IAAI;AAAA,YAC3B,UAAU;AAAA,YACV,MAAM;AAAA,cACF,UAAU;AAAA,cACV;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,eAAe,eAAe;AAAA,QAChC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,YAAY,WAAW;AAAA,QACvB,YAAY,CAAC,QAAQ,SAAS;AAC1B,iBAAO,QAAQ,GAAG;AAClB,gBAAM,WAAW,CAAC;AAClB,aAAG;AACC,qBAAS,KAAK,OAAO,UAAU,WAAW,KAAK,CAAC;AAAA,UACpD,SAAS,OAAO,QAAQ,GAAG;AAC3B,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,UAAU,CAAC,iBAAiB,IAAI,GAAG,GAAG,QAAQ;AAAA,UAClD;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,cAAc;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,2BAA2B;AAAA,UACvB,oBAAoB;AAAA,QACxB,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAEA,eAAS,sBAAsB,EAAE,8BAA8B,qBAAqB,aAAAC,aAAY,GAAG;AAC/F,eAAO,SAAS,gBAAgB,QAAQ,YAAY,MAAM;AACtD,cAAK,QAAQ,QAAS,cAAc,WAAW,WAAW;AACtD,mBAAO;AAAA,UACX;AACA,gBAAM,OAAO,OAAO,MAAM,QAAQ;AAClC,gBAAM,OAAO,OAAO,MAAM,KAAK;AAC/B,gBAAM,SAAU,SAAS,OAAO,SAAS,OACpC,SAAS,QAAQ,gCAAgC,KAAK,SAAS,oBAC/D,wBAAwB,SAAS,OAAO,SAAS;AACtD,cAAI,CAAC,QAAQ;AACT,mBAAO;AAAA,UACX;AACA,cAAI;AACJ,cAAI,WAAW;AACf,cAAI,OAAO,QAAQ,GAAG,GAAG;AACrB,uBAAW;AAAA,UACf,WACS,OAAO,QAAQ,GAAG,GAAG;AAC1B,uBAAW;AACX,uBAAW;AAAA,UACf,WACS,OAAO,QAAQ,GAAG,GAAG;AAC1B,uBAAW;AAAA,UACf,OACK;AACD,mBAAO,QAAQ,GAAG;AAClB,uBAAW;AAAA,UACf;AACA,gBAAM,aAAaA,iBAAgB,OAC7B,IAAI,OAAOA,cAAa,OAAO,OAAO,MAAM,IAC5C;AACN,gBAAM,SAAS,WAAW,sBAAsB,WAAW,SAAS;AACpE,iBAAO,iBAAiB,UAAU;AAClC,cAAI;AACJ,kBAAQ,OAAO,MAAM;AAAA,YACjB,KAAK;AACD,sBAAQ;AAAA,gBACJ,MAAM;AAAA,gBACN,OAAO,OAAO;AAAA,gBACd,MAAM;AAAA,kBACF,OAAO;AAAA,gBACX;AAAA,cACJ;AACA;AAAA,YACJ,KAAK;AACD,sBAAQ;AAAA,gBACJ,MAAM;AAAA,gBACN,OAAO,OAAO,MAAM,SAAS,EAAE;AAAA,gBAC/B,MAAM;AAAA,kBACF,OAAO;AAAA,gBACX;AAAA,cACJ;AACA;AAAA,YACJ,KAAK;AACD,sBAAQ;AAAA,gBACJ,MAAM;AAAA,gBACN,OAAO,OAAO;AAAA,gBACd,MAAM;AAAA,kBACF,OAAO,OAAO,KAAK;AAAA,gBACvB;AAAA,cACJ;AACA;AAAA,YACJ,KAAK;AACD,kBAAI,OAAO,gBAAgB,SAAS;AAChC,wBAAQ;AAAA,cACZ,OACK;AACD,sBAAM,IAAI,oBAAoB,QAAQ,0EAA8E;AAAA,cACxH;AACA;AAAA,YACJ;AACI,oBAAM,IAAI,oBAAoB,QAAQ,gGAAwG;AAAA,UACtJ;AACA,cAAI,YAAY,CAAC,OAAO,QAAQ,GAAG,GAAG;AAClC,kBAAM,QAAQ,OAAO,MAAM;AAC3B,kBAAM,IAAI,MAAM,gDAAgD,MAAM,IAAI,gBACxD,MAAM,IAAI,GAAG;AAAA,UACnC;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,MAAM,iBAAiB,IAAI;AAAA,YAC3B;AAAA,YACA;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAEA,eAAS,kBAAkB,EAAE,wBAAwB,GAAG;AACpD,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,QAAQ,UAAQ,SAAS,gBAAgB,SAAS,UAAU,SAAS,SAAS,wBAAwB,SAAS,IAAI;AAAA,UACnH,aAAa,YAAU;AACnB,kBAAM,EAAE,MAAM,KAAK,IAAI,OAAO,MAAM;AACpC,mBAAO,QAAQ,IAAI;AACnB,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,OAAO;AAAA,YACX;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,YAAM,qBAAqB,eAAe;AAAA,QACtC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,YAAU;AACnB,gBAAM,OAAO,OAAO,MAAM,QAAQ;AAClC,iBAAO,QAAQ,aAAa;AAC5B,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,OAAO,KAAK,MAAM,GAAG,EAAE;AAAA,YACvB,MAAM;AAAA,cACF,OAAO,KAAK,CAAC,MAAM,MAAO,WAAW;AAAA,YACzC;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,eAAS,6BAA6B,EAAE,aAAAA,cAAa,aAAa,GAAG;AACjE,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,QAAQ,UAAQ,aAAa,SAAS,IAAI;AAAA,UAC1C,aAAa,YAAU;AACnB,kBAAM,OAAO,OAAO,MAAM,QAAQ;AAClC,mBAAO,QAAQ,IAAI;AACnB,gBAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,qBAAO;AAAA,gBACH,MAAM;AAAA,gBACN,OAAO;AAAA,cACX;AAAA,YACJ;AACA,gBAAI;AACJ,gBAAI,QAAQ,OAAO,MAAM;AACzB,gBAAI,OAAO,QAAQ,aAAa,GAAG;AAC/B,uBAAS;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO,MAAM,KAAK,MAAM,GAAG,EAAE;AAAA,gBAC7B,aAAa;AAAA,gBACb,MAAM;AAAA,kBACF,OAAO,MAAM,KAAK,CAAC,MAAM,MAAO,WAAW;AAAA,gBAC/C;AAAA,cACJ;AAAA,YACJ,OACK;AACD,kBAAI,QAAQ;AACZ,oBAAM,UAAU,CAAC,cAAc,KAAK,GAAG;AACvC,qBAAO,QAAQ,KAAK,CAAAC,UAAQ,OAAO,QAAQA,KAAI,CAAC,GAAG;AAC/C,yBAAS,MAAM;AACf,wBAAQ,OAAO,MAAM;AAAA,cACzB;AACA,uBAAS;AAAA,gBACL,MAAM;AAAA,gBACN;AAAA,gBACA,aAAa;AAAA,gBACb,MAAM;AAAA,kBACF,OAAO;AAAA,gBACX;AAAA,cACJ;AAAA,YACJ;AACA,kBAAM,eAAe,IAAI,OAAOD,cAAa,OAAO,OAAO,MAAM;AACjE,kBAAM,eAAe,aAAa,2BAA2B,QAAQ,WAAW,GAAG;AACnF,mBAAO,iBAAiB,YAAY;AACpC,mBAAO,iBAAiB,YAAY;AAAA,UACxC;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,YAAM,kBAAkB;AAAA,QACpB,kBAAkB;AAAA,UACd,yBAAyB,CAAC,YAAY,QAAQ;AAAA,QAClD,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA,sBAAsB;AAAA,UAClB,8BAA8B;AAAA,UAC9B,qBAAqB;AAAA,UACrB,aAAa;AAAA,QACjB,CAAC;AAAA,MACL;AACA,YAAM,cAAc;AAAA,QAChB,GAAG;AAAA,QACH,6BAA6B;AAAA,UACzB,cAAc,CAAC,OAAO;AAAA,UACtB,aAAa;AAAA,QACjB,CAAC;AAAA,MACL;AAEA,eAAS,cAAc,OAAO;AAC1B,YAAI;AACJ,YAAI,MAAM,SAAS,0BAA0B;AACzC,uBAAa,MAAM;AAAA,QACvB,WACS,MAAM,SAAS,wBAAwB;AAC5C,uBAAa,CAAC,MAAM,OAAO;AAAA,QAC/B,OACK;AACD,gBAAM,IAAI,oBAAoB,KAAK;AAAA,QACvC;AACA,eAAO,WAAW,IAAI,OAAK,gCAAgC,CAAC,CAAC;AAAA,MACjE;AACA,eAAS,qBAAqB,OAAO;AACjC,cAAM,aAAa,cAAc,KAAK;AACtC,YAAI,WAAW,KAAK,OAAK,EAAE,SAAS,mBAAmB,GAAG;AACtD,gBAAM,IAAI,MAAM,8BAA8B;AAAA,QAClD;AACA,eAAO;AAAA,MACX;AACA,eAAS,sBAAsB,EAAE,sBAAsB,mBAAmB,yBAAyB,0BAA0B,GAAG;AAC5H,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,QAAQ,CAAC,MAAM,SAAS,SAAS,cAAe,6BAA6B,SAAS,SAAS,SAAS;AAAA,UACxG,aAAa,YAAU;AACnB,kBAAM,aAAa,OAAO,QAAQ,KAAK;AACvC,mBAAO,QAAQ,UAAU;AACzB,kBAAM,iBAAiB,OAAO,MAAM,QAAQ,SAAS;AACrD,gBAAI,CAAC,gBAAgB;AACjB,kBAAI,CAAC,yBAAyB;AAC1B,sBAAM,IAAI,MAAM,oCAAoC;AAAA,cACxD;AACA,qBAAO;AAAA,gBACH,MAAM;AAAA,gBACN,OAAO;AAAA,cACX;AAAA,YACJ;AACA,gBAAI,SAAS;AAAA,cACT,MAAM;AAAA,cACN,YAAY,CAAC;AAAA,cACb,OAAO;AAAA,cACP,aAAa;AAAA,cACb,aAAa;AAAA,YACjB;AACA,kBAAM,QAAQ,OAAO,sBAAsB,WAAW,QAAQ;AAC9D,gBAAI,yBAAyB,QAAW;AACpC,qBAAO,aAAa,qBAAqB,KAAK;AAAA,YAClD,WACS,cAAc,MAAM,SAAS,uBAAuB,MAAM,OAAO;AACtE,uBAAS;AACT,qBAAO,cAAc;AACrB,qBAAO;AAAA,YACX,OACK;AACD,qBAAO,aAAa,cAAc,KAAK;AACvC,yBAAW,KAAK,OAAO,YAAY;AAC/B,oBAAI,EAAE,SAAS,uBAAwB,CAAC,qBAAqB,SAAS,EAAE,GAAG,GAAI;AAC3E,wBAAM,IAAI,MAAM,qCAAqC,qBAAqB,KAAK,IAAI,CAAC,YAAY,EAAE,IAAI,EAAE;AAAA,gBAC5G;AAAA,cACJ;AAAA,YACJ;AACA,gBAAI,OAAO,QAAQ,GAAG,GAAG;AACrB,qBAAO,aAAa,OAAO,UAAU,WAAW,MAAM;AAAA,YAC1D,OACK;AACD,kBAAI,CAAC,mBAAmB;AACpB,sBAAM,IAAI,MAAM,iCAAiC;AAAA,cACrD;AAAA,YACJ;AACA,mBAAO;AAAA,UACX;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,eAAS,sBAAsB,EAAE,cAAc,uBAAuB,GAAG;AACrE,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,QAAQ,UAAQ,SAAS;AAAA,UACzB,YAAY,WAAW;AAAA,UACvB,aAAa,YAAU;AACnB,mBAAO,QAAQ,KAAK;AACpB,kBAAM,WAAW,0BAA0B,OAAO,QAAQ,GAAG;AAC7D,gBAAI;AACA,oBAAM,UAAU,OAAO,UAAU,WAAW,MAAM;AAClD,kBAAI,YAAY,CAAC,OAAO,QAAQ,GAAG,GAAG;AAClC,sBAAM,IAAI,MAAM,yCAA2C;AAAA,cAC/D;AACA,qBAAO;AAAA,gBACH,MAAM;AAAA,gBACN,SAAS,iBAAiB,OAAO;AAAA,gBACjC,MAAM;AAAA,kBACF,UAAU;AAAA,kBACV,gBAAgB;AAAA,gBACpB;AAAA,cACJ;AAAA,YACJ,SACO,GAAG;AACN,kBAAI,aAAa,qBAAqB;AAClC,oBAAI,UAAU;AACV,wBAAM,IAAI,MAAM,qDAAqD;AAAA,gBACzE;AACA,uBAAO;AAAA,kBACH,MAAM;AAAA,kBACN,MAAM;AAAA,oBACF,UAAU;AAAA,oBACV,gBAAgB;AAAA,kBACpB;AAAA,gBACJ;AAAA,cACJ,OACK;AACD,sBAAM;AAAA,cACV;AAAA,YACJ;AAAA,UACJ;AAAA,UACA,YAAY,eACN,CAAC,QAAQ,SAAS;AAChB,mBAAO,QAAQ,KAAK;AACpB,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,SAAS,iBAAiB,IAAI;AAAA,cAC9B,MAAM;AAAA,gBACF,UAAU;AAAA,gBACV,gBAAgB;AAAA,cACpB;AAAA,YACJ;AAAA,UACJ,IACE;AAAA,QACV,CAAC;AAAA,MACL;AAEA,YAAM,gBAAgB,eAAe;AAAA,QACjC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,YAAY,WAAW;AAAA,QACvB,YAAY,CAAC,QAAQ,SAAS;AAC1B,cAAI,KAAK,SAAS,iBAAiB;AAC/B,kBAAM,IAAI,MAAM,2DAA6D;AAAA,UACjF;AACA,iBAAO,QAAQ,GAAG;AAClB,gBAAM,SAAS;AAAA,YACX,MAAM;AAAA,YACN,OAAO,KAAK;AAAA,UAChB;AACA,cAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,kBAAM,OAAO,OAAO,sBAAsB,WAAW,MAAM;AAC3D,mBAAO,UAAU,iCAAiC,IAAI;AACtD,gBAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,oBAAM,IAAI,MAAM,iCAAiC;AAAA,YACrD;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA,MACJ,CAAC;AAED,YAAM,uBAAuB,eAAe;AAAA,QACxC,MAAM;AAAA,QACN,YAAY,WAAW;AAAA,QACvB,QAAQ,CAAC,MAAM,SAAS,SAAS,OAAO,SAAS;AAAA,QACjD,YAAY,CAAC,QAAQ,SAAS;AAC1B,iBAAO,QAAQ,GAAG;AAClB,iBAAO,QAAQ,GAAG;AAClB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,MAAM;AAAA,cACF,MAAM;AAAA,cACN,OAAO;AAAA,YACX;AAAA,YACA,UAAU;AAAA,cACN,iBAAiB,IAAI;AAAA,YACzB;AAAA,YACA,MAAM;AAAA,cACF,UAAU;AAAA,cACV,KAAK;AAAA,YACT;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,eAAS,oBAAoB,EAAE,oBAAAE,qBAAoB,cAAc,GAAG;AAChE,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,QAAQ,UAAQ,SAAS;AAAA,UACzB,aAAa,YAAU;AACnB,mBAAO,QAAQ,GAAG;AAClB,kBAAM,SAAS;AAAA,cACX,MAAM;AAAA,cACN,MAAM;AAAA,gBACF,WAAW;AAAA,cACf;AAAA,cACA,UAAU,CAAC;AAAA,YACf;AACA,gBAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,kBAAI;AACJ,oBAAM,cAAc,IAAI,OAAOA,qBAAoB,OAAO,OAAO,MAAM;AACvE,qBAAO,MAAM;AACT,4BAAY,iBAAiB,MAAM;AACnC,oBAAI,QAAQ,YAAY,sBAAsB,WAAW,MAAM;AAC/D,uBAAO,iBAAiB,WAAW;AACnC,oBAAI,UAAU,UAAa,eAAe;AACtC,0BAAQ,OAAO,sBAAsB,WAAW,MAAM;AAAA,gBAC1D;AACA,oBAAI,WAAW;AACf,oBAAI,MAAM,SAAS,qBAAqB;AACpC,6BAAW;AACX,0BAAQ,MAAM;AAAA,gBAClB;AACA,oBAAI,MAAM,SAAS,qBAAqB,MAAM,SAAS,mBAAmB,MAAM,SAAS,wBAAwB;AAC7G,sBAAIC;AACJ,sBAAI,MAAM,SAAS,wBAAwB;AACvC,oBAAAA,SAAQ,MAAM,KAAK;AAAA,kBACvB;AACA,yBAAO,SAAS,KAAK;AAAA,oBACjB,MAAM;AAAA,oBACN,KAAK,MAAM,MAAM,SAAS;AAAA,oBAC1B,OAAO;AAAA,oBACP;AAAA,oBACA,UAAU;AAAA,oBACV,MAAM;AAAA,sBACF,OAAAA;AAAA,oBACJ;AAAA,kBACJ,CAAC;AAAA,gBACL,WACS,MAAM,SAAS,0BAA0B,MAAM,SAAS,6BAA6B;AAC1F,yBAAO,SAAS,KAAK,KAAK;AAAA,gBAC9B,OACK;AACD,wBAAM,IAAI,oBAAoB,KAAK;AAAA,gBACvC;AACA,oBAAI,OAAO,MAAM,QAAQ,aAAa;AAClC,8BAAY;AAAA,gBAChB,WACS,OAAO,QAAQ,GAAG,GAAG;AAC1B,8BAAY;AAAA,gBAChB,WACS,OAAO,QAAQ,GAAG,GAAG;AAC1B,8BAAY;AAAA,gBAChB,OACK;AACD;AAAA,gBACJ;AACA,sBAAM,OAAO,OAAO,MAAM,QAAQ;AAClC,oBAAI,SAAS,KAAK;AACd;AAAA,gBACJ;AAAA,cACJ;AACA,qBAAO,KAAK,YAAY,cAAc,QAAQ,cAAc,SAAS,YAAY;AACjF,kBAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,sBAAM,IAAI,MAAM,uCAAyC;AAAA,cAC7D;AAAA,YACJ;AACA,mBAAO;AAAA,UACX;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,eAAS,yBAAyB,EAAE,wBAAwB,eAAe,eAAe,cAAc,GAAG;AACvG,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,YAAY,WAAW;AAAA,UACvB,QAAQ,UAAQ,SAAS;AAAA,UACzB,YAAY,CAAC,QAAQ,SAAS;AAC1B,gBAAI;AACJ,gBAAI,WAAW;AACf,gBAAI,mBAAmB;AACvB,gBAAI,iBAAiB,KAAK,SAAS,qBAAqB;AACpD,yBAAW;AACX,qBAAO,KAAK;AAAA,YAChB;AACA,gBAAI,iBAAiB,KAAK,SAAS,6BAA6B;AAC5D,iCAAmB;AACnB,qBAAO,KAAK;AAAA,YAChB;AAEA,kBAAM,gBAAgB,KAAK,OAAO,gBAAgB,QAAQ,OAAO,SAAS,KAAK;AAC/E,yBAAa,iBAAiB,MAAM;AACpC,gBAAI,KAAK,SAAS,qBAAqB,KAAK,SAAS,mBAAmB,KAAK,SAAS,0BAClF,kBAAkB,IAAI,GAAG;AACzB,kBAAI,kBAAkB,IAAI,KAAK,CAAC,wBAAwB;AACpD,sBAAM,IAAI,oBAAoB,IAAI;AAAA,cACtC;AACA,2BAAa,QAAQ,GAAG;AACxB,kBAAIA;AACJ,kBAAI,KAAK,SAAS,wBAAwB;AACtC,gBAAAA,SAAQ,KAAK,KAAK;AAAA,cACtB;AACA,oBAAM,QAAQ,aAAa,UAAU,WAAW,SAAS;AACzD,qBAAO,iBAAiB,YAAY;AACpC,qBAAO;AAAA,gBACH,MAAM;AAAA,gBACN,KAAK,kBAAkB,IAAI,IAAI,OAAO,KAAK,MAAM,SAAS;AAAA,gBAC1D;AAAA,gBACA;AAAA,gBACA,UAAU;AAAA,gBACV,MAAM;AAAA,kBACF,OAAAA;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ,OACK;AACD,kBAAI,CAAC,eAAe;AAChB,sBAAM,IAAI,oBAAoB,IAAI;AAAA,cACtC;AACA,2BAAa,QAAQ,GAAG;AACxB,oBAAM,QAAQ,aAAa,UAAU,WAAW,SAAS;AACzD,qBAAO,iBAAiB,YAAY;AACpC,qBAAO;AAAA,gBACH,MAAM;AAAA,gBACN,MAAM,iBAAiB,IAAI;AAAA,gBAC3B;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,eAAS,sBAAsB,EAAE,eAAe,cAAc,GAAG;AAC7D,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,YAAY,WAAW;AAAA,UACvB,QAAQ,UAAQ,SAAS;AAAA,UACzB,YAAY,CAAC,QAAQ,SAAS;AAC1B,gBAAI,WAAW;AACf,gBAAI,WAAW;AACf,gBAAI,iBAAiB,KAAK,SAAS,qBAAqB;AACpD,yBAAW;AACX,qBAAO,KAAK;AAAA,YAChB;AACA,gBAAI,iBAAiB,KAAK,SAAS,uBAAuB,KAAK,YAAY,QAAW;AAClF,yBAAW;AACX,qBAAO,KAAK;AAAA,YAChB;AACA,gBAAI,KAAK,SAAS,iBAAiB;AAC/B,oBAAM,IAAI,oBAAoB,IAAI;AAAA,YACtC;AACA,mBAAO,QAAQ,GAAG;AAClB,kBAAM,QAAQ,OAAO,UAAU,WAAW,SAAS;AACnD,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,KAAK,KAAK;AAAA,cACV;AAAA,cACA;AAAA,cACA;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,YAAM,mBAAmB;AAAA,QACrB,GAAG;AAAA,QACH,sBAAsB;AAAA,UAClB,yBAAyB;AAAA,UACzB,sBAAsB,CAAC,QAAQ,KAAK;AAAA,UACpC,mBAAmB;AAAA,UACnB,2BAA2B;AAAA,QAC/B,CAAC;AAAA,QACD;AAAA,QACA,6BAA6B;AAAA,UACzB,cAAc,CAAC,UAAU,YAAY,OAAO;AAAA,UAC5C;AAAA,QACJ,CAAC;AAAA,QACD,sBAAsB;AAAA,UAClB,wBAAwB;AAAA,UACxB,cAAc;AAAA,QAClB,CAAC;AAAA,QACD,kBAAkB;AAAA,UACd,yBAAyB,CAAC,OAAO;AAAA,QACrC,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA,sBAAsB;AAAA,UAClB,8BAA8B;AAAA,UAC9B,qBAAqB;AAAA,UACrB;AAAA,QACJ,CAAC;AAAA,MACL;AACA,YAAM,eAAe;AAAA,QACjB,GAAG;AAAA,QACH,oBAAoB;AAAA;AAAA;AAAA,UAGhB,oBAAoB;AAAA,YAChB,kBAAkB;AAAA,cACd,yBAAyB,CAAC,UAAU,IAAI;AAAA,YAC5C,CAAC;AAAA,YACD,yBAAyB;AAAA,cACrB,wBAAwB;AAAA,cACxB,eAAe;AAAA,cACf,eAAe;AAAA,cACf,eAAe;AAAA,YACnB,CAAC;AAAA,YACD,GAAG;AAAA,UACP;AAAA,UACA,eAAe;AAAA,QACnB,CAAC;AAAA,QACD,sBAAsB;AAAA,UAClB,eAAe;AAAA,UACf,eAAe;AAAA,QACnB,CAAC;AAAA,MACL;AAEA,YAAM,gBAAgB,eAAe;AAAA,QACjC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,YAAU;AACnB,iBAAO,QAAQ,QAAQ;AACvB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,iBAAiB,OAAO,UAAU,WAAW,cAAc,CAAC;AAAA,UACzE;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,uBAAuB;AAAA,QACzB,kBAAkB;AAAA,UACd,yBAAyB,CAAC,UAAU,SAAS,SAAS,YAAY,IAAI;AAAA,QAC1E,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,yBAAyB;AAAA,UACrB,wBAAwB;AAAA,UACxB,eAAe;AAAA,UACf,eAAe;AAAA,UACf,eAAe;AAAA,QACnB,CAAC;AAAA,MACL;AACA,YAAM,iBAAiB;AAAA,QACnB,GAAG;AAAA,QACH,oBAAoB;AAAA,UAChB,eAAe;AAAA,UACf,oBAAoB;AAAA,QACxB,CAAC;AAAA,QACD,kBAAkB;AAAA,UACd,yBAAyB,CAAC,SAAS,YAAY,IAAI;AAAA,QACvD,CAAC;AAAA,QACD;AAAA,QACA,sBAAsB;AAAA,UAClB,yBAAyB;AAAA,UACzB,sBAAsB,CAAC,QAAQ,KAAK;AAAA,UACpC,mBAAmB;AAAA,UACnB,2BAA2B;AAAA,QAC/B,CAAC;AAAA,QACD,sBAAsB;AAAA,UAClB,wBAAwB;AAAA,UACxB,cAAc;AAAA,QAClB,CAAC;AAAA;AAAA,QAED,kBAAkB;AAAA,UACd,yBAAyB,CAAC,OAAO;AAAA,QACrC,CAAC;AAAA,QACD,6BAA6B;AAAA,UACzB,cAAc,CAAC,QAAQ;AAAA,UACvB;AAAA,QACJ,CAAC;AAAA,QACD,sBAAsB;AAAA,UAClB,8BAA8B;AAAA,UAC9B,qBAAqB;AAAA,UACrB;AAAA,QACJ,CAAC;AAAA,QACD,sBAAsB;AAAA,UAClB,eAAe;AAAA,UACf,eAAe;AAAA,QACnB,CAAC;AAAA,QACD;AAAA,MACJ;AAEA,YAAM,iBAAiB,eAAe;AAAA,QAClC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,CAAC,WAAW;AACrB,iBAAO,QAAQ,SAAS;AACxB,gBAAM,OAAO,OAAO,sBAAsB,WAAW,MAAM;AAC3D,cAAI,KAAK,SAAS,iBAAiB;AAC/B,kBAAM,IAAI,oBAAoB,MAAM,kEAAkE;AAAA,UAC1G;AACA,iBAAO,QAAQ,IAAI;AACnB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN;AAAA,YACA,OAAO,iBAAiB,OAAO,sBAAsB,WAAW,KAAK,CAAC;AAAA,UAC1E;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,eAAS,mBAAmB,EAAE,kBAAkB,GAAG;AAC/C,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,QAAQ,UAAQ,SAAS;AAAA,UACzB,aAAa,YAAU;AACnB,mBAAO,QAAQ,GAAG;AAClB,kBAAM,SAAS;AAAA,cACX,MAAM;AAAA,cACN,UAAU,CAAC;AAAA,YACf;AACA,gBAAI,OAAO,QAAQ,GAAG,GAAG;AACrB,qBAAO;AAAA,YACX;AACA,kBAAM,WAAW,OAAO,sBAAsB,WAAW,GAAG;AAC5D,gBAAI,SAAS,SAAS,0BAA0B;AAC5C,kBAAI,SAAS,SAAS,CAAC,EAAE,SAAS,qBAAqB;AACnD,uBAAO,WAAW,SAAS,SAAS,IAAI,yBAAyB;AAAA,cACrE,OACK;AACD,uBAAO,WAAW,SAAS,SAAS,IAAI,gBAAgB;AAAA,cAC5D;AAAA,YACJ,OACK;AACD,kBAAI,SAAS,SAAS,qBAAqB;AACvC,uBAAO,WAAW,CAAC,0BAA0B,QAAQ,CAAC;AAAA,cAC1D,OACK;AACD,uBAAO,WAAW,CAAC,iBAAiB,QAAQ,CAAC;AAAA,cACjD;AAAA,YACJ;AACA,gBAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,oBAAM,IAAI,MAAM,kBAAoB;AAAA,YACxC;AACA,gBAAI,CAAC,qBAAqB,OAAO,SAAS,KAAK,CAAC,MAAM,EAAE,SAAS,kBAAkB,GAAG;AAClF,oBAAM,IAAI,MAAM,oCAAoC;AAAA,YACxD;AACA,mBAAO;AAAA,UACX;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,YAAM,eAAe,eAAe;AAAA,QAChC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,YAAU;AACnB,iBAAO,QAAQ,OAAO;AACtB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,iBAAiB,OAAO,UAAU,WAAW,cAAc,CAAC;AAAA,UACzE;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,gBAAgB,eAAe;AAAA,QACjC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,YAAU;AACnB,iBAAO,QAAQ,QAAQ;AACvB,cAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,kBAAM,IAAI,MAAM,0CAA0C;AAAA,UAC9D;AACA,gBAAM,OAAO,OAAO,UAAU,WAAW,MAAM;AAC/C,cAAI,KAAK,SAAS,wBAAwB;AACtC,kBAAM,IAAI,MAAM,qDAAqD;AAAA,UACzE;AACA,cAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,kBAAM,IAAI,MAAM,kDAAkD;AAAA,UACtE;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,0BAA0B,eAAe;AAAA,QAC3C,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,YAAU;AACnB,iBAAO,QAAQ,UAAU;AACzB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,OAAO,UAAU,WAAW,SAAS;AAAA,UAClD;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,uBAAuB,eAAe;AAAA,QACxC,MAAM;AAAA,QACN,YAAY,WAAW;AAAA,QACvB,QAAQ,UAAQ,SAAS;AAAA,QACzB,YAAY,CAAC,QAAQ,SAAS;AAC1B,iBAAO,QAAQ,IAAI;AACnB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,YAAY,cAAc,IAAI,EAAE,IAAI,+BAA+B;AAAA,YACnE,OAAO;AAAA,YACP,aAAa;AAAA,YACb,aAAa;AAAA,YACb,YAAY,OAAO,UAAU,WAAW,MAAM;AAAA,UAClD;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,sBAAsB,eAAe;AAAA,QACvC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,YAAY,WAAW;AAAA,QACvB,YAAY,CAAC,QAAQ,SAAS;AAC1B,iBAAO,QAAQ,GAAG;AAClB,gBAAM,WAAW,CAAC;AAClB,aAAG;AACC,qBAAS,KAAK,OAAO,UAAU,WAAW,YAAY,CAAC;AAAA,UAC3D,SAAS,OAAO,QAAQ,GAAG;AAC3B,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,UAAU,CAAC,iBAAiB,IAAI,GAAG,GAAG,QAAQ;AAAA,UAClD;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,mBAAmB,eAAe;AAAA,QACpC,MAAM;AAAA,QACN,YAAY,WAAW;AAAA,QACvB,QAAQ,UAAQ,SAAS;AAAA,QACzB,YAAY,CAAC,QAAQ,SAAS;AAC1B,cAAI,KAAK,SAAS,iBAAiB;AAC/B,kBAAM,IAAI,oBAAoB,MAAM,oEAAoE;AAAA,UAC5G;AACA,iBAAO,QAAQ,IAAI;AACnB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN;AAAA,YACA,OAAO,iBAAiB,OAAO,sBAAsB,WAAW,KAAK,CAAC;AAAA,UAC1E;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,+BAA+B,eAAe;AAAA,QAChD,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,YAAU;AACnB,cAAI,OAAO,eAAe,QAAW;AACjC,kBAAM,IAAI,MAAM,oCAAoC;AAAA,UACxD;AACA,iBAAO,QAAQ,GAAG;AAClB,gBAAM,MAAM,OAAO,MAAM,QAAQ;AACjC,iBAAO,QAAQ,YAAY;AAC3B,cAAI;AACJ,cAAI,OAAO,QAAQ,GAAG,GAAG;AACrB,kBAAM,eAAe,OAAO;AAC5B,yBAAa,iBAAiB,MAAM;AACpC,qBAAS;AAAA,cACL,MAAM;AAAA,cACN;AAAA,cACA,OAAO,aAAa,UAAU,WAAW,cAAc;AAAA,YAC3D;AACA,mBAAO,iBAAiB,YAAY;AAAA,UACxC,WACS,OAAO,QAAQ,IAAI,GAAG;AAC3B,kBAAM,eAAe,OAAO;AAC5B,yBAAa,iBAAiB,MAAM;AACpC,qBAAS;AAAA,cACL,MAAM;AAAA,cACN;AAAA,cACA,OAAO,aAAa,UAAU,WAAW,cAAc;AAAA,YAC3D;AACA,mBAAO,iBAAiB,YAAY;AAAA,UACxC,OACK;AACD,kBAAM,IAAI,MAAM,uDAA2D;AAAA,UAC/E;AACA,cAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,kBAAM,IAAI,MAAM,8BAA8B;AAAA,UAClD;AACA,iBAAO;AAAA,QACX;AAAA,MACJ,CAAC;AAED,YAAM,qBAAqB;AAAA,QACvB;AAAA,QACA,kBAAkB;AAAA,UACd,yBAAyB,CAAC,UAAU,SAAS,SAAS,SAAS,YAAY,IAAI;AAAA,QACnF,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,yBAAyB;AAAA,UACrB,wBAAwB;AAAA,UACxB,eAAe;AAAA,UACf,eAAe;AAAA,UACf,eAAe;AAAA,QACnB,CAAC;AAAA,QACD;AAAA,MACJ;AACA,YAAM,oBAAoB;AAAA,QACtB,GAAG;AAAA,QACH,oBAAoB;AAAA,UAChB,eAAe;AAAA,UACf;AAAA,QACJ,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,sBAAsB;AAAA,UAClB,yBAAyB;AAAA,UACzB,mBAAmB;AAAA,UACnB,sBAAsB,CAAC,QAAQ,OAAO,MAAM;AAAA,UAC5C,2BAA2B;AAAA,QAC/B,CAAC;AAAA,QACD,mBAAmB;AAAA,UACf,mBAAmB;AAAA,QACvB,CAAC;AAAA,QACD,sBAAsB;AAAA,UAClB,wBAAwB;AAAA,UACxB,cAAc;AAAA,QAClB,CAAC;AAAA,QACD;AAAA,QACA,kBAAkB;AAAA,UACd,yBAAyB,CAAC,SAAS,YAAY,IAAI;AAAA,QACvD,CAAC;AAAA,QACD,6BAA6B;AAAA,UACzB,cAAc,CAAC,QAAQ;AAAA,UACvB;AAAA,QACJ,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA,sBAAsB;AAAA,UAClB,8BAA8B;AAAA,UAC9B,qBAAqB;AAAA,UACrB;AAAA,QACJ,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA,sBAAsB;AAAA,UAClB,eAAe;AAAA,UACf,eAAe;AAAA,QACnB,CAAC;AAAA,MACL;AAOA,eAAS,MAAM,YAAY,MAAM;AAC7B,gBAAQ,MAAM;AAAA,UACV,KAAK;AACD,mBAAQ,IAAI,OAAO,gBAAgB,UAAU,EAAG,MAAM;AAAA,UAC1D,KAAK;AACD,mBAAQ,IAAI,OAAO,cAAc,UAAU,EAAG,MAAM;AAAA,UACxD,KAAK;AACD,mBAAQ,IAAI,OAAO,mBAAmB,UAAU,EAAG,MAAM;AAAA,QACjE;AAAA,MACJ;AAQA,eAAS,SAAS,YAAY,QAAQ,CAAC,cAAc,WAAW,OAAO,GAAG;AACtE,YAAI;AACJ,mBAAW,QAAQ,OAAO;AACtB,cAAI;AACA,mBAAO,MAAM,YAAY,IAAI;AAAA,UACjC,SACO,GAAG;AACN,oBAAQ;AAAA,UACZ;AAAA,QACJ;AACA,cAAM;AAAA,MACV;AAEA,eAAS,UAAUC,QAAO,aAAa;AACnC,cAAM,OAAOA,OAAM,YAAY,IAAI;AACnC,YAAI,SAAS,QAAW;AACpB,gBAAM,IAAI,MAAM,0DAA0D,YAAY,IAAI,GAAG;AAAA,QACjG;AACA,eAAO,KAAK,aAAa,kBAAgB,UAAUA,QAAO,YAAY,CAAC;AAAA,MAC3E;AACA,eAAS,sBAAsB,aAAa;AACxC,cAAM,IAAI,MAAM,2EAA2E;AAAA,MAC/F;AACA,eAAS,qBAAqB,QAAQ;AAClC,cAAM,SAAS;AAAA,UACX,QAAQ,CAAC;AAAA,QACb;AACA,mBAAW,SAAS,OAAO,YAAY;AACnC,cAAI,MAAM,SAAS,qBAAqB;AACpC,gBAAI,MAAM,QAAQ,QAAQ;AACtB,qBAAO,OAAO,MAAM;AAAA,YACxB,WACS,MAAM,QAAQ,OAAO;AAC1B,qBAAO,MAAM,MAAM;AAAA,YACvB,OACK;AACD,qBAAO,OAAO,KAAK,KAAK;AAAA,YAC5B;AAAA,UACJ,OACK;AACD,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC5B;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAEA,eAAS,cAAc,UAAU,QAAQ,OAAO;AAC5C,eAAO,aAAa,WAAW,QAAQ,SAAS,SAAS;AAAA,MAC7D;AACA,eAAS,MAAM,OAAOD,QAAO;AACzB,gBAAQA,QAAO;AAAA,UACX,KAAK;AACD,mBAAO,IAAI,KAAK;AAAA,UACpB,KAAK;AACD,mBAAO,IAAI,KAAK;AAAA,UACpB,KAAK;AACD,mBAAO;AAAA,QACf;AAAA,MACJ;AACA,eAAS,iBAAiB;AACtB,eAAO;AAAA,UACH,sBAAsB,CAAC,QAAQE,eAAc,IAAI,OAAO,YAAY,SAAYA,WAAU,OAAO,OAAO,IAAI,EAAE;AAAA,UAC9G,gBAAgB,CAAC,QAAQA,eAAc,SAASA,WAAU,OAAO,OAAO,CAAC;AAAA,UACzE,mBAAmB,CAAC,QAAQA,eAAc;AACtC,gBAAI,CAAC,OAAO,OAAO;AACf,kBAAI,cAAc,OAAO,cAAc,QAAQ;AAC/C,kBAAI,CAAC,OAAO,aAAa;AACrB,uBAAO;AAAA,cACX;AACA,6BAAe,IAAI,OAAO,WAAW,IAAIA,UAAS,EAAE,KAAK,IAAI,CAAC;AAC9D,kBAAI,OAAO,eAAe,QAAW;AACjC,+BAAe,KAAKA,WAAU,OAAO,UAAU,CAAC;AAAA,cACpD;AACA,qBAAO;AAAA,YACX,OACK;AACD,kBAAI,OAAO,eAAe,QAAW;AACjC,sBAAM,IAAI,MAAM,qCAAqC;AAAA,cACzD;AACA,kBAAI,cAAc,IAAI,OAAO,WAAW,IAAIA,UAAS,EAAE,KAAK,IAAI,CAAC,QAAQA,WAAU,OAAO,UAAU,CAAC;AACrG,kBAAI,OAAO,aAAa;AACpB,8BAAc,SAAS;AAAA,cAC3B;AACA,qBAAO;AAAA,YACX;AAAA,UACJ;AAAA,UACA,eAAe,YAAU,OAAO;AAAA,UAChC,gBAAgB,CAAC,QAAQA,eAAc,IAAI,OAAO,SAAS,IAAIA,UAAS,EAAE,KAAK,IAAI,CAAC;AAAA,UACpF,mBAAmB,CAAC,QAAQA,eAAc,OAAO,KAAK,aAAa,SAC7D,QACA,cAAc,OAAO,KAAK,UAAUA,WAAU,OAAO,OAAO,GAAG,KAAK;AAAA,UAC1E,mBAAmB,CAAC,QAAQA,eAAc;AACtC,kBAAM,OAAOA,WAAU,OAAO,IAAI;AAClC,kBAAM,QAAQA,WAAU,OAAO,KAAK;AACpC,oBAAQ,OAAO,UAAU;AAAA,cACrB,KAAK;AACD,uBAAO,GAAG,IAAI,IAAI,KAAK;AAAA,cAC3B,KAAK;AACD,uBAAO,GAAG,IAAI,IAAI,KAAK;AAAA,cAC3B,KAAK;AACD,uBAAO,GAAG,IAAI,IAAI,KAAK;AAAA,cAC3B,KAAK;AACD,uBAAO,GAAG,IAAI,IAAI,KAAK;AAAA,YAC/B;AAAA,UACJ;AAAA,UACA,sBAAsB,YAAU,MAAM,OAAO,OAAO,OAAO,KAAK,KAAK;AAAA,UACrE,cAAc,MAAM;AAAA,UACpB,kBAAkB,CAAC,QAAQA,eAAc;AACrC,gBAAI,OAAO,KAAK,aAAa,UAAU;AACnC,oBAAM,UAAU,OAAO,SAAS,CAAC;AACjC,oBAAM,cAAcA,WAAU,OAAO;AACrC,kBAAI,QAAQ,SAAS,oBAAoB,QAAQ,SAAS,yBAAyB;AAC/E,uBAAO,IAAI,WAAW;AAAA,cAC1B,OACK;AACD,uBAAO,GAAG,WAAW;AAAA,cACzB;AAAA,YACJ,OACK;AACD,qBAAO,GAAGA,WAAU,OAAO,IAAI,CAAC,GAAG,OAAO,KAAK,MAAM,MAAM,EAAE,IAAI,OAAO,SAAS,IAAIA,UAAS,EAAE,KAAK,IAAI,CAAC;AAAA,YAC9G;AAAA,UACJ;AAAA,UACA,iBAAiB,CAAC,QAAQA,eAAc,UAAUA,WAAU,OAAO,OAAO,CAAC;AAAA,UAC3E,sBAAsB,CAAC,QAAQA,eAAc;AACzC,gBAAI,OAAO;AACX,gBAAI,OAAO,UAAU;AACjB,sBAAQ;AAAA,YACZ;AACA,gBAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,sBAAQ,MAAM,OAAO,KAAK,OAAO,KAAK,KAAK;AAAA,YAC/C,OACK;AACD,sBAAQA,WAAU,OAAO,GAAG;AAAA,YAChC;AACA,gBAAI,OAAO,UAAU;AACjB,sBAAQ;AAAA,YACZ;AACA,gBAAI,OAAO,UAAU,QAAW;AAC5B,qBAAO;AAAA,YACX,OACK;AACD,qBAAO,OAAO,KAAKA,WAAU,OAAO,KAAK,CAAC;AAAA,YAC9C;AAAA,UACJ;AAAA,UACA,2BAA2B,CAAC,QAAQA,eAAc;AAC9C,mBAAO,GAAGA,WAAU,OAAO,IAAI,CAAC,KAAKA,WAAU,OAAO,KAAK,CAAC;AAAA,UAChE;AAAA,UACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,gBAAI,OAAO,OAAO;AAClB,gBAAI,OAAO,UAAU;AACjB,sBAAQ;AAAA,YACZ;AACA,gBAAI,OAAO,UAAU;AACjB,qBAAO,QAAQ;AAAA,YACnB;AACA,gBAAI,OAAO,UAAU,QAAW;AAC5B,qBAAO;AAAA,YACX,OACK;AACD,qBAAO,OAAO,KAAKA,WAAU,OAAO,KAAK,CAAC;AAAA,YAC9C;AAAA,UACJ;AAAA,UACA,0BAA0B,YAAU,GAAG,OAAO,WAAW,IAAI,MAAM,OAAO,OAAO,OAAO,KAAK,KAAK,CAAC;AAAA,UACnG,sBAAsB,CAAC,QAAQA,eAAc,cAAc,OAAO,KAAK,UAAUA,WAAU,OAAO,OAAO,GAAG,GAAG;AAAA,UAC/G,eAAe,MAAM;AAAA,UACrB,mBAAmB,CAAC,QAAQA,eAAc,cAAc,OAAO,KAAK,UAAUA,WAAU,OAAO,OAAO,GAAG,GAAG;AAAA,UAC5G,iBAAiB,YAAU,OAAO,MAAM,SAAS;AAAA,UACjD,iBAAiB,CAAC,QAAQA,eAAc,IAAI,OAAO,SAAS,IAAIA,UAAS,EAAE,MAAM,OAAO,KAAK,cAAc,UAAU,MAAM,OAAO,GAAG,CAAC;AAAA,UACtI,mBAAmB,CAAC,QAAQA,eAAc,cAAc,OAAO,KAAK,UAAUA,WAAU,OAAO,OAAO,GAAG,GAAG;AAAA,UAC5G,iBAAiB,CAAC,QAAQA,eAAc,GAAG,OAAO,KAAK,IAAI,OAAO,YAAY,SAAYA,WAAU,OAAO,OAAO,IAAI,EAAE;AAAA,UACxH,iBAAiB,CAAC,QAAQA,eAAc,UAAUA,WAAU,OAAO,OAAO,CAAC;AAAA,UAC3E,oBAAoB,MAAM;AAAA,UAC1B,gBAAgB,CAAC,QAAQA,eAAc,OAAO,SAAS,IAAIA,UAAS,EAAE,KAAK,KAAK;AAAA,UAChF,kBAAkB,MAAM;AAAA,UACxB,uBAAuB,CAAC,QAAQA,eAAc,OAAO,SAAS,IAAIA,UAAS,EAAE,KAAK,KAAK;AAAA,UACvF,mBAAmB,YAAU,MAAM,OAAO,OAAO,OAAO,KAAK,KAAK;AAAA,UAClE,oBAAoB,CAAC,QAAQA,eAAc,GAAGA,WAAU,OAAO,IAAI,CAAC,OAAOA,WAAU,OAAO,KAAK,CAAC;AAAA,UAClG,yBAAyB,CAAC,QAAQA,eAAc,IAAI,OAAO,GAAG,KAAKA,WAAU,OAAO,KAAK,CAAC;AAAA,UAC1F,qBAAqB,CAAC,QAAQA,eAAc,IAAI,OAAO,GAAG,OAAOA,WAAU,OAAO,KAAK,CAAC;AAAA,UACxF,kBAAkB,CAAC,QAAQA,eAAc,WAAWA,WAAU,OAAO,IAAI,CAAC,OAAOA,WAAU,OAAO,KAAK,CAAC;AAAA,QAC5G;AAAA,MACJ;AACA,YAAM,uBAAuB,eAAe;AAC5C,eAAS,UAAU,QAAQ;AACvB,eAAO,UAAU,sBAAsB,MAAM;AAAA,MACjD;AAEA,YAAM,gBAAgB;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AACA,eAAS,SAAS,OAAO;AACrB,cAAM,SAAS;AAAA,UACX,MAAM;AAAA,UACN,MAAM;AAAA,QACV;AACA,YAAI,cAAc,SAAS,KAAK,GAAG;AAC/B,iBAAO,eAAe;AAAA,QAC1B;AACA,eAAO;AAAA,MACX;AACA,YAAM,0BAA0B;AAAA,QAC5B,mBAAmB,CAAC,QAAQA,eAAc;AACtC,gBAAM,cAAcA,WAAU,OAAO,OAAO;AAC5C,sBAAY,WAAW;AACvB,iBAAO;AAAA,QACX;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,gBAAM,cAAcA,WAAU,OAAO,OAAO;AAC5C,sBAAY,WAAW;AACvB,iBAAO;AAAA,QACX;AAAA,QACA,sBAAsB,CAAC,QAAQA,eAAc;AACzC,gBAAM,cAAcA,WAAU,OAAO,OAAO;AAC5C,sBAAY,WAAW;AACvB,iBAAO;AAAA,QACX;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,cAAI,OAAO,YAAY,QAAW;AAC9B,kBAAM,IAAI,MAAM,sDAAsD;AAAA,UAC1E;AACA,gBAAM,cAAcA,WAAU,OAAO,OAAO;AAC5C,sBAAY,aAAa;AACzB,iBAAO;AAAA,QACX;AAAA,QACA,cAAc,OAAO;AAAA,UACjB,MAAM;AAAA,QACV;AAAA,QACA,eAAe,OAAO;AAAA,UAClB,MAAM;AAAA,QACV;AAAA,QACA,sBAAsB,YAAU,SAAS,MAAM,OAAO,OAAO,OAAO,KAAK,KAAK,CAAC;AAAA,QAC/E,oBAAoB,OAAO;AAAA,UACvB,MAAM;AAAA,QACV;AAAA,QACA,kBAAkB,OAAO;AAAA,UACrB,MAAM;AAAA,QACV;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,gBAAM,SAAS,qBAAqB,MAAM;AAC1C,gBAAM,cAAc;AAAA,YAChB,MAAM;AAAA,YACN,QAAQ,OAAO,OAAO,IAAIA,UAAS;AAAA,UACvC;AACA,cAAI,OAAO,SAAS,QAAW;AAC3B,wBAAY,OAAOA,WAAU,OAAO,IAAI;AAAA,UAC5C;AACA,cAAI,OAAO,QAAQ,QAAW;AAC1B,wBAAY,MAAMA,WAAU,OAAO,GAAG;AAAA,UAC1C;AACA,cAAI,OAAO,eAAe,QAAW;AACjC,wBAAY,SAASA,WAAU,OAAO,UAAU;AAAA,UACpD;AACA,iBAAO;AAAA,QACX;AAAA,QACA,kBAAkB,CAAC,QAAQA,gBAAe;AAAA,UACtC,MAAM;AAAA,UACN,cAAc,OAAO,SAAS,IAAI,OAAKA,WAAU,CAAC,CAAC;AAAA,UACnD,YAAYA,WAAU,OAAO,IAAI;AAAA,QACrC;AAAA,QACA,0BAA0B,YAAU,SAAS,OAAO,cAAc,MAAM,MAAM,OAAO,OAAO,OAAO,KAAK,KAAK,CAAC;AAAA,QAC9G,eAAe,YAAU;AACrB,cAAI,OAAO,UAAU,YAAY;AAC7B,mBAAO,SAAS,OAAO,KAAK;AAAA,UAChC,OACK;AACD,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,QAAQ,CAAC;AAAA,YACb;AAAA,UACJ;AAAA,QACJ;AAAA,QACA,iBAAiB,YAAU,SAAS,OAAO,MAAM,SAAS,CAAC;AAAA,QAC3D,iBAAiB,CAAC,QAAQA,eAAc;AACpC,gBAAM,cAAc;AAAA,YAChB,MAAM;AAAA,YACN,QAAQ,CAAC;AAAA,UACb;AACA,qBAAW,SAAS,OAAO,UAAU;AACjC,gBAAI,MAAM,SAAS,0BAA0B,MAAM,SAAS,6BAA6B;AACrF,0BAAY,OAAO,KAAK;AAAA,gBACpB,MAAM;AAAA,gBACN,KAAKA,WAAU,KAAK;AAAA,gBACpB,OAAO;AAAA,cACX,CAAC;AAAA,YACL,OACK;AACD,0BAAY,OAAO,KAAKA,WAAU,KAAK,CAAC;AAAA,YAC5C;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA,QACA,sBAAsB,CAAC,QAAQA,eAAc;AACzC,cAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,kBAAM,IAAI,MAAM,qDAAqD;AAAA,UACzE;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,KAAK,SAAS,MAAM,OAAO,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,YAClD,OAAO,OAAO,UAAU,SAAY,SAAYA,WAAU,OAAO,KAAK;AAAA,UAC1E;AAAA,QACJ;AAAA,QACA,2BAA2B,CAAC,QAAQA,gBAAe;AAAA,UAC/C,MAAM;AAAA,UACN,KAAKA,WAAU,OAAO,IAAI;AAAA,UAC1B,OAAOA,WAAU,OAAO,KAAK;AAAA,QACjC;AAAA,QACA,gBAAgB,CAAC,QAAQA,gBAAe;AAAA,UACpC,MAAM;AAAA,UACN,UAAU,OAAO,SAAS,IAAI,OAAKA,WAAU,CAAC,CAAC;AAAA,QACnD;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,KAAK,SAAS,OAAO,GAAG;AAAA,YACxB,OAAO,OAAO,UAAU,SAAY,SAAYA,WAAU,OAAO,KAAK;AAAA,UAC1E;AAAA,QACJ;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,gBAAM,aAAaA,WAAU,OAAO,IAAI;AACxC,cAAI;AACJ,cAAI,OAAO,MAAM,SAAS,4BAA4B;AAClD,yBAAaA,WAAU,OAAO,KAAK,EAAE;AAAA,UACzC,OACK;AACD,yBAAa,MAAM,OAAO,MAAM,OAAO,OAAO,MAAM,KAAK,KAAK;AAAA,UAClE;AACA,gBAAM,SAAS,OAAO,aAAa,UAAU,MAAM,OAAO,aAAa,aAAa,MAAM;AAC1F,iBAAO,SAAS,GAAG,WAAW,IAAI,GAAG,MAAM,GAAG,UAAU,EAAE;AAAA,QAC9D;AAAA,QACA,iBAAiB,YAAU;AACvB,cAAI,QAAQ;AACZ,cAAI,UAAU,OAAO;AACrB,cAAI,eAAe;AACnB,eAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,qBAAqB;AAC1F,gBAAI,QAAQ,KAAK,aAAa,UAAU;AACpC,sBAAQ;AAAA,YACZ,OACK;AACD,6BAAe;AAAA,YACnB;AACA,sBAAU,QAAQ;AAAA,UACtB;AACA,eAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,iBAAiB;AACtF,qBAAS,QAAQ;AAAA,UACrB,YACU,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,mBAAmB;AAC7F,qBAAS,QAAQ,MAAM,SAAS;AAAA,UACpC;AACA,cAAI,cAAc;AACd,qBAAS;AAAA,UACb;AACA,iBAAO,SAAS,GAAG,OAAO,KAAK,IAAI,KAAK,GAAG;AAAA,QAC/C;AAAA,QACA,sBAAsB,CAAC,QAAQA,eAAcA,WAAU,iBAAiB,OAAO,OAAO,CAAC;AAAA,QACvF,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,QACzB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,uBAAuB;AAAA,QACvB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QACpB,kBAAkB;AAAA,MACtB;AACA,eAAS,mBAAmB,QAAQ;AAChC,eAAO,UAAU,yBAAyB,MAAM;AAAA,MACpD;AAEA,eAAS,cAAcF,QAAO;AAC1B,gBAAQA,QAAO;AAAA,UACX,KAAK;AACD,mBAAO;AAAA,UACX,KAAK;AACD,mBAAO;AAAA,UACX,KAAK;AACD,mBAAO;AAAA,QACf;AAAA,MACJ;AACA,eAAS,cAAc,MAAM;AACzB,gBAAQ,MAAM;AAAA,UACV,KAAK;AACD,mBAAO;AAAA,UACX,KAAK;AACD,mBAAO;AAAA,UACX,KAAK;AACD,mBAAO;AAAA,UACX,KAAK;AACD,mBAAO;AAAA,QACf;AAAA,MACJ;AACA,eAAS,YAAY,MAAM,SAAS;AAChC,YAAI,QAAQ,WAAW,GAAG;AACtB,iBAAO;AAAA,YACH;AAAA,YACA,MAAM,QAAQ,CAAC;AAAA,YACf,OAAO,QAAQ,CAAC;AAAA,UACpB;AAAA,QACJ,OACK;AACD,iBAAO;AAAA,YACH;AAAA,YACA,MAAM,QAAQ,CAAC;AAAA,YACf,OAAO,YAAY,MAAM,QAAQ,MAAM,CAAC,CAAC;AAAA,UAC7C;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,WAAW;AAAA,QACb,mBAAmB,CAAC,QAAQE,gBAAe;AAAA,UACvC,MAAM;AAAA,UACN,OAAOA,WAAU,OAAO,OAAO;AAAA,UAC/B,MAAM;AAAA,YACF,QAAQ,OAAO,KAAK,aAAa,WAAW,sBAAsB;AAAA,UACtE;AAAA,QACJ;AAAA,QACA,mBAAmB,CAAC,QAAQA,gBAAe;AAAA,UACvC,MAAM;AAAA,UACN,OAAOA,WAAU,OAAO,OAAO;AAAA,UAC/B,MAAM;AAAA,YACF,QAAQ,OAAO,KAAK,aAAa,WAAW,yBAAyB;AAAA,UACzE;AAAA,QACJ;AAAA,QACA,sBAAsB,CAAC,QAAQA,gBAAe;AAAA,UAC1C,MAAM;AAAA,UACN,OAAOA,WAAU,OAAO,OAAO;AAAA,UAC/B,MAAM;AAAA,YACF,QAAQ,OAAO,KAAK,aAAa,WAAW,gBAAgB;AAAA,UAChE;AAAA,QACJ;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,gBAAM,cAAc;AAAA,YAChB,MAAM;AAAA,YACN,MAAM;AAAA,cACF,QAAQ,OAAO,KAAK,aAAa,WAC3B,gBACA,OAAO,KAAK,aAAa,WAAW,gBAAgB;AAAA,YAC9D;AAAA,UACJ;AACA,cAAI,OAAO,YAAY,QAAW;AAC9B,wBAAY,QAAQA,WAAU,OAAO,OAAO;AAAA,UAChD;AACA,iBAAO;AAAA,QACX;AAAA,QACA,eAAe,aAAW;AAAA,UACtB,MAAM;AAAA,UACN,MAAM,OAAO;AAAA,QACjB;AAAA,QACA,iBAAiB,CAAC,QAAQA,gBAAe;AAAA,UACrC,MAAM;AAAA,UACN,MAAMA,WAAU,OAAO,OAAO;AAAA,QAClC;AAAA,QACA,gBAAgB,CAAC,QAAQA,gBAAe;AAAA,UACpC,MAAM;AAAA,UACN,SAAS,OAAO,SAAS,IAAIA,UAAS;AAAA,QAC1C;AAAA,QACA,gBAAgB,CAAC,QAAQA,gBAAe;AAAA,UACpC,MAAM;AAAA,UACN,OAAOA,WAAU,OAAO,OAAO;AAAA,QACnC;AAAA,QACA,iBAAiB,aAAW;AAAA,UACxB,MAAM;AAAA,UACN,MAAM;AAAA,YACF,MAAM;AAAA,YACN,YAAY,cAAc,OAAO,QAAQ,KAAK,KAAK;AAAA,YACnD,QAAQ,OAAO,QAAQ;AAAA,UAC3B;AAAA,QACJ;AAAA,QACA,oBAAoB,OAAO;AAAA,UACvB,MAAM;AAAA,UACN,MAAM;AAAA,QACV;AAAA,QACA,cAAc,OAAO;AAAA,UACjB,MAAM;AAAA,QACV;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,gBAAM,gBAAgB,qBAAqB,MAAM;AACjD,gBAAM,cAAc;AAAA,YAChB,MAAM,OAAO,QAAQ,UAAU;AAAA,YAC/B,QAAQ,cAAc,OAAO,IAAI,WAAS;AACtC,kBAAI,MAAM,SAAS,qBAAqB;AACpC,oBAAI,MAAM,UAAU,QAAW;AAC3B,wBAAM,IAAI,MAAM,kEAAsE;AAAA,gBAC1F;AACA,uBAAO;AAAA,kBACH,MAAM;AAAA,kBACN,MAAM,MAAM;AAAA,kBACZ,UAAUA,WAAU,MAAM,KAAK;AAAA,gBACnC;AAAA,cACJ,OACK;AACD,uBAAOA,WAAU,KAAK;AAAA,cAC1B;AAAA,YACJ,CAAC;AAAA,YACD,KAAK;AAAA,YACL,SAAS;AAAA,UACb;AACA,cAAI,cAAc,SAAS,QAAW;AAClC,wBAAY,OAAOA,WAAU,cAAc,IAAI;AAAA,UACnD,WACS,CAAC,OAAO,OAAO;AACpB,wBAAY,OAAO;AAAA,UACvB;AACA,cAAI,cAAc,QAAQ,QAAW;AACjC,wBAAY,MAAMA,WAAU,cAAc,GAAG;AAAA,UACjD;AACA,cAAI,OAAO,eAAe,QAAW;AACjC,wBAAY,UAAUA,WAAU,OAAO,UAAU;AAAA,UACrD;AACA,iBAAO;AAAA,QACX;AAAA,QACA,kBAAkB,CAAC,QAAQA,eAAc;AACrC,gBAAM,cAAc;AAAA,YAChB,MAAM;AAAA,YACN,SAASA,WAAU,OAAO,IAAI;AAAA,YAC9B,SAAS,OAAO,SAAS,IAAIA,UAAS;AAAA,YACtC,MAAM;AAAA,cACF,QAAQ,OAAO,KAAK,aAAa,WAAW,mBAAmB,OAAO,KAAK,MAAM,2BAA2B;AAAA,YAChH;AAAA,UACJ;AACA,cAAI,OAAO,KAAK,aAAa,YAAY,OAAO,SAAS,CAAC,EAAE,SAAS,uBAAuB,CAAC,OAAO,SAAS,CAAC,EAAE,aAAa;AACzH,wBAAY,QAAQ,CAAC,IAAI;AAAA,cACrB,MAAM;AAAA,cACN,MAAM;AAAA,YACV;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA,QACA,sBAAsB,CAAC,QAAQA,eAAc;AACzC,cAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,kBAAM,IAAI,MAAM,qDAAqD;AAAA,UACzE;AACA,cAAI,OAAO,UAAU,QAAW;AAC5B,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,KAAK,OAAO;AAAA,cACZ,YAAY,cAAc,OAAO,KAAK,KAAK;AAAA,cAC3C,OAAO;AAAA,cACP,UAAU;AAAA,YACd;AAAA,UACJ;AACA,cAAI,QAAQA,WAAU,OAAO,KAAK;AAClC,cAAI,OAAO,UAAU;AACjB,oBAAQ;AAAA,cACJ,MAAM;AAAA,cACN,OAAO;AAAA,cACP,MAAM;AAAA,gBACF,QAAQ;AAAA,cACZ;AAAA,YACJ;AAAA,UACJ;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,KAAK,OAAO,IAAI,SAAS;AAAA,YACzB,YAAY,cAAc,OAAO,KAAK,KAAK;AAAA,YAC3C,OAAO;AAAA,YACP,UAAU;AAAA,UACd;AAAA,QACJ;AAAA,QACA,2BAA2B,MAAM;AAC7B,gBAAM,IAAI,MAAM,2CAA2C;AAAA,QAC/D;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,cAAI,OAAO,UAAU,QAAW;AAC5B,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,KAAK,OAAO;AAAA,cACZ,YAAY;AAAA,cACZ,OAAO;AAAA,cACP,UAAU;AAAA,YACd;AAAA,UACJ;AACA,cAAI,QAAQA,WAAU,OAAO,KAAK;AAClC,cAAI,OAAO,UAAU;AACjB,oBAAQ;AAAA,cACJ,MAAM;AAAA,cACN,OAAO;AAAA,cACP,MAAM;AAAA,gBACF,QAAQ;AAAA,cACZ;AAAA,YACJ;AAAA,UACJ;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,KAAK,OAAO;AAAA,YACZ,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,UAAU;AAAA,UACd;AAAA,QACJ;AAAA,QACA,iBAAiB,CAAC,QAAQA,eAAc;AACpC,gBAAM,UAAU,CAAC;AACjB,qBAAW,SAAS,OAAO,UAAU;AACjC,gBAAI,MAAM,SAAS,0BAA0B,MAAM,SAAS,6BAA6B;AACrF,sBAAQ,KAAKA,WAAU,KAAK,CAAC;AAAA,YACjC;AAAA,UACJ;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN;AAAA,UACJ;AAAA,QACJ;AAAA,QACA,0BAA0B,YAAU;AAChC,cAAI,OAAO,gBAAgB,UAAU;AACjC,kBAAM,IAAI,MAAM,yCAAyC,OAAO,WAAW,iBAAiB;AAAA,UAChG;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,OAAO;AAAA,cACH,MAAM;AAAA,cACN,YAAY,cAAc,OAAO,KAAK,KAAK;AAAA,cAC3C,MAAM,OAAO;AAAA,YACjB;AAAA,UACJ;AAAA,QACJ;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,cAAI,iBAAiB;AACrB,cAAI;AACJ,cAAI;AACJ,cAAI,OAAO,MAAM,SAAS,8BAA8B,OAAO,MAAM,gBAAgB,SAAS;AAC1F,6BAAiB;AACjB,mBAAO,OAAO,MAAM;AACpB,yBAAa,cAAc,OAAO,MAAM,KAAK,KAAK;AAAA,UACtD,OACK;AACD,mBAAO,OAAO,MAAM;AACpB,yBAAa,cAAc,OAAO,MAAM,KAAK,KAAK;AAAA,UACtD;AACA,gBAAM,cAAc;AAAA,YAChB,MAAM,cAAc,OAAO,QAAQ;AAAA,YACnC,OAAOA,WAAU,OAAO,IAAI;AAAA,YAC5B;AAAA,YACA;AAAA,YACA;AAAA,UACJ;AACA,cAAI,YAAY,MAAM,SAAS,UAAU;AACrC,kBAAM,UAAU,YAAY;AAC5B,wBAAY,QAAQ,YAAY,MAAM;AACtC,oBAAQ,QAAQ;AAChB,mBAAO;AAAA,UACX,OACK;AACD,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,QACA,gBAAgB,CAAC,QAAQA,eAAc,YAAY,SAAS,OAAO,SAAS,IAAIA,UAAS,CAAC;AAAA,QAC1F,sBAAsB,CAAC,QAAQA,gBAAe;AAAA,UAC1C,MAAM;AAAA,UACN,OAAOA,WAAU,iBAAiB,OAAO,OAAO,CAAC;AAAA,QACrD;AAAA,QACA,eAAe,OAAO;AAAA,UAClB,MAAM;AAAA,UACN,MAAM;AAAA,QACV;AAAA,QACA,kBAAkB,OAAO;AAAA,UACrB,MAAM;AAAA,QACV;AAAA,QACA,sBAAsB,aAAW;AAAA,UAC7B,MAAM;AAAA,UACN,YAAY,cAAc,OAAO,KAAK,KAAK;AAAA,UAC3C,QAAQ,OAAO;AAAA,QACnB;AAAA,QACA,uBAAuB,CAAC,QAAQA,eAAc,YAAY,gBAAgB,OAAO,SAAS,IAAIA,UAAS,CAAC;AAAA,QACxG,iBAAiB,aAAW;AAAA,UACxB,MAAM;AAAA,UACN,QAAQ,OAAO,MAAM,SAAS;AAAA,QAClC;AAAA,QACA,iBAAiB;AAAA,QACjB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,QACzB,kBAAkB;AAAA,MACtB;AACA,eAAS,aAAa,QAAQ;AAC1B,eAAO,UAAU,UAAU,MAAM;AAAA,MACrC;AAEA,eAAS,yBAAyB;AAC9B,eAAO;AAAA,UACH,uBAAuB,CAAC,QAAQA,gBAAe;AAAA,YAC3C,MAAM;AAAA,YACN,UAAU,OAAO,SAAS,IAAIA,UAAS;AAAA,UAC3C;AAAA,UACA,kBAAkB,CAAC,QAAQA,gBAAe;AAAA,YACtC,MAAM;AAAA,YACN,MAAMA,WAAU,OAAO,IAAI;AAAA,YAC3B,UAAU,OAAO,SAAS,IAAIA,UAAS;AAAA,YACvC,MAAM;AAAA,cACF,KAAK,OAAO,KAAK;AAAA,cACjB,UAAU,OAAO,KAAK;AAAA,YAC1B;AAAA,UACJ;AAAA,UACA,mBAAmB,YAAU;AAAA,UAC7B,gBAAgB,CAAC,QAAQA,gBAAe;AAAA,YACpC,MAAM;AAAA,YACN,UAAU,OAAO,SAAS,IAAIA,UAAS;AAAA,UAC3C;AAAA,UACA,kBAAkB,YAAU;AAAA,UAC5B,oBAAoB,YAAU;AAAA,UAC9B,iBAAiB,CAAC,QAAQA,gBAAe;AAAA,YACrC,MAAM;AAAA,YACN,SAASA,WAAU,OAAO,OAAO;AAAA,UACrC;AAAA,UACA,iBAAiB,CAAC,QAAQA,eAAc;AACpC,kBAAM,cAAc;AAAA,cAChB,MAAM;AAAA,cACN,OAAO,OAAO;AAAA,YAClB;AACA,gBAAI,OAAO,YAAY,QAAW;AAC9B,0BAAY,UAAUA,WAAU,OAAO,OAAO;AAAA,YAClD;AACA,mBAAO;AAAA,UACX;AAAA,UACA,mBAAmB,CAAC,QAAQA,gBAAe;AAAA,YACvC,MAAM;AAAA,YACN,SAASA,WAAU,OAAO,OAAO;AAAA,YACjC,MAAM;AAAA,cACF,UAAU,OAAO,KAAK;AAAA,YAC1B;AAAA,UACJ;AAAA,UACA,iBAAiB,CAAC,QAAQA,gBAAe;AAAA,YACrC,MAAM;AAAA,YACN,MAAM;AAAA,cACF,WAAW;AAAA,YACf;AAAA,YACA,UAAU,OAAO,SAAS,IAAIA,UAAS;AAAA,UAC3C;AAAA,UACA,iBAAiB,YAAU;AAAA,UAC3B,eAAe,YAAU;AAAA,UACzB,sBAAsB,CAAC,QAAQA,gBAAe;AAAA,YAC1C,MAAM;AAAA,YACN,SAASA,WAAU,OAAO,OAAO;AAAA,YACjC,MAAM;AAAA,cACF,UAAU,OAAO,KAAK;AAAA,YAC1B;AAAA,UACJ;AAAA,UACA,0BAA0B,YAAU;AAAA,UACpC,sBAAsB,CAAC,QAAQA,gBAAe;AAAA,YAC1C,MAAM;AAAA,YACN,KAAK,OAAO;AAAA,YACZ,OAAO,OAAO,UAAU,SAAY,SAAYA,WAAU,OAAO,KAAK;AAAA,YACtE,UAAU,OAAO;AAAA,YACjB,UAAU,OAAO;AAAA,YACjB,MAAM,OAAO;AAAA,UACjB;AAAA,UACA,2BAA2B,CAAC,QAAQA,gBAAe;AAAA,YAC/C,MAAM;AAAA,YACN,MAAMA,WAAU,OAAO,IAAI;AAAA,YAC3B,OAAOA,WAAU,OAAO,KAAK;AAAA,UACjC;AAAA,UACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,KAAK,OAAO;AAAA,cACZ,OAAO,OAAO,UAAU,SAAY,SAAYA,WAAU,OAAO,KAAK;AAAA,cACtE,UAAU,OAAO;AAAA,cACjB,UAAU,OAAO;AAAA,YACrB;AAAA,UACJ;AAAA,UACA,iBAAiB,CAAC,QAAQA,gBAAe;AAAA,YACrC,MAAM;AAAA,YACN,SAASA,WAAU,OAAO,OAAO;AAAA,UACrC;AAAA,UACA,cAAc,YAAU;AAAA,UACxB,sBAAsB,YAAU;AAAA,UAChC,mBAAmB,YAAU;AAAA,UAC7B,mBAAmB,CAAC,QAAQA,eAAc;AACtC,kBAAM,cAAc;AAAA,cAChB,MAAM;AAAA,cACN,MAAM;AAAA,gBACF,UAAU,OAAO,KAAK;AAAA,gBACtB,gBAAgB,OAAO,KAAK;AAAA,cAChC;AAAA,YACJ;AACA,gBAAI,OAAO,YAAY,QAAW;AAC9B,0BAAY,UAAUA,WAAU,OAAO,OAAO;AAAA,YAClD;AACA,mBAAO;AAAA,UACX;AAAA,UACA,gBAAgB,CAAC,QAAQA,gBAAe;AAAA,YACpC,MAAM;AAAA,YACN,UAAU,OAAO,SAAS,IAAIA,UAAS;AAAA,UAC3C;AAAA,UACA,eAAe,YAAU;AAAA,UACzB,mBAAmB,CAAC,QAAQA,eAAc;AACtC,kBAAM,cAAc;AAAA,cAChB,MAAM;AAAA,cACN,OAAO,OAAO;AAAA,cACd,YAAY,OAAO,WAAW,IAAIA,UAAS;AAAA,cAC3C,aAAa,OAAO;AAAA,cACpB,aAAa,OAAO;AAAA,YACxB;AACA,gBAAI,OAAO,eAAe,QAAW;AACjC,0BAAY,aAAaA,WAAU,OAAO,UAAU;AAAA,YACxD;AACA,mBAAO;AAAA,UACX;AAAA,UACA,gBAAgB,CAAC,QAAQA,gBAAe;AAAA,YACpC,MAAM;AAAA,YACN,SAASA,WAAU,OAAO,OAAO;AAAA,UACrC;AAAA,UACA,sBAAsB,CAAC,QAAQA,gBAAe;AAAA,YAC1C,MAAM;AAAA,YACN,SAASA,WAAU,OAAO,OAAO;AAAA,UACrC;AAAA,UACA,mBAAmB,YAAU;AAAA,UAC7B,oBAAoB,CAAC,QAAQA,gBAAe;AAAA,YACxC,MAAM;AAAA,YACN,MAAMA,WAAU,OAAO,IAAI;AAAA,YAC3B,OAAOA,WAAU,OAAO,KAAK;AAAA,UACjC;AAAA,UACA,yBAAyB,CAAC,QAAQA,gBAAe;AAAA,YAC7C,MAAM;AAAA,YACN,KAAK,OAAO;AAAA,YACZ,OAAOA,WAAU,OAAO,KAAK;AAAA,UACjC;AAAA,UACA,qBAAqB,CAAC,QAAQA,gBAAe;AAAA,YACzC,MAAM;AAAA,YACN,KAAK,OAAO;AAAA,YACZ,OAAOA,WAAU,OAAO,KAAK;AAAA,UACjC;AAAA,UACA,kBAAkB,CAAC,QAAQA,gBAAe;AAAA,YACtC,MAAM;AAAA,YACN,MAAMA,WAAU,OAAO,IAAI;AAAA,YAC3B,OAAOA,WAAU,OAAO,KAAK;AAAA,UACjC;AAAA,QACJ;AAAA,MACJ;AAEA,YAAM,cAAc;AAAA,QAChB,cAAc,CAAC;AAAA,QACf,mBAAmB,CAAC,cAAc,YAAY;AAAA,QAC9C,kBAAkB,CAAC,QAAQ,UAAU;AAAA,QACrC,iBAAiB,CAAC;AAAA,QAClB,yBAAyB,CAAC,OAAO;AAAA,QACjC,uBAAuB,CAAC,UAAU;AAAA,QAClC,gBAAgB,CAAC,SAAS;AAAA,QAC1B,mBAAmB,CAAC,OAAO;AAAA,QAC3B,qBAAqB,CAAC,OAAO;AAAA,QAC7B,eAAe,CAAC;AAAA,QAChB,mBAAmB,CAAC,QAAQ,OAAO;AAAA,QACnC,sBAAsB,CAAC,SAAS;AAAA,QAChC,eAAe,CAAC;AAAA,QAChB,mBAAmB,CAAC,SAAS;AAAA,QAC7B,iBAAiB,CAAC;AAAA,QAClB,iBAAiB,CAAC,UAAU;AAAA,QAC5B,sBAAsB,CAAC,OAAO;AAAA,QAC9B,2BAA2B,CAAC,QAAQ,OAAO;AAAA,QAC3C,mBAAmB,CAAC,SAAS;AAAA,QAC7B,sBAAsB,CAAC,SAAS;AAAA,QAChC,0BAA0B,CAAC;AAAA,QAC3B,sBAAsB,CAAC;AAAA,QACvB,iBAAiB,CAAC,SAAS;AAAA,QAC3B,gBAAgB,CAAC,UAAU;AAAA,QAC3B,iBAAiB,CAAC,SAAS;AAAA,QAC3B,oBAAoB,CAAC;AAAA,QACrB,gBAAgB,CAAC,UAAU;AAAA,QAC3B,kBAAkB,CAAC;AAAA,QACnB,mBAAmB,CAAC,SAAS;AAAA,QAC7B,mBAAmB,CAAC;AAAA,QACpB,oBAAoB,CAAC,QAAQ,OAAO;AAAA,QACpC,kBAAkB,CAAC,QAAQ,OAAO;AAAA,MACtC;AAEA,eAAS,UAAU,MAAM,YAAY,UAAU,SAAS,SAAS;AAC7D,oBAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,MAAM,YAAY,QAAQ;AACpF,cAAM,cAAc,YAAY,KAAK,IAAI;AACzC,mBAAW,OAAO,aAAa;AAC3B,gBAAM,QAAQ,KAAK,GAAG;AACtB,cAAI,UAAU,QAAW;AACrB,gBAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,yBAAW,WAAW,OAAO;AACzB,0BAAU,SAAS,MAAM,KAAK,SAAS,OAAO;AAAA,cAClD;AAAA,YACJ,OACK;AACD,wBAAU,OAAO,MAAM,KAAK,SAAS,OAAO;AAAA,YAChD;AAAA,UACJ;AAAA,QACJ;AACA,oBAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,MAAM,YAAY,QAAQ;AAAA,MACxF;AAOA,eAAS,SAAS,MAAM,SAAS,SAAS;AACtC,kBAAU,MAAM,QAAW,QAAW,SAAS,OAAO;AAAA,MAC1D;AAEA,MAAAP,SAAQ,qBAAqB;AAC7B,MAAAA,SAAQ,yBAAyB;AACjC,MAAAA,SAAQ,eAAe;AACvB,MAAAA,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,iBAAiB;AACzB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,WAAW;AACnB,MAAAA,SAAQ,WAAW;AACnB,MAAAA,SAAQ,cAAc;AAAA,IAE1B,CAAE;AAAA;AAAA;", "names": ["exports", "Precedence", "pathGrammar", "type", "objectFieldGrammar", "quote", "rules", "transform"]}