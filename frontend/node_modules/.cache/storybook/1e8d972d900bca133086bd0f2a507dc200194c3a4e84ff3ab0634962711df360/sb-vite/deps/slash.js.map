{"version": 3, "sources": ["../../../../../slash/index.js"], "sourcesContent": ["'use strict';\nmodule.exports = path => {\n\tconst isExtendedLengthPath = /^\\\\\\\\\\?\\\\/.test(path);\n\tconst hasNonAscii = /[^\\u0000-\\u0080]+/.test(path); // eslint-disable-line no-control-regex\n\n\tif (isExtendedLengthPath || hasNonAscii) {\n\t\treturn path;\n\t}\n\n\treturn path.replace(/\\\\/g, '/');\n};\n"], "mappings": ";;;;;AAAA;AAAA;AACA,WAAO,UAAU,UAAQ;AACxB,YAAM,uBAAuB,YAAY,KAAK,IAAI;AAClD,YAAM,cAAc,oBAAoB,KAAK,IAAI;AAEjD,UAAI,wBAAwB,aAAa;AACxC,eAAO;AAAA,MACR;AAEA,aAAO,KAAK,QAAQ,OAAO,GAAG;AAAA,IAC/B;AAAA;AAAA;", "names": []}