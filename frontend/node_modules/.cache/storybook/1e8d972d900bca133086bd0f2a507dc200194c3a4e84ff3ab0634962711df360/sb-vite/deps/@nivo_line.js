import {
  $r,
  Ai,
  Bt,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  T,
  Ti,
  <PERSON>,
  Wi,
  X,
  X2,
  <PERSON>e,
  Zr,
  _i,
  animated,
  area_default,
  bt,
  dn,
  g,
  j,
  ji,
  k,
  line_default,
  mt,
  pr,
  useSpring,
  w,
  w2,
  yt,
  z,
  zt
} from "./chunk-E5QKIBDY.js";
import "./chunk-3ZRET7BV.js";
import {
  require_prop_types
} from "./chunk-Q7ZCQDVE.js";
import "./chunk-TTKHV656.js";
import "./chunk-AZWIPJ2R.js";
import "./chunk-XS2UDL7X.js";
import "./chunk-GQKKNNL7.js";
import "./chunk-D3GWGFQY.js";
import "./chunk-4BVSGRBS.js";
import "./chunk-576SI5Y5.js";
import "./chunk-QKAKBYX7.js";
import "./chunk-3RUYYLMN.js";
import "./chunk-PBLA2M5Y.js";
import "./chunk-OGGZ2H5M.js";
import "./chunk-VANSMPSB.js";
import "./chunk-AFKAW5NK.js";
import "./chunk-L2MYVCJB.js";
import "./chunk-IMZCYE2V.js";
import "./chunk-EBHN46KS.js";
import "./chunk-A2RL5ZZB.js";
import "./chunk-5TUT4DVI.js";
import "./chunk-GZNJXC33.js";
import "./chunk-IYBXXNPR.js";
import "./chunk-AX2LFTCP.js";
import "./chunk-LBPJMYE3.js";
import "./chunk-CNJEIZNL.js";
import "./chunk-YWE747UV.js";
import "./chunk-HKPTUGQM.js";
import "./chunk-2HO6J6X4.js";
import "./chunk-WJPTHV5P.js";
import "./chunk-KHZOBGHN.js";
import "./chunk-KXJRCZQV.js";
import "./chunk-B5CG2ER2.js";
import "./chunk-37GKQVQ3.js";
import "./chunk-LD63QSJ3.js";
import "./chunk-Y3FQZQPT.js";
import "./chunk-DH5RY6YN.js";
import "./chunk-URS5MDWH.js";
import "./chunk-LCZ7HEDH.js";
import {
  require_jsx_runtime
} from "./chunk-LHKICBWK.js";
import "./chunk-U62OR5NG.js";
import {
  require_react
} from "./chunk-JXCADB3Y.js";
import {
  __toESM
} from "./chunk-2LSFTFF7.js";

// node_modules/@nivo/line/dist/nivo-line.es.js
var import_react2 = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var import_jsx_runtime2 = __toESM(require_jsx_runtime());

// node_modules/@nivo/voronoi/dist/nivo-voronoi.es.js
var import_react = __toESM(require_react());

// node_modules/delaunator/index.js
var EPSILON = Math.pow(2, -52);
var EDGE_STACK = new Uint32Array(512);
var Delaunator = class _Delaunator {
  static from(points, getX = defaultGetX, getY = defaultGetY) {
    const n3 = points.length;
    const coords = new Float64Array(n3 * 2);
    for (let i3 = 0; i3 < n3; i3++) {
      const p2 = points[i3];
      coords[2 * i3] = getX(p2);
      coords[2 * i3 + 1] = getY(p2);
    }
    return new _Delaunator(coords);
  }
  constructor(coords) {
    const n3 = coords.length >> 1;
    if (n3 > 0 && typeof coords[0] !== "number")
      throw new Error("Expected coords to contain numbers.");
    this.coords = coords;
    const maxTriangles = Math.max(2 * n3 - 5, 0);
    this._triangles = new Uint32Array(maxTriangles * 3);
    this._halfedges = new Int32Array(maxTriangles * 3);
    this._hashSize = Math.ceil(Math.sqrt(n3));
    this._hullPrev = new Uint32Array(n3);
    this._hullNext = new Uint32Array(n3);
    this._hullTri = new Uint32Array(n3);
    this._hullHash = new Int32Array(this._hashSize).fill(-1);
    this._ids = new Uint32Array(n3);
    this._dists = new Float64Array(n3);
    this.update();
  }
  update() {
    const { coords, _hullPrev: hullPrev, _hullNext: hullNext, _hullTri: hullTri, _hullHash: hullHash } = this;
    const n3 = coords.length >> 1;
    let minX = Infinity;
    let minY = Infinity;
    let maxX = -Infinity;
    let maxY = -Infinity;
    for (let i3 = 0; i3 < n3; i3++) {
      const x2 = coords[2 * i3];
      const y = coords[2 * i3 + 1];
      if (x2 < minX)
        minX = x2;
      if (y < minY)
        minY = y;
      if (x2 > maxX)
        maxX = x2;
      if (y > maxY)
        maxY = y;
      this._ids[i3] = i3;
    }
    const cx = (minX + maxX) / 2;
    const cy = (minY + maxY) / 2;
    let minDist = Infinity;
    let i0, i1, i22;
    for (let i3 = 0; i3 < n3; i3++) {
      const d = dist(cx, cy, coords[2 * i3], coords[2 * i3 + 1]);
      if (d < minDist) {
        i0 = i3;
        minDist = d;
      }
    }
    const i0x = coords[2 * i0];
    const i0y = coords[2 * i0 + 1];
    minDist = Infinity;
    for (let i3 = 0; i3 < n3; i3++) {
      if (i3 === i0)
        continue;
      const d = dist(i0x, i0y, coords[2 * i3], coords[2 * i3 + 1]);
      if (d < minDist && d > 0) {
        i1 = i3;
        minDist = d;
      }
    }
    let i1x = coords[2 * i1];
    let i1y = coords[2 * i1 + 1];
    let minRadius = Infinity;
    for (let i3 = 0; i3 < n3; i3++) {
      if (i3 === i0 || i3 === i1)
        continue;
      const r3 = circumradius(i0x, i0y, i1x, i1y, coords[2 * i3], coords[2 * i3 + 1]);
      if (r3 < minRadius) {
        i22 = i3;
        minRadius = r3;
      }
    }
    let i2x = coords[2 * i22];
    let i2y = coords[2 * i22 + 1];
    if (minRadius === Infinity) {
      for (let i3 = 0; i3 < n3; i3++) {
        this._dists[i3] = coords[2 * i3] - coords[0] || coords[2 * i3 + 1] - coords[1];
      }
      quicksort(this._ids, this._dists, 0, n3 - 1);
      const hull = new Uint32Array(n3);
      let j3 = 0;
      for (let i3 = 0, d0 = -Infinity; i3 < n3; i3++) {
        const id = this._ids[i3];
        if (this._dists[id] > d0) {
          hull[j3++] = id;
          d0 = this._dists[id];
        }
      }
      this.hull = hull.subarray(0, j3);
      this.triangles = new Uint32Array(0);
      this.halfedges = new Uint32Array(0);
      return;
    }
    if (orient(i0x, i0y, i1x, i1y, i2x, i2y)) {
      const i3 = i1;
      const x2 = i1x;
      const y = i1y;
      i1 = i22;
      i1x = i2x;
      i1y = i2y;
      i22 = i3;
      i2x = x2;
      i2y = y;
    }
    const center = circumcenter(i0x, i0y, i1x, i1y, i2x, i2y);
    this._cx = center.x;
    this._cy = center.y;
    for (let i3 = 0; i3 < n3; i3++) {
      this._dists[i3] = dist(coords[2 * i3], coords[2 * i3 + 1], center.x, center.y);
    }
    quicksort(this._ids, this._dists, 0, n3 - 1);
    this._hullStart = i0;
    let hullSize = 3;
    hullNext[i0] = hullPrev[i22] = i1;
    hullNext[i1] = hullPrev[i0] = i22;
    hullNext[i22] = hullPrev[i1] = i0;
    hullTri[i0] = 0;
    hullTri[i1] = 1;
    hullTri[i22] = 2;
    hullHash.fill(-1);
    hullHash[this._hashKey(i0x, i0y)] = i0;
    hullHash[this._hashKey(i1x, i1y)] = i1;
    hullHash[this._hashKey(i2x, i2y)] = i22;
    this.trianglesLen = 0;
    this._addTriangle(i0, i1, i22, -1, -1, -1);
    for (let k3 = 0, xp, yp; k3 < this._ids.length; k3++) {
      const i3 = this._ids[k3];
      const x2 = coords[2 * i3];
      const y = coords[2 * i3 + 1];
      if (k3 > 0 && Math.abs(x2 - xp) <= EPSILON && Math.abs(y - yp) <= EPSILON)
        continue;
      xp = x2;
      yp = y;
      if (i3 === i0 || i3 === i1 || i3 === i22)
        continue;
      let start = 0;
      for (let j3 = 0, key = this._hashKey(x2, y); j3 < this._hashSize; j3++) {
        start = hullHash[(key + j3) % this._hashSize];
        if (start !== -1 && start !== hullNext[start])
          break;
      }
      start = hullPrev[start];
      let e3 = start, q;
      while (q = hullNext[e3], !orient(x2, y, coords[2 * e3], coords[2 * e3 + 1], coords[2 * q], coords[2 * q + 1])) {
        e3 = q;
        if (e3 === start) {
          e3 = -1;
          break;
        }
      }
      if (e3 === -1)
        continue;
      let t3 = this._addTriangle(e3, i3, hullNext[e3], -1, -1, hullTri[e3]);
      hullTri[i3] = this._legalize(t3 + 2);
      hullTri[e3] = t3;
      hullSize++;
      let n4 = hullNext[e3];
      while (q = hullNext[n4], orient(x2, y, coords[2 * n4], coords[2 * n4 + 1], coords[2 * q], coords[2 * q + 1])) {
        t3 = this._addTriangle(n4, i3, q, hullTri[i3], -1, hullTri[n4]);
        hullTri[i3] = this._legalize(t3 + 2);
        hullNext[n4] = n4;
        hullSize--;
        n4 = q;
      }
      if (e3 === start) {
        while (q = hullPrev[e3], orient(x2, y, coords[2 * q], coords[2 * q + 1], coords[2 * e3], coords[2 * e3 + 1])) {
          t3 = this._addTriangle(q, i3, e3, -1, hullTri[e3], hullTri[q]);
          this._legalize(t3 + 2);
          hullTri[q] = t3;
          hullNext[e3] = e3;
          hullSize--;
          e3 = q;
        }
      }
      this._hullStart = hullPrev[i3] = e3;
      hullNext[e3] = hullPrev[n4] = i3;
      hullNext[i3] = n4;
      hullHash[this._hashKey(x2, y)] = i3;
      hullHash[this._hashKey(coords[2 * e3], coords[2 * e3 + 1])] = e3;
    }
    this.hull = new Uint32Array(hullSize);
    for (let i3 = 0, e3 = this._hullStart; i3 < hullSize; i3++) {
      this.hull[i3] = e3;
      e3 = hullNext[e3];
    }
    this.triangles = this._triangles.subarray(0, this.trianglesLen);
    this.halfedges = this._halfedges.subarray(0, this.trianglesLen);
  }
  _hashKey(x2, y) {
    return Math.floor(pseudoAngle(x2 - this._cx, y - this._cy) * this._hashSize) % this._hashSize;
  }
  _legalize(a2) {
    const { _triangles: triangles, _halfedges: halfedges, coords } = this;
    let i3 = 0;
    let ar = 0;
    while (true) {
      const b2 = halfedges[a2];
      const a0 = a2 - a2 % 3;
      ar = a0 + (a2 + 2) % 3;
      if (b2 === -1) {
        if (i3 === 0)
          break;
        a2 = EDGE_STACK[--i3];
        continue;
      }
      const b0 = b2 - b2 % 3;
      const al = a0 + (a2 + 1) % 3;
      const bl = b0 + (b2 + 2) % 3;
      const p0 = triangles[ar];
      const pr2 = triangles[a2];
      const pl = triangles[al];
      const p1 = triangles[bl];
      const illegal = inCircle(
        coords[2 * p0],
        coords[2 * p0 + 1],
        coords[2 * pr2],
        coords[2 * pr2 + 1],
        coords[2 * pl],
        coords[2 * pl + 1],
        coords[2 * p1],
        coords[2 * p1 + 1]
      );
      if (illegal) {
        triangles[a2] = p1;
        triangles[b2] = p0;
        const hbl = halfedges[bl];
        if (hbl === -1) {
          let e3 = this._hullStart;
          do {
            if (this._hullTri[e3] === bl) {
              this._hullTri[e3] = a2;
              break;
            }
            e3 = this._hullPrev[e3];
          } while (e3 !== this._hullStart);
        }
        this._link(a2, hbl);
        this._link(b2, halfedges[ar]);
        this._link(ar, bl);
        const br = b0 + (b2 + 1) % 3;
        if (i3 < EDGE_STACK.length) {
          EDGE_STACK[i3++] = br;
        }
      } else {
        if (i3 === 0)
          break;
        a2 = EDGE_STACK[--i3];
      }
    }
    return ar;
  }
  _link(a2, b2) {
    this._halfedges[a2] = b2;
    if (b2 !== -1)
      this._halfedges[b2] = a2;
  }
  // add a new triangle given vertex indices and adjacent half-edge ids
  _addTriangle(i0, i1, i22, a2, b2, c) {
    const t3 = this.trianglesLen;
    this._triangles[t3] = i0;
    this._triangles[t3 + 1] = i1;
    this._triangles[t3 + 2] = i22;
    this._link(t3, a2);
    this._link(t3 + 1, b2);
    this._link(t3 + 2, c);
    this.trianglesLen += 3;
    return t3;
  }
};
function pseudoAngle(dx, dy) {
  const p2 = dx / (Math.abs(dx) + Math.abs(dy));
  return (dy > 0 ? 3 - p2 : 1 + p2) / 4;
}
function dist(ax, ay, bx, by) {
  const dx = ax - bx;
  const dy = ay - by;
  return dx * dx + dy * dy;
}
function orientIfSure(px, py, rx, ry, qx, qy) {
  const l2 = (ry - py) * (qx - px);
  const r3 = (rx - px) * (qy - py);
  return Math.abs(l2 - r3) >= 33306690738754716e-32 * Math.abs(l2 + r3) ? l2 - r3 : 0;
}
function orient(rx, ry, qx, qy, px, py) {
  const sign = orientIfSure(px, py, rx, ry, qx, qy) || orientIfSure(rx, ry, qx, qy, px, py) || orientIfSure(qx, qy, px, py, rx, ry);
  return sign < 0;
}
function inCircle(ax, ay, bx, by, cx, cy, px, py) {
  const dx = ax - px;
  const dy = ay - py;
  const ex = bx - px;
  const ey = by - py;
  const fx = cx - px;
  const fy = cy - py;
  const ap = dx * dx + dy * dy;
  const bp = ex * ex + ey * ey;
  const cp = fx * fx + fy * fy;
  return dx * (ey * cp - bp * fy) - dy * (ex * cp - bp * fx) + ap * (ex * fy - ey * fx) < 0;
}
function circumradius(ax, ay, bx, by, cx, cy) {
  const dx = bx - ax;
  const dy = by - ay;
  const ex = cx - ax;
  const ey = cy - ay;
  const bl = dx * dx + dy * dy;
  const cl = ex * ex + ey * ey;
  const d = 0.5 / (dx * ey - dy * ex);
  const x2 = (ey * bl - dy * cl) * d;
  const y = (dx * cl - ex * bl) * d;
  return x2 * x2 + y * y;
}
function circumcenter(ax, ay, bx, by, cx, cy) {
  const dx = bx - ax;
  const dy = by - ay;
  const ex = cx - ax;
  const ey = cy - ay;
  const bl = dx * dx + dy * dy;
  const cl = ex * ex + ey * ey;
  const d = 0.5 / (dx * ey - dy * ex);
  const x2 = ax + (ey * bl - dy * cl) * d;
  const y = ay + (dx * cl - ex * bl) * d;
  return { x: x2, y };
}
function quicksort(ids, dists, left, right) {
  if (right - left <= 20) {
    for (let i3 = left + 1; i3 <= right; i3++) {
      const temp = ids[i3];
      const tempDist = dists[temp];
      let j3 = i3 - 1;
      while (j3 >= left && dists[ids[j3]] > tempDist)
        ids[j3 + 1] = ids[j3--];
      ids[j3 + 1] = temp;
    }
  } else {
    const median = left + right >> 1;
    let i3 = left + 1;
    let j3 = right;
    swap(ids, median, i3);
    if (dists[ids[left]] > dists[ids[right]])
      swap(ids, left, right);
    if (dists[ids[i3]] > dists[ids[right]])
      swap(ids, i3, right);
    if (dists[ids[left]] > dists[ids[i3]])
      swap(ids, left, i3);
    const temp = ids[i3];
    const tempDist = dists[temp];
    while (true) {
      do
        i3++;
      while (dists[ids[i3]] < tempDist);
      do
        j3--;
      while (dists[ids[j3]] > tempDist);
      if (j3 < i3)
        break;
      swap(ids, i3, j3);
    }
    ids[left + 1] = ids[j3];
    ids[j3] = temp;
    if (right - i3 + 1 >= j3 - left) {
      quicksort(ids, dists, i3, right);
      quicksort(ids, dists, left, j3 - 1);
    } else {
      quicksort(ids, dists, left, j3 - 1);
      quicksort(ids, dists, i3, right);
    }
  }
}
function swap(arr, i3, j3) {
  const tmp = arr[i3];
  arr[i3] = arr[j3];
  arr[j3] = tmp;
}
function defaultGetX(p2) {
  return p2[0];
}
function defaultGetY(p2) {
  return p2[1];
}

// node_modules/d3-delaunay/src/path.js
var epsilon = 1e-6;
var Path = class {
  constructor() {
    this._x0 = this._y0 = // start of current subpath
    this._x1 = this._y1 = null;
    this._ = "";
  }
  moveTo(x2, y) {
    this._ += `M${this._x0 = this._x1 = +x2},${this._y0 = this._y1 = +y}`;
  }
  closePath() {
    if (this._x1 !== null) {
      this._x1 = this._x0, this._y1 = this._y0;
      this._ += "Z";
    }
  }
  lineTo(x2, y) {
    this._ += `L${this._x1 = +x2},${this._y1 = +y}`;
  }
  arc(x2, y, r3) {
    x2 = +x2, y = +y, r3 = +r3;
    const x0 = x2 + r3;
    const y0 = y;
    if (r3 < 0)
      throw new Error("negative radius");
    if (this._x1 === null)
      this._ += `M${x0},${y0}`;
    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon)
      this._ += "L" + x0 + "," + y0;
    if (!r3)
      return;
    this._ += `A${r3},${r3},0,1,1,${x2 - r3},${y}A${r3},${r3},0,1,1,${this._x1 = x0},${this._y1 = y0}`;
  }
  rect(x2, y, w3, h) {
    this._ += `M${this._x0 = this._x1 = +x2},${this._y0 = this._y1 = +y}h${+w3}v${+h}h${-w3}Z`;
  }
  value() {
    return this._ || null;
  }
};

// node_modules/d3-delaunay/src/polygon.js
var Polygon = class {
  constructor() {
    this._ = [];
  }
  moveTo(x2, y) {
    this._.push([x2, y]);
  }
  closePath() {
    this._.push(this._[0].slice());
  }
  lineTo(x2, y) {
    this._.push([x2, y]);
  }
  value() {
    return this._.length ? this._ : null;
  }
};

// node_modules/d3-delaunay/src/voronoi.js
var Voronoi = class {
  constructor(delaunay, [xmin, ymin, xmax, ymax] = [0, 0, 960, 500]) {
    if (!((xmax = +xmax) >= (xmin = +xmin)) || !((ymax = +ymax) >= (ymin = +ymin)))
      throw new Error("invalid bounds");
    this.delaunay = delaunay;
    this._circumcenters = new Float64Array(delaunay.points.length * 2);
    this.vectors = new Float64Array(delaunay.points.length * 2);
    this.xmax = xmax, this.xmin = xmin;
    this.ymax = ymax, this.ymin = ymin;
    this._init();
  }
  update() {
    this.delaunay.update();
    this._init();
    return this;
  }
  _init() {
    const { delaunay: { points, hull, triangles }, vectors } = this;
    const circumcenters = this.circumcenters = this._circumcenters.subarray(0, triangles.length / 3 * 2);
    for (let i3 = 0, j3 = 0, n3 = triangles.length, x2, y; i3 < n3; i3 += 3, j3 += 2) {
      const t1 = triangles[i3] * 2;
      const t22 = triangles[i3 + 1] * 2;
      const t3 = triangles[i3 + 2] * 2;
      const x12 = points[t1];
      const y12 = points[t1 + 1];
      const x22 = points[t22];
      const y2 = points[t22 + 1];
      const x3 = points[t3];
      const y3 = points[t3 + 1];
      const dx = x22 - x12;
      const dy = y2 - y12;
      const ex = x3 - x12;
      const ey = y3 - y12;
      const bl = dx * dx + dy * dy;
      const cl = ex * ex + ey * ey;
      const ab = (dx * ey - dy * ex) * 2;
      if (!ab) {
        x2 = (x12 + x3) / 2 - 1e8 * ey;
        y = (y12 + y3) / 2 + 1e8 * ex;
      } else if (Math.abs(ab) < 1e-8) {
        x2 = (x12 + x3) / 2;
        y = (y12 + y3) / 2;
      } else {
        const d = 1 / ab;
        x2 = x12 + (ey * bl - dy * cl) * d;
        y = y12 + (dx * cl - ex * bl) * d;
      }
      circumcenters[j3] = x2;
      circumcenters[j3 + 1] = y;
    }
    let h = hull[hull.length - 1];
    let p0, p1 = h * 4;
    let x0, x1 = points[2 * h];
    let y0, y1 = points[2 * h + 1];
    vectors.fill(0);
    for (let i3 = 0; i3 < hull.length; ++i3) {
      h = hull[i3];
      p0 = p1, x0 = x1, y0 = y1;
      p1 = h * 4, x1 = points[2 * h], y1 = points[2 * h + 1];
      vectors[p0 + 2] = vectors[p1] = y0 - y1;
      vectors[p0 + 3] = vectors[p1 + 1] = x1 - x0;
    }
  }
  render(context) {
    const buffer = context == null ? context = new Path() : void 0;
    const { delaunay: { halfedges, inedges, hull }, circumcenters, vectors } = this;
    if (hull.length <= 1)
      return null;
    for (let i3 = 0, n3 = halfedges.length; i3 < n3; ++i3) {
      const j3 = halfedges[i3];
      if (j3 < i3)
        continue;
      const ti = Math.floor(i3 / 3) * 2;
      const tj = Math.floor(j3 / 3) * 2;
      const xi = circumcenters[ti];
      const yi = circumcenters[ti + 1];
      const xj = circumcenters[tj];
      const yj = circumcenters[tj + 1];
      this._renderSegment(xi, yi, xj, yj, context);
    }
    let h0, h1 = hull[hull.length - 1];
    for (let i3 = 0; i3 < hull.length; ++i3) {
      h0 = h1, h1 = hull[i3];
      const t3 = Math.floor(inedges[h1] / 3) * 2;
      const x2 = circumcenters[t3];
      const y = circumcenters[t3 + 1];
      const v2 = h0 * 4;
      const p2 = this._project(x2, y, vectors[v2 + 2], vectors[v2 + 3]);
      if (p2)
        this._renderSegment(x2, y, p2[0], p2[1], context);
    }
    return buffer && buffer.value();
  }
  renderBounds(context) {
    const buffer = context == null ? context = new Path() : void 0;
    context.rect(this.xmin, this.ymin, this.xmax - this.xmin, this.ymax - this.ymin);
    return buffer && buffer.value();
  }
  renderCell(i3, context) {
    const buffer = context == null ? context = new Path() : void 0;
    const points = this._clip(i3);
    if (points === null || !points.length)
      return;
    context.moveTo(points[0], points[1]);
    let n3 = points.length;
    while (points[0] === points[n3 - 2] && points[1] === points[n3 - 1] && n3 > 1)
      n3 -= 2;
    for (let i4 = 2; i4 < n3; i4 += 2) {
      if (points[i4] !== points[i4 - 2] || points[i4 + 1] !== points[i4 - 1])
        context.lineTo(points[i4], points[i4 + 1]);
    }
    context.closePath();
    return buffer && buffer.value();
  }
  *cellPolygons() {
    const { delaunay: { points } } = this;
    for (let i3 = 0, n3 = points.length / 2; i3 < n3; ++i3) {
      const cell = this.cellPolygon(i3);
      if (cell)
        cell.index = i3, yield cell;
    }
  }
  cellPolygon(i3) {
    const polygon = new Polygon();
    this.renderCell(i3, polygon);
    return polygon.value();
  }
  _renderSegment(x0, y0, x1, y1, context) {
    let S;
    const c0 = this._regioncode(x0, y0);
    const c1 = this._regioncode(x1, y1);
    if (c0 === 0 && c1 === 0) {
      context.moveTo(x0, y0);
      context.lineTo(x1, y1);
    } else if (S = this._clipSegment(x0, y0, x1, y1, c0, c1)) {
      context.moveTo(S[0], S[1]);
      context.lineTo(S[2], S[3]);
    }
  }
  contains(i3, x2, y) {
    if ((x2 = +x2, x2 !== x2) || (y = +y, y !== y))
      return false;
    return this.delaunay._step(i3, x2, y) === i3;
  }
  *neighbors(i3) {
    const ci = this._clip(i3);
    if (ci)
      for (const j3 of this.delaunay.neighbors(i3)) {
        const cj = this._clip(j3);
        if (cj)
          loop:
            for (let ai = 0, li = ci.length; ai < li; ai += 2) {
              for (let aj = 0, lj = cj.length; aj < lj; aj += 2) {
                if (ci[ai] == cj[aj] && ci[ai + 1] == cj[aj + 1] && ci[(ai + 2) % li] == cj[(aj + lj - 2) % lj] && ci[(ai + 3) % li] == cj[(aj + lj - 1) % lj]) {
                  yield j3;
                  break loop;
                }
              }
            }
      }
  }
  _cell(i3) {
    const { circumcenters, delaunay: { inedges, halfedges, triangles } } = this;
    const e0 = inedges[i3];
    if (e0 === -1)
      return null;
    const points = [];
    let e3 = e0;
    do {
      const t3 = Math.floor(e3 / 3);
      points.push(circumcenters[t3 * 2], circumcenters[t3 * 2 + 1]);
      e3 = e3 % 3 === 2 ? e3 - 2 : e3 + 1;
      if (triangles[e3] !== i3)
        break;
      e3 = halfedges[e3];
    } while (e3 !== e0 && e3 !== -1);
    return points;
  }
  _clip(i3) {
    if (i3 === 0 && this.delaunay.hull.length === 1) {
      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];
    }
    const points = this._cell(i3);
    if (points === null)
      return null;
    const { vectors: V } = this;
    const v2 = i3 * 4;
    return V[v2] || V[v2 + 1] ? this._clipInfinite(i3, points, V[v2], V[v2 + 1], V[v2 + 2], V[v2 + 3]) : this._clipFinite(i3, points);
  }
  _clipFinite(i3, points) {
    const n3 = points.length;
    let P3 = null;
    let x0, y0, x1 = points[n3 - 2], y1 = points[n3 - 1];
    let c0, c1 = this._regioncode(x1, y1);
    let e0, e1;
    for (let j3 = 0; j3 < n3; j3 += 2) {
      x0 = x1, y0 = y1, x1 = points[j3], y1 = points[j3 + 1];
      c0 = c1, c1 = this._regioncode(x1, y1);
      if (c0 === 0 && c1 === 0) {
        e0 = e1, e1 = 0;
        if (P3)
          P3.push(x1, y1);
        else
          P3 = [x1, y1];
      } else {
        let S, sx0, sy0, sx1, sy1;
        if (c0 === 0) {
          if ((S = this._clipSegment(x0, y0, x1, y1, c0, c1)) === null)
            continue;
          [sx0, sy0, sx1, sy1] = S;
        } else {
          if ((S = this._clipSegment(x1, y1, x0, y0, c1, c0)) === null)
            continue;
          [sx1, sy1, sx0, sy0] = S;
          e0 = e1, e1 = this._edgecode(sx0, sy0);
          if (e0 && e1)
            this._edge(i3, e0, e1, P3, P3.length);
          if (P3)
            P3.push(sx0, sy0);
          else
            P3 = [sx0, sy0];
        }
        e0 = e1, e1 = this._edgecode(sx1, sy1);
        if (e0 && e1)
          this._edge(i3, e0, e1, P3, P3.length);
        if (P3)
          P3.push(sx1, sy1);
        else
          P3 = [sx1, sy1];
      }
    }
    if (P3) {
      e0 = e1, e1 = this._edgecode(P3[0], P3[1]);
      if (e0 && e1)
        this._edge(i3, e0, e1, P3, P3.length);
    } else if (this.contains(i3, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {
      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];
    }
    return P3;
  }
  _clipSegment(x0, y0, x1, y1, c0, c1) {
    while (true) {
      if (c0 === 0 && c1 === 0)
        return [x0, y0, x1, y1];
      if (c0 & c1)
        return null;
      let x2, y, c = c0 || c1;
      if (c & 8)
        x2 = x0 + (x1 - x0) * (this.ymax - y0) / (y1 - y0), y = this.ymax;
      else if (c & 4)
        x2 = x0 + (x1 - x0) * (this.ymin - y0) / (y1 - y0), y = this.ymin;
      else if (c & 2)
        y = y0 + (y1 - y0) * (this.xmax - x0) / (x1 - x0), x2 = this.xmax;
      else
        y = y0 + (y1 - y0) * (this.xmin - x0) / (x1 - x0), x2 = this.xmin;
      if (c0)
        x0 = x2, y0 = y, c0 = this._regioncode(x0, y0);
      else
        x1 = x2, y1 = y, c1 = this._regioncode(x1, y1);
    }
  }
  _clipInfinite(i3, points, vx0, vy0, vxn, vyn) {
    let P3 = Array.from(points), p2;
    if (p2 = this._project(P3[0], P3[1], vx0, vy0))
      P3.unshift(p2[0], p2[1]);
    if (p2 = this._project(P3[P3.length - 2], P3[P3.length - 1], vxn, vyn))
      P3.push(p2[0], p2[1]);
    if (P3 = this._clipFinite(i3, P3)) {
      for (let j3 = 0, n3 = P3.length, c0, c1 = this._edgecode(P3[n3 - 2], P3[n3 - 1]); j3 < n3; j3 += 2) {
        c0 = c1, c1 = this._edgecode(P3[j3], P3[j3 + 1]);
        if (c0 && c1)
          j3 = this._edge(i3, c0, c1, P3, j3), n3 = P3.length;
      }
    } else if (this.contains(i3, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {
      P3 = [this.xmin, this.ymin, this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax];
    }
    return P3;
  }
  _edge(i3, e0, e1, P3, j3) {
    while (e0 !== e1) {
      let x2, y;
      switch (e0) {
        case 5:
          e0 = 4;
          continue;
        case 4:
          e0 = 6, x2 = this.xmax, y = this.ymin;
          break;
        case 6:
          e0 = 2;
          continue;
        case 2:
          e0 = 10, x2 = this.xmax, y = this.ymax;
          break;
        case 10:
          e0 = 8;
          continue;
        case 8:
          e0 = 9, x2 = this.xmin, y = this.ymax;
          break;
        case 9:
          e0 = 1;
          continue;
        case 1:
          e0 = 5, x2 = this.xmin, y = this.ymin;
          break;
      }
      if ((P3[j3] !== x2 || P3[j3 + 1] !== y) && this.contains(i3, x2, y)) {
        P3.splice(j3, 0, x2, y), j3 += 2;
      }
    }
    if (P3.length > 4) {
      for (let i4 = 0; i4 < P3.length; i4 += 2) {
        const j4 = (i4 + 2) % P3.length, k3 = (i4 + 4) % P3.length;
        if (P3[i4] === P3[j4] && P3[j4] === P3[k3] || P3[i4 + 1] === P3[j4 + 1] && P3[j4 + 1] === P3[k3 + 1])
          P3.splice(j4, 2), i4 -= 2;
      }
    }
    return j3;
  }
  _project(x0, y0, vx, vy) {
    let t3 = Infinity, c, x2, y;
    if (vy < 0) {
      if (y0 <= this.ymin)
        return null;
      if ((c = (this.ymin - y0) / vy) < t3)
        y = this.ymin, x2 = x0 + (t3 = c) * vx;
    } else if (vy > 0) {
      if (y0 >= this.ymax)
        return null;
      if ((c = (this.ymax - y0) / vy) < t3)
        y = this.ymax, x2 = x0 + (t3 = c) * vx;
    }
    if (vx > 0) {
      if (x0 >= this.xmax)
        return null;
      if ((c = (this.xmax - x0) / vx) < t3)
        x2 = this.xmax, y = y0 + (t3 = c) * vy;
    } else if (vx < 0) {
      if (x0 <= this.xmin)
        return null;
      if ((c = (this.xmin - x0) / vx) < t3)
        x2 = this.xmin, y = y0 + (t3 = c) * vy;
    }
    return [x2, y];
  }
  _edgecode(x2, y) {
    return (x2 === this.xmin ? 1 : x2 === this.xmax ? 2 : 0) | (y === this.ymin ? 4 : y === this.ymax ? 8 : 0);
  }
  _regioncode(x2, y) {
    return (x2 < this.xmin ? 1 : x2 > this.xmax ? 2 : 0) | (y < this.ymin ? 4 : y > this.ymax ? 8 : 0);
  }
};

// node_modules/d3-delaunay/src/delaunay.js
var tau = 2 * Math.PI;
var pow = Math.pow;
function pointX(p2) {
  return p2[0];
}
function pointY(p2) {
  return p2[1];
}
function collinear(d) {
  const { triangles, coords } = d;
  for (let i3 = 0; i3 < triangles.length; i3 += 3) {
    const a2 = 2 * triangles[i3], b2 = 2 * triangles[i3 + 1], c = 2 * triangles[i3 + 2], cross = (coords[c] - coords[a2]) * (coords[b2 + 1] - coords[a2 + 1]) - (coords[b2] - coords[a2]) * (coords[c + 1] - coords[a2 + 1]);
    if (cross > 1e-10)
      return false;
  }
  return true;
}
function jitter(x2, y, r3) {
  return [x2 + Math.sin(x2 + y) * r3, y + Math.cos(x2 - y) * r3];
}
var Delaunay = class _Delaunay {
  static from(points, fx = pointX, fy = pointY, that) {
    return new _Delaunay("length" in points ? flatArray(points, fx, fy, that) : Float64Array.from(flatIterable(points, fx, fy, that)));
  }
  constructor(points) {
    this._delaunator = new Delaunator(points);
    this.inedges = new Int32Array(points.length / 2);
    this._hullIndex = new Int32Array(points.length / 2);
    this.points = this._delaunator.coords;
    this._init();
  }
  update() {
    this._delaunator.update();
    this._init();
    return this;
  }
  _init() {
    const d = this._delaunator, points = this.points;
    if (d.hull && d.hull.length > 2 && collinear(d)) {
      this.collinear = Int32Array.from({ length: points.length / 2 }, (_, i3) => i3).sort((i3, j3) => points[2 * i3] - points[2 * j3] || points[2 * i3 + 1] - points[2 * j3 + 1]);
      const e3 = this.collinear[0], f2 = this.collinear[this.collinear.length - 1], bounds = [points[2 * e3], points[2 * e3 + 1], points[2 * f2], points[2 * f2 + 1]], r3 = 1e-8 * Math.hypot(bounds[3] - bounds[1], bounds[2] - bounds[0]);
      for (let i3 = 0, n3 = points.length / 2; i3 < n3; ++i3) {
        const p2 = jitter(points[2 * i3], points[2 * i3 + 1], r3);
        points[2 * i3] = p2[0];
        points[2 * i3 + 1] = p2[1];
      }
      this._delaunator = new Delaunator(points);
    } else {
      delete this.collinear;
    }
    const halfedges = this.halfedges = this._delaunator.halfedges;
    const hull = this.hull = this._delaunator.hull;
    const triangles = this.triangles = this._delaunator.triangles;
    const inedges = this.inedges.fill(-1);
    const hullIndex = this._hullIndex.fill(-1);
    for (let e3 = 0, n3 = halfedges.length; e3 < n3; ++e3) {
      const p2 = triangles[e3 % 3 === 2 ? e3 - 2 : e3 + 1];
      if (halfedges[e3] === -1 || inedges[p2] === -1)
        inedges[p2] = e3;
    }
    for (let i3 = 0, n3 = hull.length; i3 < n3; ++i3) {
      hullIndex[hull[i3]] = i3;
    }
    if (hull.length <= 2 && hull.length > 0) {
      this.triangles = new Int32Array(3).fill(-1);
      this.halfedges = new Int32Array(3).fill(-1);
      this.triangles[0] = hull[0];
      this.triangles[1] = hull[1];
      this.triangles[2] = hull[1];
      inedges[hull[0]] = 1;
      if (hull.length === 2)
        inedges[hull[1]] = 0;
    }
  }
  voronoi(bounds) {
    return new Voronoi(this, bounds);
  }
  *neighbors(i3) {
    const { inedges, hull, _hullIndex, halfedges, triangles, collinear: collinear2 } = this;
    if (collinear2) {
      const l2 = collinear2.indexOf(i3);
      if (l2 > 0)
        yield collinear2[l2 - 1];
      if (l2 < collinear2.length - 1)
        yield collinear2[l2 + 1];
      return;
    }
    const e0 = inedges[i3];
    if (e0 === -1)
      return;
    let e3 = e0, p0 = -1;
    do {
      yield p0 = triangles[e3];
      e3 = e3 % 3 === 2 ? e3 - 2 : e3 + 1;
      if (triangles[e3] !== i3)
        return;
      e3 = halfedges[e3];
      if (e3 === -1) {
        const p2 = hull[(_hullIndex[i3] + 1) % hull.length];
        if (p2 !== p0)
          yield p2;
        return;
      }
    } while (e3 !== e0);
  }
  find(x2, y, i3 = 0) {
    if ((x2 = +x2, x2 !== x2) || (y = +y, y !== y))
      return -1;
    const i0 = i3;
    let c;
    while ((c = this._step(i3, x2, y)) >= 0 && c !== i3 && c !== i0)
      i3 = c;
    return c;
  }
  _step(i3, x2, y) {
    const { inedges, hull, _hullIndex, halfedges, triangles, points } = this;
    if (inedges[i3] === -1 || !points.length)
      return (i3 + 1) % (points.length >> 1);
    let c = i3;
    let dc = pow(x2 - points[i3 * 2], 2) + pow(y - points[i3 * 2 + 1], 2);
    const e0 = inedges[i3];
    let e3 = e0;
    do {
      let t3 = triangles[e3];
      const dt = pow(x2 - points[t3 * 2], 2) + pow(y - points[t3 * 2 + 1], 2);
      if (dt < dc)
        dc = dt, c = t3;
      e3 = e3 % 3 === 2 ? e3 - 2 : e3 + 1;
      if (triangles[e3] !== i3)
        break;
      e3 = halfedges[e3];
      if (e3 === -1) {
        e3 = hull[(_hullIndex[i3] + 1) % hull.length];
        if (e3 !== t3) {
          if (pow(x2 - points[e3 * 2], 2) + pow(y - points[e3 * 2 + 1], 2) < dc)
            return e3;
        }
        break;
      }
    } while (e3 !== e0);
    return c;
  }
  render(context) {
    const buffer = context == null ? context = new Path() : void 0;
    const { points, halfedges, triangles } = this;
    for (let i3 = 0, n3 = halfedges.length; i3 < n3; ++i3) {
      const j3 = halfedges[i3];
      if (j3 < i3)
        continue;
      const ti = triangles[i3] * 2;
      const tj = triangles[j3] * 2;
      context.moveTo(points[ti], points[ti + 1]);
      context.lineTo(points[tj], points[tj + 1]);
    }
    this.renderHull(context);
    return buffer && buffer.value();
  }
  renderPoints(context, r3 = 2) {
    const buffer = context == null ? context = new Path() : void 0;
    const { points } = this;
    for (let i3 = 0, n3 = points.length; i3 < n3; i3 += 2) {
      const x2 = points[i3], y = points[i3 + 1];
      context.moveTo(x2 + r3, y);
      context.arc(x2, y, r3, 0, tau);
    }
    return buffer && buffer.value();
  }
  renderHull(context) {
    const buffer = context == null ? context = new Path() : void 0;
    const { hull, points } = this;
    const h = hull[0] * 2, n3 = hull.length;
    context.moveTo(points[h], points[h + 1]);
    for (let i3 = 1; i3 < n3; ++i3) {
      const h2 = 2 * hull[i3];
      context.lineTo(points[h2], points[h2 + 1]);
    }
    context.closePath();
    return buffer && buffer.value();
  }
  hullPolygon() {
    const polygon = new Polygon();
    this.renderHull(polygon);
    return polygon.value();
  }
  renderTriangle(i3, context) {
    const buffer = context == null ? context = new Path() : void 0;
    const { points, triangles } = this;
    const t0 = triangles[i3 *= 3] * 2;
    const t1 = triangles[i3 + 1] * 2;
    const t22 = triangles[i3 + 2] * 2;
    context.moveTo(points[t0], points[t0 + 1]);
    context.lineTo(points[t1], points[t1 + 1]);
    context.lineTo(points[t22], points[t22 + 1]);
    context.closePath();
    return buffer && buffer.value();
  }
  *trianglePolygons() {
    const { triangles } = this;
    for (let i3 = 0, n3 = triangles.length / 3; i3 < n3; ++i3) {
      yield this.trianglePolygon(i3);
    }
  }
  trianglePolygon(i3) {
    const polygon = new Polygon();
    this.renderTriangle(i3, polygon);
    return polygon.value();
  }
};
function flatArray(points, fx, fy, that) {
  const n3 = points.length;
  const array = new Float64Array(n3 * 2);
  for (let i3 = 0; i3 < n3; ++i3) {
    const p2 = points[i3];
    array[i3 * 2] = fx.call(that, p2, i3, points);
    array[i3 * 2 + 1] = fy.call(that, p2, i3, points);
  }
  return array;
}
function* flatIterable(points, fx, fy, that) {
  let i3 = 0;
  for (const p2 of points) {
    yield fx.call(that, p2, i3, points);
    yield fy.call(that, p2, i3, points);
    ++i3;
  }
}

// node_modules/@nivo/voronoi/dist/nivo-voronoi.es.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var g2 = function(n3) {
  return "function" == typeof n3 ? n3 : function(e3) {
    return e3[n3];
  };
};
var b = function(n3) {
  var e3 = n3.points, i3 = n3.x, o3 = void 0 === i3 ? "x" : i3, t3 = n3.y, r3 = void 0 === t3 ? "y" : t3, l2 = g2(o3), u = g2(r3);
  return e3.map(function(n4) {
    return [l2(n4), u(n4)];
  });
};
var k2 = function(n3) {
  var e3 = n3.points, i3 = n3.width, o3 = n3.height, t3 = n3.debug, r3 = Delaunay.from(e3), l2 = t3 ? r3.voronoi([0, 0, i3, o3]) : void 0;
  return { delaunay: r3, voronoi: l2 };
};
var x = function(e3) {
  var i3 = e3.points, o3 = e3.x, t3 = e3.y, r3 = e3.width, l2 = e3.height, u = e3.debug, a2 = (0, import_react.useMemo)(function() {
    return b({ points: i3, x: o3, y: t3 });
  }, [i3, o3, t3]);
  return (0, import_react.useMemo)(function() {
    return k2({ points: a2, width: r3, height: l2, debug: u });
  }, [a2, r3, l2, u]);
};
var O = function(e3) {
  var i3 = e3.nodes, l2 = e3.width, u = e3.height, a2 = e3.x, d = e3.y, c = e3.onMouseEnter, h = e3.onMouseMove, m = e3.onMouseLeave, y = e3.onClick, g3 = e3.debug, b2 = (0, import_react.useRef)(null), k3 = (0, import_react.useState)(null), C2 = k3[0], L = k3[1], w3 = x({ points: i3, x: a2, y: d, width: l2, height: u, debug: g3 }), W = w3.delaunay, D2 = w3.voronoi, M = (0, import_react.useMemo)(function() {
    if (g3 && D2)
      return D2.render();
  }, [g3, D2]), O2 = (0, import_react.useCallback)(function(n3) {
    if (!b2.current)
      return [null, null];
    var e4 = Fi(b2.current, n3), o3 = e4[0], t3 = e4[1], r3 = W.find(o3, t3);
    return [r3, void 0 !== r3 ? i3[r3] : null];
  }, [b2, W]), P3 = (0, import_react.useCallback)(function(n3) {
    var e4 = O2(n3), i4 = e4[0], o3 = e4[1];
    L(i4), o3 && (null == c || c(o3, n3));
  }, [O2, L, c]), j3 = (0, import_react.useCallback)(function(n3) {
    var e4 = O2(n3), i4 = e4[0], o3 = e4[1];
    L(i4), o3 && (null == h || h(o3, n3));
  }, [O2, L, h]), S = (0, import_react.useCallback)(function(n3) {
    if (L(null), m) {
      var e4 = void 0;
      null !== C2 && (e4 = i3[C2]), e4 && m(e4, n3);
    }
  }, [L, C2, m, i3]), z2 = (0, import_react.useCallback)(function(n3) {
    var e4 = O2(n3), i4 = e4[0], o3 = e4[1];
    L(i4), o3 && (null == y || y(o3, n3));
  }, [O2, L, y]);
  return (0, import_jsx_runtime.jsxs)("g", { ref: b2, children: [g3 && D2 && (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [(0, import_jsx_runtime.jsx)("path", { d: M, stroke: "red", strokeWidth: 1, opacity: 0.75 }), null !== C2 && (0, import_jsx_runtime.jsx)("path", { fill: "pink", opacity: 0.35, d: D2.renderCell(C2) })] }), (0, import_jsx_runtime.jsx)("rect", { width: l2, height: u, fill: "red", opacity: 0, style: { cursor: "auto" }, onMouseEnter: P3, onMouseMove: j3, onMouseLeave: S, onClick: z2 })] });
};
var P2 = function(n3, e3) {
  n3.save(), n3.globalAlpha = 0.75, n3.beginPath(), e3.render(n3), n3.strokeStyle = "red", n3.lineWidth = 1, n3.stroke(), n3.restore();
};
var j2 = function(n3, e3, i3) {
  n3.save(), n3.globalAlpha = 0.35, n3.beginPath(), e3.renderCell(i3, n3), n3.fillStyle = "red", n3.fill(), n3.restore();
};

// node_modules/@nivo/line/dist/nivo-line.es.js
function re() {
  return re = Object.assign ? Object.assign.bind() : function(e3) {
    for (var i3 = 1; i3 < arguments.length; i3++) {
      var r3 = arguments[i3];
      for (var n3 in r3)
        Object.prototype.hasOwnProperty.call(r3, n3) && (e3[n3] = r3[n3]);
    }
    return e3;
  }, re.apply(this, arguments);
}
var ne = function(e3) {
  var i3 = e3.point;
  return (0, import_jsx_runtime2.jsx)(w, { id: (0, import_jsx_runtime2.jsxs)("span", { children: ["x: ", (0, import_jsx_runtime2.jsx)("strong", { children: i3.data.xFormatted }), ", y:", " ", (0, import_jsx_runtime2.jsx)("strong", { children: i3.data.yFormatted })] }), enableChip: true, color: i3.serieColor });
};
ne.propTypes = { point: import_prop_types.default.object.isRequired };
var oe = (0, import_react2.memo)(ne);
var te = function(e3) {
  var i3 = e3.slice, r3 = e3.axis, n3 = Et(), o3 = "x" === r3 ? "y" : "x";
  return (0, import_jsx_runtime2.jsx)(C, { rows: i3.points.map(function(e4) {
    return [(0, import_jsx_runtime2.jsx)(g, { color: e4.serieColor, style: n3.tooltip.chip }, "chip"), e4.serieId, (0, import_jsx_runtime2.jsx)("span", { style: n3.tooltip.tableCellValue, children: e4.data[o3 + "Formatted"] }, "value")];
  }) });
};
te.propTypes = { slice: import_prop_types.default.object.isRequired, axis: import_prop_types.default.oneOf(["x", "y"]).isRequired };
var ae = (0, import_react2.memo)(te);
var se = { data: import_prop_types.default.arrayOf(import_prop_types.default.shape({ id: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number]).isRequired, data: import_prop_types.default.arrayOf(import_prop_types.default.shape({ x: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.instanceOf(Date)]), y: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.instanceOf(Date)]) })).isRequired })).isRequired, xScale: import_prop_types.default.object.isRequired, xFormat: import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.string]), yScale: import_prop_types.default.object.isRequired, yFormat: import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.string]), layers: import_prop_types.default.arrayOf(import_prop_types.default.oneOfType([import_prop_types.default.oneOf(["grid", "markers", "axes", "areas", "crosshair", "lines", "slices", "points", "mesh", "legends"]), import_prop_types.default.func])).isRequired, curve: bt.isRequired, axisTop: w2, axisRight: w2, axisBottom: w2, axisLeft: w2, enableGridX: import_prop_types.default.bool.isRequired, enableGridY: import_prop_types.default.bool.isRequired, gridXValues: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.arrayOf(import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.instanceOf(Date)]))]), gridYValues: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.arrayOf(import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.instanceOf(Date)]))]), enablePoints: import_prop_types.default.bool.isRequired, pointSymbol: import_prop_types.default.func, pointSize: import_prop_types.default.number.isRequired, pointColor: import_prop_types.default.any.isRequired, pointBorderWidth: import_prop_types.default.number.isRequired, pointBorderColor: import_prop_types.default.any.isRequired, enablePointLabel: import_prop_types.default.bool.isRequired, pointLabel: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]).isRequired, markers: import_prop_types.default.arrayOf(import_prop_types.default.shape({ axis: import_prop_types.default.oneOf(["x", "y"]).isRequired, value: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.instanceOf(Date)]).isRequired, style: import_prop_types.default.object })), colors: Xe.isRequired, enableArea: import_prop_types.default.bool.isRequired, areaOpacity: import_prop_types.default.number.isRequired, areaBlendMode: zt.isRequired, areaBaselineValue: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.instanceOf(Date)]).isRequired, lineWidth: import_prop_types.default.number.isRequired, legends: import_prop_types.default.arrayOf(import_prop_types.default.shape(T)).isRequired, isInteractive: import_prop_types.default.bool.isRequired, debugMesh: import_prop_types.default.bool.isRequired, tooltip: import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object]).isRequired, enableSlices: import_prop_types.default.oneOf(["x", "y", false]).isRequired, debugSlices: import_prop_types.default.bool.isRequired, sliceTooltip: import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object]).isRequired, enableCrosshair: import_prop_types.default.bool.isRequired, crosshairType: import_prop_types.default.string.isRequired };
var le = re({}, se, { enablePointLabel: import_prop_types.default.bool.isRequired, role: import_prop_types.default.string.isRequired, useMesh: import_prop_types.default.bool.isRequired }, Jr, yt);
var ue = re({ pixelRatio: import_prop_types.default.number.isRequired }, se);
var de = { curve: "linear", xScale: { type: "point" }, yScale: { type: "linear", min: 0, max: "auto" }, layers: ["grid", "markers", "axes", "areas", "crosshair", "lines", "points", "slices", "mesh", "legends"], axisBottom: {}, axisLeft: {}, enableGridX: true, enableGridY: true, enablePoints: true, pointSize: 6, pointColor: { from: "color" }, pointBorderWidth: 0, pointBorderColor: { theme: "background" }, enablePointLabel: false, pointLabel: "yFormatted", colors: { scheme: "nivo" }, enableArea: false, areaBaselineValue: 0, areaOpacity: 0.2, areaBlendMode: "normal", lineWidth: 2, legends: [], isInteractive: true, tooltip: oe, enableSlices: false, debugSlices: false, sliceTooltip: ae, debugMesh: false, enableCrosshair: true, crosshairType: "bottom-left" };
var ce = re({}, de, { enablePointLabel: false, useMesh: false, animate: true, motionConfig: "gentle", defs: [], fill: [], role: "img" });
var fe = re({}, de, { pixelRatio: "undefined" != typeof window && window.devicePixelRatio || 1 });
var pe = function(e3) {
  var r3 = e3.curve;
  return (0, import_react2.useMemo)(function() {
    return line_default().defined(function(e4) {
      return null !== e4.x && null !== e4.y;
    }).x(function(e4) {
      return e4.x;
    }).y(function(e4) {
      return e4.y;
    }).curve(mt(r3));
  }, [r3]);
};
var he = function(e3) {
  var r3 = e3.curve, n3 = e3.yScale, o3 = e3.areaBaselineValue;
  return (0, import_react2.useMemo)(function() {
    return area_default().defined(function(e4) {
      return null !== e4.x && null !== e4.y;
    }).x(function(e4) {
      return e4.x;
    }).y1(function(e4) {
      return e4.y;
    }).curve(mt(r3)).y0(n3(o3));
  }, [r3, n3, o3]);
};
var ye = function(e3) {
  var r3 = e3.enableSlices, n3 = e3.points, o3 = e3.width, t3 = e3.height;
  return (0, import_react2.useMemo)(function() {
    if (false === r3)
      return [];
    if ("x" === r3) {
      var e4 = /* @__PURE__ */ new Map();
      return n3.forEach(function(i4) {
        null !== i4.data.x && null !== i4.data.y && (e4.has(i4.x) ? e4.get(i4.x).push(i4) : e4.set(i4.x, [i4]));
      }), Array.from(e4.entries()).sort(function(e5, i4) {
        return e5[0] - i4[0];
      }).map(function(e5, i4, r4) {
        var n4, a2 = e5[0], s2 = e5[1], l2 = r4[i4 - 1], u = r4[i4 + 1];
        return { id: a2, x0: n4 = l2 ? a2 - (a2 - l2[0]) / 2 : a2, x: a2, y0: 0, y: 0, width: u ? a2 - n4 + (u[0] - a2) / 2 : o3 - n4, height: t3, points: s2.reverse() };
      });
    }
    if ("y" === r3) {
      var i3 = /* @__PURE__ */ new Map();
      return n3.forEach(function(e5) {
        null !== e5.data.x && null !== e5.data.y && (i3.has(e5.y) ? i3.get(e5.y).push(e5) : i3.set(e5.y, [e5]));
      }), Array.from(i3.entries()).sort(function(e5, i4) {
        return e5[0] - i4[0];
      }).map(function(e5, i4, r4) {
        var n4, a2, s2 = e5[0], l2 = e5[1], u = r4[i4 - 1], d = r4[i4 + 1];
        return n4 = u ? s2 - (s2 - u[0]) / 2 : s2, a2 = d ? s2 - n4 + (d[0] - s2) / 2 : t3 - n4, { id: s2, x0: 0, x: 0, y0: n4, y: s2, width: o3, height: a2, points: l2.reverse() };
      });
    }
  }, [r3, n3]);
};
var be = function(e3) {
  var o3 = e3.data, t3 = e3.xScale, a2 = void 0 === t3 ? ce.xScale : t3, s2 = e3.xFormat, l2 = e3.yScale, d = void 0 === l2 ? ce.yScale : l2, c = e3.yFormat, f2 = e3.width, p2 = e3.height, h = e3.colors, b2 = void 0 === h ? ce.colors : h, g3 = e3.curve, m = void 0 === g3 ? ce.curve : g3, v2 = e3.areaBaselineValue, x2 = void 0 === v2 ? ce.areaBaselineValue : v2, R = e3.pointColor, q = void 0 === R ? ce.pointColor : R, O2 = e3.pointBorderColor, M = void 0 === O2 ? ce.pointBorderColor : O2, C2 = e3.enableSlices, S = void 0 === C2 ? ce.enableSlicesTooltip : C2, T2 = Dt(s2), w3 = Dt(c), L = pr(b2, "id"), W = Et(), G = We(q, W), P3 = We(M, W), E = (0, import_react2.useState)([]), j3 = E[0], F = E[1], V = (0, import_react2.useMemo)(function() {
    return dn(o3.filter(function(e4) {
      return -1 === j3.indexOf(e4.id);
    }), a2, d, f2, p2);
  }, [o3, j3, a2, d, f2, p2]), Y = V.xScale, D2 = V.yScale, X3 = V.series, z2 = (0, import_react2.useMemo)(function() {
    var e4 = o3.map(function(e5) {
      return { id: e5.id, label: e5.id, color: L(e5) };
    }), i3 = e4.map(function(e5) {
      return re({}, X3.find(function(i4) {
        return i4.id === e5.id;
      }), { color: e5.color });
    }).filter(function(e5) {
      return Boolean(e5.id);
    });
    return { legendData: e4.map(function(e5) {
      return re({}, e5, { hidden: !i3.find(function(i4) {
        return i4.id === e5.id;
      }) });
    }).reverse(), series: i3 };
  }, [o3, X3, L]), A = z2.legendData, H2 = z2.series, I = (0, import_react2.useCallback)(function(e4) {
    F(function(i3) {
      return i3.indexOf(e4) > -1 ? i3.filter(function(i4) {
        return i4 !== e4;
      }) : [].concat(i3, [e4]);
    });
  }, []), K2 = function(e4) {
    var r3 = e4.series, n3 = e4.getPointColor, o4 = e4.getPointBorderColor, t4 = e4.formatX, a3 = e4.formatY;
    return (0, import_react2.useMemo)(function() {
      return r3.reduce(function(e5, i3) {
        return [].concat(e5, i3.data.filter(function(e6) {
          return null !== e6.position.x && null !== e6.position.y;
        }).map(function(r4, s3) {
          var l3 = { id: i3.id + "." + s3, index: e5.length + s3, serieId: i3.id, serieColor: i3.color, x: r4.position.x, y: r4.position.y };
          return l3.color = n3(i3), l3.borderColor = o4(l3), l3.data = re({}, r4.data, { xFormatted: t4(r4.data.x), yFormatted: a3(r4.data.y) }), l3;
        }));
      }, []);
    }, [r3, n3, o4, t4, a3]);
  }({ series: H2, getPointColor: G, getPointBorderColor: P3, formatX: T2, formatY: w3 }), N2 = ye({ enableSlices: S, points: K2, width: f2, height: p2 });
  return { legendData: A, toggleSerie: I, lineGenerator: pe({ curve: m }), areaGenerator: he({ curve: m, yScale: D2, areaBaselineValue: x2 }), getColor: L, series: H2, xScale: Y, yScale: D2, slices: N2, points: K2 };
};
var ge = function(e3) {
  var i3 = e3.areaBlendMode, r3 = e3.areaOpacity, n3 = e3.color, o3 = e3.fill, t3 = e3.path, a2 = Zr(), s2 = a2.animate, l2 = a2.config, u = $r(t3), d = useSpring({ color: n3, config: l2, immediate: !s2 });
  return (0, import_jsx_runtime2.jsx)(animated.path, { d: u, fill: o3 || d.color, fillOpacity: r3, strokeWidth: 0, style: { mixBlendMode: i3 } });
};
ge.propTypes = { areaBlendMode: zt.isRequired, areaOpacity: import_prop_types.default.number.isRequired, color: import_prop_types.default.string, fill: import_prop_types.default.string, path: import_prop_types.default.string.isRequired };
var me = function(e3) {
  var i3 = e3.areaGenerator, r3 = e3.areaOpacity, n3 = e3.areaBlendMode, o3 = e3.lines.slice(0).reverse();
  return (0, import_jsx_runtime2.jsx)("g", { children: o3.map(function(e4) {
    return (0, import_jsx_runtime2.jsx)(ge, re({ path: i3(e4.data.map(function(e5) {
      return e5.position;
    })) }, re({ areaOpacity: r3, areaBlendMode: n3 }, e4)), e4.id);
  }) });
};
me.propTypes = { areaGenerator: import_prop_types.default.func.isRequired, areaOpacity: import_prop_types.default.number.isRequired, areaBlendMode: zt.isRequired, lines: import_prop_types.default.arrayOf(import_prop_types.default.object).isRequired };
var ve = (0, import_react2.memo)(me);
var xe = function(e3) {
  var r3 = e3.lineGenerator, n3 = e3.points, o3 = e3.color, t3 = e3.thickness, a2 = (0, import_react2.useMemo)(function() {
    return r3(n3);
  }, [r3, n3]), s2 = $r(a2);
  return (0, import_jsx_runtime2.jsx)(animated.path, { d: s2, fill: "none", strokeWidth: t3, stroke: o3 });
};
xe.propTypes = { points: import_prop_types.default.arrayOf(import_prop_types.default.shape({ x: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number]), y: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number]) })), lineGenerator: import_prop_types.default.func.isRequired, color: import_prop_types.default.string.isRequired, thickness: import_prop_types.default.number.isRequired };
var Re = (0, import_react2.memo)(xe);
var qe = function(e3) {
  var i3 = e3.lines, r3 = e3.lineGenerator, n3 = e3.lineWidth;
  return i3.slice(0).reverse().map(function(e4) {
    var i4 = e4.id, o3 = e4.data, t3 = e4.color;
    return (0, import_jsx_runtime2.jsx)(Re, { id: i4, points: o3.map(function(e5) {
      return e5.position;
    }), lineGenerator: r3, color: t3, thickness: n3 }, i4);
  });
};
qe.propTypes = { lines: import_prop_types.default.arrayOf(import_prop_types.default.shape({ id: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number]).isRequired, color: import_prop_types.default.string.isRequired, data: import_prop_types.default.arrayOf(import_prop_types.default.shape({ data: import_prop_types.default.shape({ x: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number, import_prop_types.default.instanceOf(Date)]), y: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number, import_prop_types.default.instanceOf(Date)]) }).isRequired, position: import_prop_types.default.shape({ x: import_prop_types.default.number, y: import_prop_types.default.number }).isRequired })).isRequired })).isRequired, lineWidth: import_prop_types.default.number.isRequired, lineGenerator: import_prop_types.default.func.isRequired };
var Oe = (0, import_react2.memo)(qe);
var Me = function(e3) {
  var i3 = e3.slice, r3 = e3.axis, t3 = e3.debug, a2 = e3.tooltip, s2 = e3.isCurrent, l2 = e3.setCurrent, u = e3.onMouseEnter, d = e3.onMouseMove, c = e3.onMouseLeave, f2 = e3.onClick, p2 = k(), h = p2.showTooltipFromEvent, y = p2.hideTooltip, b2 = (0, import_react2.useCallback)(function(e4) {
    h((0, import_react2.createElement)(a2, { slice: i3, axis: r3 }), e4, "right"), l2(i3), u && u(i3, e4);
  }, [h, a2, i3, u]), g3 = (0, import_react2.useCallback)(function(e4) {
    h((0, import_react2.createElement)(a2, { slice: i3, axis: r3 }), e4, "right"), d && d(i3, e4);
  }, [h, a2, i3, d]), m = (0, import_react2.useCallback)(function(e4) {
    y(), l2(null), c && c(i3, e4);
  }, [y, i3, c]), v2 = (0, import_react2.useCallback)(function(e4) {
    f2 && f2(i3, e4);
  }, [i3, f2]);
  return (0, import_jsx_runtime2.jsx)("rect", { x: i3.x0, y: i3.y0, width: i3.width, height: i3.height, stroke: "red", strokeWidth: t3 ? 1 : 0, strokeOpacity: 0.75, fill: "red", fillOpacity: s2 && t3 ? 0.35 : 0, onMouseEnter: b2, onMouseMove: g3, onMouseLeave: m, onClick: v2, "data-testid": "slice-" + i3.id });
};
Me.propTypes = { slice: import_prop_types.default.object.isRequired, axis: import_prop_types.default.oneOf(["x", "y"]).isRequired, debug: import_prop_types.default.bool.isRequired, height: import_prop_types.default.number.isRequired, tooltip: import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object]), isCurrent: import_prop_types.default.bool.isRequired, setCurrent: import_prop_types.default.func.isRequired, onMouseEnter: import_prop_types.default.func, onMouseMove: import_prop_types.default.func, onMouseLeave: import_prop_types.default.func, onClick: import_prop_types.default.func };
var Ce = (0, import_react2.memo)(Me);
var Se = function(e3) {
  var i3 = e3.slices, r3 = e3.axis, n3 = e3.debug, o3 = e3.height, t3 = e3.tooltip, a2 = e3.current, s2 = e3.setCurrent, l2 = e3.onMouseEnter, u = e3.onMouseMove, d = e3.onMouseLeave, c = e3.onClick;
  return i3.map(function(e4) {
    return (0, import_jsx_runtime2.jsx)(Ce, { slice: e4, axis: r3, debug: n3, height: o3, tooltip: t3, setCurrent: s2, isCurrent: null !== a2 && a2.id === e4.id, onMouseEnter: l2, onMouseMove: u, onMouseLeave: d, onClick: c }, e4.id);
  });
};
Se.propTypes = { slices: import_prop_types.default.arrayOf(import_prop_types.default.shape({ id: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.instanceOf(Date)]).isRequired, x: import_prop_types.default.number.isRequired, y: import_prop_types.default.number.isRequired, points: import_prop_types.default.arrayOf(import_prop_types.default.object).isRequired })).isRequired, axis: import_prop_types.default.oneOf(["x", "y"]).isRequired, debug: import_prop_types.default.bool.isRequired, height: import_prop_types.default.number.isRequired, tooltip: import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object]).isRequired, current: import_prop_types.default.object, setCurrent: import_prop_types.default.func.isRequired, onMouseEnter: import_prop_types.default.func, onMouseMove: import_prop_types.default.func, onMouseLeave: import_prop_types.default.func, onClick: import_prop_types.default.func };
var Te = (0, import_react2.memo)(Se);
var we = function(e3) {
  var i3 = e3.points, r3 = e3.symbol, n3 = e3.size, o3 = e3.borderWidth, t3 = e3.enableLabel, a2 = e3.label, s2 = e3.labelYOffset, l2 = Et(), d = ji(a2), c = i3.slice(0).reverse().map(function(e4) {
    return { id: e4.id, x: e4.x, y: e4.y, datum: e4.data, fill: e4.color, stroke: e4.borderColor, label: t3 ? d(e4.data) : null };
  });
  return (0, import_jsx_runtime2.jsx)("g", { children: c.map(function(e4) {
    return (0, import_jsx_runtime2.jsx)(Wi, { x: e4.x, y: e4.y, datum: e4.datum, symbol: r3, size: n3, color: e4.fill, borderWidth: o3, borderColor: e4.stroke, label: e4.label, labelYOffset: s2, theme: l2 }, e4.id);
  }) });
};
we.propTypes = { points: import_prop_types.default.arrayOf(import_prop_types.default.object), symbol: import_prop_types.default.func, size: import_prop_types.default.number.isRequired, color: import_prop_types.default.func.isRequired, borderWidth: import_prop_types.default.number.isRequired, borderColor: import_prop_types.default.func.isRequired, enableLabel: import_prop_types.default.bool.isRequired, label: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]).isRequired, labelYOffset: import_prop_types.default.number };
var ke = (0, import_react2.memo)(we);
var Be = function(e3) {
  var i3 = e3.points, r3 = e3.width, t3 = e3.height, a2 = e3.margin, s2 = e3.setCurrent, l2 = e3.onMouseEnter, u = e3.onMouseMove, d = e3.onMouseLeave, c = e3.onClick, f2 = e3.tooltip, p2 = e3.debug, h = k(), y = h.showTooltipAt, b2 = h.hideTooltip, g3 = (0, import_react2.useCallback)(function(e4, i4) {
    y((0, import_react2.createElement)(f2, { point: e4 }), [e4.x + a2.left, e4.y + a2.top], "top"), s2(e4), l2 && l2(e4, i4);
  }, [s2, y, f2, l2, a2]), m = (0, import_react2.useCallback)(function(e4, i4) {
    y((0, import_react2.createElement)(f2, { point: e4 }), [e4.x + a2.left, e4.y + a2.top], "top"), s2(e4), u && u(e4, i4);
  }, [s2, y, f2, u]), v2 = (0, import_react2.useCallback)(function(e4, i4) {
    b2(), s2(null), d && d(e4, i4);
  }, [b2, s2, d]), x2 = (0, import_react2.useCallback)(function(e4, i4) {
    c && c(e4, i4);
  }, [c]);
  return (0, import_jsx_runtime2.jsx)(O, { nodes: i3, width: r3, height: t3, onMouseEnter: g3, onMouseMove: m, onMouseLeave: v2, onClick: x2, debug: p2 });
};
Be.propTypes = { points: import_prop_types.default.arrayOf(import_prop_types.default.object).isRequired, width: import_prop_types.default.number.isRequired, height: import_prop_types.default.number.isRequired, margin: import_prop_types.default.object.isRequired, setCurrent: import_prop_types.default.func.isRequired, onMouseEnter: import_prop_types.default.func, onMouseMove: import_prop_types.default.func, onMouseLeave: import_prop_types.default.func, onClick: import_prop_types.default.func, tooltip: import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object]).isRequired, debug: import_prop_types.default.bool.isRequired };
var Le = (0, import_react2.memo)(Be);
var We2 = function(e3) {
  var i3 = e3.data, n3 = e3.xScale, o3 = e3.xFormat, a2 = e3.yScale, s2 = e3.yFormat, l2 = e3.layers, d = e3.curve, c = e3.areaBaselineValue, f2 = e3.colors, p2 = e3.margin, h = e3.width, y = e3.height, b2 = e3.axisTop, g3 = e3.axisRight, m = e3.axisBottom, v2 = e3.axisLeft, x2 = e3.enableGridX, C2 = e3.enableGridY, S = e3.gridXValues, T2 = e3.gridYValues, w3 = e3.lineWidth, k3 = e3.enableArea, L = e3.areaOpacity, P3 = e3.areaBlendMode, E = e3.enablePoints, j3 = e3.pointSymbol, V = e3.pointSize, Y = e3.pointColor, D2 = e3.pointBorderWidth, X3 = e3.pointBorderColor, z2 = e3.enablePointLabel, H2 = e3.pointLabel, I = e3.pointLabelYOffset, J = e3.defs, K2 = e3.fill, Q2 = e3.markers, U = e3.legends, Z = e3.isInteractive, $ = e3.useMesh, _ = e3.debugMesh, ee = e3.onMouseEnter, ie = e3.onMouseMove, ne2 = e3.onMouseLeave, oe2 = e3.onClick, te2 = e3.tooltip, ae2 = e3.enableSlices, se2 = e3.debugSlices, le2 = e3.sliceTooltip, ue2 = e3.enableCrosshair, de2 = e3.crosshairType, ce2 = e3.role, fe2 = Bt(h, y, p2), pe2 = fe2.margin, he2 = fe2.innerWidth, ye2 = fe2.innerHeight, ge2 = fe2.outerWidth, me2 = fe2.outerHeight, xe2 = be({ data: i3, xScale: n3, xFormat: o3, yScale: a2, yFormat: s2, width: he2, height: ye2, colors: f2, curve: d, areaBaselineValue: c, pointColor: Y, pointBorderColor: X3, enableSlices: ae2 }), Re2 = xe2.legendData, qe2 = xe2.toggleSerie, Me2 = xe2.lineGenerator, Ce2 = xe2.areaGenerator, Se2 = xe2.series, we2 = xe2.xScale, Be2 = xe2.yScale, We3 = xe2.slices, Ge2 = xe2.points, Pe2 = Et(), Ee2 = We(Y, Pe2), je2 = We(X3, Pe2), Fe2 = (0, import_react2.useState)(null), Ve2 = Fe2[0], Ye = Fe2[1], De = (0, import_react2.useState)(null), Xe2 = De[0], ze = De[1], Ae = { grid: (0, import_jsx_runtime2.jsx)(z, { theme: Pe2, width: he2, height: ye2, xScale: x2 ? we2 : null, yScale: C2 ? Be2 : null, xValues: S, yValues: T2 }, "grid"), markers: (0, import_jsx_runtime2.jsx)(Ti, { markers: Q2, width: he2, height: ye2, xScale: we2, yScale: Be2, theme: Pe2 }, "markers"), axes: (0, import_jsx_runtime2.jsx)(X, { xScale: we2, yScale: Be2, width: he2, height: ye2, theme: Pe2, top: b2, right: g3, bottom: m, left: v2 }, "axes"), areas: null, lines: (0, import_jsx_runtime2.jsx)(Oe, { lines: Se2, lineGenerator: Me2, lineWidth: w3 }, "lines"), slices: null, points: null, crosshair: null, mesh: null, legends: U.map(function(e4, i4) {
    return (0, import_jsx_runtime2.jsx)(X2, re({}, e4, { containerWidth: he2, containerHeight: ye2, data: e4.data || Re2, theme: Pe2, toggleSerie: e4.toggleSerie ? qe2 : void 0 }), "legend." + i4);
  }) }, He = Hi(J, Se2, K2);
  return k3 && (Ae.areas = (0, import_jsx_runtime2.jsx)(ve, { areaGenerator: Ce2, areaOpacity: L, areaBlendMode: P3, lines: Se2 }, "areas")), Z && false !== ae2 && (Ae.slices = (0, import_jsx_runtime2.jsx)(Te, { slices: We3, axis: ae2, debug: se2, height: ye2, tooltip: le2, current: Xe2, setCurrent: ze, onMouseEnter: ee, onMouseMove: ie, onMouseLeave: ne2, onClick: oe2 }, "slices")), E && (Ae.points = (0, import_jsx_runtime2.jsx)(ke, { points: Ge2, symbol: j3, size: V, color: Ee2, borderWidth: D2, borderColor: je2, enableLabel: z2, label: H2, labelYOffset: I }, "points")), Z && ue2 && (null !== Ve2 && (Ae.crosshair = (0, import_jsx_runtime2.jsx)(P, { width: he2, height: ye2, x: Ve2.x, y: Ve2.y, type: de2 }, "crosshair")), null !== Xe2 && (Ae.crosshair = (0, import_jsx_runtime2.jsx)(P, { width: he2, height: ye2, x: Xe2.x, y: Xe2.y, type: ae2 }, "crosshair"))), Z && $ && false === ae2 && (Ae.mesh = (0, import_jsx_runtime2.jsx)(Le, { points: Ge2, width: he2, height: ye2, margin: pe2, current: Ve2, setCurrent: Ye, onMouseEnter: ee, onMouseMove: ie, onMouseLeave: ne2, onClick: oe2, tooltip: te2, debug: _ }, "mesh")), (0, import_jsx_runtime2.jsx)(_i, { defs: He, width: ge2, height: me2, margin: pe2, role: ce2, children: l2.map(function(i4, r3) {
    return "function" == typeof i4 ? (0, import_jsx_runtime2.jsx)(import_react2.Fragment, { children: i4(re({}, e3, { innerWidth: he2, innerHeight: ye2, series: Se2, slices: We3, points: Ge2, xScale: we2, yScale: Be2, lineGenerator: Me2, areaGenerator: Ce2, currentPoint: Ve2, setCurrentPoint: Ye, currentSlice: Xe2, setCurrentSlice: ze })) }, r3) : Ae[i4];
  }) });
};
We2.propTypes = le, We2.defaultProps = ce;
var Ge = Mi(We2);
var Pe = function(e3) {
  return (0, import_jsx_runtime2.jsx)(Jt, { children: function(i3) {
    var r3 = i3.width, n3 = i3.height;
    return (0, import_jsx_runtime2.jsx)(Ge, re({ width: r3, height: n3 }, e3));
  } });
};
var Ee = function(e3) {
  var i3 = e3.width, t3 = e3.height, a2 = e3.margin, d = e3.pixelRatio, c = e3.data, f2 = e3.xScale, p2 = e3.xFormat, h = e3.yScale, y = e3.yFormat, b2 = e3.curve, g3 = e3.layers, m = e3.colors, v2 = e3.lineWidth, x2 = e3.enableArea, q = e3.areaBaselineValue, O2 = e3.areaOpacity, M = e3.enablePoints, C2 = e3.pointSize, w3 = e3.pointColor, k3 = e3.pointBorderWidth, B = e3.pointBorderColor, L = e3.enableGridX, W = e3.gridXValues, G = e3.enableGridY, j3 = e3.gridYValues, F = e3.axisTop, Y = e3.axisRight, D2 = e3.axisBottom, X3 = e3.axisLeft, A = e3.legends, H2 = e3.isInteractive, I = e3.debugMesh, J = e3.onMouseLeave, K2 = e3.onClick, Q2 = e3.tooltip, U = e3.canvasRef, Z = (0, import_react2.useRef)(null), $ = Bt(i3, t3, a2), ne2 = $.margin, oe2 = $.innerWidth, te2 = $.innerHeight, ae2 = $.outerWidth, se2 = $.outerHeight, le2 = Et(), ue2 = (0, import_react2.useState)(null), de2 = ue2[0], ce2 = ue2[1], fe2 = be({ data: c, xScale: f2, xFormat: p2, yScale: h, yFormat: y, width: oe2, height: te2, colors: m, curve: b2, areaBaselineValue: q, pointColor: w3, pointBorderColor: B }), pe2 = fe2.lineGenerator, he2 = fe2.areaGenerator, ye2 = fe2.series, ge2 = fe2.xScale, me2 = fe2.yScale, ve2 = fe2.points, xe2 = x({ points: ve2, width: oe2, height: te2, debug: I }), Re2 = xe2.delaunay, qe2 = xe2.voronoi;
  (0, import_react2.useEffect)(function() {
    U && (U.current = Z.current), Z.current.width = ae2 * d, Z.current.height = se2 * d;
    var e4 = Z.current.getContext("2d");
    e4.scale(d, d), e4.fillStyle = le2.background, e4.fillRect(0, 0, ae2, se2), e4.translate(ne2.left, ne2.top), g3.forEach(function(i4) {
      if ("function" == typeof i4 && i4({ ctx: e4, innerWidth: oe2, innerHeight: te2, series: ye2, points: ve2, xScale: ge2, yScale: me2, lineWidth: v2, lineGenerator: pe2, areaGenerator: he2, currentPoint: de2, setCurrentPoint: ce2 }), "grid" === i4 && le2.grid.line.strokeWidth > 0 && (e4.lineWidth = le2.grid.line.strokeWidth, e4.strokeStyle = le2.grid.line.stroke, L && D(e4, { width: oe2, height: te2, scale: ge2, axis: "x", values: W }), G && D(e4, { width: oe2, height: te2, scale: me2, axis: "y", values: j3 })), "axes" === i4 && j(e4, { xScale: ge2, yScale: me2, width: oe2, height: te2, top: F, right: Y, bottom: D2, left: X3, theme: le2 }), "areas" === i4 && true === x2 && (e4.save(), e4.globalAlpha = O2, he2.context(e4), ye2.forEach(function(i5) {
        e4.fillStyle = i5.color, e4.beginPath(), he2(i5.data.map(function(e5) {
          return e5.position;
        })), e4.fill();
      }), e4.restore()), "lines" === i4 && (pe2.context(e4), ye2.forEach(function(i5) {
        e4.strokeStyle = i5.color, e4.lineWidth = v2, e4.beginPath(), pe2(i5.data.map(function(e5) {
          return e5.position;
        })), e4.stroke();
      })), "points" === i4 && true === M && C2 > 0 && ve2.forEach(function(i5) {
        e4.fillStyle = i5.color, e4.beginPath(), e4.arc(i5.x, i5.y, C2 / 2, 0, 2 * Math.PI), e4.fill(), k3 > 0 && (e4.strokeStyle = i5.borderColor, e4.lineWidth = k3, e4.stroke());
      }), "mesh" === i4 && true === I && (P2(e4, qe2), de2 && j2(e4, qe2, de2.index)), "legends" === i4) {
        var r3 = ye2.map(function(e5) {
          return { id: e5.id, label: e5.id, color: e5.color };
        }).reverse();
        A.forEach(function(i5) {
          H(e4, re({}, i5, { data: i5.data || r3, containerWidth: oe2, containerHeight: te2, theme: le2 }));
        });
      }
    });
  }, [Z, ae2, se2, g3, le2, pe2, ye2, ge2, me2, L, W, G, j3, F, Y, D2, X3, A, ve2, M, C2, de2]);
  var Oe2 = (0, import_react2.useCallback)(function(e4) {
    var i4 = Fi(Z.current, e4), r3 = i4[0], n3 = i4[1];
    if (!Ai(ne2.left, ne2.top, oe2, te2, r3, n3))
      return null;
    var o3 = Re2.find(r3 - ne2.left, n3 - ne2.top);
    return ve2[o3];
  }, [Z, ne2, oe2, te2, Re2]), Me2 = k(), Ce2 = Me2.showTooltipFromEvent, Se2 = Me2.hideTooltip, Te2 = (0, import_react2.useCallback)(function(e4) {
    var i4 = Oe2(e4);
    ce2(i4), i4 ? Ce2((0, import_react2.createElement)(Q2, { point: i4 }), e4) : Se2();
  }, [Oe2, ce2, Ce2, Se2, Q2]), we2 = (0, import_react2.useCallback)(function(e4) {
    Se2(), ce2(null), de2 && J && J(de2, e4);
  }, [Se2, ce2, J]), ke2 = (0, import_react2.useCallback)(function(e4) {
    if (K2) {
      var i4 = Oe2(e4);
      i4 && K2(i4, e4);
    }
  }, [Oe2, K2]);
  return (0, import_jsx_runtime2.jsx)("canvas", { ref: Z, width: ae2 * d, height: se2 * d, style: { width: ae2, height: se2, cursor: H2 ? "auto" : "normal" }, onMouseEnter: H2 ? Te2 : void 0, onMouseMove: H2 ? Te2 : void 0, onMouseLeave: H2 ? we2 : void 0, onClick: H2 ? ke2 : void 0 });
};
Ee.propTypes = ue, Ee.defaultProps = fe;
var je = Mi(Ee);
var Fe = (0, import_react2.forwardRef)(function(e3, i3) {
  return (0, import_jsx_runtime2.jsx)(je, re({}, e3, { canvasRef: i3 }));
});
var Ve = (0, import_react2.forwardRef)(function(e3, i3) {
  return (0, import_jsx_runtime2.jsx)(Jt, { children: function(r3) {
    var n3 = r3.width, o3 = r3.height;
    return (0, import_jsx_runtime2.jsx)(Fe, re({ width: n3, height: o3 }, e3, { ref: i3 }));
  } });
});
export {
  Ge as Line,
  Fe as LineCanvas,
  fe as LineCanvasDefaultProps,
  ue as LineCanvasPropTypes,
  ce as LineDefaultProps,
  le as LinePropTypes,
  Pe as ResponsiveLine,
  Ve as ResponsiveLineCanvas,
  he as useAreaGenerator,
  be as useLine,
  pe as useLineGenerator,
  ye as useSlices
};
//# sourceMappingURL=@nivo_line.js.map
