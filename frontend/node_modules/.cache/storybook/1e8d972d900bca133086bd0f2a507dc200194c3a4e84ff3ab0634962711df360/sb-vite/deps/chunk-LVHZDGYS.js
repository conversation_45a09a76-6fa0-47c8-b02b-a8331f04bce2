import {
  require_upperFirst
} from "./chunk-5FSLK3P4.js";
import {
  require_createCompounder
} from "./chunk-VHG7YKWX.js";
import {
  __commonJS
} from "./chunk-2LSFTFF7.js";

// node_modules/lodash/startCase.js
var require_startCase = __commonJS({
  "node_modules/lodash/startCase.js"(exports, module) {
    var createCompounder = require_createCompounder();
    var upperFirst = require_upperFirst();
    var startCase = createCompounder(function(result, word, index) {
      return result + (index ? " " : "") + upperFirst(word);
    });
    module.exports = startCase;
  }
});

export {
  require_startCase
};
//# sourceMappingURL=chunk-LVHZDGYS.js.map
