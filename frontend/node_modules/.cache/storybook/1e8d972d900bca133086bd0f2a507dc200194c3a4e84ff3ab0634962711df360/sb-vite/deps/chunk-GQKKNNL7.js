import {
  require_baseMerge,
  require_createAssigner
} from "./chunk-D3GWGFQY.js";
import {
  __commonJS
} from "./chunk-2LSFTFF7.js";

// node_modules/lodash/merge.js
var require_merge = __commonJS({
  "node_modules/lodash/merge.js"(exports, module) {
    var baseMerge = require_baseMerge();
    var createAssigner = require_createAssigner();
    var merge = createAssigner(function(object, source, srcIndex) {
      baseMerge(object, source, srcIndex);
    });
    module.exports = merge;
  }
});

export {
  require_merge
};
//# sourceMappingURL=chunk-GQKKNNL7.js.map
