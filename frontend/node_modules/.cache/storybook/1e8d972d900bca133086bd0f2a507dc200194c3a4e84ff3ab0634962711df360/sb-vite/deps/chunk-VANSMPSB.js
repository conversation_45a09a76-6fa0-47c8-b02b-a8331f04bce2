import {
  require_overArg
} from "./chunk-B5CG2ER2.js";
import {
  __commonJS
} from "./chunk-2LSFTFF7.js";

// node_modules/lodash/_getPrototype.js
var require_getPrototype = __commonJS({
  "node_modules/lodash/_getPrototype.js"(exports, module) {
    var overArg = require_overArg();
    var getPrototype = overArg(Object.getPrototypeOf, Object);
    module.exports = getPrototype;
  }
});

export {
  require_getPrototype
};
//# sourceMappingURL=chunk-VANSMPSB.js.map
