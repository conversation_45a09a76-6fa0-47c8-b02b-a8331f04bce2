// node_modules/@storybook/addon-viewport/dist/preview.mjs
var PARAM_KEY = "viewport";
var modern = { [PARAM_KEY]: { value: void 0, isRotated: false } };
var legacy = { viewport: "reset", viewportRotated: false };
var _a;
var initialGlobals = ((_a = globalThis.FEATURES) == null ? void 0 : _a.viewportStoryGlobals) ? modern : legacy;

export {
  initialGlobals
};
//# sourceMappingURL=chunk-JT7OIGPO.js.map
