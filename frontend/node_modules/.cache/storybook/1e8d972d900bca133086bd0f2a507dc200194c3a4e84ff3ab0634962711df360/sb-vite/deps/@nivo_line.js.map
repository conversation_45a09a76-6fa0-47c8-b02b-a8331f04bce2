{"version": 3, "sources": ["../../../../../delaunator/index.js", "../../../../../d3-delaunay/src/path.js", "../../../../../d3-delaunay/src/polygon.js", "../../../../../d3-delaunay/src/voronoi.js", "../../../../../d3-delaunay/src/delaunay.js", "../../../../../@nivo/voronoi/src/props.ts", "../../../../../@nivo/voronoi/src/computeMesh.ts", "../../../../../@nivo/voronoi/src/hooks.ts", "../../../../../@nivo/voronoi/src/Voronoi.tsx", "../../../../../@nivo/voronoi/src/ResponsiveVoronoi.tsx", "../../../../../@nivo/voronoi/src/Mesh.tsx", "../../../../../@nivo/voronoi/src/meshCanvas.ts", "../../../../../@nivo/line/src/PointTooltip.js", "../../../../../@nivo/line/src/SliceTooltip.js", "../../../../../@nivo/line/src/props.js", "../../../../../@nivo/line/src/hooks.js", "../../../../../@nivo/line/src/Areas.js", "../../../../../@nivo/line/src/LinesItem.js", "../../../../../@nivo/line/src/Lines.js", "../../../../../@nivo/line/src/SlicesItem.js", "../../../../../@nivo/line/src/Slices.js", "../../../../../@nivo/line/src/Points.js", "../../../../../@nivo/line/src/Mesh.js", "../../../../../@nivo/line/src/Line.js", "../../../../../@nivo/line/src/ResponsiveLine.js", "../../../../../@nivo/line/src/LineCanvas.js", "../../../../../@nivo/line/src/ResponsiveLineCanvas.js"], "sourcesContent": ["\nconst EPSILON = Math.pow(2, -52);\nconst EDGE_STACK = new Uint32Array(512);\n\nexport default class Delaunator {\n\n    static from(points, getX = defaultGetX, getY = defaultGetY) {\n        const n = points.length;\n        const coords = new Float64Array(n * 2);\n\n        for (let i = 0; i < n; i++) {\n            const p = points[i];\n            coords[2 * i] = getX(p);\n            coords[2 * i + 1] = getY(p);\n        }\n\n        return new Delaunator(coords);\n    }\n\n    constructor(coords) {\n        const n = coords.length >> 1;\n        if (n > 0 && typeof coords[0] !== 'number') throw new Error('Expected coords to contain numbers.');\n\n        this.coords = coords;\n\n        // arrays that will store the triangulation graph\n        const maxTriangles = Math.max(2 * n - 5, 0);\n        this._triangles = new Uint32Array(maxTriangles * 3);\n        this._halfedges = new Int32Array(maxTriangles * 3);\n\n        // temporary arrays for tracking the edges of the advancing convex hull\n        this._hashSize = Math.ceil(Math.sqrt(n));\n        this._hullPrev = new Uint32Array(n); // edge to prev edge\n        this._hullNext = new Uint32Array(n); // edge to next edge\n        this._hullTri = new Uint32Array(n); // edge to adjacent triangle\n        this._hullHash = new Int32Array(this._hashSize).fill(-1); // angular edge hash\n\n        // temporary arrays for sorting points\n        this._ids = new Uint32Array(n);\n        this._dists = new Float64Array(n);\n\n        this.update();\n    }\n\n    update() {\n        const {coords, _hullPrev: hullPrev, _hullNext: hullNext, _hullTri: hullTri, _hullHash: hullHash} =  this;\n        const n = coords.length >> 1;\n\n        // populate an array of point indices; calculate input data bbox\n        let minX = Infinity;\n        let minY = Infinity;\n        let maxX = -Infinity;\n        let maxY = -Infinity;\n\n        for (let i = 0; i < n; i++) {\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n            if (x < minX) minX = x;\n            if (y < minY) minY = y;\n            if (x > maxX) maxX = x;\n            if (y > maxY) maxY = y;\n            this._ids[i] = i;\n        }\n        const cx = (minX + maxX) / 2;\n        const cy = (minY + maxY) / 2;\n\n        let minDist = Infinity;\n        let i0, i1, i2;\n\n        // pick a seed point close to the center\n        for (let i = 0; i < n; i++) {\n            const d = dist(cx, cy, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist) {\n                i0 = i;\n                minDist = d;\n            }\n        }\n        const i0x = coords[2 * i0];\n        const i0y = coords[2 * i0 + 1];\n\n        minDist = Infinity;\n\n        // find the point closest to the seed\n        for (let i = 0; i < n; i++) {\n            if (i === i0) continue;\n            const d = dist(i0x, i0y, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist && d > 0) {\n                i1 = i;\n                minDist = d;\n            }\n        }\n        let i1x = coords[2 * i1];\n        let i1y = coords[2 * i1 + 1];\n\n        let minRadius = Infinity;\n\n        // find the third point which forms the smallest circumcircle with the first two\n        for (let i = 0; i < n; i++) {\n            if (i === i0 || i === i1) continue;\n            const r = circumradius(i0x, i0y, i1x, i1y, coords[2 * i], coords[2 * i + 1]);\n            if (r < minRadius) {\n                i2 = i;\n                minRadius = r;\n            }\n        }\n        let i2x = coords[2 * i2];\n        let i2y = coords[2 * i2 + 1];\n\n        if (minRadius === Infinity) {\n            // order collinear points by dx (or dy if all x are identical)\n            // and return the list as a hull\n            for (let i = 0; i < n; i++) {\n                this._dists[i] = (coords[2 * i] - coords[0]) || (coords[2 * i + 1] - coords[1]);\n            }\n            quicksort(this._ids, this._dists, 0, n - 1);\n            const hull = new Uint32Array(n);\n            let j = 0;\n            for (let i = 0, d0 = -Infinity; i < n; i++) {\n                const id = this._ids[i];\n                if (this._dists[id] > d0) {\n                    hull[j++] = id;\n                    d0 = this._dists[id];\n                }\n            }\n            this.hull = hull.subarray(0, j);\n            this.triangles = new Uint32Array(0);\n            this.halfedges = new Uint32Array(0);\n            return;\n        }\n\n        // swap the order of the seed points for counter-clockwise orientation\n        if (orient(i0x, i0y, i1x, i1y, i2x, i2y)) {\n            const i = i1;\n            const x = i1x;\n            const y = i1y;\n            i1 = i2;\n            i1x = i2x;\n            i1y = i2y;\n            i2 = i;\n            i2x = x;\n            i2y = y;\n        }\n\n        const center = circumcenter(i0x, i0y, i1x, i1y, i2x, i2y);\n        this._cx = center.x;\n        this._cy = center.y;\n\n        for (let i = 0; i < n; i++) {\n            this._dists[i] = dist(coords[2 * i], coords[2 * i + 1], center.x, center.y);\n        }\n\n        // sort the points by distance from the seed triangle circumcenter\n        quicksort(this._ids, this._dists, 0, n - 1);\n\n        // set up the seed triangle as the starting hull\n        this._hullStart = i0;\n        let hullSize = 3;\n\n        hullNext[i0] = hullPrev[i2] = i1;\n        hullNext[i1] = hullPrev[i0] = i2;\n        hullNext[i2] = hullPrev[i1] = i0;\n\n        hullTri[i0] = 0;\n        hullTri[i1] = 1;\n        hullTri[i2] = 2;\n\n        hullHash.fill(-1);\n        hullHash[this._hashKey(i0x, i0y)] = i0;\n        hullHash[this._hashKey(i1x, i1y)] = i1;\n        hullHash[this._hashKey(i2x, i2y)] = i2;\n\n        this.trianglesLen = 0;\n        this._addTriangle(i0, i1, i2, -1, -1, -1);\n\n        for (let k = 0, xp, yp; k < this._ids.length; k++) {\n            const i = this._ids[k];\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n\n            // skip near-duplicate points\n            if (k > 0 && Math.abs(x - xp) <= EPSILON && Math.abs(y - yp) <= EPSILON) continue;\n            xp = x;\n            yp = y;\n\n            // skip seed triangle points\n            if (i === i0 || i === i1 || i === i2) continue;\n\n            // find a visible edge on the convex hull using edge hash\n            let start = 0;\n            for (let j = 0, key = this._hashKey(x, y); j < this._hashSize; j++) {\n                start = hullHash[(key + j) % this._hashSize];\n                if (start !== -1 && start !== hullNext[start]) break;\n            }\n\n            start = hullPrev[start];\n            let e = start, q;\n            while (q = hullNext[e], !orient(x, y, coords[2 * e], coords[2 * e + 1], coords[2 * q], coords[2 * q + 1])) {\n                e = q;\n                if (e === start) {\n                    e = -1;\n                    break;\n                }\n            }\n            if (e === -1) continue; // likely a near-duplicate point; skip it\n\n            // add the first triangle from the point\n            let t = this._addTriangle(e, i, hullNext[e], -1, -1, hullTri[e]);\n\n            // recursively flip triangles from the point until they satisfy the Delaunay condition\n            hullTri[i] = this._legalize(t + 2);\n            hullTri[e] = t; // keep track of boundary triangles on the hull\n            hullSize++;\n\n            // walk forward through the hull, adding more triangles and flipping recursively\n            let n = hullNext[e];\n            while (q = hullNext[n], orient(x, y, coords[2 * n], coords[2 * n + 1], coords[2 * q], coords[2 * q + 1])) {\n                t = this._addTriangle(n, i, q, hullTri[i], -1, hullTri[n]);\n                hullTri[i] = this._legalize(t + 2);\n                hullNext[n] = n; // mark as removed\n                hullSize--;\n                n = q;\n            }\n\n            // walk backward from the other side, adding more triangles and flipping\n            if (e === start) {\n                while (q = hullPrev[e], orient(x, y, coords[2 * q], coords[2 * q + 1], coords[2 * e], coords[2 * e + 1])) {\n                    t = this._addTriangle(q, i, e, -1, hullTri[e], hullTri[q]);\n                    this._legalize(t + 2);\n                    hullTri[q] = t;\n                    hullNext[e] = e; // mark as removed\n                    hullSize--;\n                    e = q;\n                }\n            }\n\n            // update the hull indices\n            this._hullStart = hullPrev[i] = e;\n            hullNext[e] = hullPrev[n] = i;\n            hullNext[i] = n;\n\n            // save the two new edges in the hash table\n            hullHash[this._hashKey(x, y)] = i;\n            hullHash[this._hashKey(coords[2 * e], coords[2 * e + 1])] = e;\n        }\n\n        this.hull = new Uint32Array(hullSize);\n        for (let i = 0, e = this._hullStart; i < hullSize; i++) {\n            this.hull[i] = e;\n            e = hullNext[e];\n        }\n\n        // trim typed triangle mesh arrays\n        this.triangles = this._triangles.subarray(0, this.trianglesLen);\n        this.halfedges = this._halfedges.subarray(0, this.trianglesLen);\n    }\n\n    _hashKey(x, y) {\n        return Math.floor(pseudoAngle(x - this._cx, y - this._cy) * this._hashSize) % this._hashSize;\n    }\n\n    _legalize(a) {\n        const {_triangles: triangles, _halfedges: halfedges, coords} = this;\n\n        let i = 0;\n        let ar = 0;\n\n        // recursion eliminated with a fixed-size stack\n        while (true) {\n            const b = halfedges[a];\n\n            /* if the pair of triangles doesn't satisfy the Delaunay condition\n             * (p1 is inside the circumcircle of [p0, pl, pr]), flip them,\n             * then do the same check/flip recursively for the new pair of triangles\n             *\n             *           pl                    pl\n             *          /||\\                  /  \\\n             *       al/ || \\bl            al/    \\a\n             *        /  ||  \\              /      \\\n             *       /  a||b  \\    flip    /___ar___\\\n             *     p0\\   ||   /p1   =>   p0\\---bl---/p1\n             *        \\  ||  /              \\      /\n             *       ar\\ || /br             b\\    /br\n             *          \\||/                  \\  /\n             *           pr                    pr\n             */\n            const a0 = a - a % 3;\n            ar = a0 + (a + 2) % 3;\n\n            if (b === -1) { // convex hull edge\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n                continue;\n            }\n\n            const b0 = b - b % 3;\n            const al = a0 + (a + 1) % 3;\n            const bl = b0 + (b + 2) % 3;\n\n            const p0 = triangles[ar];\n            const pr = triangles[a];\n            const pl = triangles[al];\n            const p1 = triangles[bl];\n\n            const illegal = inCircle(\n                coords[2 * p0], coords[2 * p0 + 1],\n                coords[2 * pr], coords[2 * pr + 1],\n                coords[2 * pl], coords[2 * pl + 1],\n                coords[2 * p1], coords[2 * p1 + 1]);\n\n            if (illegal) {\n                triangles[a] = p1;\n                triangles[b] = p0;\n\n                const hbl = halfedges[bl];\n\n                // edge swapped on the other side of the hull (rare); fix the halfedge reference\n                if (hbl === -1) {\n                    let e = this._hullStart;\n                    do {\n                        if (this._hullTri[e] === bl) {\n                            this._hullTri[e] = a;\n                            break;\n                        }\n                        e = this._hullPrev[e];\n                    } while (e !== this._hullStart);\n                }\n                this._link(a, hbl);\n                this._link(b, halfedges[ar]);\n                this._link(ar, bl);\n\n                const br = b0 + (b + 1) % 3;\n\n                // don't worry about hitting the cap: it can only happen on extremely degenerate input\n                if (i < EDGE_STACK.length) {\n                    EDGE_STACK[i++] = br;\n                }\n            } else {\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n            }\n        }\n\n        return ar;\n    }\n\n    _link(a, b) {\n        this._halfedges[a] = b;\n        if (b !== -1) this._halfedges[b] = a;\n    }\n\n    // add a new triangle given vertex indices and adjacent half-edge ids\n    _addTriangle(i0, i1, i2, a, b, c) {\n        const t = this.trianglesLen;\n\n        this._triangles[t] = i0;\n        this._triangles[t + 1] = i1;\n        this._triangles[t + 2] = i2;\n\n        this._link(t, a);\n        this._link(t + 1, b);\n        this._link(t + 2, c);\n\n        this.trianglesLen += 3;\n\n        return t;\n    }\n}\n\n// monotonically increases with real angle, but doesn't need expensive trigonometry\nfunction pseudoAngle(dx, dy) {\n    const p = dx / (Math.abs(dx) + Math.abs(dy));\n    return (dy > 0 ? 3 - p : 1 + p) / 4; // [0..1]\n}\n\nfunction dist(ax, ay, bx, by) {\n    const dx = ax - bx;\n    const dy = ay - by;\n    return dx * dx + dy * dy;\n}\n\n// return 2d orientation sign if we're confident in it through J. Shewchuk's error bound check\nfunction orientIfSure(px, py, rx, ry, qx, qy) {\n    const l = (ry - py) * (qx - px);\n    const r = (rx - px) * (qy - py);\n    return Math.abs(l - r) >= 3.3306690738754716e-16 * Math.abs(l + r) ? l - r : 0;\n}\n\n// a more robust orientation test that's stable in a given triangle (to fix robustness issues)\nfunction orient(rx, ry, qx, qy, px, py) {\n    const sign = orientIfSure(px, py, rx, ry, qx, qy) ||\n    orientIfSure(rx, ry, qx, qy, px, py) ||\n    orientIfSure(qx, qy, px, py, rx, ry);\n    return sign < 0;\n}\n\nfunction inCircle(ax, ay, bx, by, cx, cy, px, py) {\n    const dx = ax - px;\n    const dy = ay - py;\n    const ex = bx - px;\n    const ey = by - py;\n    const fx = cx - px;\n    const fy = cy - py;\n\n    const ap = dx * dx + dy * dy;\n    const bp = ex * ex + ey * ey;\n    const cp = fx * fx + fy * fy;\n\n    return dx * (ey * cp - bp * fy) -\n           dy * (ex * cp - bp * fx) +\n           ap * (ex * fy - ey * fx) < 0;\n}\n\nfunction circumradius(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n\n    const x = (ey * bl - dy * cl) * d;\n    const y = (dx * cl - ex * bl) * d;\n\n    return x * x + y * y;\n}\n\nfunction circumcenter(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n\n    const x = ax + (ey * bl - dy * cl) * d;\n    const y = ay + (dx * cl - ex * bl) * d;\n\n    return {x, y};\n}\n\nfunction quicksort(ids, dists, left, right) {\n    if (right - left <= 20) {\n        for (let i = left + 1; i <= right; i++) {\n            const temp = ids[i];\n            const tempDist = dists[temp];\n            let j = i - 1;\n            while (j >= left && dists[ids[j]] > tempDist) ids[j + 1] = ids[j--];\n            ids[j + 1] = temp;\n        }\n    } else {\n        const median = (left + right) >> 1;\n        let i = left + 1;\n        let j = right;\n        swap(ids, median, i);\n        if (dists[ids[left]] > dists[ids[right]]) swap(ids, left, right);\n        if (dists[ids[i]] > dists[ids[right]]) swap(ids, i, right);\n        if (dists[ids[left]] > dists[ids[i]]) swap(ids, left, i);\n\n        const temp = ids[i];\n        const tempDist = dists[temp];\n        while (true) {\n            do i++; while (dists[ids[i]] < tempDist);\n            do j--; while (dists[ids[j]] > tempDist);\n            if (j < i) break;\n            swap(ids, i, j);\n        }\n        ids[left + 1] = ids[j];\n        ids[j] = temp;\n\n        if (right - i + 1 >= j - left) {\n            quicksort(ids, dists, i, right);\n            quicksort(ids, dists, left, j - 1);\n        } else {\n            quicksort(ids, dists, left, j - 1);\n            quicksort(ids, dists, i, right);\n        }\n    }\n}\n\nfunction swap(arr, i, j) {\n    const tmp = arr[i];\n    arr[i] = arr[j];\n    arr[j] = tmp;\n}\n\nfunction defaultGetX(p) {\n    return p[0];\n}\nfunction defaultGetY(p) {\n    return p[1];\n}\n", "const epsilon = 1e-6;\n\nexport default class Path {\n  constructor() {\n    this._x0 = this._y0 = // start of current subpath\n    this._x1 = this._y1 = null; // end of current subpath\n    this._ = \"\";\n  }\n  moveTo(x, y) {\n    this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}`;\n  }\n  closePath() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._ += \"Z\";\n    }\n  }\n  lineTo(x, y) {\n    this._ += `L${this._x1 = +x},${this._y1 = +y}`;\n  }\n  arc(x, y, r) {\n    x = +x, y = +y, r = +r;\n    const x0 = x + r;\n    const y0 = y;\n    if (r < 0) throw new Error(\"negative radius\");\n    if (this._x1 === null) this._ += `M${x0},${y0}`;\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) this._ += \"L\" + x0 + \",\" + y0;\n    if (!r) return;\n    this._ += `A${r},${r},0,1,1,${x - r},${y}A${r},${r},0,1,1,${this._x1 = x0},${this._y1 = y0}`;\n  }\n  rect(x, y, w, h) {\n    this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}h${+w}v${+h}h${-w}Z`;\n  }\n  value() {\n    return this._ || null;\n  }\n}\n", "export default class Polygon {\n  constructor() {\n    this._ = [];\n  }\n  moveTo(x, y) {\n    this._.push([x, y]);\n  }\n  closePath() {\n    this._.push(this._[0].slice());\n  }\n  lineTo(x, y) {\n    this._.push([x, y]);\n  }\n  value() {\n    return this._.length ? this._ : null;\n  }\n}\n", "import Path from \"./path.js\";\nimport Polygon from \"./polygon.js\";\n\nexport default class Voronoi {\n  constructor(delaunay, [xmin, ymin, xmax, ymax] = [0, 0, 960, 500]) {\n    if (!((xmax = +xmax) >= (xmin = +xmin)) || !((ymax = +ymax) >= (ymin = +ymin))) throw new Error(\"invalid bounds\");\n    this.delaunay = delaunay;\n    this._circumcenters = new Float64Array(delaunay.points.length * 2);\n    this.vectors = new Float64Array(delaunay.points.length * 2);\n    this.xmax = xmax, this.xmin = xmin;\n    this.ymax = ymax, this.ymin = ymin;\n    this._init();\n  }\n  update() {\n    this.delaunay.update();\n    this._init();\n    return this;\n  }\n  _init() {\n    const {delaunay: {points, hull, triangles}, vectors} = this;\n\n    // Compute circumcenters.\n    const circumcenters = this.circumcenters = this._circumcenters.subarray(0, triangles.length / 3 * 2);\n    for (let i = 0, j = 0, n = triangles.length, x, y; i < n; i += 3, j += 2) {\n      const t1 = triangles[i] * 2;\n      const t2 = triangles[i + 1] * 2;\n      const t3 = triangles[i + 2] * 2;\n      const x1 = points[t1];\n      const y1 = points[t1 + 1];\n      const x2 = points[t2];\n      const y2 = points[t2 + 1];\n      const x3 = points[t3];\n      const y3 = points[t3 + 1];\n\n      const dx = x2 - x1;\n      const dy = y2 - y1;\n      const ex = x3 - x1;\n      const ey = y3 - y1;\n      const bl = dx * dx + dy * dy;\n      const cl = ex * ex + ey * ey;\n      const ab = (dx * ey - dy * ex) * 2;\n\n      if (!ab) {\n        // degenerate case (collinear diagram)\n        x = (x1 + x3) / 2 - 1e8 * ey;\n        y = (y1 + y3) / 2 + 1e8 * ex;\n      }\n      else if (Math.abs(ab) < 1e-8) {\n        // almost equal points (degenerate triangle)\n        x = (x1 + x3) / 2;\n        y = (y1 + y3) / 2;\n      } else {\n        const d = 1 / ab;\n        x = x1 + (ey * bl - dy * cl) * d;\n        y = y1 + (dx * cl - ex * bl) * d;\n      }\n      circumcenters[j] = x;\n      circumcenters[j + 1] = y;\n    }\n\n    // Compute exterior cell rays.\n    let h = hull[hull.length - 1];\n    let p0, p1 = h * 4;\n    let x0, x1 = points[2 * h];\n    let y0, y1 = points[2 * h + 1];\n    vectors.fill(0);\n    for (let i = 0; i < hull.length; ++i) {\n      h = hull[i];\n      p0 = p1, x0 = x1, y0 = y1;\n      p1 = h * 4, x1 = points[2 * h], y1 = points[2 * h + 1];\n      vectors[p0 + 2] = vectors[p1] = y0 - y1;\n      vectors[p0 + 3] = vectors[p1 + 1] = x1 - x0;\n    }\n  }\n  render(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {delaunay: {halfedges, inedges, hull}, circumcenters, vectors} = this;\n    if (hull.length <= 1) return null;\n    for (let i = 0, n = halfedges.length; i < n; ++i) {\n      const j = halfedges[i];\n      if (j < i) continue;\n      const ti = Math.floor(i / 3) * 2;\n      const tj = Math.floor(j / 3) * 2;\n      const xi = circumcenters[ti];\n      const yi = circumcenters[ti + 1];\n      const xj = circumcenters[tj];\n      const yj = circumcenters[tj + 1];\n      this._renderSegment(xi, yi, xj, yj, context);\n    }\n    let h0, h1 = hull[hull.length - 1];\n    for (let i = 0; i < hull.length; ++i) {\n      h0 = h1, h1 = hull[i];\n      const t = Math.floor(inedges[h1] / 3) * 2;\n      const x = circumcenters[t];\n      const y = circumcenters[t + 1];\n      const v = h0 * 4;\n      const p = this._project(x, y, vectors[v + 2], vectors[v + 3]);\n      if (p) this._renderSegment(x, y, p[0], p[1], context);\n    }\n    return buffer && buffer.value();\n  }\n  renderBounds(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    context.rect(this.xmin, this.ymin, this.xmax - this.xmin, this.ymax - this.ymin);\n    return buffer && buffer.value();\n  }\n  renderCell(i, context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const points = this._clip(i);\n    if (points === null || !points.length) return;\n    context.moveTo(points[0], points[1]);\n    let n = points.length;\n    while (points[0] === points[n-2] && points[1] === points[n-1] && n > 1) n -= 2;\n    for (let i = 2; i < n; i += 2) {\n      if (points[i] !== points[i-2] || points[i+1] !== points[i-1])\n        context.lineTo(points[i], points[i + 1]);\n    }\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  *cellPolygons() {\n    const {delaunay: {points}} = this;\n    for (let i = 0, n = points.length / 2; i < n; ++i) {\n      const cell = this.cellPolygon(i);\n      if (cell) cell.index = i, yield cell;\n    }\n  }\n  cellPolygon(i) {\n    const polygon = new Polygon;\n    this.renderCell(i, polygon);\n    return polygon.value();\n  }\n  _renderSegment(x0, y0, x1, y1, context) {\n    let S;\n    const c0 = this._regioncode(x0, y0);\n    const c1 = this._regioncode(x1, y1);\n    if (c0 === 0 && c1 === 0) {\n      context.moveTo(x0, y0);\n      context.lineTo(x1, y1);\n    } else if (S = this._clipSegment(x0, y0, x1, y1, c0, c1)) {\n      context.moveTo(S[0], S[1]);\n      context.lineTo(S[2], S[3]);\n    }\n  }\n  contains(i, x, y) {\n    if ((x = +x, x !== x) || (y = +y, y !== y)) return false;\n    return this.delaunay._step(i, x, y) === i;\n  }\n  *neighbors(i) {\n    const ci = this._clip(i);\n    if (ci) for (const j of this.delaunay.neighbors(i)) {\n      const cj = this._clip(j);\n      // find the common edge\n      if (cj) loop: for (let ai = 0, li = ci.length; ai < li; ai += 2) {\n        for (let aj = 0, lj = cj.length; aj < lj; aj += 2) {\n          if (ci[ai] == cj[aj]\n          && ci[ai + 1] == cj[aj + 1]\n          && ci[(ai + 2) % li] == cj[(aj + lj - 2) % lj]\n          && ci[(ai + 3) % li] == cj[(aj + lj - 1) % lj]\n          ) {\n            yield j;\n            break loop;\n          }\n        }\n      }\n    }\n  }\n  _cell(i) {\n    const {circumcenters, delaunay: {inedges, halfedges, triangles}} = this;\n    const e0 = inedges[i];\n    if (e0 === -1) return null; // coincident point\n    const points = [];\n    let e = e0;\n    do {\n      const t = Math.floor(e / 3);\n      points.push(circumcenters[t * 2], circumcenters[t * 2 + 1]);\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) break; // bad triangulation\n      e = halfedges[e];\n    } while (e !== e0 && e !== -1);\n    return points;\n  }\n  _clip(i) {\n    // degenerate case (1 valid point: return the box)\n    if (i === 0 && this.delaunay.hull.length === 1) {\n      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];\n    }\n    const points = this._cell(i);\n    if (points === null) return null;\n    const {vectors: V} = this;\n    const v = i * 4;\n    return V[v] || V[v + 1]\n        ? this._clipInfinite(i, points, V[v], V[v + 1], V[v + 2], V[v + 3])\n        : this._clipFinite(i, points);\n  }\n  _clipFinite(i, points) {\n    const n = points.length;\n    let P = null;\n    let x0, y0, x1 = points[n - 2], y1 = points[n - 1];\n    let c0, c1 = this._regioncode(x1, y1);\n    let e0, e1;\n    for (let j = 0; j < n; j += 2) {\n      x0 = x1, y0 = y1, x1 = points[j], y1 = points[j + 1];\n      c0 = c1, c1 = this._regioncode(x1, y1);\n      if (c0 === 0 && c1 === 0) {\n        e0 = e1, e1 = 0;\n        if (P) P.push(x1, y1);\n        else P = [x1, y1];\n      } else {\n        let S, sx0, sy0, sx1, sy1;\n        if (c0 === 0) {\n          if ((S = this._clipSegment(x0, y0, x1, y1, c0, c1)) === null) continue;\n          [sx0, sy0, sx1, sy1] = S;\n        } else {\n          if ((S = this._clipSegment(x1, y1, x0, y0, c1, c0)) === null) continue;\n          [sx1, sy1, sx0, sy0] = S;\n          e0 = e1, e1 = this._edgecode(sx0, sy0);\n          if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n          if (P) P.push(sx0, sy0);\n          else P = [sx0, sy0];\n        }\n        e0 = e1, e1 = this._edgecode(sx1, sy1);\n        if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n        if (P) P.push(sx1, sy1);\n        else P = [sx1, sy1];\n      }\n    }\n    if (P) {\n      e0 = e1, e1 = this._edgecode(P[0], P[1]);\n      if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n    } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];\n    }\n    return P;\n  }\n  _clipSegment(x0, y0, x1, y1, c0, c1) {\n    while (true) {\n      if (c0 === 0 && c1 === 0) return [x0, y0, x1, y1];\n      if (c0 & c1) return null;\n      let x, y, c = c0 || c1;\n      if (c & 0b1000) x = x0 + (x1 - x0) * (this.ymax - y0) / (y1 - y0), y = this.ymax;\n      else if (c & 0b0100) x = x0 + (x1 - x0) * (this.ymin - y0) / (y1 - y0), y = this.ymin;\n      else if (c & 0b0010) y = y0 + (y1 - y0) * (this.xmax - x0) / (x1 - x0), x = this.xmax;\n      else y = y0 + (y1 - y0) * (this.xmin - x0) / (x1 - x0), x = this.xmin;\n      if (c0) x0 = x, y0 = y, c0 = this._regioncode(x0, y0);\n      else x1 = x, y1 = y, c1 = this._regioncode(x1, y1);\n    }\n  }\n  _clipInfinite(i, points, vx0, vy0, vxn, vyn) {\n    let P = Array.from(points), p;\n    if (p = this._project(P[0], P[1], vx0, vy0)) P.unshift(p[0], p[1]);\n    if (p = this._project(P[P.length - 2], P[P.length - 1], vxn, vyn)) P.push(p[0], p[1]);\n    if (P = this._clipFinite(i, P)) {\n      for (let j = 0, n = P.length, c0, c1 = this._edgecode(P[n - 2], P[n - 1]); j < n; j += 2) {\n        c0 = c1, c1 = this._edgecode(P[j], P[j + 1]);\n        if (c0 && c1) j = this._edge(i, c0, c1, P, j), n = P.length;\n      }\n    } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n      P = [this.xmin, this.ymin, this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax];\n    }\n    return P;\n  }\n  _edge(i, e0, e1, P, j) {\n    while (e0 !== e1) {\n      let x, y;\n      switch (e0) {\n        case 0b0101: e0 = 0b0100; continue; // top-left\n        case 0b0100: e0 = 0b0110, x = this.xmax, y = this.ymin; break; // top\n        case 0b0110: e0 = 0b0010; continue; // top-right\n        case 0b0010: e0 = 0b1010, x = this.xmax, y = this.ymax; break; // right\n        case 0b1010: e0 = 0b1000; continue; // bottom-right\n        case 0b1000: e0 = 0b1001, x = this.xmin, y = this.ymax; break; // bottom\n        case 0b1001: e0 = 0b0001; continue; // bottom-left\n        case 0b0001: e0 = 0b0101, x = this.xmin, y = this.ymin; break; // left\n      }\n      if ((P[j] !== x || P[j + 1] !== y) && this.contains(i, x, y)) {\n        P.splice(j, 0, x, y), j += 2;\n      }\n    }\n    if (P.length > 4) {\n      for (let i = 0; i < P.length; i+= 2) {\n        const j = (i + 2) % P.length, k = (i + 4) % P.length;\n        if (P[i] === P[j] && P[j] === P[k]\n        || P[i + 1] === P[j + 1] && P[j + 1] === P[k + 1])\n          P.splice(j, 2), i -= 2;\n      }\n    }\n    return j;\n  }\n  _project(x0, y0, vx, vy) {\n    let t = Infinity, c, x, y;\n    if (vy < 0) { // top\n      if (y0 <= this.ymin) return null;\n      if ((c = (this.ymin - y0) / vy) < t) y = this.ymin, x = x0 + (t = c) * vx;\n    } else if (vy > 0) { // bottom\n      if (y0 >= this.ymax) return null;\n      if ((c = (this.ymax - y0) / vy) < t) y = this.ymax, x = x0 + (t = c) * vx;\n    }\n    if (vx > 0) { // right\n      if (x0 >= this.xmax) return null;\n      if ((c = (this.xmax - x0) / vx) < t) x = this.xmax, y = y0 + (t = c) * vy;\n    } else if (vx < 0) { // left\n      if (x0 <= this.xmin) return null;\n      if ((c = (this.xmin - x0) / vx) < t) x = this.xmin, y = y0 + (t = c) * vy;\n    }\n    return [x, y];\n  }\n  _edgecode(x, y) {\n    return (x === this.xmin ? 0b0001\n        : x === this.xmax ? 0b0010 : 0b0000)\n        | (y === this.ymin ? 0b0100\n        : y === this.ymax ? 0b1000 : 0b0000);\n  }\n  _regioncode(x, y) {\n    return (x < this.xmin ? 0b0001\n        : x > this.xmax ? 0b0010 : 0b0000)\n        | (y < this.ymin ? 0b0100\n        : y > this.ymax ? 0b1000 : 0b0000);\n  }\n}\n", "import Delaunator from \"delaunator\";\nimport Path from \"./path.js\";\nimport Polygon from \"./polygon.js\";\nimport Voronoi from \"./voronoi.js\";\n\nconst tau = 2 * Math.PI, pow = Math.pow;\n\nfunction pointX(p) {\n  return p[0];\n}\n\nfunction pointY(p) {\n  return p[1];\n}\n\n// A triangulation is collinear if all its triangles have a non-null area\nfunction collinear(d) {\n  const {triangles, coords} = d;\n  for (let i = 0; i < triangles.length; i += 3) {\n    const a = 2 * triangles[i],\n          b = 2 * triangles[i + 1],\n          c = 2 * triangles[i + 2],\n          cross = (coords[c] - coords[a]) * (coords[b + 1] - coords[a + 1])\n                - (coords[b] - coords[a]) * (coords[c + 1] - coords[a + 1]);\n    if (cross > 1e-10) return false;\n  }\n  return true;\n}\n\nfunction jitter(x, y, r) {\n  return [x + Math.sin(x + y) * r, y + Math.cos(x - y) * r];\n}\n\nexport default class Delaunay {\n  static from(points, fx = pointX, fy = pointY, that) {\n    return new Delaunay(\"length\" in points\n        ? flatArray(points, fx, fy, that)\n        : Float64Array.from(flatIterable(points, fx, fy, that)));\n  }\n  constructor(points) {\n    this._delaunator = new Delaunator(points);\n    this.inedges = new Int32Array(points.length / 2);\n    this._hullIndex = new Int32Array(points.length / 2);\n    this.points = this._delaunator.coords;\n    this._init();\n  }\n  update() {\n    this._delaunator.update();\n    this._init();\n    return this;\n  }\n  _init() {\n    const d = this._delaunator, points = this.points;\n\n    // check for collinear\n    if (d.hull && d.hull.length > 2 && collinear(d)) {\n      this.collinear = Int32Array.from({length: points.length/2}, (_,i) => i)\n        .sort((i, j) => points[2 * i] - points[2 * j] || points[2 * i + 1] - points[2 * j + 1]); // for exact neighbors\n      const e = this.collinear[0], f = this.collinear[this.collinear.length - 1],\n        bounds = [ points[2 * e], points[2 * e + 1], points[2 * f], points[2 * f + 1] ],\n        r = 1e-8 * Math.hypot(bounds[3] - bounds[1], bounds[2] - bounds[0]);\n      for (let i = 0, n = points.length / 2; i < n; ++i) {\n        const p = jitter(points[2 * i], points[2 * i + 1], r);\n        points[2 * i] = p[0];\n        points[2 * i + 1] = p[1];\n      }\n      this._delaunator = new Delaunator(points);\n    } else {\n      delete this.collinear;\n    }\n\n    const halfedges = this.halfedges = this._delaunator.halfedges;\n    const hull = this.hull = this._delaunator.hull;\n    const triangles = this.triangles = this._delaunator.triangles;\n    const inedges = this.inedges.fill(-1);\n    const hullIndex = this._hullIndex.fill(-1);\n\n    // Compute an index from each point to an (arbitrary) incoming halfedge\n    // Used to give the first neighbor of each point; for this reason,\n    // on the hull we give priority to exterior halfedges\n    for (let e = 0, n = halfedges.length; e < n; ++e) {\n      const p = triangles[e % 3 === 2 ? e - 2 : e + 1];\n      if (halfedges[e] === -1 || inedges[p] === -1) inedges[p] = e;\n    }\n    for (let i = 0, n = hull.length; i < n; ++i) {\n      hullIndex[hull[i]] = i;\n    }\n\n    // degenerate case: 1 or 2 (distinct) points\n    if (hull.length <= 2 && hull.length > 0) {\n      this.triangles = new Int32Array(3).fill(-1);\n      this.halfedges = new Int32Array(3).fill(-1);\n      this.triangles[0] = hull[0];\n      this.triangles[1] = hull[1];\n      this.triangles[2] = hull[1];\n      inedges[hull[0]] = 1;\n      if (hull.length === 2) inedges[hull[1]] = 0;\n    }\n  }\n  voronoi(bounds) {\n    return new Voronoi(this, bounds);\n  }\n  *neighbors(i) {\n    const {inedges, hull, _hullIndex, halfedges, triangles, collinear} = this;\n\n    // degenerate case with several collinear points\n    if (collinear) {\n      const l = collinear.indexOf(i);\n      if (l > 0) yield collinear[l - 1];\n      if (l < collinear.length - 1) yield collinear[l + 1];\n      return;\n    }\n\n    const e0 = inedges[i];\n    if (e0 === -1) return; // coincident point\n    let e = e0, p0 = -1;\n    do {\n      yield p0 = triangles[e];\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) return; // bad triangulation\n      e = halfedges[e];\n      if (e === -1) {\n        const p = hull[(_hullIndex[i] + 1) % hull.length];\n        if (p !== p0) yield p;\n        return;\n      }\n    } while (e !== e0);\n  }\n  find(x, y, i = 0) {\n    if ((x = +x, x !== x) || (y = +y, y !== y)) return -1;\n    const i0 = i;\n    let c;\n    while ((c = this._step(i, x, y)) >= 0 && c !== i && c !== i0) i = c;\n    return c;\n  }\n  _step(i, x, y) {\n    const {inedges, hull, _hullIndex, halfedges, triangles, points} = this;\n    if (inedges[i] === -1 || !points.length) return (i + 1) % (points.length >> 1);\n    let c = i;\n    let dc = pow(x - points[i * 2], 2) + pow(y - points[i * 2 + 1], 2);\n    const e0 = inedges[i];\n    let e = e0;\n    do {\n      let t = triangles[e];\n      const dt = pow(x - points[t * 2], 2) + pow(y - points[t * 2 + 1], 2);\n      if (dt < dc) dc = dt, c = t;\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) break; // bad triangulation\n      e = halfedges[e];\n      if (e === -1) {\n        e = hull[(_hullIndex[i] + 1) % hull.length];\n        if (e !== t) {\n          if (pow(x - points[e * 2], 2) + pow(y - points[e * 2 + 1], 2) < dc) return e;\n        }\n        break;\n      }\n    } while (e !== e0);\n    return c;\n  }\n  render(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {points, halfedges, triangles} = this;\n    for (let i = 0, n = halfedges.length; i < n; ++i) {\n      const j = halfedges[i];\n      if (j < i) continue;\n      const ti = triangles[i] * 2;\n      const tj = triangles[j] * 2;\n      context.moveTo(points[ti], points[ti + 1]);\n      context.lineTo(points[tj], points[tj + 1]);\n    }\n    this.renderHull(context);\n    return buffer && buffer.value();\n  }\n  renderPoints(context, r = 2) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {points} = this;\n    for (let i = 0, n = points.length; i < n; i += 2) {\n      const x = points[i], y = points[i + 1];\n      context.moveTo(x + r, y);\n      context.arc(x, y, r, 0, tau);\n    }\n    return buffer && buffer.value();\n  }\n  renderHull(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {hull, points} = this;\n    const h = hull[0] * 2, n = hull.length;\n    context.moveTo(points[h], points[h + 1]);\n    for (let i = 1; i < n; ++i) {\n      const h = 2 * hull[i];\n      context.lineTo(points[h], points[h + 1]);\n    }\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  hullPolygon() {\n    const polygon = new Polygon;\n    this.renderHull(polygon);\n    return polygon.value();\n  }\n  renderTriangle(i, context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {points, triangles} = this;\n    const t0 = triangles[i *= 3] * 2;\n    const t1 = triangles[i + 1] * 2;\n    const t2 = triangles[i + 2] * 2;\n    context.moveTo(points[t0], points[t0 + 1]);\n    context.lineTo(points[t1], points[t1 + 1]);\n    context.lineTo(points[t2], points[t2 + 1]);\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  *trianglePolygons() {\n    const {triangles} = this;\n    for (let i = 0, n = triangles.length / 3; i < n; ++i) {\n      yield this.trianglePolygon(i);\n    }\n  }\n  trianglePolygon(i) {\n    const polygon = new Polygon;\n    this.renderTriangle(i, polygon);\n    return polygon.value();\n  }\n}\n\nfunction flatArray(points, fx, fy, that) {\n  const n = points.length;\n  const array = new Float64Array(n * 2);\n  for (let i = 0; i < n; ++i) {\n    const p = points[i];\n    array[i * 2] = fx.call(that, p, i, points);\n    array[i * 2 + 1] = fy.call(that, p, i, points);\n  }\n  return array;\n}\n\nfunction* flatIterable(points, fx, fy, that) {\n  let i = 0;\n  for (const p of points) {\n    yield fx.call(that, p, i, points);\n    yield fy.call(that, p, i, points);\n    ++i;\n  }\n}\n", "import { VoronoiDomain, VoronoiLayer } from './types'\n\nexport const defaultVoronoiProps = {\n    xDomain: [0, 1] as VoronoiDomain,\n    yDomain: [0, 1] as VoronoiDomain,\n    layers: ['links', 'cells', 'points', 'bounds'] as VoronoiLayer[],\n    enableLinks: false,\n    linkLineWidth: 1,\n    linkLineColor: '#bbbbbb',\n    enableCells: true,\n    cellLineWidth: 2,\n    cellLineColor: '#000000',\n    enablePoints: true,\n    pointSize: 4,\n    pointColor: '#666666',\n    role: 'img',\n}\n", "import { Delaunay } from 'd3-delaunay'\n\ntype NumberPropertyNames<T> = {\n    [K in keyof T]: T[K] extends number ? K : never\n}[keyof T]\n\nexport type XYAccessor<Datum> = NumberPropertyNames<Datum> | ((datum: Datum) => number)\n\nconst getAccessor = <Datum>(directive: XYAccessor<Datum>) =>\n    typeof directive === 'function' ? directive : (datum: Datum) => datum[directive]\n\n/**\n * The delaunay generator requires an array\n * where each point is defined as an array\n * of 2 elements: [x: number, y: number].\n *\n * Points represent the raw input data\n * and x/y represent accessors to x & y.\n */\nexport const computeMeshPoints = <Datum>({\n    points,\n    x = 'x' as NumberPropertyNames<Datum>,\n    y = 'y' as NumberPropertyNames<Datum>,\n}: {\n    points: Datum[]\n    x?: XYAccessor<Datum>\n    y?: XYAccessor<Datum>\n}): [number, number][] => {\n    const getX = getAccessor<Datum>(x)\n    const getY = getAccessor<Datum>(y)\n\n    return points.map(point => [getX(point) as number, getY(point) as number])\n}\n\nexport const computeMesh = ({\n    points,\n    width,\n    height,\n    debug,\n}: {\n    points: [number, number][]\n    width: number\n    height: number\n    debug?: boolean\n}) => {\n    const delaunay = Delaunay.from(points)\n    const voronoi = debug ? delaunay.voronoi([0, 0, width, height]) : undefined\n\n    return { delaunay, voronoi }\n}\n", "import { useMemo } from 'react'\nimport { scaleLinear } from 'd3-scale'\nimport { Delaunay } from 'd3-delaunay'\nimport { computeMeshPoints, computeMesh, XYAccessor } from './computeMesh'\nimport { VoronoiCommonProps, VoronoiDatum, VoronoiCustomLayerProps } from './types'\n\nexport const useVoronoiMesh = <Datum>({\n    points,\n    x,\n    y,\n    width,\n    height,\n    debug,\n}: {\n    points: Datum[]\n    x?: XYAccessor<Datum>\n    y?: XYAccessor<Datum>\n    width: number\n    height: number\n    debug?: boolean\n}) => {\n    const points2d = useMemo(() => computeMeshPoints<Datum>({ points, x, y }), [points, x, y])\n\n    return useMemo(\n        () => computeMesh({ points: points2d, width, height, debug }),\n        [points2d, width, height, debug]\n    )\n}\n\nexport const useVoronoi = ({\n    data,\n    width,\n    height,\n    xDomain,\n    yDomain,\n}: {\n    data: VoronoiDatum[]\n    width: number\n    height: number\n    xDomain: VoronoiCommonProps['xDomain']\n    yDomain: VoronoiCommonProps['yDomain']\n}) => {\n    const xScale = useMemo(() => scaleLinear().domain(xDomain).range([0, width]), [xDomain, width])\n    const yScale = useMemo(\n        () => scaleLinear().domain(yDomain).range([0, height]),\n        [yDomain, height]\n    )\n\n    const points = useMemo(\n        () =>\n            data.map(d => ({\n                x: xScale(d.x),\n                y: yScale(d.y),\n                data: d,\n            })),\n        [data, xScale, yScale]\n    )\n\n    return useMemo(() => {\n        const delaunay = Delaunay.from(points.map(p => [p.x, p.y]))\n        const voronoi = delaunay.voronoi([0, 0, width, height])\n\n        return {\n            points,\n            delaunay,\n            voronoi,\n        }\n    }, [points, width, height])\n}\n\n/**\n * Memoize the context to pass to custom layers.\n */\nexport const useVoronoiLayerContext = ({\n    points,\n    delaunay,\n    voronoi,\n}: VoronoiCustomLayerProps): VoronoiCustomLayerProps =>\n    useMemo(\n        () => ({\n            points,\n            delaunay,\n            voronoi,\n        }),\n        [points, delaunay, voronoi]\n    )\n", "import { createElement, Fragment, ReactNode } from 'react'\nimport { Container, SvgWrapper, useDimensions } from '@nivo/core'\nimport { VoronoiSvgProps, VoronoiLayerId } from './types'\nimport { defaultVoronoiProps } from './props'\nimport { useVoronoi, useVoronoiLayerContext } from './hooks'\n\ntype InnerVoronoiProps = Partial<Omit<VoronoiSvgProps, 'data' | 'width' | 'height'>> &\n    Pick<VoronoiSvgProps, 'data' | 'width' | 'height'>\n\nconst InnerVoronoi = ({\n    data,\n    width,\n    height,\n    margin: partialMargin,\n    layers = defaultVoronoiProps.layers,\n    xDomain = defaultVoronoiProps.xDomain,\n    yDomain = defaultVoronoiProps.yDomain,\n    enableLinks = defaultVoronoiProps.enableLinks,\n    linkLineWidth = defaultVoronoiProps.linkLineWidth,\n    linkLineColor = defaultVoronoiProps.linkLineColor,\n    enableCells = defaultVoronoiProps.enableCells,\n    cellLineWidth = defaultVoronoiProps.cellLineWidth,\n    cellLineColor = defaultVoronoiProps.cellLineColor,\n    enablePoints = defaultVoronoiProps.enableCells,\n    pointSize = defaultVoronoiProps.pointSize,\n    pointColor = defaultVoronoiProps.pointColor,\n    role = defaultVoronoiProps.role,\n}: InnerVoronoiProps) => {\n    const { outerWidth, outerHeight, margin, innerWidth, innerHeight } = useDimensions(\n        width,\n        height,\n        partialMargin\n    )\n\n    const { points, delaunay, voronoi } = useVoronoi({\n        data,\n        width: innerWidth,\n        height: innerHeight,\n        xDomain,\n        yDomain,\n    })\n\n    const layerById: Record<VoronoiLayerId, ReactNode> = {\n        links: null,\n        cells: null,\n        points: null,\n        bounds: null,\n    }\n\n    if (enableLinks && layers.includes('links')) {\n        layerById.links = (\n            <path\n                key=\"links\"\n                stroke={linkLineColor}\n                strokeWidth={linkLineWidth}\n                fill=\"none\"\n                d={delaunay.render()}\n            />\n        )\n    }\n\n    if (enableCells && layers.includes('cells')) {\n        layerById.cells = (\n            <path\n                key=\"cells\"\n                d={voronoi.render()}\n                fill=\"none\"\n                stroke={cellLineColor}\n                strokeWidth={cellLineWidth}\n            />\n        )\n    }\n\n    if (enablePoints && layers.includes('points')) {\n        layerById.points = (\n            <path\n                key=\"points\"\n                stroke=\"none\"\n                fill={pointColor}\n                d={delaunay.renderPoints(undefined, pointSize / 2)}\n            />\n        )\n    }\n\n    if (layers.includes('bounds')) {\n        layerById.bounds = (\n            <path\n                key=\"bounds\"\n                fill=\"none\"\n                stroke={cellLineColor}\n                strokeWidth={cellLineWidth}\n                d={voronoi.renderBounds()}\n            />\n        )\n    }\n\n    const layerContext = useVoronoiLayerContext({\n        points,\n        delaunay,\n        voronoi,\n    })\n\n    return (\n        <SvgWrapper width={outerWidth} height={outerHeight} margin={margin} role={role}>\n            {layers.map((layer, i) => {\n                if (layerById[layer as VoronoiLayerId] !== undefined) {\n                    return layerById[layer as VoronoiLayerId]\n                }\n\n                if (typeof layer === 'function') {\n                    return <Fragment key={i}>{createElement(layer, layerContext)}</Fragment>\n                }\n\n                return null\n            })}\n        </SvgWrapper>\n    )\n}\n\nexport const Voronoi = ({\n    theme,\n    ...otherProps\n}: Partial<Omit<VoronoiSvgProps, 'data' | 'width' | 'height'>> &\n    Pick<VoronoiSvgProps, 'data' | 'width' | 'height'>) => (\n    <Container isInteractive={false} animate={false} theme={theme}>\n        <InnerVoronoi {...otherProps} />\n    </Container>\n)\n", "import { ResponsiveWrapper } from '@nivo/core'\nimport { VoronoiSvgProps } from './types'\nimport { Voronoi } from './Voronoi'\n\ntype ResponsiveVoronoiProps = Partial<Omit<VoronoiSvgProps, 'data' | 'width' | 'height'>> &\n    Pick<VoronoiSvgProps, 'data'>\n\nexport const ResponsiveVoronoi = (props: ResponsiveVoronoiProps) => (\n    <ResponsiveWrapper>\n        {({ width, height }: { width: number; height: number }) => (\n            <Voronoi width={width} height={height} {...props} />\n        )}\n    </ResponsiveWrapper>\n)\n", "import { useRef, useState, useCallback, useMemo, MouseEvent } from 'react'\nimport { getRelativeCursor } from '@nivo/core'\nimport { useVoronoiMesh } from './hooks'\nimport { XYAccessor } from './computeMesh'\n\ntype Mouse<PERSON>andler<Datum> = (datum: Datum, event: MouseEvent) => void\n\ninterface MeshProps<Datum> {\n    nodes: Datum[]\n    width: number\n    height: number\n    x?: XYAccessor<Datum>\n    y?: XYAccessor<Datum>\n    onMouseEnter?: <PERSON><PERSON><PERSON><PERSON><Datum>\n    onMouseMove?: <PERSON><PERSON><PERSON>ler<Datum>\n    onMouseLeave?: MouseHandler<Datum>\n    onClick?: <PERSON><PERSON><PERSON><PERSON><Datum>\n    debug?: boolean\n}\n\nexport const Mesh = <Datum,>({\n    nodes,\n    width,\n    height,\n    x,\n    y,\n    onMouseEnter,\n    onMouseMove,\n    onMouseLeave,\n    onClick,\n    debug,\n}: MeshProps<Datum>) => {\n    const elementRef = useRef<SVGGElement>(null)\n    const [currentIndex, setCurrentIndex] = useState<number | null>(null)\n\n    const { delaunay, voronoi } = useVoronoiMesh({\n        points: nodes,\n        x,\n        y,\n        width,\n        height,\n        debug,\n    })\n\n    const voronoiPath = useMemo(() => {\n        if (debug && voronoi) {\n            return voronoi.render()\n        }\n\n        return undefined\n    }, [debug, voronoi])\n\n    const getIndexAndNodeFromEvent = useCallback(\n        (event: MouseEvent<SVGRectElement>) => {\n            if (!elementRef.current) {\n                return [null, null]\n            }\n\n            const [x, y] = getRelativeCursor(elementRef.current, event)\n            const index = delaunay.find(x, y)\n\n            return [index, index !== undefined ? nodes[index] : null] as [number, Datum | null]\n        },\n        [elementRef, delaunay]\n    )\n\n    const handleMouseEnter = useCallback(\n        (event: MouseEvent<SVGRectElement>) => {\n            const [index, node] = getIndexAndNodeFromEvent(event)\n            setCurrentIndex(index)\n            if (node) {\n                onMouseEnter?.(node, event)\n            }\n        },\n        [getIndexAndNodeFromEvent, setCurrentIndex, onMouseEnter]\n    )\n\n    const handleMouseMove = useCallback(\n        (event: MouseEvent<SVGRectElement>) => {\n            const [index, node] = getIndexAndNodeFromEvent(event)\n            setCurrentIndex(index)\n            if (node) {\n                onMouseMove?.(node, event)\n            }\n        },\n        [getIndexAndNodeFromEvent, setCurrentIndex, onMouseMove]\n    )\n\n    const handleMouseLeave = useCallback(\n        (event: MouseEvent<SVGRectElement>) => {\n            setCurrentIndex(null)\n            if (onMouseLeave) {\n                let previousNode: Datum | undefined = undefined\n                if (currentIndex !== null) {\n                    previousNode = nodes[currentIndex]\n                }\n                previousNode && onMouseLeave(previousNode, event)\n            }\n        },\n        [setCurrentIndex, currentIndex, onMouseLeave, nodes]\n    )\n\n    const handleClick = useCallback(\n        (event: MouseEvent<SVGRectElement>) => {\n            const [index, node] = getIndexAndNodeFromEvent(event)\n            setCurrentIndex(index)\n            if (node) {\n                onClick?.(node, event)\n            }\n        },\n        [getIndexAndNodeFromEvent, setCurrentIndex, onClick]\n    )\n\n    return (\n        <g ref={elementRef}>\n            {debug && voronoi && (\n                <>\n                    <path d={voronoiPath} stroke=\"red\" strokeWidth={1} opacity={0.75} />\n                    {/* highlight current cell */}\n                    {currentIndex !== null && (\n                        <path fill=\"pink\" opacity={0.35} d={voronoi.renderCell(currentIndex)} />\n                    )}\n                </>\n            )}\n            {/* transparent rect to intercept mouse events */}\n            <rect\n                width={width}\n                height={height}\n                fill=\"red\"\n                opacity={0}\n                style={{ cursor: 'auto' }}\n                onMouseEnter={handleMouseEnter}\n                onMouseMove={handleMouseMove}\n                onMouseLeave={handleMouseLeave}\n                onClick={handleClick}\n            />\n        </g>\n    )\n}\n", "import { Delaunay, Voronoi } from 'd3-delaunay'\n\nexport const renderVoronoiToCanvas = (\n    ctx: CanvasRenderingContext2D,\n    voronoi: Voronoi<Delaunay.Point>\n) => {\n    ctx.save()\n\n    ctx.globalAlpha = 0.75\n    ctx.beginPath()\n    voronoi.render(ctx)\n    ctx.strokeStyle = 'red'\n    ctx.lineWidth = 1\n    ctx.stroke()\n\n    ctx.restore()\n}\n\nexport const renderVoronoiCellToCanvas = (\n    ctx: CanvasRenderingContext2D,\n    voronoi: Voronoi<Delaunay.Point>,\n    index: number\n) => {\n    ctx.save()\n\n    ctx.globalAlpha = 0.35\n    ctx.beginPath()\n    voronoi.renderCell(index, ctx)\n    ctx.fillStyle = 'red'\n    ctx.fill()\n\n    ctx.restore()\n}\n", "/*\n * This file is part of the nivo project.\n *\n * Copyright 2016-present, <PERSON><PERSON><PERSON>.\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\nimport { memo } from 'react'\nimport PropTypes from 'prop-types'\nimport { BasicTooltip } from '@nivo/tooltip'\n\nconst LinePointTooltip = ({ point }) => {\n    return (\n        <BasicTooltip\n            id={\n                <span>\n                    x: <strong>{point.data.xFormatted}</strong>, y:{' '}\n                    <strong>{point.data.yFormatted}</strong>\n                </span>\n            }\n            enableChip={true}\n            color={point.serieColor}\n        />\n    )\n}\n\nLinePointTooltip.propTypes = {\n    point: PropTypes.object.isRequired,\n}\n\nexport default memo(LinePointTooltip)\n", "/*\n * This file is part of the nivo project.\n *\n * Copyright 2016-present, <PERSON><PERSON><PERSON>.\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\nimport { memo } from 'react'\nimport PropTypes from 'prop-types'\nimport { useTheme } from '@nivo/core'\nimport { Chip, TableTooltip } from '@nivo/tooltip'\n\nconst SliceTooltip = ({ slice, axis }) => {\n    const theme = useTheme()\n    const otherAxis = axis === 'x' ? 'y' : 'x'\n\n    return (\n        <TableTooltip\n            rows={slice.points.map(point => [\n                <Chip key=\"chip\" color={point.serieColor} style={theme.tooltip.chip} />,\n                point.serieId,\n                <span key=\"value\" style={theme.tooltip.tableCellValue}>\n                    {point.data[`${otherAxis}Formatted`]}\n                </span>,\n            ])}\n        />\n    )\n}\n\nSliceTooltip.propTypes = {\n    slice: PropTypes.object.isRequired,\n    axis: PropTypes.oneOf(['x', 'y']).isRequired,\n}\n\nexport default memo(SliceTooltip)\n", "/*\n * This file is part of the nivo project.\n *\n * Copyright 2016-present, <PERSON><PERSON><PERSON>.\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\nimport PropTypes from 'prop-types'\nimport { lineCurvePropType, blendModePropType, motionPropTypes, defsPropTypes } from '@nivo/core'\nimport { ordinalColorsPropType } from '@nivo/colors'\nimport { axisPropType } from '@nivo/axes'\nimport { LegendPropShape } from '@nivo/legends'\nimport PointTooltip from './PointTooltip'\nimport SliceTooltip from './SliceTooltip'\n\nconst commonPropTypes = {\n    data: PropTypes.arrayOf(\n        PropTypes.shape({\n            id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,\n            data: PropTypes.arrayOf(\n                PropTypes.shape({\n                    x: PropTypes.oneOfType([\n                        PropTypes.number,\n                        PropTypes.string,\n                        PropTypes.instanceOf(Date),\n                    ]),\n                    y: PropTypes.oneOfType([\n                        PropTypes.number,\n                        PropTypes.string,\n                        PropTypes.instanceOf(Date),\n                    ]),\n                })\n            ).isRequired,\n        })\n    ).isRequired,\n\n    xScale: PropTypes.object.isRequired,\n    xFormat: PropTypes.oneOfType([PropTypes.func, PropTypes.string]),\n    yScale: PropTypes.object.isRequired,\n    yFormat: PropTypes.oneOfType([PropTypes.func, PropTypes.string]),\n\n    layers: PropTypes.arrayOf(\n        PropTypes.oneOfType([\n            PropTypes.oneOf([\n                'grid',\n                'markers',\n                'axes',\n                'areas',\n                'crosshair',\n                'lines',\n                'slices',\n                'points',\n                'mesh',\n                'legends',\n            ]),\n            PropTypes.func,\n        ])\n    ).isRequired,\n\n    curve: lineCurvePropType.isRequired,\n\n    axisTop: axisPropType,\n    axisRight: axisPropType,\n    axisBottom: axisPropType,\n    axisLeft: axisPropType,\n\n    enableGridX: PropTypes.bool.isRequired,\n    enableGridY: PropTypes.bool.isRequired,\n    gridXValues: PropTypes.oneOfType([\n        PropTypes.number,\n        PropTypes.arrayOf(\n            PropTypes.oneOfType([PropTypes.number, PropTypes.string, PropTypes.instanceOf(Date)])\n        ),\n    ]),\n    gridYValues: PropTypes.oneOfType([\n        PropTypes.number,\n        PropTypes.arrayOf(\n            PropTypes.oneOfType([PropTypes.number, PropTypes.string, PropTypes.instanceOf(Date)])\n        ),\n    ]),\n\n    enablePoints: PropTypes.bool.isRequired,\n    pointSymbol: PropTypes.func,\n    pointSize: PropTypes.number.isRequired,\n    pointColor: PropTypes.any.isRequired,\n    pointBorderWidth: PropTypes.number.isRequired,\n    pointBorderColor: PropTypes.any.isRequired,\n    enablePointLabel: PropTypes.bool.isRequired,\n    pointLabel: PropTypes.oneOfType([PropTypes.string, PropTypes.func]).isRequired,\n\n    markers: PropTypes.arrayOf(\n        PropTypes.shape({\n            axis: PropTypes.oneOf(['x', 'y']).isRequired,\n            value: PropTypes.oneOfType([\n                PropTypes.number,\n                PropTypes.string,\n                PropTypes.instanceOf(Date),\n            ]).isRequired,\n            style: PropTypes.object,\n        })\n    ),\n\n    colors: ordinalColorsPropType.isRequired,\n\n    enableArea: PropTypes.bool.isRequired,\n    areaOpacity: PropTypes.number.isRequired,\n    areaBlendMode: blendModePropType.isRequired,\n    areaBaselineValue: PropTypes.oneOfType([\n        PropTypes.number,\n        PropTypes.string,\n        PropTypes.instanceOf(Date),\n    ]).isRequired,\n    lineWidth: PropTypes.number.isRequired,\n\n    legends: PropTypes.arrayOf(PropTypes.shape(LegendPropShape)).isRequired,\n\n    isInteractive: PropTypes.bool.isRequired,\n    debugMesh: PropTypes.bool.isRequired,\n\n    tooltip: PropTypes.oneOfType([PropTypes.func, PropTypes.object]).isRequired,\n\n    enableSlices: PropTypes.oneOf(['x', 'y', false]).isRequired,\n    debugSlices: PropTypes.bool.isRequired,\n    sliceTooltip: PropTypes.oneOfType([PropTypes.func, PropTypes.object]).isRequired,\n\n    enableCrosshair: PropTypes.bool.isRequired,\n    crosshairType: PropTypes.string.isRequired,\n}\n\nexport const LinePropTypes = {\n    ...commonPropTypes,\n    enablePointLabel: PropTypes.bool.isRequired,\n    role: PropTypes.string.isRequired,\n    useMesh: PropTypes.bool.isRequired,\n    ...motionPropTypes,\n    ...defsPropTypes,\n}\n\nexport const LineCanvasPropTypes = {\n    pixelRatio: PropTypes.number.isRequired,\n    ...commonPropTypes,\n}\n\nconst commonDefaultProps = {\n    curve: 'linear',\n\n    xScale: {\n        type: 'point',\n    },\n    yScale: {\n        type: 'linear',\n        min: 0,\n        max: 'auto',\n    },\n\n    layers: [\n        'grid',\n        'markers',\n        'axes',\n        'areas',\n        'crosshair',\n        'lines',\n        'points',\n        'slices',\n        'mesh',\n        'legends',\n    ],\n    axisBottom: {},\n    axisLeft: {},\n    enableGridX: true,\n    enableGridY: true,\n\n    enablePoints: true,\n    pointSize: 6,\n    pointColor: { from: 'color' },\n    pointBorderWidth: 0,\n    pointBorderColor: { theme: 'background' },\n    enablePointLabel: false,\n    pointLabel: 'yFormatted',\n\n    colors: { scheme: 'nivo' },\n    enableArea: false,\n    areaBaselineValue: 0,\n    areaOpacity: 0.2,\n    areaBlendMode: 'normal',\n    lineWidth: 2,\n\n    legends: [],\n\n    isInteractive: true,\n    tooltip: PointTooltip,\n    enableSlices: false,\n    debugSlices: false,\n    sliceTooltip: SliceTooltip,\n    debugMesh: false,\n    enableCrosshair: true,\n    crosshairType: 'bottom-left',\n}\n\nexport const LineDefaultProps = {\n    ...commonDefaultProps,\n    enablePointLabel: false,\n    useMesh: false,\n    animate: true,\n    motionConfig: 'gentle',\n    defs: [],\n    fill: [],\n    role: 'img',\n}\n\nexport const LineCanvasDefaultProps = {\n    ...commonDefaultProps,\n    pixelRatio: typeof window !== 'undefined' ? window.devicePixelRatio || 1 : 1,\n}\n", "/*\n * This file is part of the nivo project.\n *\n * Copyright 2016-present, <PERSON><PERSON><PERSON>.\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\nimport { useCallback, useMemo, useState } from 'react'\nimport { area, line } from 'd3-shape'\nimport { curveFromProp, useTheme, useValueFormatter } from '@nivo/core'\nimport { useOrdinalColorScale, useInheritedColor } from '@nivo/colors'\nimport { computeXYScalesForSeries } from '@nivo/scales'\nimport { LineDefaultProps } from './props'\n\nexport const useLineGenerator = ({ curve }) => {\n    return useMemo(\n        () =>\n            line()\n                .defined(d => d.x !== null && d.y !== null)\n                .x(d => d.x)\n                .y(d => d.y)\n                .curve(curveFromProp(curve)),\n        [curve]\n    )\n}\n\nexport const useAreaGenerator = ({ curve, yScale, areaBaselineValue }) => {\n    return useMemo(() => {\n        return area()\n            .defined(d => d.x !== null && d.y !== null)\n            .x(d => d.x)\n            .y1(d => d.y)\n            .curve(curveFromProp(curve))\n            .y0(yScale(areaBaselineValue))\n    }, [curve, yScale, areaBaselineValue])\n}\n\nconst usePoints = ({ series, getPointColor, getPointBorderColor, formatX, formatY }) => {\n    return useMemo(() => {\n        return series.reduce((acc, serie) => {\n            return [\n                ...acc,\n                ...serie.data\n                    .filter(datum => datum.position.x !== null && datum.position.y !== null)\n                    .map((datum, i) => {\n                        const point = {\n                            id: `${serie.id}.${i}`,\n                            index: acc.length + i,\n                            serieId: serie.id,\n                            serieColor: serie.color,\n                            x: datum.position.x,\n                            y: datum.position.y,\n                        }\n                        point.color = getPointColor(serie)\n                        point.borderColor = getPointBorderColor(point)\n                        point.data = {\n                            ...datum.data,\n                            xFormatted: formatX(datum.data.x),\n                            yFormatted: formatY(datum.data.y),\n                        }\n\n                        return point\n                    }),\n            ]\n        }, [])\n    }, [series, getPointColor, getPointBorderColor, formatX, formatY])\n}\n\nexport const useSlices = ({ enableSlices, points, width, height }) => {\n    return useMemo(() => {\n        if (enableSlices === false) return []\n\n        if (enableSlices === 'x') {\n            const map = new Map()\n            points.forEach(point => {\n                if (point.data.x === null || point.data.y === null) return\n                if (!map.has(point.x)) map.set(point.x, [point])\n                else map.get(point.x).push(point)\n            })\n            return Array.from(map.entries())\n                .sort((a, b) => a[0] - b[0])\n                .map(([x, slicePoints], i, slices) => {\n                    const prevSlice = slices[i - 1]\n                    const nextSlice = slices[i + 1]\n\n                    let x0\n                    if (!prevSlice) x0 = x\n                    else x0 = x - (x - prevSlice[0]) / 2\n\n                    let sliceWidth\n                    if (!nextSlice) sliceWidth = width - x0\n                    else sliceWidth = x - x0 + (nextSlice[0] - x) / 2\n\n                    return {\n                        id: x,\n                        x0,\n                        x,\n                        y0: 0,\n                        y: 0,\n                        width: sliceWidth,\n                        height,\n                        points: slicePoints.reverse(),\n                    }\n                })\n        } else if (enableSlices === 'y') {\n            const map = new Map()\n            points.forEach(point => {\n                if (point.data.x === null || point.data.y === null) return\n                if (!map.has(point.y)) map.set(point.y, [point])\n                else map.get(point.y).push(point)\n            })\n            return Array.from(map.entries())\n                .sort((a, b) => a[0] - b[0])\n                .map(([y, slicePoints], i, slices) => {\n                    const prevSlice = slices[i - 1]\n                    const nextSlice = slices[i + 1]\n\n                    let y0\n                    if (!prevSlice) y0 = y\n                    else y0 = y - (y - prevSlice[0]) / 2\n\n                    let sliceHeight\n                    if (!nextSlice) sliceHeight = height - y0\n                    else sliceHeight = y - y0 + (nextSlice[0] - y) / 2\n\n                    return {\n                        id: y,\n                        x0: 0,\n                        x: 0,\n                        y0,\n                        y,\n                        width,\n                        height: sliceHeight,\n                        points: slicePoints.reverse(),\n                    }\n                })\n        }\n    }, [enableSlices, points])\n}\n\nexport const useLine = ({\n    data,\n    xScale: xScaleSpec = LineDefaultProps.xScale,\n    xFormat,\n    yScale: yScaleSpec = LineDefaultProps.yScale,\n    yFormat,\n    width,\n    height,\n    colors = LineDefaultProps.colors,\n    curve = LineDefaultProps.curve,\n    areaBaselineValue = LineDefaultProps.areaBaselineValue,\n    pointColor = LineDefaultProps.pointColor,\n    pointBorderColor = LineDefaultProps.pointBorderColor,\n    enableSlices = LineDefaultProps.enableSlicesTooltip,\n}) => {\n    const formatX = useValueFormatter(xFormat)\n    const formatY = useValueFormatter(yFormat)\n    const getColor = useOrdinalColorScale(colors, 'id')\n    const theme = useTheme()\n    const getPointColor = useInheritedColor(pointColor, theme)\n    const getPointBorderColor = useInheritedColor(pointBorderColor, theme)\n    const [hiddenIds, setHiddenIds] = useState([])\n\n    const {\n        xScale,\n        yScale,\n        series: rawSeries,\n    } = useMemo(\n        () =>\n            computeXYScalesForSeries(\n                data.filter(item => hiddenIds.indexOf(item.id) === -1),\n                xScaleSpec,\n                yScaleSpec,\n                width,\n                height\n            ),\n        [data, hiddenIds, xScaleSpec, yScaleSpec, width, height]\n    )\n\n    const { legendData, series } = useMemo(() => {\n        const dataWithColor = data.map(line => ({\n            id: line.id,\n            label: line.id,\n            color: getColor(line),\n        }))\n        const series = dataWithColor\n            .map(datum => ({\n                ...rawSeries.find(serie => serie.id === datum.id),\n                color: datum.color,\n            }))\n            .filter(item => Boolean(item.id))\n        const legendData = dataWithColor\n            .map(item => ({ ...item, hidden: !series.find(serie => serie.id === item.id) }))\n            .reverse()\n\n        return { legendData, series }\n    }, [data, rawSeries, getColor])\n\n    const toggleSerie = useCallback(id => {\n        setHiddenIds(state =>\n            state.indexOf(id) > -1 ? state.filter(item => item !== id) : [...state, id]\n        )\n    }, [])\n\n    const points = usePoints({\n        series,\n        getPointColor,\n        getPointBorderColor,\n        formatX,\n        formatY,\n    })\n\n    const slices = useSlices({\n        enableSlices,\n        points,\n        width,\n        height,\n    })\n\n    const lineGenerator = useLineGenerator({ curve })\n    const areaGenerator = useAreaGenerator({\n        curve,\n        yScale,\n        areaBaselineValue,\n    })\n\n    return {\n        legendData,\n        toggleSerie,\n        lineGenerator,\n        areaGenerator,\n        getColor,\n        series,\n        xScale,\n        yScale,\n        slices,\n        points,\n    }\n}\n", "/*\n * This file is part of the nivo project.\n *\n * Copyright 2016-present, <PERSON><PERSON><PERSON>.\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\nimport { memo } from 'react'\nimport PropTypes from 'prop-types'\nimport { useSpring, animated } from '@react-spring/web'\nimport { useAnimatedPath, useMotionConfig, blendModePropType } from '@nivo/core'\n\nconst AreaPath = ({ areaBlendMode, areaOpacity, color, fill, path }) => {\n    const { animate, config: springConfig } = useMotionConfig()\n\n    const animatedPath = useAnimatedPath(path)\n    const animatedProps = useSpring({\n        color,\n        config: springConfig,\n        immediate: !animate,\n    })\n\n    return (\n        <animated.path\n            d={animatedPath}\n            fill={fill ? fill : animatedProps.color}\n            fillOpacity={areaOpacity}\n            strokeWidth={0}\n            style={{\n                mixBlendMode: areaBlendMode,\n            }}\n        />\n    )\n}\n\nAreaPath.propTypes = {\n    areaBlendMode: blendModePropType.isRequired,\n    areaOpacity: PropTypes.number.isRequired,\n    color: PropTypes.string,\n    fill: PropTypes.string,\n    path: PropTypes.string.isRequired,\n}\n\nconst Areas = ({ areaGenerator, areaOpacity, areaBlendMode, lines }) => {\n    const computedLines = lines.slice(0).reverse()\n\n    return (\n        <g>\n            {computedLines.map(line => (\n                <AreaPath\n                    key={line.id}\n                    path={areaGenerator(line.data.map(d => d.position))}\n                    {...{ areaOpacity, areaBlendMode, ...line }}\n                />\n            ))}\n        </g>\n    )\n}\n\nAreas.propTypes = {\n    areaGenerator: PropTypes.func.isRequired,\n    areaOpacity: PropTypes.number.isRequired,\n    areaBlendMode: blendModePropType.isRequired,\n    lines: PropTypes.arrayOf(PropTypes.object).isRequired,\n}\n\nexport default memo(Areas)\n", "/*\n * This file is part of the nivo project.\n *\n * Copyright 2016-present, <PERSON><PERSON><PERSON>.\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\nimport { memo, useMemo } from 'react'\nimport PropTypes from 'prop-types'\nimport { animated } from '@react-spring/web'\nimport { useAnimatedPath } from '@nivo/core'\n\nconst LinesItem = ({ lineGenerator, points, color, thickness }) => {\n    const path = useMemo(() => lineGenerator(points), [lineGenerator, points])\n    const animatedPath = useAnimatedPath(path)\n\n    return <animated.path d={animatedPath} fill=\"none\" strokeWidth={thickness} stroke={color} />\n}\n\nLinesItem.propTypes = {\n    points: PropTypes.arrayOf(\n        PropTypes.shape({\n            x: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n            y: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n        })\n    ),\n    lineGenerator: PropTypes.func.isRequired,\n    color: PropTypes.string.isRequired,\n    thickness: PropTypes.number.isRequired,\n}\n\nexport default memo(LinesItem)\n", "/*\n * This file is part of the nivo project.\n *\n * Copyright 2016-present, <PERSON><PERSON><PERSON>.\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\nimport { memo } from 'react'\nimport PropTypes from 'prop-types'\nimport LinesItem from './LinesItem'\n\nconst Lines = ({ lines, lineGenerator, lineWidth }) => {\n    return lines\n        .slice(0)\n        .reverse()\n        .map(({ id, data, color }) => (\n            <LinesItem\n                key={id}\n                id={id}\n                points={data.map(d => d.position)}\n                lineGenerator={lineGenerator}\n                color={color}\n                thickness={lineWidth}\n            />\n        ))\n}\n\nLines.propTypes = {\n    lines: PropTypes.arrayOf(\n        PropTypes.shape({\n            id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,\n            color: PropTypes.string.isRequired,\n            data: PropTypes.arrayOf(\n                PropTypes.shape({\n                    data: PropTypes.shape({\n                        x: PropTypes.oneOfType([\n                            PropTypes.string,\n                            PropTypes.number,\n                            PropTypes.instanceOf(Date),\n                        ]),\n                        y: PropTypes.oneOfType([\n                            PropTypes.string,\n                            PropTypes.number,\n                            PropTypes.instanceOf(Date),\n                        ]),\n                    }).isRequired,\n                    position: PropTypes.shape({\n                        x: PropTypes.number,\n                        y: PropTypes.number,\n                    }).isRequired,\n                })\n            ).isRequired,\n        })\n    ).isRequired,\n    lineWidth: PropTypes.number.isRequired,\n    lineGenerator: PropTypes.func.isRequired,\n}\n\nexport default memo(Lines)\n", "/*\n * This file is part of the nivo project.\n *\n * Copyright 2016-present, <PERSON><PERSON><PERSON>.\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\nimport { createElement, memo, useCallback } from 'react'\nimport PropTypes from 'prop-types'\nimport { useTooltip } from '@nivo/tooltip'\n\nconst SlicesItem = ({\n    slice,\n    axis,\n    debug,\n    tooltip,\n    isCurrent,\n    setCurrent,\n    onMouseEnter,\n    onMouseMove,\n    onMouseLeave,\n    onClick,\n}) => {\n    const { showTooltipFromEvent, hideTooltip } = useTooltip()\n\n    const handleMouseEnter = useCallback(\n        event => {\n            showTooltipFromEvent(createElement(tooltip, { slice, axis }), event, 'right')\n            setCurrent(slice)\n            onMouseEnter && onMouseEnter(slice, event)\n        },\n        [showTooltipFromEvent, tooltip, slice, onMouseEnter]\n    )\n\n    const handleMouseMove = useCallback(\n        event => {\n            showTooltipFromEvent(createElement(tooltip, { slice, axis }), event, 'right')\n            onMouseMove && onMouseMove(slice, event)\n        },\n        [showTooltipFromEvent, tooltip, slice, onMouseMove]\n    )\n\n    const handleMouseLeave = useCallback(\n        event => {\n            hideTooltip()\n            setCurrent(null)\n            onMouseLeave && onMouseLeave(slice, event)\n        },\n        [hideTooltip, slice, onMouseLeave]\n    )\n\n    const handleClick = useCallback(\n        event => {\n            onClick && onClick(slice, event)\n        },\n        [slice, onClick]\n    )\n\n    return (\n        <rect\n            x={slice.x0}\n            y={slice.y0}\n            width={slice.width}\n            height={slice.height}\n            stroke=\"red\"\n            strokeWidth={debug ? 1 : 0}\n            strokeOpacity={0.75}\n            fill=\"red\"\n            fillOpacity={isCurrent && debug ? 0.35 : 0}\n            onMouseEnter={handleMouseEnter}\n            onMouseMove={handleMouseMove}\n            onMouseLeave={handleMouseLeave}\n            onClick={handleClick}\n            data-testid={`slice-${slice.id}`}\n        />\n    )\n}\n\nSlicesItem.propTypes = {\n    slice: PropTypes.object.isRequired,\n    axis: PropTypes.oneOf(['x', 'y']).isRequired,\n    debug: PropTypes.bool.isRequired,\n    height: PropTypes.number.isRequired,\n    tooltip: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    isCurrent: PropTypes.bool.isRequired,\n    setCurrent: PropTypes.func.isRequired,\n    onMouseEnter: PropTypes.func,\n    onMouseMove: PropTypes.func,\n    onMouseLeave: PropTypes.func,\n    onClick: PropTypes.func,\n}\n\nexport default memo(SlicesItem)\n", "/*\n * This file is part of the nivo project.\n *\n * Copyright 2016-present, <PERSON><PERSON><PERSON>.\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\nimport { memo } from 'react'\nimport PropTypes from 'prop-types'\nimport SlicesItem from './SlicesItem'\n\nconst Slices = ({\n    slices,\n    axis,\n    debug,\n    height,\n    tooltip,\n    current,\n    setCurrent,\n    onMouseEnter,\n    onMouseMove,\n    onMouseLeave,\n    onClick,\n}) => {\n    return slices.map(slice => (\n        <SlicesItem\n            key={slice.id}\n            slice={slice}\n            axis={axis}\n            debug={debug}\n            height={height}\n            tooltip={tooltip}\n            setCurrent={setCurrent}\n            isCurrent={current !== null && current.id === slice.id}\n            onMouseEnter={onMouseEnter}\n            onMouseMove={onMouseMove}\n            onMouseLeave={onMouseLeave}\n            onClick={onClick}\n        />\n    ))\n}\n\nSlices.propTypes = {\n    slices: PropTypes.arrayOf(\n        PropTypes.shape({\n            id: PropTypes.oneOfType([\n                PropTypes.number,\n                PropTypes.string,\n                PropTypes.instanceOf(Date),\n            ]).isRequired,\n            x: PropTypes.number.isRequired,\n            y: PropTypes.number.isRequired,\n            points: PropTypes.arrayOf(PropTypes.object).isRequired,\n        })\n    ).isRequired,\n    axis: PropTypes.oneOf(['x', 'y']).isRequired,\n    debug: PropTypes.bool.isRequired,\n    height: PropTypes.number.isRequired,\n    tooltip: PropTypes.oneOfType([PropTypes.func, PropTypes.object]).isRequired,\n    current: PropTypes.object,\n    setCurrent: PropTypes.func.isRequired,\n    onMouseEnter: PropTypes.func,\n    onMouseMove: PropTypes.func,\n    onMouseLeave: PropTypes.func,\n    onClick: PropTypes.func,\n}\n\nexport default memo(Slices)\n", "/*\n * This file is part of the nivo project.\n *\n * Copyright 2016-present, <PERSON><PERSON><PERSON>.\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\nimport { memo } from 'react'\nimport PropTypes from 'prop-types'\nimport { getLabelGenerator, DotsItem, useTheme } from '@nivo/core'\n\nconst Points = ({ points, symbol, size, borderWidth, enableLabel, label, labelYOffset }) => {\n    const theme = useTheme()\n    const getLabel = getLabelGenerator(label)\n\n    /**\n     * We reverse the `points` array so that points from the lower lines in stacked lines\n     * graph are drawn on top. See https://github.com/plouc/nivo/issues/1051.\n     */\n    const mappedPoints = points\n        .slice(0)\n        .reverse()\n        .map(point => {\n            const mappedPoint = {\n                id: point.id,\n                x: point.x,\n                y: point.y,\n                datum: point.data,\n                fill: point.color,\n                stroke: point.borderColor,\n                label: enableLabel ? getLabel(point.data) : null,\n            }\n\n            return mappedPoint\n        })\n\n    return (\n        <g>\n            {mappedPoints.map(point => (\n                <DotsItem\n                    key={point.id}\n                    x={point.x}\n                    y={point.y}\n                    datum={point.datum}\n                    symbol={symbol}\n                    size={size}\n                    color={point.fill}\n                    borderWidth={borderWidth}\n                    borderColor={point.stroke}\n                    label={point.label}\n                    labelYOffset={labelYOffset}\n                    theme={theme}\n                />\n            ))}\n        </g>\n    )\n}\n\nPoints.propTypes = {\n    points: PropTypes.arrayOf(PropTypes.object),\n    symbol: PropTypes.func,\n    size: PropTypes.number.isRequired,\n    color: PropTypes.func.isRequired,\n    borderWidth: PropTypes.number.isRequired,\n    borderColor: PropTypes.func.isRequired,\n    enableLabel: PropTypes.bool.isRequired,\n    label: PropTypes.oneOfType([PropTypes.string, PropTypes.func]).isRequired,\n    labelYOffset: PropTypes.number,\n}\n\nexport default memo(Points)\n", "/*\n * This file is part of the nivo project.\n *\n * Copyright 2016-present, <PERSON><PERSON><PERSON>.\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\nimport { createElement, memo, useCallback } from 'react'\nimport PropTypes from 'prop-types'\nimport { useTooltip } from '@nivo/tooltip'\nimport { Mesh as BaseMesh } from '@nivo/voronoi'\n\nconst Mesh = ({\n    points,\n    width,\n    height,\n    margin,\n    setCurrent,\n    onMouseEnter,\n    onMouseMove,\n    onMouseLeave,\n    onClick,\n    tooltip,\n    debug,\n}) => {\n    const { showTooltipAt, hideTooltip } = useTooltip()\n\n    const handleMouseEnter = useCallback(\n        (point, event) => {\n            showTooltipAt(\n                createElement(tooltip, { point }),\n                [point.x + margin.left, point.y + margin.top],\n                'top'\n            )\n            setCurrent(point)\n            onMouseEnter && onMouseEnter(point, event)\n        },\n        [setCurrent, showTooltipAt, tooltip, onMouseEnter, margin]\n    )\n\n    const handleMouseMove = useCallback(\n        (point, event) => {\n            showTooltipAt(\n                createElement(tooltip, { point }),\n                [point.x + margin.left, point.y + margin.top],\n                'top'\n            )\n            setCurrent(point)\n            onMouseMove && onMouseMove(point, event)\n        },\n        [setCurrent, showTooltipAt, tooltip, onMouseMove]\n    )\n\n    const handleMouseLeave = useCallback(\n        (point, event) => {\n            hideTooltip()\n            setCurrent(null)\n            onMouseLeave && onMouseLeave(point, event)\n        },\n        [hideTooltip, setCurrent, onMouseLeave]\n    )\n\n    const handleClick = useCallback(\n        (point, event) => {\n            onClick && onClick(point, event)\n        },\n        [onClick]\n    )\n\n    return (\n        <BaseMesh\n            nodes={points}\n            width={width}\n            height={height}\n            onMouseEnter={handleMouseEnter}\n            onMouseMove={handleMouseMove}\n            onMouseLeave={handleMouseLeave}\n            onClick={handleClick}\n            debug={debug}\n        />\n    )\n}\n\nMesh.propTypes = {\n    points: PropTypes.arrayOf(PropTypes.object).isRequired,\n    width: PropTypes.number.isRequired,\n    height: PropTypes.number.isRequired,\n    margin: PropTypes.object.isRequired,\n    setCurrent: PropTypes.func.isRequired,\n    onMouseEnter: PropTypes.func,\n    onMouseMove: PropTypes.func,\n    onMouseLeave: PropTypes.func,\n    onClick: PropTypes.func,\n    tooltip: PropTypes.oneOfType([PropTypes.func, PropTypes.object]).isRequired,\n    debug: PropTypes.bool.isRequired,\n}\n\nexport default memo(Mesh)\n", "/*\n * This file is part of the nivo project.\n *\n * Copyright 2016-present, <PERSON><PERSON><PERSON>.\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\nimport { Fragment, useState } from 'react'\nimport {\n    bindDefs,\n    withContainer,\n    useDimensions,\n    useTheme,\n    SvgWrapper,\n    CartesianMarkers,\n} from '@nivo/core'\nimport { useInheritedColor } from '@nivo/colors'\nimport { Axes, Grid } from '@nivo/axes'\nimport { BoxLegendSvg } from '@nivo/legends'\nimport { Crosshair } from '@nivo/tooltip'\nimport { useLine } from './hooks'\nimport { LinePropTypes, LineDefaultProps } from './props'\nimport Areas from './Areas'\nimport Lines from './Lines'\nimport Slices from './Slices'\nimport Points from './Points'\nimport Mesh from './Mesh'\n\nconst Line = props => {\n    const {\n        data,\n        xScale: xScaleSpec,\n        xFormat,\n        yScale: yScaleSpec,\n        yFormat,\n        layers,\n        curve,\n        areaBaselineValue,\n\n        colors,\n\n        margin: partialMargin,\n        width,\n        height,\n\n        axisTop,\n        axisRight,\n        axisBottom,\n        axisLeft,\n        enableGridX,\n        enableGridY,\n        gridXValues,\n        gridYValues,\n\n        lineWidth,\n        enableArea,\n        areaOpacity,\n        areaBlendMode,\n\n        enablePoints,\n        pointSymbol,\n        pointSize,\n        pointColor,\n        pointBorderWidth,\n        pointBorderColor,\n        enablePointLabel,\n        pointLabel,\n        pointLabelYOffset,\n\n        defs,\n        fill,\n\n        markers,\n\n        legends,\n\n        isInteractive,\n\n        useMesh,\n        debugMesh,\n\n        onMouseEnter,\n        onMouseMove,\n        onMouseLeave,\n        onClick,\n\n        tooltip,\n\n        enableSlices,\n        debugSlices,\n        sliceTooltip,\n\n        enableCrosshair,\n        crosshairType,\n\n        role,\n    } = props\n\n    const { margin, innerWidth, innerHeight, outerWidth, outerHeight } = useDimensions(\n        width,\n        height,\n        partialMargin\n    )\n\n    const {\n        legendData,\n        toggleSerie,\n        lineGenerator,\n        areaGenerator,\n        series,\n        xScale,\n        yScale,\n        slices,\n        points,\n    } = useLine({\n        data,\n        xScale: xScaleSpec,\n        xFormat,\n        yScale: yScaleSpec,\n        yFormat,\n        width: innerWidth,\n        height: innerHeight,\n        colors,\n        curve,\n        areaBaselineValue,\n        pointColor,\n        pointBorderColor,\n        enableSlices,\n    })\n\n    const theme = useTheme()\n    const getPointColor = useInheritedColor(pointColor, theme)\n    const getPointBorderColor = useInheritedColor(pointBorderColor, theme)\n\n    const [currentPoint, setCurrentPoint] = useState(null)\n    const [currentSlice, setCurrentSlice] = useState(null)\n\n    const layerById = {\n        grid: (\n            <Grid\n                key=\"grid\"\n                theme={theme}\n                width={innerWidth}\n                height={innerHeight}\n                xScale={enableGridX ? xScale : null}\n                yScale={enableGridY ? yScale : null}\n                xValues={gridXValues}\n                yValues={gridYValues}\n            />\n        ),\n        markers: (\n            <CartesianMarkers\n                key=\"markers\"\n                markers={markers}\n                width={innerWidth}\n                height={innerHeight}\n                xScale={xScale}\n                yScale={yScale}\n                theme={theme}\n            />\n        ),\n        axes: (\n            <Axes\n                key=\"axes\"\n                xScale={xScale}\n                yScale={yScale}\n                width={innerWidth}\n                height={innerHeight}\n                theme={theme}\n                top={axisTop}\n                right={axisRight}\n                bottom={axisBottom}\n                left={axisLeft}\n            />\n        ),\n        areas: null,\n        lines: (\n            <Lines key=\"lines\" lines={series} lineGenerator={lineGenerator} lineWidth={lineWidth} />\n        ),\n        slices: null,\n        points: null,\n        crosshair: null,\n        mesh: null,\n        legends: legends.map((legend, i) => (\n            <BoxLegendSvg\n                key={`legend.${i}`}\n                {...legend}\n                containerWidth={innerWidth}\n                containerHeight={innerHeight}\n                data={legend.data || legendData}\n                theme={theme}\n                toggleSerie={legend.toggleSerie ? toggleSerie : undefined}\n            />\n        )),\n    }\n\n    const boundDefs = bindDefs(defs, series, fill)\n\n    if (enableArea) {\n        layerById.areas = (\n            <Areas\n                key=\"areas\"\n                areaGenerator={areaGenerator}\n                areaOpacity={areaOpacity}\n                areaBlendMode={areaBlendMode}\n                lines={series}\n            />\n        )\n    }\n\n    if (isInteractive && enableSlices !== false) {\n        layerById.slices = (\n            <Slices\n                key=\"slices\"\n                slices={slices}\n                axis={enableSlices}\n                debug={debugSlices}\n                height={innerHeight}\n                tooltip={sliceTooltip}\n                current={currentSlice}\n                setCurrent={setCurrentSlice}\n                onMouseEnter={onMouseEnter}\n                onMouseMove={onMouseMove}\n                onMouseLeave={onMouseLeave}\n                onClick={onClick}\n            />\n        )\n    }\n\n    if (enablePoints) {\n        layerById.points = (\n            <Points\n                key=\"points\"\n                points={points}\n                symbol={pointSymbol}\n                size={pointSize}\n                color={getPointColor}\n                borderWidth={pointBorderWidth}\n                borderColor={getPointBorderColor}\n                enableLabel={enablePointLabel}\n                label={pointLabel}\n                labelYOffset={pointLabelYOffset}\n            />\n        )\n    }\n\n    if (isInteractive && enableCrosshair) {\n        if (currentPoint !== null) {\n            layerById.crosshair = (\n                <Crosshair\n                    key=\"crosshair\"\n                    width={innerWidth}\n                    height={innerHeight}\n                    x={currentPoint.x}\n                    y={currentPoint.y}\n                    type={crosshairType}\n                />\n            )\n        }\n        if (currentSlice !== null) {\n            layerById.crosshair = (\n                <Crosshair\n                    key=\"crosshair\"\n                    width={innerWidth}\n                    height={innerHeight}\n                    x={currentSlice.x}\n                    y={currentSlice.y}\n                    type={enableSlices}\n                />\n            )\n        }\n    }\n\n    if (isInteractive && useMesh && enableSlices === false) {\n        layerById.mesh = (\n            <Mesh\n                key=\"mesh\"\n                points={points}\n                width={innerWidth}\n                height={innerHeight}\n                margin={margin}\n                current={currentPoint}\n                setCurrent={setCurrentPoint}\n                onMouseEnter={onMouseEnter}\n                onMouseMove={onMouseMove}\n                onMouseLeave={onMouseLeave}\n                onClick={onClick}\n                tooltip={tooltip}\n                debug={debugMesh}\n            />\n        )\n    }\n\n    return (\n        <SvgWrapper\n            defs={boundDefs}\n            width={outerWidth}\n            height={outerHeight}\n            margin={margin}\n            role={role}\n        >\n            {layers.map((layer, i) => {\n                if (typeof layer === 'function') {\n                    return (\n                        <Fragment key={i}>\n                            {layer({\n                                ...props,\n                                innerWidth,\n                                innerHeight,\n                                series,\n                                slices,\n                                points,\n                                xScale,\n                                yScale,\n                                lineGenerator,\n                                areaGenerator,\n                                currentPoint,\n                                setCurrentPoint,\n                                currentSlice,\n                                setCurrentSlice,\n                            })}\n                        </Fragment>\n                    )\n                }\n\n                return layerById[layer]\n            })}\n        </SvgWrapper>\n    )\n}\n\nLine.propTypes = LinePropTypes\nLine.defaultProps = LineDefaultProps\n\nexport default withContainer(Line)\n", "/*\n * This file is part of the nivo project.\n *\n * Copyright 2016-present, <PERSON><PERSON><PERSON>.\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\nimport { ResponsiveWrapper } from '@nivo/core'\nimport Line from './Line'\n\nconst ResponsiveLine = props => (\n    <ResponsiveWrapper>\n        {({ width, height }) => <Line width={width} height={height} {...props} />}\n    </ResponsiveWrapper>\n)\n\nexport default ResponsiveLine\n", "/*\n * This file is part of the nivo project.\n *\n * Copyright 2016-present, <PERSON><PERSON><PERSON>.\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\nimport { createElement, useRef, useEffect, useState, useCallback, forwardRef } from 'react'\nimport {\n    withContainer,\n    useDimensions,\n    useTheme,\n    getRelativeCursor,\n    isCursorInRect,\n} from '@nivo/core'\nimport { renderAxesToCanvas, renderGridLinesToCanvas } from '@nivo/axes'\nimport { renderLegendToCanvas } from '@nivo/legends'\nimport { useTooltip } from '@nivo/tooltip'\nimport { useVoronoiMesh, renderVoronoiToCanvas, renderVoronoiCellToCanvas } from '@nivo/voronoi'\nimport { LineCanvasPropTypes, LineCanvasDefaultProps } from './props'\nimport { useLine } from './hooks'\n\nconst LineCanvas = ({\n    width,\n    height,\n    margin: partialMargin,\n    pixelRatio,\n\n    data,\n    xScale: xScaleSpec,\n    xFormat,\n    yScale: yScaleSpec,\n    yFormat,\n    curve,\n\n    layers,\n\n    colors,\n    lineWidth,\n\n    enableArea,\n    areaBaselineValue,\n    areaOpacity,\n\n    enablePoints,\n    pointSize,\n    pointColor,\n    pointBorderWidth,\n    pointBorderColor,\n\n    enableGridX,\n    gridXValues,\n    enableGridY,\n    gridYValues,\n    axisTop,\n    axisRight,\n    axisBottom,\n    axisLeft,\n\n    legends,\n\n    isInteractive,\n    debugMesh,\n    //onMouseEnter,\n    //onMouseMove,\n    onMouseLeave,\n    onClick,\n    tooltip,\n\n    canvasRef,\n}) => {\n    const canvasEl = useRef(null)\n    const { margin, innerWidth, innerHeight, outerWidth, outerHeight } = useDimensions(\n        width,\n        height,\n        partialMargin\n    )\n    const theme = useTheme()\n    const [currentPoint, setCurrentPoint] = useState(null)\n\n    const { lineGenerator, areaGenerator, series, xScale, yScale, points } = useLine({\n        data,\n        xScale: xScaleSpec,\n        xFormat,\n        yScale: yScaleSpec,\n        yFormat,\n        width: innerWidth,\n        height: innerHeight,\n        colors,\n        curve,\n        areaBaselineValue,\n        pointColor,\n        pointBorderColor,\n    })\n\n    const { delaunay, voronoi } = useVoronoiMesh({\n        points,\n        width: innerWidth,\n        height: innerHeight,\n        debug: debugMesh,\n    })\n\n    useEffect(() => {\n        if (canvasRef) {\n            canvasRef.current = canvasEl.current\n        }\n\n        canvasEl.current.width = outerWidth * pixelRatio\n        canvasEl.current.height = outerHeight * pixelRatio\n\n        const ctx = canvasEl.current.getContext('2d')\n\n        ctx.scale(pixelRatio, pixelRatio)\n\n        ctx.fillStyle = theme.background\n        ctx.fillRect(0, 0, outerWidth, outerHeight)\n        ctx.translate(margin.left, margin.top)\n\n        layers.forEach(layer => {\n            if (typeof layer === 'function') {\n                layer({\n                    ctx,\n                    innerWidth,\n                    innerHeight,\n                    series,\n                    points,\n                    xScale,\n                    yScale,\n                    lineWidth,\n                    lineGenerator,\n                    areaGenerator,\n                    currentPoint,\n                    setCurrentPoint,\n                })\n            }\n\n            if (layer === 'grid' && theme.grid.line.strokeWidth > 0) {\n                ctx.lineWidth = theme.grid.line.strokeWidth\n                ctx.strokeStyle = theme.grid.line.stroke\n\n                enableGridX &&\n                    renderGridLinesToCanvas(ctx, {\n                        width: innerWidth,\n                        height: innerHeight,\n                        scale: xScale,\n                        axis: 'x',\n                        values: gridXValues,\n                    })\n\n                enableGridY &&\n                    renderGridLinesToCanvas(ctx, {\n                        width: innerWidth,\n                        height: innerHeight,\n                        scale: yScale,\n                        axis: 'y',\n                        values: gridYValues,\n                    })\n            }\n\n            if (layer === 'axes') {\n                renderAxesToCanvas(ctx, {\n                    xScale,\n                    yScale,\n                    width: innerWidth,\n                    height: innerHeight,\n                    top: axisTop,\n                    right: axisRight,\n                    bottom: axisBottom,\n                    left: axisLeft,\n                    theme,\n                })\n            }\n\n            if (layer === 'areas' && enableArea === true) {\n                ctx.save()\n                ctx.globalAlpha = areaOpacity\n\n                areaGenerator.context(ctx)\n                series.forEach(serie => {\n                    ctx.fillStyle = serie.color\n                    ctx.beginPath()\n                    areaGenerator(serie.data.map(d => d.position))\n                    ctx.fill()\n                })\n\n                ctx.restore()\n            }\n\n            if (layer === 'lines') {\n                lineGenerator.context(ctx)\n                series.forEach(serie => {\n                    ctx.strokeStyle = serie.color\n                    ctx.lineWidth = lineWidth\n                    ctx.beginPath()\n                    lineGenerator(serie.data.map(d => d.position))\n                    ctx.stroke()\n                })\n            }\n\n            if (layer === 'points' && enablePoints === true && pointSize > 0) {\n                points.forEach(point => {\n                    ctx.fillStyle = point.color\n                    ctx.beginPath()\n                    ctx.arc(point.x, point.y, pointSize / 2, 0, 2 * Math.PI)\n                    ctx.fill()\n\n                    if (pointBorderWidth > 0) {\n                        ctx.strokeStyle = point.borderColor\n                        ctx.lineWidth = pointBorderWidth\n                        ctx.stroke()\n                    }\n                })\n            }\n\n            if (layer === 'mesh' && debugMesh === true) {\n                renderVoronoiToCanvas(ctx, voronoi)\n                if (currentPoint) {\n                    renderVoronoiCellToCanvas(ctx, voronoi, currentPoint.index)\n                }\n            }\n\n            if (layer === 'legends') {\n                const legendData = series\n                    .map(serie => ({\n                        id: serie.id,\n                        label: serie.id,\n                        color: serie.color,\n                    }))\n                    .reverse()\n\n                legends.forEach(legend => {\n                    renderLegendToCanvas(ctx, {\n                        ...legend,\n                        data: legend.data || legendData,\n                        containerWidth: innerWidth,\n                        containerHeight: innerHeight,\n                        theme,\n                    })\n                })\n            }\n        })\n    }, [\n        canvasEl,\n        outerWidth,\n        outerHeight,\n        layers,\n        theme,\n        lineGenerator,\n        series,\n        xScale,\n        yScale,\n        enableGridX,\n        gridXValues,\n        enableGridY,\n        gridYValues,\n        axisTop,\n        axisRight,\n        axisBottom,\n        axisLeft,\n        legends,\n        points,\n        enablePoints,\n        pointSize,\n        currentPoint,\n    ])\n\n    const getPointFromMouseEvent = useCallback(\n        event => {\n            const [x, y] = getRelativeCursor(canvasEl.current, event)\n            if (!isCursorInRect(margin.left, margin.top, innerWidth, innerHeight, x, y)) return null\n\n            const pointIndex = delaunay.find(x - margin.left, y - margin.top)\n            return points[pointIndex]\n        },\n        [canvasEl, margin, innerWidth, innerHeight, delaunay]\n    )\n\n    const { showTooltipFromEvent, hideTooltip } = useTooltip()\n\n    const handleMouseHover = useCallback(\n        event => {\n            const point = getPointFromMouseEvent(event)\n            setCurrentPoint(point)\n\n            if (point) {\n                showTooltipFromEvent(createElement(tooltip, { point }), event)\n            } else {\n                hideTooltip()\n            }\n        },\n        [getPointFromMouseEvent, setCurrentPoint, showTooltipFromEvent, hideTooltip, tooltip]\n    )\n\n    const handleMouseLeave = useCallback(\n        event => {\n            hideTooltip()\n            setCurrentPoint(null)\n            currentPoint && onMouseLeave && onMouseLeave(currentPoint, event)\n        },\n        [hideTooltip, setCurrentPoint, onMouseLeave]\n    )\n\n    const handleClick = useCallback(\n        event => {\n            if (onClick) {\n                const point = getPointFromMouseEvent(event)\n                point && onClick(point, event)\n            }\n        },\n        [getPointFromMouseEvent, onClick]\n    )\n\n    return (\n        <canvas\n            ref={canvasEl}\n            width={outerWidth * pixelRatio}\n            height={outerHeight * pixelRatio}\n            style={{\n                width: outerWidth,\n                height: outerHeight,\n                cursor: isInteractive ? 'auto' : 'normal',\n            }}\n            onMouseEnter={isInteractive ? handleMouseHover : undefined}\n            onMouseMove={isInteractive ? handleMouseHover : undefined}\n            onMouseLeave={isInteractive ? handleMouseLeave : undefined}\n            onClick={isInteractive ? handleClick : undefined}\n        />\n    )\n}\n\nLineCanvas.propTypes = LineCanvasPropTypes\nLineCanvas.defaultProps = LineCanvasDefaultProps\n\nconst LineCanvasWithContainer = withContainer(LineCanvas)\n\nexport default forwardRef((props, ref) => <LineCanvasWithContainer {...props} canvasRef={ref} />)\n", "/*\n * This file is part of the nivo project.\n *\n * Copyright 2016-present, <PERSON><PERSON><PERSON>.\n *\n * For the full copyright and license information, please view the LICENSE\n * file that was distributed with this source code.\n */\nimport { forwardRef } from 'react'\nimport { ResponsiveWrapper } from '@nivo/core'\nimport LineCanvas from './LineCanvas'\n\nconst ResponsiveLineCanvas = (props, ref) => (\n    <ResponsiveWrapper>\n        {({ width, height }) => <LineCanvas width={width} height={height} {...props} ref={ref} />}\n    </ResponsiveWrapper>\n)\n\nexport default forwardRef(ResponsiveLineCanvas)\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAM,UAAU,KAAK,IAAI,GAAG,GAAG;AAC/B,IAAM,aAAa,IAAI,YAAY,GAAG;AAEtC,IAAqB,aAArB,MAAqB,YAAW;AAAA,EAE5B,OAAO,KAAK,QAAQ,OAAO,aAAa,OAAO,aAAa;AACxD,UAAMA,KAAI,OAAO;AACjB,UAAM,SAAS,IAAI,aAAaA,KAAI,CAAC;AAErC,aAASC,KAAI,GAAGA,KAAID,IAAGC,MAAK;AACxB,YAAMC,KAAI,OAAOD,EAAC;AAClB,aAAO,IAAIA,EAAC,IAAI,KAAKC,EAAC;AACtB,aAAO,IAAID,KAAI,CAAC,IAAI,KAAKC,EAAC;AAAA,IAC9B;AAEA,WAAO,IAAI,YAAW,MAAM;AAAA,EAChC;AAAA,EAEA,YAAY,QAAQ;AAChB,UAAMF,KAAI,OAAO,UAAU;AAC3B,QAAIA,KAAI,KAAK,OAAO,OAAO,CAAC,MAAM;AAAU,YAAM,IAAI,MAAM,qCAAqC;AAEjG,SAAK,SAAS;AAGd,UAAM,eAAe,KAAK,IAAI,IAAIA,KAAI,GAAG,CAAC;AAC1C,SAAK,aAAa,IAAI,YAAY,eAAe,CAAC;AAClD,SAAK,aAAa,IAAI,WAAW,eAAe,CAAC;AAGjD,SAAK,YAAY,KAAK,KAAK,KAAK,KAAKA,EAAC,CAAC;AACvC,SAAK,YAAY,IAAI,YAAYA,EAAC;AAClC,SAAK,YAAY,IAAI,YAAYA,EAAC;AAClC,SAAK,WAAW,IAAI,YAAYA,EAAC;AACjC,SAAK,YAAY,IAAI,WAAW,KAAK,SAAS,EAAE,KAAK,EAAE;AAGvD,SAAK,OAAO,IAAI,YAAYA,EAAC;AAC7B,SAAK,SAAS,IAAI,aAAaA,EAAC;AAEhC,SAAK,OAAO;AAAA,EAChB;AAAA,EAEA,SAAS;AACL,UAAM,EAAC,QAAQ,WAAW,UAAU,WAAW,UAAU,UAAU,SAAS,WAAW,SAAQ,IAAK;AACpG,UAAMA,KAAI,OAAO,UAAU;AAG3B,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AAEX,aAASC,KAAI,GAAGA,KAAID,IAAGC,MAAK;AACxB,YAAME,KAAI,OAAO,IAAIF,EAAC;AACtB,YAAM,IAAI,OAAO,IAAIA,KAAI,CAAC;AAC1B,UAAIE,KAAI;AAAM,eAAOA;AACrB,UAAI,IAAI;AAAM,eAAO;AACrB,UAAIA,KAAI;AAAM,eAAOA;AACrB,UAAI,IAAI;AAAM,eAAO;AACrB,WAAK,KAAKF,EAAC,IAAIA;AAAA,IACnB;AACA,UAAM,MAAM,OAAO,QAAQ;AAC3B,UAAM,MAAM,OAAO,QAAQ;AAE3B,QAAI,UAAU;AACd,QAAI,IAAI,IAAIG;AAGZ,aAASH,KAAI,GAAGA,KAAID,IAAGC,MAAK;AACxB,YAAM,IAAI,KAAK,IAAI,IAAI,OAAO,IAAIA,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,CAAC;AACvD,UAAI,IAAI,SAAS;AACb,aAAKA;AACL,kBAAU;AAAA,MACd;AAAA,IACJ;AACA,UAAM,MAAM,OAAO,IAAI,EAAE;AACzB,UAAM,MAAM,OAAO,IAAI,KAAK,CAAC;AAE7B,cAAU;AAGV,aAASA,KAAI,GAAGA,KAAID,IAAGC,MAAK;AACxB,UAAIA,OAAM;AAAI;AACd,YAAM,IAAI,KAAK,KAAK,KAAK,OAAO,IAAIA,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,CAAC;AACzD,UAAI,IAAI,WAAW,IAAI,GAAG;AACtB,aAAKA;AACL,kBAAU;AAAA,MACd;AAAA,IACJ;AACA,QAAI,MAAM,OAAO,IAAI,EAAE;AACvB,QAAI,MAAM,OAAO,IAAI,KAAK,CAAC;AAE3B,QAAI,YAAY;AAGhB,aAASA,KAAI,GAAGA,KAAID,IAAGC,MAAK;AACxB,UAAIA,OAAM,MAAMA,OAAM;AAAI;AAC1B,YAAMI,KAAI,aAAa,KAAK,KAAK,KAAK,KAAK,OAAO,IAAIJ,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,CAAC;AAC3E,UAAII,KAAI,WAAW;AACf,QAAAD,MAAKH;AACL,oBAAYI;AAAA,MAChB;AAAA,IACJ;AACA,QAAI,MAAM,OAAO,IAAID,GAAE;AACvB,QAAI,MAAM,OAAO,IAAIA,MAAK,CAAC;AAE3B,QAAI,cAAc,UAAU;AAGxB,eAASH,KAAI,GAAGA,KAAID,IAAGC,MAAK;AACxB,aAAK,OAAOA,EAAC,IAAK,OAAO,IAAIA,EAAC,IAAI,OAAO,CAAC,KAAO,OAAO,IAAIA,KAAI,CAAC,IAAI,OAAO,CAAC;AAAA,MACjF;AACA,gBAAU,KAAK,MAAM,KAAK,QAAQ,GAAGD,KAAI,CAAC;AAC1C,YAAM,OAAO,IAAI,YAAYA,EAAC;AAC9B,UAAIM,KAAI;AACR,eAASL,KAAI,GAAG,KAAK,WAAWA,KAAID,IAAGC,MAAK;AACxC,cAAM,KAAK,KAAK,KAAKA,EAAC;AACtB,YAAI,KAAK,OAAO,EAAE,IAAI,IAAI;AACtB,eAAKK,IAAG,IAAI;AACZ,eAAK,KAAK,OAAO,EAAE;AAAA,QACvB;AAAA,MACJ;AACA,WAAK,OAAO,KAAK,SAAS,GAAGA,EAAC;AAC9B,WAAK,YAAY,IAAI,YAAY,CAAC;AAClC,WAAK,YAAY,IAAI,YAAY,CAAC;AAClC;AAAA,IACJ;AAGA,QAAI,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG;AACtC,YAAML,KAAI;AACV,YAAME,KAAI;AACV,YAAM,IAAI;AACV,WAAKC;AACL,YAAM;AACN,YAAM;AACN,MAAAA,MAAKH;AACL,YAAME;AACN,YAAM;AAAA,IACV;AAEA,UAAM,SAAS,aAAa,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AACxD,SAAK,MAAM,OAAO;AAClB,SAAK,MAAM,OAAO;AAElB,aAASF,KAAI,GAAGA,KAAID,IAAGC,MAAK;AACxB,WAAK,OAAOA,EAAC,IAAI,KAAK,OAAO,IAAIA,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,GAAG,OAAO,GAAG,OAAO,CAAC;AAAA,IAC9E;AAGA,cAAU,KAAK,MAAM,KAAK,QAAQ,GAAGD,KAAI,CAAC;AAG1C,SAAK,aAAa;AAClB,QAAI,WAAW;AAEf,aAAS,EAAE,IAAI,SAASI,GAAE,IAAI;AAC9B,aAAS,EAAE,IAAI,SAAS,EAAE,IAAIA;AAC9B,aAASA,GAAE,IAAI,SAAS,EAAE,IAAI;AAE9B,YAAQ,EAAE,IAAI;AACd,YAAQ,EAAE,IAAI;AACd,YAAQA,GAAE,IAAI;AAEd,aAAS,KAAK,EAAE;AAChB,aAAS,KAAK,SAAS,KAAK,GAAG,CAAC,IAAI;AACpC,aAAS,KAAK,SAAS,KAAK,GAAG,CAAC,IAAI;AACpC,aAAS,KAAK,SAAS,KAAK,GAAG,CAAC,IAAIA;AAEpC,SAAK,eAAe;AACpB,SAAK,aAAa,IAAI,IAAIA,KAAI,IAAI,IAAI,EAAE;AAExC,aAASG,KAAI,GAAG,IAAI,IAAIA,KAAI,KAAK,KAAK,QAAQA,MAAK;AAC/C,YAAMN,KAAI,KAAK,KAAKM,EAAC;AACrB,YAAMJ,KAAI,OAAO,IAAIF,EAAC;AACtB,YAAM,IAAI,OAAO,IAAIA,KAAI,CAAC;AAG1B,UAAIM,KAAI,KAAK,KAAK,IAAIJ,KAAI,EAAE,KAAK,WAAW,KAAK,IAAI,IAAI,EAAE,KAAK;AAAS;AACzE,WAAKA;AACL,WAAK;AAGL,UAAIF,OAAM,MAAMA,OAAM,MAAMA,OAAMG;AAAI;AAGtC,UAAI,QAAQ;AACZ,eAASE,KAAI,GAAG,MAAM,KAAK,SAASH,IAAG,CAAC,GAAGG,KAAI,KAAK,WAAWA,MAAK;AAChE,gBAAQ,UAAU,MAAMA,MAAK,KAAK,SAAS;AAC3C,YAAI,UAAU,MAAM,UAAU,SAAS,KAAK;AAAG;AAAA,MACnD;AAEA,cAAQ,SAAS,KAAK;AACtB,UAAIE,KAAI,OAAO;AACf,aAAO,IAAI,SAASA,EAAC,GAAG,CAAC,OAAOL,IAAG,GAAG,OAAO,IAAIK,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG;AACvG,QAAAA,KAAI;AACJ,YAAIA,OAAM,OAAO;AACb,UAAAA,KAAI;AACJ;AAAA,QACJ;AAAA,MACJ;AACA,UAAIA,OAAM;AAAI;AAGd,UAAIC,KAAI,KAAK,aAAaD,IAAGP,IAAG,SAASO,EAAC,GAAG,IAAI,IAAI,QAAQA,EAAC,CAAC;AAG/D,cAAQP,EAAC,IAAI,KAAK,UAAUQ,KAAI,CAAC;AACjC,cAAQD,EAAC,IAAIC;AACb;AAGA,UAAIT,KAAI,SAASQ,EAAC;AAClB,aAAO,IAAI,SAASR,EAAC,GAAG,OAAOG,IAAG,GAAG,OAAO,IAAIH,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG;AACtG,QAAAS,KAAI,KAAK,aAAaT,IAAGC,IAAG,GAAG,QAAQA,EAAC,GAAG,IAAI,QAAQD,EAAC,CAAC;AACzD,gBAAQC,EAAC,IAAI,KAAK,UAAUQ,KAAI,CAAC;AACjC,iBAAST,EAAC,IAAIA;AACd;AACA,QAAAA,KAAI;AAAA,MACR;AAGA,UAAIQ,OAAM,OAAO;AACb,eAAO,IAAI,SAASA,EAAC,GAAG,OAAOL,IAAG,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,OAAO,IAAIK,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,CAAC,GAAG;AACtG,UAAAC,KAAI,KAAK,aAAa,GAAGR,IAAGO,IAAG,IAAI,QAAQA,EAAC,GAAG,QAAQ,CAAC,CAAC;AACzD,eAAK,UAAUC,KAAI,CAAC;AACpB,kBAAQ,CAAC,IAAIA;AACb,mBAASD,EAAC,IAAIA;AACd;AACA,UAAAA,KAAI;AAAA,QACR;AAAA,MACJ;AAGA,WAAK,aAAa,SAASP,EAAC,IAAIO;AAChC,eAASA,EAAC,IAAI,SAASR,EAAC,IAAIC;AAC5B,eAASA,EAAC,IAAID;AAGd,eAAS,KAAK,SAASG,IAAG,CAAC,CAAC,IAAIF;AAChC,eAAS,KAAK,SAAS,OAAO,IAAIO,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,CAAC,CAAC,IAAIA;AAAA,IAChE;AAEA,SAAK,OAAO,IAAI,YAAY,QAAQ;AACpC,aAASP,KAAI,GAAGO,KAAI,KAAK,YAAYP,KAAI,UAAUA,MAAK;AACpD,WAAK,KAAKA,EAAC,IAAIO;AACf,MAAAA,KAAI,SAASA,EAAC;AAAA,IAClB;AAGA,SAAK,YAAY,KAAK,WAAW,SAAS,GAAG,KAAK,YAAY;AAC9D,SAAK,YAAY,KAAK,WAAW,SAAS,GAAG,KAAK,YAAY;AAAA,EAClE;AAAA,EAEA,SAASL,IAAG,GAAG;AACX,WAAO,KAAK,MAAM,YAAYA,KAAI,KAAK,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,SAAS,IAAI,KAAK;AAAA,EACvF;AAAA,EAEA,UAAUO,IAAG;AACT,UAAM,EAAC,YAAY,WAAW,YAAY,WAAW,OAAM,IAAI;AAE/D,QAAIT,KAAI;AACR,QAAI,KAAK;AAGT,WAAO,MAAM;AACT,YAAMU,KAAI,UAAUD,EAAC;AAiBrB,YAAM,KAAKA,KAAIA,KAAI;AACnB,WAAK,MAAMA,KAAI,KAAK;AAEpB,UAAIC,OAAM,IAAI;AACV,YAAIV,OAAM;AAAG;AACb,QAAAS,KAAI,WAAW,EAAET,EAAC;AAClB;AAAA,MACJ;AAEA,YAAM,KAAKU,KAAIA,KAAI;AACnB,YAAM,KAAK,MAAMD,KAAI,KAAK;AAC1B,YAAM,KAAK,MAAMC,KAAI,KAAK;AAE1B,YAAM,KAAK,UAAU,EAAE;AACvB,YAAMC,MAAK,UAAUF,EAAC;AACtB,YAAM,KAAK,UAAU,EAAE;AACvB,YAAM,KAAK,UAAU,EAAE;AAEvB,YAAM,UAAU;AAAA,QACZ,OAAO,IAAI,EAAE;AAAA,QAAG,OAAO,IAAI,KAAK,CAAC;AAAA,QACjC,OAAO,IAAIE,GAAE;AAAA,QAAG,OAAO,IAAIA,MAAK,CAAC;AAAA,QACjC,OAAO,IAAI,EAAE;AAAA,QAAG,OAAO,IAAI,KAAK,CAAC;AAAA,QACjC,OAAO,IAAI,EAAE;AAAA,QAAG,OAAO,IAAI,KAAK,CAAC;AAAA,MAAC;AAEtC,UAAI,SAAS;AACT,kBAAUF,EAAC,IAAI;AACf,kBAAUC,EAAC,IAAI;AAEf,cAAM,MAAM,UAAU,EAAE;AAGxB,YAAI,QAAQ,IAAI;AACZ,cAAIH,KAAI,KAAK;AACb,aAAG;AACC,gBAAI,KAAK,SAASA,EAAC,MAAM,IAAI;AACzB,mBAAK,SAASA,EAAC,IAAIE;AACnB;AAAA,YACJ;AACA,YAAAF,KAAI,KAAK,UAAUA,EAAC;AAAA,UACxB,SAASA,OAAM,KAAK;AAAA,QACxB;AACA,aAAK,MAAME,IAAG,GAAG;AACjB,aAAK,MAAMC,IAAG,UAAU,EAAE,CAAC;AAC3B,aAAK,MAAM,IAAI,EAAE;AAEjB,cAAM,KAAK,MAAMA,KAAI,KAAK;AAG1B,YAAIV,KAAI,WAAW,QAAQ;AACvB,qBAAWA,IAAG,IAAI;AAAA,QACtB;AAAA,MACJ,OAAO;AACH,YAAIA,OAAM;AAAG;AACb,QAAAS,KAAI,WAAW,EAAET,EAAC;AAAA,MACtB;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,MAAMS,IAAGC,IAAG;AACR,SAAK,WAAWD,EAAC,IAAIC;AACrB,QAAIA,OAAM;AAAI,WAAK,WAAWA,EAAC,IAAID;AAAA,EACvC;AAAA;AAAA,EAGA,aAAa,IAAI,IAAIN,KAAIM,IAAGC,IAAG,GAAG;AAC9B,UAAMF,KAAI,KAAK;AAEf,SAAK,WAAWA,EAAC,IAAI;AACrB,SAAK,WAAWA,KAAI,CAAC,IAAI;AACzB,SAAK,WAAWA,KAAI,CAAC,IAAIL;AAEzB,SAAK,MAAMK,IAAGC,EAAC;AACf,SAAK,MAAMD,KAAI,GAAGE,EAAC;AACnB,SAAK,MAAMF,KAAI,GAAG,CAAC;AAEnB,SAAK,gBAAgB;AAErB,WAAOA;AAAA,EACX;AACJ;AAGA,SAAS,YAAY,IAAI,IAAI;AACzB,QAAMP,KAAI,MAAM,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AAC1C,UAAQ,KAAK,IAAI,IAAIA,KAAI,IAAIA,MAAK;AACtC;AAEA,SAAS,KAAK,IAAI,IAAI,IAAI,IAAI;AAC1B,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,SAAO,KAAK,KAAK,KAAK;AAC1B;AAGA,SAAS,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC1C,QAAMW,MAAK,KAAK,OAAO,KAAK;AAC5B,QAAMR,MAAK,KAAK,OAAO,KAAK;AAC5B,SAAO,KAAK,IAAIQ,KAAIR,EAAC,KAAK,wBAAyB,KAAK,IAAIQ,KAAIR,EAAC,IAAIQ,KAAIR,KAAI;AACjF;AAGA,SAAS,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACpC,QAAM,OAAO,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,KAChD,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,KACnC,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AACnC,SAAO,OAAO;AAClB;AAEA,SAAS,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC9C,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAEhB,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,KAAK,KAAK,KAAK,KAAK;AAE1B,SAAO,MAAM,KAAK,KAAK,KAAK,MACrB,MAAM,KAAK,KAAK,KAAK,MACrB,MAAM,KAAK,KAAK,KAAK,MAAM;AACtC;AAEA,SAAS,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC1C,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAEhB,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,IAAI,OAAO,KAAK,KAAK,KAAK;AAEhC,QAAMF,MAAK,KAAK,KAAK,KAAK,MAAM;AAChC,QAAM,KAAK,KAAK,KAAK,KAAK,MAAM;AAEhC,SAAOA,KAAIA,KAAI,IAAI;AACvB;AAEA,SAAS,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC1C,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAEhB,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,IAAI,OAAO,KAAK,KAAK,KAAK;AAEhC,QAAMA,KAAI,MAAM,KAAK,KAAK,KAAK,MAAM;AACrC,QAAM,IAAI,MAAM,KAAK,KAAK,KAAK,MAAM;AAErC,SAAO,EAAC,GAAAA,IAAG,EAAC;AAChB;AAEA,SAAS,UAAU,KAAK,OAAO,MAAM,OAAO;AACxC,MAAI,QAAQ,QAAQ,IAAI;AACpB,aAASF,KAAI,OAAO,GAAGA,MAAK,OAAOA,MAAK;AACpC,YAAM,OAAO,IAAIA,EAAC;AAClB,YAAM,WAAW,MAAM,IAAI;AAC3B,UAAIK,KAAIL,KAAI;AACZ,aAAOK,MAAK,QAAQ,MAAM,IAAIA,EAAC,CAAC,IAAI;AAAU,YAAIA,KAAI,CAAC,IAAI,IAAIA,IAAG;AAClE,UAAIA,KAAI,CAAC,IAAI;AAAA,IACjB;AAAA,EACJ,OAAO;AACH,UAAM,SAAU,OAAO,SAAU;AACjC,QAAIL,KAAI,OAAO;AACf,QAAIK,KAAI;AACR,SAAK,KAAK,QAAQL,EAAC;AACnB,QAAI,MAAM,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,KAAK,CAAC;AAAG,WAAK,KAAK,MAAM,KAAK;AAC/D,QAAI,MAAM,IAAIA,EAAC,CAAC,IAAI,MAAM,IAAI,KAAK,CAAC;AAAG,WAAK,KAAKA,IAAG,KAAK;AACzD,QAAI,MAAM,IAAI,IAAI,CAAC,IAAI,MAAM,IAAIA,EAAC,CAAC;AAAG,WAAK,KAAK,MAAMA,EAAC;AAEvD,UAAM,OAAO,IAAIA,EAAC;AAClB,UAAM,WAAW,MAAM,IAAI;AAC3B,WAAO,MAAM;AACT;AAAG,QAAAA;AAAA,aAAY,MAAM,IAAIA,EAAC,CAAC,IAAI;AAC/B;AAAG,QAAAK;AAAA,aAAY,MAAM,IAAIA,EAAC,CAAC,IAAI;AAC/B,UAAIA,KAAIL;AAAG;AACX,WAAK,KAAKA,IAAGK,EAAC;AAAA,IAClB;AACA,QAAI,OAAO,CAAC,IAAI,IAAIA,EAAC;AACrB,QAAIA,EAAC,IAAI;AAET,QAAI,QAAQL,KAAI,KAAKK,KAAI,MAAM;AAC3B,gBAAU,KAAK,OAAOL,IAAG,KAAK;AAC9B,gBAAU,KAAK,OAAO,MAAMK,KAAI,CAAC;AAAA,IACrC,OAAO;AACH,gBAAU,KAAK,OAAO,MAAMA,KAAI,CAAC;AACjC,gBAAU,KAAK,OAAOL,IAAG,KAAK;AAAA,IAClC;AAAA,EACJ;AACJ;AAEA,SAAS,KAAK,KAAKA,IAAGK,IAAG;AACrB,QAAM,MAAM,IAAIL,EAAC;AACjB,MAAIA,EAAC,IAAI,IAAIK,EAAC;AACd,MAAIA,EAAC,IAAI;AACb;AAEA,SAAS,YAAYJ,IAAG;AACpB,SAAOA,GAAE,CAAC;AACd;AACA,SAAS,YAAYA,IAAG;AACpB,SAAOA,GAAE,CAAC;AACd;;;AC9eA,IAAM,UAAU;AAEhB,IAAqB,OAArB,MAA0B;AAAA,EACxB,cAAc;AACZ,SAAK,MAAM,KAAK;AAAA,IAChB,KAAK,MAAM,KAAK,MAAM;AACtB,SAAK,IAAI;AAAA,EACX;AAAA,EACA,OAAOY,IAAG,GAAG;AACX,SAAK,KAAK,IAAI,KAAK,MAAM,KAAK,MAAM,CAACA,EAAC,IAAI,KAAK,MAAM,KAAK,MAAM,CAAC,CAAC;AAAA,EACpE;AAAA,EACA,YAAY;AACV,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK;AACrC,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,OAAOA,IAAG,GAAG;AACX,SAAK,KAAK,IAAI,KAAK,MAAM,CAACA,EAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,EAC9C;AAAA,EACA,IAAIA,IAAG,GAAGC,IAAG;AACX,IAAAD,KAAI,CAACA,IAAG,IAAI,CAAC,GAAGC,KAAI,CAACA;AACrB,UAAM,KAAKD,KAAIC;AACf,UAAM,KAAK;AACX,QAAIA,KAAI;AAAG,YAAM,IAAI,MAAM,iBAAiB;AAC5C,QAAI,KAAK,QAAQ;AAAM,WAAK,KAAK,IAAI,EAAE,IAAI,EAAE;AAAA,aACpC,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI,WAAW,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI;AAAS,WAAK,KAAK,MAAM,KAAK,MAAM;AAC5G,QAAI,CAACA;AAAG;AACR,SAAK,KAAK,IAAIA,EAAC,IAAIA,EAAC,UAAUD,KAAIC,EAAC,IAAI,CAAC,IAAIA,EAAC,IAAIA,EAAC,UAAU,KAAK,MAAM,EAAE,IAAI,KAAK,MAAM,EAAE;AAAA,EAC5F;AAAA,EACA,KAAKD,IAAG,GAAGE,IAAG,GAAG;AACf,SAAK,KAAK,IAAI,KAAK,MAAM,KAAK,MAAM,CAACF,EAAC,IAAI,KAAK,MAAM,KAAK,MAAM,CAAC,CAAC,IAAI,CAACE,EAAC,IAAI,CAAC,CAAC,IAAI,CAACA,EAAC;AAAA,EACtF;AAAA,EACA,QAAQ;AACN,WAAO,KAAK,KAAK;AAAA,EACnB;AACF;;;ACpCA,IAAqB,UAArB,MAA6B;AAAA,EAC3B,cAAc;AACZ,SAAK,IAAI,CAAC;AAAA,EACZ;AAAA,EACA,OAAOC,IAAG,GAAG;AACX,SAAK,EAAE,KAAK,CAACA,IAAG,CAAC,CAAC;AAAA,EACpB;AAAA,EACA,YAAY;AACV,SAAK,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC;AAAA,EAC/B;AAAA,EACA,OAAOA,IAAG,GAAG;AACX,SAAK,EAAE,KAAK,CAACA,IAAG,CAAC,CAAC;AAAA,EACpB;AAAA,EACA,QAAQ;AACN,WAAO,KAAK,EAAE,SAAS,KAAK,IAAI;AAAA,EAClC;AACF;;;ACbA,IAAqB,UAArB,MAA6B;AAAA,EAC3B,YAAY,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG,GAAG;AACjE,QAAI,GAAG,OAAO,CAAC,UAAU,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,OAAO,CAAC;AAAQ,YAAM,IAAI,MAAM,gBAAgB;AAChH,SAAK,WAAW;AAChB,SAAK,iBAAiB,IAAI,aAAa,SAAS,OAAO,SAAS,CAAC;AACjE,SAAK,UAAU,IAAI,aAAa,SAAS,OAAO,SAAS,CAAC;AAC1D,SAAK,OAAO,MAAM,KAAK,OAAO;AAC9B,SAAK,OAAO,MAAM,KAAK,OAAO;AAC9B,SAAK,MAAM;AAAA,EACb;AAAA,EACA,SAAS;AACP,SAAK,SAAS,OAAO;AACrB,SAAK,MAAM;AACX,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,UAAM,EAAC,UAAU,EAAC,QAAQ,MAAM,UAAS,GAAG,QAAO,IAAI;AAGvD,UAAM,gBAAgB,KAAK,gBAAgB,KAAK,eAAe,SAAS,GAAG,UAAU,SAAS,IAAI,CAAC;AACnG,aAASC,KAAI,GAAGC,KAAI,GAAGC,KAAI,UAAU,QAAQC,IAAG,GAAGH,KAAIE,IAAGF,MAAK,GAAGC,MAAK,GAAG;AACxE,YAAM,KAAK,UAAUD,EAAC,IAAI;AAC1B,YAAMI,MAAK,UAAUJ,KAAI,CAAC,IAAI;AAC9B,YAAM,KAAK,UAAUA,KAAI,CAAC,IAAI;AAC9B,YAAMK,MAAK,OAAO,EAAE;AACpB,YAAMC,MAAK,OAAO,KAAK,CAAC;AACxB,YAAMC,MAAK,OAAOH,GAAE;AACpB,YAAM,KAAK,OAAOA,MAAK,CAAC;AACxB,YAAM,KAAK,OAAO,EAAE;AACpB,YAAM,KAAK,OAAO,KAAK,CAAC;AAExB,YAAM,KAAKG,MAAKF;AAChB,YAAM,KAAK,KAAKC;AAChB,YAAM,KAAK,KAAKD;AAChB,YAAM,KAAK,KAAKC;AAChB,YAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,YAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,YAAM,MAAM,KAAK,KAAK,KAAK,MAAM;AAEjC,UAAI,CAAC,IAAI;AAEP,QAAAH,MAAKE,MAAK,MAAM,IAAI,MAAM;AAC1B,aAAKC,MAAK,MAAM,IAAI,MAAM;AAAA,MAC5B,WACS,KAAK,IAAI,EAAE,IAAI,MAAM;AAE5B,QAAAH,MAAKE,MAAK,MAAM;AAChB,aAAKC,MAAK,MAAM;AAAA,MAClB,OAAO;AACL,cAAM,IAAI,IAAI;AACd,QAAAH,KAAIE,OAAM,KAAK,KAAK,KAAK,MAAM;AAC/B,YAAIC,OAAM,KAAK,KAAK,KAAK,MAAM;AAAA,MACjC;AACA,oBAAcL,EAAC,IAAIE;AACnB,oBAAcF,KAAI,CAAC,IAAI;AAAA,IACzB;AAGA,QAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC5B,QAAI,IAAI,KAAK,IAAI;AACjB,QAAI,IAAI,KAAK,OAAO,IAAI,CAAC;AACzB,QAAI,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC;AAC7B,YAAQ,KAAK,CAAC;AACd,aAASD,KAAI,GAAGA,KAAI,KAAK,QAAQ,EAAEA,IAAG;AACpC,UAAI,KAAKA,EAAC;AACV,WAAK,IAAI,KAAK,IAAI,KAAK;AACvB,WAAK,IAAI,GAAG,KAAK,OAAO,IAAI,CAAC,GAAG,KAAK,OAAO,IAAI,IAAI,CAAC;AACrD,cAAQ,KAAK,CAAC,IAAI,QAAQ,EAAE,IAAI,KAAK;AACrC,cAAQ,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,KAAK;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO,SAAS;AACd,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,UAAU,EAAC,WAAW,SAAS,KAAI,GAAG,eAAe,QAAO,IAAI;AACvE,QAAI,KAAK,UAAU;AAAG,aAAO;AAC7B,aAASA,KAAI,GAAGE,KAAI,UAAU,QAAQF,KAAIE,IAAG,EAAEF,IAAG;AAChD,YAAMC,KAAI,UAAUD,EAAC;AACrB,UAAIC,KAAID;AAAG;AACX,YAAM,KAAK,KAAK,MAAMA,KAAI,CAAC,IAAI;AAC/B,YAAM,KAAK,KAAK,MAAMC,KAAI,CAAC,IAAI;AAC/B,YAAM,KAAK,cAAc,EAAE;AAC3B,YAAM,KAAK,cAAc,KAAK,CAAC;AAC/B,YAAM,KAAK,cAAc,EAAE;AAC3B,YAAM,KAAK,cAAc,KAAK,CAAC;AAC/B,WAAK,eAAe,IAAI,IAAI,IAAI,IAAI,OAAO;AAAA,IAC7C;AACA,QAAI,IAAI,KAAK,KAAK,KAAK,SAAS,CAAC;AACjC,aAASD,KAAI,GAAGA,KAAI,KAAK,QAAQ,EAAEA,IAAG;AACpC,WAAK,IAAI,KAAK,KAAKA,EAAC;AACpB,YAAMQ,KAAI,KAAK,MAAM,QAAQ,EAAE,IAAI,CAAC,IAAI;AACxC,YAAML,KAAI,cAAcK,EAAC;AACzB,YAAM,IAAI,cAAcA,KAAI,CAAC;AAC7B,YAAMC,KAAI,KAAK;AACf,YAAMC,KAAI,KAAK,SAASP,IAAG,GAAG,QAAQM,KAAI,CAAC,GAAG,QAAQA,KAAI,CAAC,CAAC;AAC5D,UAAIC;AAAG,aAAK,eAAeP,IAAG,GAAGO,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAG,OAAO;AAAA,IACtD;AACA,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,aAAa,SAAS;AACpB,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,YAAQ,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK,OAAO,KAAK,IAAI;AAC/E,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,WAAWV,IAAG,SAAS;AACrB,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,SAAS,KAAK,MAAMA,EAAC;AAC3B,QAAI,WAAW,QAAQ,CAAC,OAAO;AAAQ;AACvC,YAAQ,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AACnC,QAAIE,KAAI,OAAO;AACf,WAAO,OAAO,CAAC,MAAM,OAAOA,KAAE,CAAC,KAAK,OAAO,CAAC,MAAM,OAAOA,KAAE,CAAC,KAAKA,KAAI;AAAG,MAAAA,MAAK;AAC7E,aAASF,KAAI,GAAGA,KAAIE,IAAGF,MAAK,GAAG;AAC7B,UAAI,OAAOA,EAAC,MAAM,OAAOA,KAAE,CAAC,KAAK,OAAOA,KAAE,CAAC,MAAM,OAAOA,KAAE,CAAC;AACzD,gBAAQ,OAAO,OAAOA,EAAC,GAAG,OAAOA,KAAI,CAAC,CAAC;AAAA,IAC3C;AACA,YAAQ,UAAU;AAClB,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,CAAC,eAAe;AACd,UAAM,EAAC,UAAU,EAAC,OAAM,EAAC,IAAI;AAC7B,aAASA,KAAI,GAAGE,KAAI,OAAO,SAAS,GAAGF,KAAIE,IAAG,EAAEF,IAAG;AACjD,YAAM,OAAO,KAAK,YAAYA,EAAC;AAC/B,UAAI;AAAM,aAAK,QAAQA,IAAG,MAAM;AAAA,IAClC;AAAA,EACF;AAAA,EACA,YAAYA,IAAG;AACb,UAAM,UAAU,IAAI;AACpB,SAAK,WAAWA,IAAG,OAAO;AAC1B,WAAO,QAAQ,MAAM;AAAA,EACvB;AAAA,EACA,eAAe,IAAI,IAAI,IAAI,IAAI,SAAS;AACtC,QAAI;AACJ,UAAM,KAAK,KAAK,YAAY,IAAI,EAAE;AAClC,UAAM,KAAK,KAAK,YAAY,IAAI,EAAE;AAClC,QAAI,OAAO,KAAK,OAAO,GAAG;AACxB,cAAQ,OAAO,IAAI,EAAE;AACrB,cAAQ,OAAO,IAAI,EAAE;AAAA,IACvB,WAAW,IAAI,KAAK,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AACxD,cAAQ,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACzB,cAAQ,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,SAASA,IAAGG,IAAG,GAAG;AAChB,SAAKA,KAAI,CAACA,IAAGA,OAAMA,QAAO,IAAI,CAAC,GAAG,MAAM;AAAI,aAAO;AACnD,WAAO,KAAK,SAAS,MAAMH,IAAGG,IAAG,CAAC,MAAMH;AAAA,EAC1C;AAAA,EACA,CAAC,UAAUA,IAAG;AACZ,UAAM,KAAK,KAAK,MAAMA,EAAC;AACvB,QAAI;AAAI,iBAAWC,MAAK,KAAK,SAAS,UAAUD,EAAC,GAAG;AAClD,cAAM,KAAK,KAAK,MAAMC,EAAC;AAEvB,YAAI;AAAI;AAAM,qBAAS,KAAK,GAAG,KAAK,GAAG,QAAQ,KAAK,IAAI,MAAM,GAAG;AAC/D,uBAAS,KAAK,GAAG,KAAK,GAAG,QAAQ,KAAK,IAAI,MAAM,GAAG;AACjD,oBAAI,GAAG,EAAE,KAAK,GAAG,EAAE,KAChB,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KACvB,IAAI,KAAK,KAAK,EAAE,KAAK,IAAI,KAAK,KAAK,KAAK,EAAE,KAC1C,IAAI,KAAK,KAAK,EAAE,KAAK,IAAI,KAAK,KAAK,KAAK,EAAE,GAC3C;AACA,wBAAMA;AACN,wBAAM;AAAA,gBACR;AAAA,cACF;AAAA,YACF;AAAA,MACF;AAAA,EACF;AAAA,EACA,MAAMD,IAAG;AACP,UAAM,EAAC,eAAe,UAAU,EAAC,SAAS,WAAW,UAAS,EAAC,IAAI;AACnE,UAAM,KAAK,QAAQA,EAAC;AACpB,QAAI,OAAO;AAAI,aAAO;AACtB,UAAM,SAAS,CAAC;AAChB,QAAIW,KAAI;AACR,OAAG;AACD,YAAMH,KAAI,KAAK,MAAMG,KAAI,CAAC;AAC1B,aAAO,KAAK,cAAcH,KAAI,CAAC,GAAG,cAAcA,KAAI,IAAI,CAAC,CAAC;AAC1D,MAAAG,KAAIA,KAAI,MAAM,IAAIA,KAAI,IAAIA,KAAI;AAC9B,UAAI,UAAUA,EAAC,MAAMX;AAAG;AACxB,MAAAW,KAAI,UAAUA,EAAC;AAAA,IACjB,SAASA,OAAM,MAAMA,OAAM;AAC3B,WAAO;AAAA,EACT;AAAA,EACA,MAAMX,IAAG;AAEP,QAAIA,OAAM,KAAK,KAAK,SAAS,KAAK,WAAW,GAAG;AAC9C,aAAO,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AAAA,IAChG;AACA,UAAM,SAAS,KAAK,MAAMA,EAAC;AAC3B,QAAI,WAAW;AAAM,aAAO;AAC5B,UAAM,EAAC,SAAS,EAAC,IAAI;AACrB,UAAMS,KAAIT,KAAI;AACd,WAAO,EAAES,EAAC,KAAK,EAAEA,KAAI,CAAC,IAChB,KAAK,cAAcT,IAAG,QAAQ,EAAES,EAAC,GAAG,EAAEA,KAAI,CAAC,GAAG,EAAEA,KAAI,CAAC,GAAG,EAAEA,KAAI,CAAC,CAAC,IAChE,KAAK,YAAYT,IAAG,MAAM;AAAA,EAClC;AAAA,EACA,YAAYA,IAAG,QAAQ;AACrB,UAAME,KAAI,OAAO;AACjB,QAAIU,KAAI;AACR,QAAI,IAAI,IAAI,KAAK,OAAOV,KAAI,CAAC,GAAG,KAAK,OAAOA,KAAI,CAAC;AACjD,QAAI,IAAI,KAAK,KAAK,YAAY,IAAI,EAAE;AACpC,QAAI,IAAI;AACR,aAASD,KAAI,GAAGA,KAAIC,IAAGD,MAAK,GAAG;AAC7B,WAAK,IAAI,KAAK,IAAI,KAAK,OAAOA,EAAC,GAAG,KAAK,OAAOA,KAAI,CAAC;AACnD,WAAK,IAAI,KAAK,KAAK,YAAY,IAAI,EAAE;AACrC,UAAI,OAAO,KAAK,OAAO,GAAG;AACxB,aAAK,IAAI,KAAK;AACd,YAAIW;AAAG,UAAAA,GAAE,KAAK,IAAI,EAAE;AAAA;AACf,UAAAA,KAAI,CAAC,IAAI,EAAE;AAAA,MAClB,OAAO;AACL,YAAI,GAAG,KAAK,KAAK,KAAK;AACtB,YAAI,OAAO,GAAG;AACZ,eAAK,IAAI,KAAK,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,OAAO;AAAM;AAC9D,WAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAAA,QACzB,OAAO;AACL,eAAK,IAAI,KAAK,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,OAAO;AAAM;AAC9D,WAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AACvB,eAAK,IAAI,KAAK,KAAK,UAAU,KAAK,GAAG;AACrC,cAAI,MAAM;AAAI,iBAAK,MAAMZ,IAAG,IAAI,IAAIY,IAAGA,GAAE,MAAM;AAC/C,cAAIA;AAAG,YAAAA,GAAE,KAAK,KAAK,GAAG;AAAA;AACjB,YAAAA,KAAI,CAAC,KAAK,GAAG;AAAA,QACpB;AACA,aAAK,IAAI,KAAK,KAAK,UAAU,KAAK,GAAG;AACrC,YAAI,MAAM;AAAI,eAAK,MAAMZ,IAAG,IAAI,IAAIY,IAAGA,GAAE,MAAM;AAC/C,YAAIA;AAAG,UAAAA,GAAE,KAAK,KAAK,GAAG;AAAA;AACjB,UAAAA,KAAI,CAAC,KAAK,GAAG;AAAA,MACpB;AAAA,IACF;AACA,QAAIA,IAAG;AACL,WAAK,IAAI,KAAK,KAAK,UAAUA,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AACvC,UAAI,MAAM;AAAI,aAAK,MAAMZ,IAAG,IAAI,IAAIY,IAAGA,GAAE,MAAM;AAAA,IACjD,WAAW,KAAK,SAASZ,KAAI,KAAK,OAAO,KAAK,QAAQ,IAAI,KAAK,OAAO,KAAK,QAAQ,CAAC,GAAG;AACrF,aAAO,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AAAA,IAChG;AACA,WAAOY;AAAA,EACT;AAAA,EACA,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACnC,WAAO,MAAM;AACX,UAAI,OAAO,KAAK,OAAO;AAAG,eAAO,CAAC,IAAI,IAAI,IAAI,EAAE;AAChD,UAAI,KAAK;AAAI,eAAO;AACpB,UAAIT,IAAG,GAAG,IAAI,MAAM;AACpB,UAAI,IAAI;AAAQ,QAAAA,KAAI,MAAM,KAAK,OAAO,KAAK,OAAO,OAAO,KAAK,KAAK,IAAI,KAAK;AAAA,eACnE,IAAI;AAAQ,QAAAA,KAAI,MAAM,KAAK,OAAO,KAAK,OAAO,OAAO,KAAK,KAAK,IAAI,KAAK;AAAA,eACxE,IAAI;AAAQ,YAAI,MAAM,KAAK,OAAO,KAAK,OAAO,OAAO,KAAK,KAAKA,KAAI,KAAK;AAAA;AAC5E,YAAI,MAAM,KAAK,OAAO,KAAK,OAAO,OAAO,KAAK,KAAKA,KAAI,KAAK;AACjE,UAAI;AAAI,aAAKA,IAAG,KAAK,GAAG,KAAK,KAAK,YAAY,IAAI,EAAE;AAAA;AAC/C,aAAKA,IAAG,KAAK,GAAG,KAAK,KAAK,YAAY,IAAI,EAAE;AAAA,IACnD;AAAA,EACF;AAAA,EACA,cAAcH,IAAG,QAAQ,KAAK,KAAK,KAAK,KAAK;AAC3C,QAAIY,KAAI,MAAM,KAAK,MAAM,GAAGF;AAC5B,QAAIA,KAAI,KAAK,SAASE,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAG,KAAK,GAAG;AAAG,MAAAA,GAAE,QAAQF,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AACjE,QAAIA,KAAI,KAAK,SAASE,GAAEA,GAAE,SAAS,CAAC,GAAGA,GAAEA,GAAE,SAAS,CAAC,GAAG,KAAK,GAAG;AAAG,MAAAA,GAAE,KAAKF,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AACpF,QAAIE,KAAI,KAAK,YAAYZ,IAAGY,EAAC,GAAG;AAC9B,eAASX,KAAI,GAAGC,KAAIU,GAAE,QAAQ,IAAI,KAAK,KAAK,UAAUA,GAAEV,KAAI,CAAC,GAAGU,GAAEV,KAAI,CAAC,CAAC,GAAGD,KAAIC,IAAGD,MAAK,GAAG;AACxF,aAAK,IAAI,KAAK,KAAK,UAAUW,GAAEX,EAAC,GAAGW,GAAEX,KAAI,CAAC,CAAC;AAC3C,YAAI,MAAM;AAAI,UAAAA,KAAI,KAAK,MAAMD,IAAG,IAAI,IAAIY,IAAGX,EAAC,GAAGC,KAAIU,GAAE;AAAA,MACvD;AAAA,IACF,WAAW,KAAK,SAASZ,KAAI,KAAK,OAAO,KAAK,QAAQ,IAAI,KAAK,OAAO,KAAK,QAAQ,CAAC,GAAG;AACrF,MAAAY,KAAI,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AAAA,IAC7F;AACA,WAAOA;AAAA,EACT;AAAA,EACA,MAAMZ,IAAG,IAAI,IAAIY,IAAGX,IAAG;AACrB,WAAO,OAAO,IAAI;AAChB,UAAIE,IAAG;AACP,cAAQ,IAAI;AAAA,QACV,KAAK;AAAQ,eAAK;AAAQ;AAAA,QAC1B,KAAK;AAAQ,eAAK,GAAQA,KAAI,KAAK,MAAM,IAAI,KAAK;AAAM;AAAA,QACxD,KAAK;AAAQ,eAAK;AAAQ;AAAA,QAC1B,KAAK;AAAQ,eAAK,IAAQA,KAAI,KAAK,MAAM,IAAI,KAAK;AAAM;AAAA,QACxD,KAAK;AAAQ,eAAK;AAAQ;AAAA,QAC1B,KAAK;AAAQ,eAAK,GAAQA,KAAI,KAAK,MAAM,IAAI,KAAK;AAAM;AAAA,QACxD,KAAK;AAAQ,eAAK;AAAQ;AAAA,QAC1B,KAAK;AAAQ,eAAK,GAAQA,KAAI,KAAK,MAAM,IAAI,KAAK;AAAM;AAAA,MAC1D;AACA,WAAKS,GAAEX,EAAC,MAAME,MAAKS,GAAEX,KAAI,CAAC,MAAM,MAAM,KAAK,SAASD,IAAGG,IAAG,CAAC,GAAG;AAC5D,QAAAS,GAAE,OAAOX,IAAG,GAAGE,IAAG,CAAC,GAAGF,MAAK;AAAA,MAC7B;AAAA,IACF;AACA,QAAIW,GAAE,SAAS,GAAG;AAChB,eAASZ,KAAI,GAAGA,KAAIY,GAAE,QAAQZ,MAAI,GAAG;AACnC,cAAMC,MAAKD,KAAI,KAAKY,GAAE,QAAQC,MAAKb,KAAI,KAAKY,GAAE;AAC9C,YAAIA,GAAEZ,EAAC,MAAMY,GAAEX,EAAC,KAAKW,GAAEX,EAAC,MAAMW,GAAEC,EAAC,KAC9BD,GAAEZ,KAAI,CAAC,MAAMY,GAAEX,KAAI,CAAC,KAAKW,GAAEX,KAAI,CAAC,MAAMW,GAAEC,KAAI,CAAC;AAC9C,UAAAD,GAAE,OAAOX,IAAG,CAAC,GAAGD,MAAK;AAAA,MACzB;AAAA,IACF;AACA,WAAOC;AAAA,EACT;AAAA,EACA,SAAS,IAAI,IAAI,IAAI,IAAI;AACvB,QAAIO,KAAI,UAAU,GAAGL,IAAG;AACxB,QAAI,KAAK,GAAG;AACV,UAAI,MAAM,KAAK;AAAM,eAAO;AAC5B,WAAK,KAAK,KAAK,OAAO,MAAM,MAAMK;AAAG,YAAI,KAAK,MAAML,KAAI,MAAMK,KAAI,KAAK;AAAA,IACzE,WAAW,KAAK,GAAG;AACjB,UAAI,MAAM,KAAK;AAAM,eAAO;AAC5B,WAAK,KAAK,KAAK,OAAO,MAAM,MAAMA;AAAG,YAAI,KAAK,MAAML,KAAI,MAAMK,KAAI,KAAK;AAAA,IACzE;AACA,QAAI,KAAK,GAAG;AACV,UAAI,MAAM,KAAK;AAAM,eAAO;AAC5B,WAAK,KAAK,KAAK,OAAO,MAAM,MAAMA;AAAG,QAAAL,KAAI,KAAK,MAAM,IAAI,MAAMK,KAAI,KAAK;AAAA,IACzE,WAAW,KAAK,GAAG;AACjB,UAAI,MAAM,KAAK;AAAM,eAAO;AAC5B,WAAK,KAAK,KAAK,OAAO,MAAM,MAAMA;AAAG,QAAAL,KAAI,KAAK,MAAM,IAAI,MAAMK,KAAI,KAAK;AAAA,IACzE;AACA,WAAO,CAACL,IAAG,CAAC;AAAA,EACd;AAAA,EACA,UAAUA,IAAG,GAAG;AACd,YAAQA,OAAM,KAAK,OAAO,IACpBA,OAAM,KAAK,OAAO,IAAS,MAC1B,MAAM,KAAK,OAAO,IACnB,MAAM,KAAK,OAAO,IAAS;AAAA,EACnC;AAAA,EACA,YAAYA,IAAG,GAAG;AAChB,YAAQA,KAAI,KAAK,OAAO,IAClBA,KAAI,KAAK,OAAO,IAAS,MACxB,IAAI,KAAK,OAAO,IACjB,IAAI,KAAK,OAAO,IAAS;AAAA,EACjC;AACF;;;AC1TA,IAAM,MAAM,IAAI,KAAK;AAArB,IAAyB,MAAM,KAAK;AAEpC,SAAS,OAAOW,IAAG;AACjB,SAAOA,GAAE,CAAC;AACZ;AAEA,SAAS,OAAOA,IAAG;AACjB,SAAOA,GAAE,CAAC;AACZ;AAGA,SAAS,UAAU,GAAG;AACpB,QAAM,EAAC,WAAW,OAAM,IAAI;AAC5B,WAASC,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK,GAAG;AAC5C,UAAMC,KAAI,IAAI,UAAUD,EAAC,GACnBE,KAAI,IAAI,UAAUF,KAAI,CAAC,GACvB,IAAI,IAAI,UAAUA,KAAI,CAAC,GACvB,SAAS,OAAO,CAAC,IAAI,OAAOC,EAAC,MAAM,OAAOC,KAAI,CAAC,IAAI,OAAOD,KAAI,CAAC,MACtD,OAAOC,EAAC,IAAI,OAAOD,EAAC,MAAM,OAAO,IAAI,CAAC,IAAI,OAAOA,KAAI,CAAC;AACrE,QAAI,QAAQ;AAAO,aAAO;AAAA,EAC5B;AACA,SAAO;AACT;AAEA,SAAS,OAAOE,IAAG,GAAGC,IAAG;AACvB,SAAO,CAACD,KAAI,KAAK,IAAIA,KAAI,CAAC,IAAIC,IAAG,IAAI,KAAK,IAAID,KAAI,CAAC,IAAIC,EAAC;AAC1D;AAEA,IAAqB,WAArB,MAAqB,UAAS;AAAA,EAC5B,OAAO,KAAK,QAAQ,KAAK,QAAQ,KAAK,QAAQ,MAAM;AAClD,WAAO,IAAI,UAAS,YAAY,SAC1B,UAAU,QAAQ,IAAI,IAAI,IAAI,IAC9B,aAAa,KAAK,aAAa,QAAQ,IAAI,IAAI,IAAI,CAAC,CAAC;AAAA,EAC7D;AAAA,EACA,YAAY,QAAQ;AAClB,SAAK,cAAc,IAAI,WAAW,MAAM;AACxC,SAAK,UAAU,IAAI,WAAW,OAAO,SAAS,CAAC;AAC/C,SAAK,aAAa,IAAI,WAAW,OAAO,SAAS,CAAC;AAClD,SAAK,SAAS,KAAK,YAAY;AAC/B,SAAK,MAAM;AAAA,EACb;AAAA,EACA,SAAS;AACP,SAAK,YAAY,OAAO;AACxB,SAAK,MAAM;AACX,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,UAAM,IAAI,KAAK,aAAa,SAAS,KAAK;AAG1C,QAAI,EAAE,QAAQ,EAAE,KAAK,SAAS,KAAK,UAAU,CAAC,GAAG;AAC/C,WAAK,YAAY,WAAW,KAAK,EAAC,QAAQ,OAAO,SAAO,EAAC,GAAG,CAAC,GAAEJ,OAAMA,EAAC,EACnE,KAAK,CAACA,IAAGK,OAAM,OAAO,IAAIL,EAAC,IAAI,OAAO,IAAIK,EAAC,KAAK,OAAO,IAAIL,KAAI,CAAC,IAAI,OAAO,IAAIK,KAAI,CAAC,CAAC;AACxF,YAAMC,KAAI,KAAK,UAAU,CAAC,GAAGC,KAAI,KAAK,UAAU,KAAK,UAAU,SAAS,CAAC,GACvE,SAAS,CAAE,OAAO,IAAID,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,GAAG,OAAO,IAAIC,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,CAAE,GAC9EH,KAAI,OAAO,KAAK,MAAM,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC;AACpE,eAASJ,KAAI,GAAGQ,KAAI,OAAO,SAAS,GAAGR,KAAIQ,IAAG,EAAER,IAAG;AACjD,cAAMD,KAAI,OAAO,OAAO,IAAIC,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,GAAGI,EAAC;AACpD,eAAO,IAAIJ,EAAC,IAAID,GAAE,CAAC;AACnB,eAAO,IAAIC,KAAI,CAAC,IAAID,GAAE,CAAC;AAAA,MACzB;AACA,WAAK,cAAc,IAAI,WAAW,MAAM;AAAA,IAC1C,OAAO;AACL,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,YAAY,KAAK,YAAY,KAAK,YAAY;AACpD,UAAM,OAAO,KAAK,OAAO,KAAK,YAAY;AAC1C,UAAM,YAAY,KAAK,YAAY,KAAK,YAAY;AACpD,UAAM,UAAU,KAAK,QAAQ,KAAK,EAAE;AACpC,UAAM,YAAY,KAAK,WAAW,KAAK,EAAE;AAKzC,aAASO,KAAI,GAAGE,KAAI,UAAU,QAAQF,KAAIE,IAAG,EAAEF,IAAG;AAChD,YAAMP,KAAI,UAAUO,KAAI,MAAM,IAAIA,KAAI,IAAIA,KAAI,CAAC;AAC/C,UAAI,UAAUA,EAAC,MAAM,MAAM,QAAQP,EAAC,MAAM;AAAI,gBAAQA,EAAC,IAAIO;AAAA,IAC7D;AACA,aAASN,KAAI,GAAGQ,KAAI,KAAK,QAAQR,KAAIQ,IAAG,EAAER,IAAG;AAC3C,gBAAU,KAAKA,EAAC,CAAC,IAAIA;AAAA,IACvB;AAGA,QAAI,KAAK,UAAU,KAAK,KAAK,SAAS,GAAG;AACvC,WAAK,YAAY,IAAI,WAAW,CAAC,EAAE,KAAK,EAAE;AAC1C,WAAK,YAAY,IAAI,WAAW,CAAC,EAAE,KAAK,EAAE;AAC1C,WAAK,UAAU,CAAC,IAAI,KAAK,CAAC;AAC1B,WAAK,UAAU,CAAC,IAAI,KAAK,CAAC;AAC1B,WAAK,UAAU,CAAC,IAAI,KAAK,CAAC;AAC1B,cAAQ,KAAK,CAAC,CAAC,IAAI;AACnB,UAAI,KAAK,WAAW;AAAG,gBAAQ,KAAK,CAAC,CAAC,IAAI;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,QAAQ,QAAQ;AACd,WAAO,IAAI,QAAQ,MAAM,MAAM;AAAA,EACjC;AAAA,EACA,CAAC,UAAUA,IAAG;AACZ,UAAM,EAAC,SAAS,MAAM,YAAY,WAAW,WAAW,WAAAS,WAAS,IAAI;AAGrE,QAAIA,YAAW;AACb,YAAMC,KAAID,WAAU,QAAQT,EAAC;AAC7B,UAAIU,KAAI;AAAG,cAAMD,WAAUC,KAAI,CAAC;AAChC,UAAIA,KAAID,WAAU,SAAS;AAAG,cAAMA,WAAUC,KAAI,CAAC;AACnD;AAAA,IACF;AAEA,UAAM,KAAK,QAAQV,EAAC;AACpB,QAAI,OAAO;AAAI;AACf,QAAIM,KAAI,IAAI,KAAK;AACjB,OAAG;AACD,YAAM,KAAK,UAAUA,EAAC;AACtB,MAAAA,KAAIA,KAAI,MAAM,IAAIA,KAAI,IAAIA,KAAI;AAC9B,UAAI,UAAUA,EAAC,MAAMN;AAAG;AACxB,MAAAM,KAAI,UAAUA,EAAC;AACf,UAAIA,OAAM,IAAI;AACZ,cAAMP,KAAI,MAAM,WAAWC,EAAC,IAAI,KAAK,KAAK,MAAM;AAChD,YAAID,OAAM;AAAI,gBAAMA;AACpB;AAAA,MACF;AAAA,IACF,SAASO,OAAM;AAAA,EACjB;AAAA,EACA,KAAKH,IAAG,GAAGH,KAAI,GAAG;AAChB,SAAKG,KAAI,CAACA,IAAGA,OAAMA,QAAO,IAAI,CAAC,GAAG,MAAM;AAAI,aAAO;AACnD,UAAM,KAAKH;AACX,QAAI;AACJ,YAAQ,IAAI,KAAK,MAAMA,IAAGG,IAAG,CAAC,MAAM,KAAK,MAAMH,MAAK,MAAM;AAAI,MAAAA,KAAI;AAClE,WAAO;AAAA,EACT;AAAA,EACA,MAAMA,IAAGG,IAAG,GAAG;AACb,UAAM,EAAC,SAAS,MAAM,YAAY,WAAW,WAAW,OAAM,IAAI;AAClE,QAAI,QAAQH,EAAC,MAAM,MAAM,CAAC,OAAO;AAAQ,cAAQA,KAAI,MAAM,OAAO,UAAU;AAC5E,QAAI,IAAIA;AACR,QAAI,KAAK,IAAIG,KAAI,OAAOH,KAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,OAAOA,KAAI,IAAI,CAAC,GAAG,CAAC;AACjE,UAAM,KAAK,QAAQA,EAAC;AACpB,QAAIM,KAAI;AACR,OAAG;AACD,UAAIK,KAAI,UAAUL,EAAC;AACnB,YAAM,KAAK,IAAIH,KAAI,OAAOQ,KAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,OAAOA,KAAI,IAAI,CAAC,GAAG,CAAC;AACnE,UAAI,KAAK;AAAI,aAAK,IAAI,IAAIA;AAC1B,MAAAL,KAAIA,KAAI,MAAM,IAAIA,KAAI,IAAIA,KAAI;AAC9B,UAAI,UAAUA,EAAC,MAAMN;AAAG;AACxB,MAAAM,KAAI,UAAUA,EAAC;AACf,UAAIA,OAAM,IAAI;AACZ,QAAAA,KAAI,MAAM,WAAWN,EAAC,IAAI,KAAK,KAAK,MAAM;AAC1C,YAAIM,OAAMK,IAAG;AACX,cAAI,IAAIR,KAAI,OAAOG,KAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,OAAOA,KAAI,IAAI,CAAC,GAAG,CAAC,IAAI;AAAI,mBAAOA;AAAA,QAC7E;AACA;AAAA,MACF;AAAA,IACF,SAASA,OAAM;AACf,WAAO;AAAA,EACT;AAAA,EACA,OAAO,SAAS;AACd,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,QAAQ,WAAW,UAAS,IAAI;AACvC,aAASN,KAAI,GAAGQ,KAAI,UAAU,QAAQR,KAAIQ,IAAG,EAAER,IAAG;AAChD,YAAMK,KAAI,UAAUL,EAAC;AACrB,UAAIK,KAAIL;AAAG;AACX,YAAM,KAAK,UAAUA,EAAC,IAAI;AAC1B,YAAM,KAAK,UAAUK,EAAC,IAAI;AAC1B,cAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AACzC,cAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AAAA,IAC3C;AACA,SAAK,WAAW,OAAO;AACvB,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,aAAa,SAASD,KAAI,GAAG;AAC3B,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,OAAM,IAAI;AACjB,aAASJ,KAAI,GAAGQ,KAAI,OAAO,QAAQR,KAAIQ,IAAGR,MAAK,GAAG;AAChD,YAAMG,KAAI,OAAOH,EAAC,GAAG,IAAI,OAAOA,KAAI,CAAC;AACrC,cAAQ,OAAOG,KAAIC,IAAG,CAAC;AACvB,cAAQ,IAAID,IAAG,GAAGC,IAAG,GAAG,GAAG;AAAA,IAC7B;AACA,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,WAAW,SAAS;AAClB,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,MAAM,OAAM,IAAI;AACvB,UAAM,IAAI,KAAK,CAAC,IAAI,GAAGI,KAAI,KAAK;AAChC,YAAQ,OAAO,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AACvC,aAASR,KAAI,GAAGA,KAAIQ,IAAG,EAAER,IAAG;AAC1B,YAAMY,KAAI,IAAI,KAAKZ,EAAC;AACpB,cAAQ,OAAO,OAAOY,EAAC,GAAG,OAAOA,KAAI,CAAC,CAAC;AAAA,IACzC;AACA,YAAQ,UAAU;AAClB,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,UAAM,UAAU,IAAI;AACpB,SAAK,WAAW,OAAO;AACvB,WAAO,QAAQ,MAAM;AAAA,EACvB;AAAA,EACA,eAAeZ,IAAG,SAAS;AACzB,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,QAAQ,UAAS,IAAI;AAC5B,UAAM,KAAK,UAAUA,MAAK,CAAC,IAAI;AAC/B,UAAM,KAAK,UAAUA,KAAI,CAAC,IAAI;AAC9B,UAAMa,MAAK,UAAUb,KAAI,CAAC,IAAI;AAC9B,YAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AACzC,YAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AACzC,YAAQ,OAAO,OAAOa,GAAE,GAAG,OAAOA,MAAK,CAAC,CAAC;AACzC,YAAQ,UAAU;AAClB,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,CAAC,mBAAmB;AAClB,UAAM,EAAC,UAAS,IAAI;AACpB,aAASb,KAAI,GAAGQ,KAAI,UAAU,SAAS,GAAGR,KAAIQ,IAAG,EAAER,IAAG;AACpD,YAAM,KAAK,gBAAgBA,EAAC;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,gBAAgBA,IAAG;AACjB,UAAM,UAAU,IAAI;AACpB,SAAK,eAAeA,IAAG,OAAO;AAC9B,WAAO,QAAQ,MAAM;AAAA,EACvB;AACF;AAEA,SAAS,UAAU,QAAQ,IAAI,IAAI,MAAM;AACvC,QAAMQ,KAAI,OAAO;AACjB,QAAM,QAAQ,IAAI,aAAaA,KAAI,CAAC;AACpC,WAASR,KAAI,GAAGA,KAAIQ,IAAG,EAAER,IAAG;AAC1B,UAAMD,KAAI,OAAOC,EAAC;AAClB,UAAMA,KAAI,CAAC,IAAI,GAAG,KAAK,MAAMD,IAAGC,IAAG,MAAM;AACzC,UAAMA,KAAI,IAAI,CAAC,IAAI,GAAG,KAAK,MAAMD,IAAGC,IAAG,MAAM;AAAA,EAC/C;AACA,SAAO;AACT;AAEA,UAAU,aAAa,QAAQ,IAAI,IAAI,MAAM;AAC3C,MAAIA,KAAI;AACR,aAAWD,MAAK,QAAQ;AACtB,UAAM,GAAG,KAAK,MAAMA,IAAGC,IAAG,MAAM;AAChC,UAAM,GAAG,KAAK,MAAMD,IAAGC,IAAG,MAAM;AAChC,MAAEA;AAAA,EACJ;AACF;;;;ACjPO,ICMDc,KAAc,SAAQC,IAAAA;AAA4B,SAC/B,cAAA,OAAdA,KAA2BA,KAAY,SAACC,IAAAA;AAAY,WAAKA,GAAMD,EAAAA;EAAU;AAAA;ADP7E,ICiBME,IAAoB,SAAHC,IAAAA;AAQJ,MAPtBC,KAAMD,GAANC,QAAMC,KAAAF,GACNG,GAAAA,KAAAA,WAACD,KAAG,MAAGA,IAAAE,KAAAJ,GACPK,GAAAA,KAAAA,WAACD,KAAG,MAAGA,IAMDE,KAAOV,GAAmBO,EAAAA,GAC1BI,IAAOX,GAAmBS,EAAAA;AAEhC,SAAOJ,GAAOO,IAAI,SAAAC,IAAAA;AAAK,WAAI,CAACH,GAAKG,EAAAA,GAAkBF,EAAKE,EAAAA,CAAAA;EAAAA,CAAAA;AAC5D;AD9BO,ICgCMC,KAAc,SAAHC,IAAAA;AAUlB,MATFV,KAAMU,GAANV,QACAW,KAAKD,GAALC,OACAC,KAAMF,GAANE,QACAC,KAAKH,GAALG,OAOMC,KAAWC,SAASC,KAAKhB,EAAAA,GACzBiB,KAAUJ,KAAQC,GAASG,QAAQ,CAAC,GAAG,GAAGN,IAAOC,EAAAA,CAAAA,IAAAA;AAEvD,SAAO,EAAEE,UAAAA,IAAUG,SAAAA,GAAAA;AACvB;AD/CO,IEIMC,IAAiB,SAAHnB,IAAAA;AAcrB,MAbFC,KAAMD,GAANC,QACAE,KAACH,GAADG,GACAE,KAACL,GAADK,GACAO,KAAKZ,GAALY,OACAC,KAAMb,GAANa,QACAC,IAAKd,GAALc,OASMM,SAAWC,aAAAA,SAAQ,WAAA;AAAA,WAAMtB,EAAyB,EAAEE,QAAAA,IAAQE,GAAAA,IAAGE,GAAAA,GAAAA,CAAAA;EAAI,GAAE,CAACJ,IAAQE,IAAGE,EAAAA,CAAAA;AAEvF,aAAOgB,aAAAA,SACH,WAAA;AAAA,WAAMX,GAAY,EAAET,QAAQmB,IAAUR,OAAAA,IAAOC,QAAAA,IAAQC,OAAAA,EAAAA,CAAAA;EACrD,GAAA,CAACM,IAAUR,IAAOC,IAAQC,CAAAA,CAAAA;AAElC;AFzBO,IKkBMQ,IAAO,SAAHC,IAAAA;AAWO,MAVpBC,KAAKD,GAALC,OACAC,KAAKF,GAALE,OACAC,IAAMH,GAANG,QACAC,KAACJ,GAADI,GACAC,IAACL,GAADK,GACAC,IAAYN,GAAZM,cACAC,IAAWP,GAAXO,aACAC,IAAYR,GAAZQ,cACAC,IAAOT,GAAPS,SACAC,KAAKV,GAALU,OAEMC,SAAaC,aAAAA,QAAoB,IAAA,GACvCC,SAAwCC,aAAAA,UAAwB,IAAA,GAAzDC,KAAYF,GAAA,CAAA,GAAEG,IAAeH,GAAA,CAAA,GAEpCI,KAA8BC,EAAe,EACzCC,QAAQlB,IACRG,GAAAA,IACAC,GAAAA,GACAH,OAAAA,IACAC,QAAAA,GACAO,OAAAA,GAAAA,CAAAA,GANIU,IAAQH,GAARG,UAAUC,KAAOJ,GAAPI,SASZC,QAAcC,aAAAA,SAAQ,WAAA;AACxB,QAAIb,MAASW;AACT,aAAOA,GAAQG,OAAAA;EAIvB,GAAG,CAACd,IAAOW,EAAAA,CAAAA,GAELI,SAA2BC,aAAAA,aAC7B,SAACC,IAAAA;AACG,QAAA,CAAKhB,GAAWiB;AACZ,aAAO,CAAC,MAAM,IAAA;AAGlB,QAAAC,KAAeC,GAAkBnB,GAAWiB,SAASD,EAAAA,GAA9CvB,KAACyB,GAAA,CAAA,GAAExB,KAACwB,GAAA,CAAA,GACLE,KAAQX,EAASY,KAAK5B,IAAGC,EAAAA;AAE/B,WAAO,CAAC0B,IAAAA,WAAOA,KAAsB9B,GAAM8B,EAAAA,IAAS,IAAA;EACxD,GACA,CAACpB,IAAYS,CAAAA,CAAAA,GAGXa,SAAmBP,aAAAA,aACrB,SAACC,IAAAA;AACG,QAAAO,KAAsBT,GAAyBE,EAAAA,GAAxCI,KAAKG,GAAA,CAAA,GAAEC,KAAID,GAAA,CAAA;AAClBlB,MAAgBe,EAAAA,GACZI,OAAAA,QACA7B,KAAAA,EAAe6B,IAAMR,EAAAA;EAE5B,GACD,CAACF,IAA0BT,GAAiBV,CAAAA,CAAAA,GAG1C8B,SAAkBV,aAAAA,aACpB,SAACC,IAAAA;AACG,QAAAU,KAAsBZ,GAAyBE,EAAAA,GAAxCI,KAAKM,GAAA,CAAA,GAAEF,KAAIE,GAAA,CAAA;AAClBrB,MAAgBe,EAAAA,GACZI,OAAAA,QACA5B,KAAAA,EAAc4B,IAAMR,EAAAA;EAE3B,GACD,CAACF,IAA0BT,GAAiBT,CAAAA,CAAAA,GAG1C+B,QAAmBZ,aAAAA,aACrB,SAACC,IAAAA;AAEG,QADAX,EAAgB,IAAA,GACZR,GAAc;AACd,UAAI+B,KAAAA;AACiB,eAAjBxB,OACAwB,KAAetC,GAAMc,EAAAA,IAEzBwB,MAAgB/B,EAAa+B,IAAcZ,EAAAA;IAC/C;EACH,GACD,CAACX,GAAiBD,IAAcP,GAAcP,EAAAA,CAAAA,GAG5CuC,SAAcd,aAAAA,aAChB,SAACC,IAAAA;AACG,QAAAc,KAAsBhB,GAAyBE,EAAAA,GAAxCI,KAAKU,GAAA,CAAA,GAAEN,KAAIM,GAAA,CAAA;AAClBzB,MAAgBe,EAAAA,GACZI,OAAAA,QACA1B,KAAAA,EAAU0B,IAAMR,EAAAA;EAEvB,GACD,CAACF,IAA0BT,GAAiBP,CAAAA,CAAAA;AAGhD,aACIiC,mBAAAA,MAAA,KAAA,EAAGC,KAAKhC,IAAWiC,UAAAA,CACdlC,MAASW,UACNqB,mBAAAA,MAAAG,mBAAAA,UAAA,EAAAD,UAAAA,KACIE,mBAAAA,KAAA,QAAA,EAAMC,GAAGzB,GAAa0B,QAAO,OAAMC,aAAa,GAAGC,SAAS,KAAA,CAAA,GAE1C,SAAjBnC,UACG+B,mBAAAA,KAAA,QAAA,EAAMK,MAAK,QAAOD,SAAS,MAAMH,GAAG1B,GAAQ+B,WAAWrC,EAAAA,EAAAA,CAAAA,CAAAA,EAAAA,CAAAA,OAKnE+B,mBAAAA,KAAA,QAAA,EACI5C,OAAOA,IACPC,QAAQA,GACRgD,MAAK,OACLD,SAAS,GACTG,OAAO,EAAEC,QAAQ,OAAA,GACjBhD,cAAc2B,IACd1B,aAAa6B,IACb5B,cAAc8B,GACd7B,SAAS+B,GAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAIzB;ALxIO,IMAMe,KAAwB,SACjCC,IACAnC,IAAAA;AAEAmC,EAAAA,GAAIC,KAAAA,GAEJD,GAAIE,cAAc,MAClBF,GAAIG,UAAAA,GACJtC,GAAQG,OAAOgC,EAAAA,GACfA,GAAII,cAAc,OAClBJ,GAAIK,YAAY,GAChBL,GAAIR,OAAAA,GAEJQ,GAAIM,QAAAA;AACR;ANdO,IMgBMC,KAA4B,SACrCP,IACAnC,IACAU,IAAAA;AAEAyB,EAAAA,GAAIC,KAAAA,GAEJD,GAAIE,cAAc,MAClBF,GAAIG,UAAAA,GACJtC,GAAQ+B,WAAWrB,IAAOyB,EAAAA,GAC1BA,GAAIQ,YAAY,OAChBR,GAAIL,KAAAA,GAEJK,GAAIM,QAAAA;AACR;;;;;;;;;;;;;ACpBA,IAAMG,KAAmB,SAAHC,IAAAA;AAAkB,MAAZC,KAAKD,GAALC;AACxB,aACIC,oBAAAA,KAACC,GAAY,EACTC,QACIC,oBAAAA,MAAA,QAAA,EAAAC,UAAA,CAAM,WACCJ,oBAAAA,KAAA,UAAA,EAAAI,UAASL,GAAMM,KAAKC,WAAAA,CAAAA,GAAoB,QAAK,SAChDN,oBAAAA,KAAA,UAAA,EAAAI,UAASL,GAAMM,KAAKE,WAAAA,CAAAA,CAAAA,EAAAA,CAAAA,GAG5BC,YAAAA,MACAC,OAAOV,GAAMW,WAAAA,CAAAA;AAGzB;AAEAb,GAAiBc,YAAY,EACzBZ,OAAOa,kBAAAA,QAAUC,OAAOC,WAAAA;AAG5B,IAAeC,SAAAA,cAAAA,MAAKlB,EAAAA;AAApB,IClBMmB,KAAe,SAAHlB,IAAAA;AAAwB,MAAlBmB,KAAKnB,GAALmB,OAAOC,KAAIpB,GAAJoB,MACrBC,KAAQC,GAAAA,GACRC,KAAqB,QAATH,KAAe,MAAM;AAEvC,aACIlB,oBAAAA,KAACsB,GAAY,EACTC,MAAMN,GAAMO,OAAOC,IAAI,SAAA1B,IAAAA;AAAK,WAAI,KAC5BC,oBAAAA,KAAC0B,GAAI,EAAYjB,OAAOV,GAAMW,YAAYiB,OAAOR,GAAMS,QAAQC,KAAAA,GAArD,MAAA,GACV9B,GAAM+B,aACN9B,oBAAAA,KAAA,QAAA,EAAkB2B,OAAOR,GAAMS,QAAQG,gBAAe3B,UACjDL,GAAMM,KAAQgB,KAAS,WAAA,EAAA,GADlB,OAAA,CAAA;EAGb,CAAA,EAAA,CAAA;AAGb;AAEAL,GAAaL,YAAY,EACrBM,OAAOL,kBAAAA,QAAUC,OAAOC,YACxBI,MAAMN,kBAAAA,QAAUoB,MAAM,CAAC,KAAK,GAAA,CAAA,EAAMlB,WAAAA;AAGtC,IAAeC,SAAAA,cAAAA,MAAKC,EAAAA;AAApB,ICnBMiB,KAAkB,EACpB5B,MAAMO,kBAAAA,QAAUsB,QACZtB,kBAAAA,QAAUuB,MAAM,EACZjC,IAAIU,kBAAAA,QAAUwB,UAAU,CAACxB,kBAAAA,QAAUyB,QAAQzB,kBAAAA,QAAU0B,MAAAA,CAAAA,EAASxB,YAC9DT,MAAMO,kBAAAA,QAAUsB,QACZtB,kBAAAA,QAAUuB,MAAM,EACZI,GAAG3B,kBAAAA,QAAUwB,UAAU,CACnBxB,kBAAAA,QAAU0B,QACV1B,kBAAAA,QAAUyB,QACVzB,kBAAAA,QAAU4B,WAAWC,IAAAA,CAAAA,CAAAA,GAEzBC,GAAG9B,kBAAAA,QAAUwB,UAAU,CACnBxB,kBAAAA,QAAU0B,QACV1B,kBAAAA,QAAUyB,QACVzB,kBAAAA,QAAU4B,WAAWC,IAAAA,CAAAA,CAAAA,EAAAA,CAAAA,CAAAA,EAG/B3B,WAAAA,CAAAA,CAAAA,EAERA,YAEF6B,QAAQ/B,kBAAAA,QAAUC,OAAOC,YACzB8B,SAAShC,kBAAAA,QAAUwB,UAAU,CAACxB,kBAAAA,QAAUiC,MAAMjC,kBAAAA,QAAUyB,MAAAA,CAAAA,GACxDS,QAAQlC,kBAAAA,QAAUC,OAAOC,YACzBiC,SAASnC,kBAAAA,QAAUwB,UAAU,CAACxB,kBAAAA,QAAUiC,MAAMjC,kBAAAA,QAAUyB,MAAAA,CAAAA,GAExDW,QAAQpC,kBAAAA,QAAUsB,QACdtB,kBAAAA,QAAUwB,UAAU,CAChBxB,kBAAAA,QAAUoB,MAAM,CACZ,QACA,WACA,QACA,SACA,aACA,SACA,UACA,UACA,QACA,SAAA,CAAA,GAEJpB,kBAAAA,QAAUiC,IAAAA,CAAAA,CAAAA,EAEhB/B,YAEFmC,OAAOC,GAAkBpC,YAEzBqC,SAASC,IACTC,WAAWD,IACXE,YAAYF,IACZG,UAAUH,IAEVI,aAAa5C,kBAAAA,QAAU6C,KAAK3C,YAC5B4C,aAAa9C,kBAAAA,QAAU6C,KAAK3C,YAC5B6C,aAAa/C,kBAAAA,QAAUwB,UAAU,CAC7BxB,kBAAAA,QAAU0B,QACV1B,kBAAAA,QAAUsB,QACNtB,kBAAAA,QAAUwB,UAAU,CAACxB,kBAAAA,QAAU0B,QAAQ1B,kBAAAA,QAAUyB,QAAQzB,kBAAAA,QAAU4B,WAAWC,IAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,GAGtFmB,aAAahD,kBAAAA,QAAUwB,UAAU,CAC7BxB,kBAAAA,QAAU0B,QACV1B,kBAAAA,QAAUsB,QACNtB,kBAAAA,QAAUwB,UAAU,CAACxB,kBAAAA,QAAU0B,QAAQ1B,kBAAAA,QAAUyB,QAAQzB,kBAAAA,QAAU4B,WAAWC,IAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,GAItFoB,cAAcjD,kBAAAA,QAAU6C,KAAK3C,YAC7BgD,aAAalD,kBAAAA,QAAUiC,MACvBkB,WAAWnD,kBAAAA,QAAU0B,OAAOxB,YAC5BkD,YAAYpD,kBAAAA,QAAUqD,IAAInD,YAC1BoD,kBAAkBtD,kBAAAA,QAAU0B,OAAOxB,YACnCqD,kBAAkBvD,kBAAAA,QAAUqD,IAAInD,YAChCsD,kBAAkBxD,kBAAAA,QAAU6C,KAAK3C,YACjCuD,YAAYzD,kBAAAA,QAAUwB,UAAU,CAACxB,kBAAAA,QAAUyB,QAAQzB,kBAAAA,QAAUiC,IAAAA,CAAAA,EAAO/B,YAEpEwD,SAAS1D,kBAAAA,QAAUsB,QACftB,kBAAAA,QAAUuB,MAAM,EACZjB,MAAMN,kBAAAA,QAAUoB,MAAM,CAAC,KAAK,GAAA,CAAA,EAAMlB,YAClCyD,OAAO3D,kBAAAA,QAAUwB,UAAU,CACvBxB,kBAAAA,QAAU0B,QACV1B,kBAAAA,QAAUyB,QACVzB,kBAAAA,QAAU4B,WAAWC,IAAAA,CAAAA,CAAAA,EACtB3B,YACHa,OAAOf,kBAAAA,QAAUC,OAAAA,CAAAA,CAAAA,GAIzB2D,QAAQC,GAAsB3D,YAE9B4D,YAAY9D,kBAAAA,QAAU6C,KAAK3C,YAC3B6D,aAAa/D,kBAAAA,QAAU0B,OAAOxB,YAC9B8D,eAAeC,GAAkB/D,YACjCgE,mBAAmBlE,kBAAAA,QAAUwB,UAAU,CACnCxB,kBAAAA,QAAU0B,QACV1B,kBAAAA,QAAUyB,QACVzB,kBAAAA,QAAU4B,WAAWC,IAAAA,CAAAA,CAAAA,EACtB3B,YACHiE,WAAWnE,kBAAAA,QAAU0B,OAAOxB,YAE5BkE,SAASpE,kBAAAA,QAAUsB,QAAQtB,kBAAAA,QAAUuB,MAAM8C,CAAAA,CAAAA,EAAkBnE,YAE7DoE,eAAetE,kBAAAA,QAAU6C,KAAK3C,YAC9BqE,WAAWvE,kBAAAA,QAAU6C,KAAK3C,YAE1Bc,SAAShB,kBAAAA,QAAUwB,UAAU,CAACxB,kBAAAA,QAAUiC,MAAMjC,kBAAAA,QAAUC,MAAAA,CAAAA,EAASC,YAEjEsE,cAAcxE,kBAAAA,QAAUoB,MAAM,CAAC,KAAK,KAAA,KAAK,CAAA,EAAQlB,YACjDuE,aAAazE,kBAAAA,QAAU6C,KAAK3C,YAC5BwE,cAAc1E,kBAAAA,QAAUwB,UAAU,CAACxB,kBAAAA,QAAUiC,MAAMjC,kBAAAA,QAAUC,MAAAA,CAAAA,EAASC,YAEtEyE,iBAAiB3E,kBAAAA,QAAU6C,KAAK3C,YAChC0E,eAAe5E,kBAAAA,QAAUyB,OAAOvB,WAAAA;AD5FpC,IC+Fa2E,KAAaC,GAAAA,CAAAA,GACnBzD,IAAe,EAClBmC,kBAAkBxD,kBAAAA,QAAU6C,KAAK3C,YACjC6E,MAAM/E,kBAAAA,QAAUyB,OAAOvB,YACvB8E,SAAShF,kBAAAA,QAAU6C,KAAK3C,WAAAA,GACrB+E,IACAC,EAAAA;ADrGP,ICwGaC,KAAmBL,GAAA,EAC5BM,YAAYpF,kBAAAA,QAAU0B,OAAOxB,WAAAA,GAC1BmB,EAAAA;AD1GP,IC6GMgE,KAAqB,EACvBhD,OAAO,UAEPN,QAAQ,EACJuD,MAAM,QAAA,GAEVpD,QAAQ,EACJoD,MAAM,UACNC,KAAK,GACLC,KAAK,OAAA,GAGTpD,QAAQ,CACJ,QACA,WACA,QACA,SACA,aACA,SACA,UACA,UACA,QACA,SAAA,GAEJM,YAAY,CAAE,GACdC,UAAU,CAAE,GACZC,aAAAA,MACAE,aAAAA,MAEAG,cAAAA,MACAE,WAAW,GACXC,YAAY,EAAEqC,MAAM,QAAA,GACpBnC,kBAAkB,GAClBC,kBAAkB,EAAEhD,OAAO,aAAA,GAC3BiD,kBAAAA,OACAC,YAAY,cAEZG,QAAQ,EAAE8B,QAAQ,OAAA,GAClB5B,YAAAA,OACAI,mBAAmB,GACnBH,aAAa,KACbC,eAAe,UACfG,WAAW,GAEXC,SAAS,CAAA,GAETE,eAAAA,MACAtD,SAAS2E,IACTnB,cAAAA,OACAC,aAAAA,OACAC,cAActE,IACdmE,WAAAA,OACAI,iBAAAA,MACAC,eAAe,cAAA;ADlKnB,ICqKagB,KAAgBd,GAAAA,CAAAA,GACtBO,IAAkB,EACrB7B,kBAAAA,OACAwB,SAAAA,OACAa,SAAAA,MACAC,cAAc,UACdC,MAAM,CAAA,GACNC,MAAM,CAAA,GACNjB,MAAM,MAAA,CAAA;AD7KV,ICgLakB,KAAsBnB,GAAAA,CAAAA,GAC5BO,IAAkB,EACrBD,YAA8B,eAAA,OAAXc,UAAyBA,OAAOC,oBAAwB,EAAA,CAAA;ADlL/E,IEpBaC,KAAmB,SAAHlH,IAAAA;AAAkB,MAAZmD,KAAKnD,GAALmD;AAC/B,aAAOgE,cAAAA,SACH,WAAA;AAAA,WACIC,aAAAA,EACKC,QAAQ,SAAAC,IAAAA;AAAC,aAAY,SAARA,GAAE7E,KAAsB,SAAR6E,GAAE1E;IAAU,CAAA,EACzCH,EAAE,SAAA6E,IAAAA;AAAC,aAAIA,GAAE7E;IAAC,CAAA,EACVG,EAAE,SAAA0E,IAAAA;AAAC,aAAIA,GAAE1E;IAAC,CAAA,EACVO,MAAMoE,GAAcpE,EAAAA,CAAAA;EAAAA,GAC7B,CAACA,EAAAA,CAAAA;AAET;AFUA,IERaqE,KAAmB,SAAHC,IAAAA;AAA6C,MAAvCtE,KAAKsE,GAALtE,OAAOH,KAAMyE,GAANzE,QAAQgC,KAAiByC,GAAjBzC;AAC9C,aAAOmC,cAAAA,SAAQ,WAAA;AACX,WAAOO,aAAAA,EACFL,QAAQ,SAAAC,IAAAA;AAAC,aAAY,SAARA,GAAE7E,KAAsB,SAAR6E,GAAE1E;IAAU,CAAA,EACzCH,EAAE,SAAA6E,IAAAA;AAAC,aAAIA,GAAE7E;IAAC,CAAA,EACVkF,GAAG,SAAAL,IAAAA;AAAC,aAAIA,GAAE1E;IAAC,CAAA,EACXO,MAAMoE,GAAcpE,EAAAA,CAAAA,EACpByE,GAAG5E,GAAOgC,EAAAA,CAAAA;EAClB,GAAE,CAAC7B,IAAOH,IAAQgC,EAAAA,CAAAA;AACvB;AFDA,IEkCa6C,KAAY,SAAHC,IAAAA;AAAgD,MAA1CxC,KAAYwC,GAAZxC,cAAc5D,KAAMoG,GAANpG,QAAQqG,KAAKD,GAALC,OAAOC,KAAMF,GAANE;AACrD,aAAOb,cAAAA,SAAQ,WAAA;AACX,QAAA,UAAI7B;AAAwB,aAAO,CAAA;AAEnC,QAAqB,QAAjBA,IAAsB;AACtB,UAAM3D,KAAM,oBAAIsG;AAMhB,aALAvG,GAAOwG,QAAQ,SAAAjI,IAAAA;AACU,iBAAjBA,GAAMM,KAAKkC,KAA+B,SAAjBxC,GAAMM,KAAKqC,MACnCjB,GAAIwG,IAAIlI,GAAMwC,CAAAA,IACdd,GAAIyG,IAAInI,GAAMwC,CAAAA,EAAG4F,KAAKpI,EAAAA,IADJ0B,GAAI2G,IAAIrI,GAAMwC,GAAG,CAACxC,EAAAA,CAAAA;MAE7C,CAAA,GACOsI,MAAMhC,KAAK5E,GAAI6G,QAAAA,CAAAA,EACjBC,KAAK,SAACC,IAAGC,IAAAA;AAAC,eAAKD,GAAE,CAAA,IAAKC,GAAE,CAAA;MAAG,CAAA,EAC3BhH,IAAI,SAAAiH,IAAmBC,IAAGC,IAAAA;AAAW,YAI9BC,IAJDtG,KAACmG,GAAA,CAAA,GAAEI,KAAWJ,GAAA,CAAA,GACXK,KAAYH,GAAOD,KAAI,CAAA,GACvBK,IAAYJ,GAAOD,KAAI,CAAA;AAU7B,eAAO,EACHzI,IAAIqC,IACJsG,IARCA,KADAE,KACKxG,MAAKA,KAAIwG,GAAU,CAAA,KAAM,IADdxG,IAUjBA,GAAAA,IACAmF,IAAI,GACJhF,GAAG,GACHmF,OATCmB,IACazG,KAAIsG,MAAMG,EAAU,CAAA,IAAKzG,MAAK,IADnBsF,KAAQgB,IAUjCf,QAAAA,IACAtG,QAAQsH,GAAYG,QAAAA,EAAAA;MAE5B,CAAA;IACR;AAAO,QAAqB,QAAjB7D,IAAsB;AAC7B,UAAM3D,KAAM,oBAAIsG;AAMhB,aALAvG,GAAOwG,QAAQ,SAAAjI,IAAAA;AACU,iBAAjBA,GAAMM,KAAKkC,KAA+B,SAAjBxC,GAAMM,KAAKqC,MACnCjB,GAAIwG,IAAIlI,GAAM2C,CAAAA,IACdjB,GAAIyG,IAAInI,GAAM2C,CAAAA,EAAGyF,KAAKpI,EAAAA,IADJ0B,GAAI2G,IAAIrI,GAAM2C,GAAG,CAAC3C,EAAAA,CAAAA;MAE7C,CAAA,GACOsI,MAAMhC,KAAK5E,GAAI6G,QAAAA,CAAAA,EACjBC,KAAK,SAACC,IAAGC,IAAAA;AAAC,eAAKD,GAAE,CAAA,IAAKC,GAAE,CAAA;MAAG,CAAA,EAC3BhH,IAAI,SAAAyH,IAAmBP,IAAGC,IAAAA;AAAW,YAI9BlB,IAIAyB,IARDzG,KAACwG,GAAA,CAAA,GAAEJ,KAAWI,GAAA,CAAA,GACXH,IAAYH,GAAOD,KAAI,CAAA,GACvBK,IAAYJ,GAAOD,KAAI,CAAA;AAU7B,eANKjB,KADAqB,IACKrG,MAAKA,KAAIqG,EAAU,CAAA,KAAM,IADdrG,IAKhByG,KADAH,IACctG,KAAIgF,MAAMsB,EAAU,CAAA,IAAKtG,MAAK,IADnBoF,KAASJ,IAGhC,EACHxH,IAAIwC,IACJmG,IAAI,GACJtG,GAAG,GACHmF,IAAAA,IACAhF,GAAAA,IACAmF,OAAAA,IACAC,QAAQqB,IACR3H,QAAQsH,GAAYG,QAAAA,EAAAA;MAE5B,CAAA;IACR;EACJ,GAAG,CAAC7D,IAAc5D,EAAAA,CAAAA;AACtB;AFxGA,IE0Ga4H,KAAU,SAAHC,IAAAA;AAcd,MAbFhJ,KAAIgJ,GAAJhJ,MAAIiJ,KAAAD,GACJ1G,QAAQ4G,KAAAA,WAAUD,KAAG9C,GAAiB7D,SAAM2G,IAC5C1G,KAAOyG,GAAPzG,SAAO4G,KAAAH,GACPvG,QAAQ2G,IAAAA,WAAUD,KAAGhD,GAAiB1D,SAAM0G,IAC5CzG,IAAOsG,GAAPtG,SACA8E,KAAKwB,GAALxB,OACAC,KAAMuB,GAANvB,QAAM4B,IAAAL,GACN7E,QAAAA,KAAAA,WAAMkF,IAAGlD,GAAiBhC,SAAMkF,GAAAC,KAAAN,GAChCpG,OAAAA,IAAAA,WAAK0G,KAAGnD,GAAiBvD,QAAK0G,IAAAC,KAAAP,GAC9BvE,mBAAAA,KAAAA,WAAiB8E,KAAGpD,GAAiB1B,oBAAiB8E,IAAAC,IAAAR,GACtDrF,YAAAA,IAAAA,WAAU6F,IAAGrD,GAAiBxC,aAAU6F,GAAAC,KAAAT,GACxClF,kBAAAA,IAAAA,WAAgB2F,KAAGtD,GAAiBrC,mBAAgB2F,IAAAC,KAAAV,GACpDjE,cAAAA,IAAAA,WAAY2E,KAAGvD,GAAiBwD,sBAAmBD,IAE7CE,KAAUC,GAAkBtH,EAAAA,GAC5BuH,KAAUD,GAAkBnH,CAAAA,GAC5BqH,IAAWC,GAAqB7F,IAAQ,IAAA,GACxCrD,IAAQC,GAAAA,GACRkJ,IAAgBC,GAAkBvG,GAAY7C,CAAAA,GAC9CqJ,KAAsBD,GAAkBpG,GAAkBhD,CAAAA,GAChEsJ,QAAkCC,cAAAA,UAAS,CAAA,CAAA,GAApCC,KAASF,EAAA,CAAA,GAAEG,IAAYH,EAAA,CAAA,GAE9BI,QAII5D,cAAAA,SACA,WAAA;AAAA,WACI6D,GACIzK,GAAK0K,OAAO,SAAAC,IAAAA;AAAI,aAAA,OAAIL,GAAUM,QAAQD,GAAK9K,EAAAA;IAAW,CAAA,GACtDqJ,IACAE,GACA5B,IACAC,EAAAA;EACH,GACL,CAACzH,IAAMsK,IAAWpB,IAAYE,GAAY5B,IAAOC,EAAAA,CAAAA,GAZjDnF,IAAMkI,EAANlI,QACAG,KAAM+H,EAAN/H,QACQoI,KAASL,EAAjBM,QAaJC,SAA+BnE,cAAAA,SAAQ,WAAA;AACnC,QAAMoE,KAAgBhL,GAAKoB,IAAI,SAAAyF,IAAAA;AAAI,aAAK,EACpChH,IAAIgH,GAAKhH,IACToL,OAAOpE,GAAKhH,IACZO,OAAO2J,EAASlD,EAAAA,EAAAA;IACnB,CAAA,GACKiE,KAASE,GACV5J,IAAI,SAAA8J,IAAAA;AAAK,aAAA7F,GACHwF,CAAAA,GAAAA,GAAUM,KAAK,SAAAC,IAAAA;AAAK,eAAIA,GAAMvL,OAAOqL,GAAMrL;MAAAA,CAAAA,GAAG,EACjDO,OAAO8K,GAAM9K,MAAAA,CAAAA;IAAK,CAAA,EAErBsK,OAAO,SAAAC,IAAAA;AAAI,aAAIU,QAAQV,GAAK9K,EAAAA;IAAAA,CAAAA;AAKjC,WAAO,EAAEyL,YAJUN,GACd5J,IAAI,SAAAuJ,IAAAA;AAAI,aAAAtF,GAAAA,CAAAA,GAAUsF,IAAI,EAAEY,QAAAA,CAAST,GAAOK,KAAK,SAAAC,IAAAA;AAAK,eAAIA,GAAMvL,OAAO8K,GAAK9K;MAAE,CAAA,EAAA,CAAA;IAAC,CAAA,EAC3E+I,QAAAA,GAEgBkC,QAAAA,GAAAA;EACxB,GAAE,CAAC9K,IAAM6K,IAAWd,CAAAA,CAAAA,GAjBbuB,IAAUP,GAAVO,YAAYR,KAAMC,GAAND,QAmBdU,QAAcC,cAAAA,aAAY,SAAA5L,IAAAA;AAC5B0K,MAAa,SAAAmB,IAAAA;AAAK,aACdA,GAAMd,QAAQ/K,EAAAA,IAAAA,KAAW6L,GAAMhB,OAAO,SAAAC,IAAAA;AAAI,eAAIA,OAAS9K;MAAE,CAAA,IAAA,CAAA,EAAC8L,OAAOD,IAAK,CAAE7L,EAAAA,CAAAA;IAAG,CAAA;EAElF,GAAE,CAAA,CAAA,GAEGsB,KAvKQ,SAAHyK,IAAAA;AAAyE,QAAnEd,KAAMc,GAANd,QAAQb,KAAa2B,GAAb3B,eAAeE,KAAmByB,GAAnBzB,qBAAqBP,KAAOgC,GAAPhC,SAASE,KAAO8B,GAAP9B;AACtE,eAAOlD,cAAAA,SAAQ,WAAA;AACX,aAAOkE,GAAOe,OAAO,SAACC,IAAKV,IAAAA;AACvB,eAAAO,CAAAA,EAAAA,OACOG,IACAV,GAAMpL,KACJ0K,OAAO,SAAAQ,IAAAA;AAAK,iBAAyB,SAArBA,GAAMa,SAAS7J,KAAmC,SAArBgJ,GAAMa,SAAS1J;QAAU,CAAA,EACtEjB,IAAI,SAAC8J,IAAO5C,IAAAA;AACT,cAAM5I,KAAQ,EACVG,IAAOuL,GAAMvL,KAAAA,MAAMyI,IACnB0D,OAAOF,GAAIG,SAAS3D,IACpB7G,SAAS2J,GAAMvL,IACfQ,YAAY+K,GAAMhL,OAClB8B,GAAGgJ,GAAMa,SAAS7J,GAClBG,GAAG6I,GAAMa,SAAS1J,EAAAA;AAUtB,iBARA3C,GAAMU,QAAQ6J,GAAcmB,EAAAA,GAC5B1L,GAAMwM,cAAc/B,GAAoBzK,EAAAA,GACxCA,GAAMM,OAAIqF,GACH6F,CAAAA,GAAAA,GAAMlL,MAAI,EACbC,YAAY2J,GAAQsB,GAAMlL,KAAKkC,CAAAA,GAC/BhC,YAAY4J,GAAQoB,GAAMlL,KAAKqC,CAAAA,EAAAA,CAAAA,GAG5B3C;QACV,CAAA,CAAA;MAEZ,GAAE,CAAA,CAAA;IACP,GAAG,CAACoL,IAAQb,IAAeE,IAAqBP,IAASE,EAAAA,CAAAA;EAC7D,EA0I6B,EACrBgB,QAAAA,IACAb,eAAAA,GACAE,qBAAAA,IACAP,SAAAA,IACAE,SAAAA,GAAAA,CAAAA,GAGEvB,KAASjB,GAAU,EACrBvC,cAAAA,GACA5D,QAAAA,IACAqG,OAAAA,IACAC,QAAAA,GAAAA,CAAAA;AAUJ,SAAO,EACH6D,YAAAA,GACAE,aAAAA,GACAW,eAVkBxF,GAAiB,EAAE/D,OAAAA,EAAAA,CAAAA,GAWrCwJ,eAVkBnF,GAAiB,EACnCrE,OAAAA,GACAH,QAAAA,IACAgC,mBAAAA,GAAAA,CAAAA,GAQAsF,UAAAA,GACAe,QAAAA,IACAxI,QAAAA,GACAG,QAAAA,IACA8F,QAAAA,IACApH,QAAAA,GAAAA;AAER;AF5MA,IGtBMkL,KAAW,SAAH5M,IAAAA;AAA0D,MAApD8E,KAAa9E,GAAb8E,eAAeD,KAAW7E,GAAX6E,aAAalE,KAAKX,GAALW,OAAOmG,KAAI9G,GAAJ8G,MAAM+F,KAAI7M,GAAJ6M,MACzDC,KAA0CC,GAAAA,GAAlCpG,KAAOmG,GAAPnG,SAAiBqG,KAAYF,GAApBG,QAEXC,IAAeC,GAAgBN,EAAAA,GAC/BO,IAAgBC,UAAU,EAC5B1M,OAAAA,IACAsM,QAAQD,IACRM,WAAAA,CAAY3G,GAAAA,CAAAA;AAGhB,aACIzG,oBAAAA,KAACqN,SAASV,MAAI,EACVvF,GAAG4F,GACHpG,MAAMA,MAAcsG,EAAczM,OAClC6M,aAAa3I,IACb4I,aAAa,GACb5L,OAAO,EACH6L,cAAc5I,GAAAA,EAAAA,CAAAA;AAI9B;AAEA8H,GAAS/L,YAAY,EACjBiE,eAAeC,GAAkB/D,YACjC6D,aAAa/D,kBAAAA,QAAU0B,OAAOxB,YAC9BL,OAAOG,kBAAAA,QAAUyB,QACjBuE,MAAMhG,kBAAAA,QAAUyB,QAChBsK,MAAM/L,kBAAAA,QAAUyB,OAAOvB,WAAAA;AAG3B,IAAM2M,KAAQ,SAAHlG,IAAAA;AAA6D,MAAvDkF,KAAalF,GAAbkF,eAAe9H,KAAW4C,GAAX5C,aAAaC,KAAa2C,GAAb3C,eACnC8I,KADuDnG,GAALoG,MAC5B1M,MAAM,CAAA,EAAGgI,QAAAA;AAErC,aACIjJ,oBAAAA,KAAA,KAAA,EAAAI,UACKsN,GAAcjM,IAAI,SAAAyF,IAAAA;AAAI,eACnBlH,oBAAAA,KAAC0M,IAAQhH,GAAA,EAELiH,MAAMF,GAAcvF,GAAK7G,KAAKoB,IAAI,SAAA2F,IAAAA;AAAC,aAAIA,GAAEgF;IAAAA,CAAAA,CAAAA,EAAAA,GAAW1G,GAAA,EAC9Cf,aAAAA,IAAaC,eAAAA,GAAAA,GAAkBsC,EAAAA,CAAAA,GAFhCA,GAAKhH,EAAAA;EAAAA,CAAAA,EAAAA,CAAAA;AAO9B;AAEAuN,GAAM9M,YAAY,EACd8L,eAAe7L,kBAAAA,QAAUiC,KAAK/B,YAC9B6D,aAAa/D,kBAAAA,QAAU0B,OAAOxB,YAC9B8D,eAAeC,GAAkB/D,YACjC6M,OAAO/M,kBAAAA,QAAUsB,QAAQtB,kBAAAA,QAAUC,MAAAA,EAAQC,WAAAA;AAG/C,IAAeC,SAAAA,cAAAA,MAAK0M,EAAAA;AAApB,ICtDMG,KAAY,SAAH9N,IAAAA;AAAoD,MAA9C0M,KAAa1M,GAAb0M,eAAehL,KAAM1B,GAAN0B,QAAQf,KAAKX,GAALW,OAAOoN,KAAS/N,GAAT+N,WACzClB,SAAO1F,cAAAA,SAAQ,WAAA;AAAA,WAAMuF,GAAchL,EAAAA;EAAO,GAAE,CAACgL,IAAehL,EAAAA,CAAAA,GAC5DwL,KAAeC,GAAgBN,EAAAA;AAErC,aAAO3M,oBAAAA,KAACqN,SAASV,MAAI,EAACvF,GAAG4F,IAAcpG,MAAK,QAAO2G,aAAaM,IAAWC,QAAQrN,GAAAA,CAAAA;AACvF;AAEAmN,GAAUjN,YAAY,EAClBa,QAAQZ,kBAAAA,QAAUsB,QACdtB,kBAAAA,QAAUuB,MAAM,EACZI,GAAG3B,kBAAAA,QAAUwB,UAAU,CAACxB,kBAAAA,QAAUyB,QAAQzB,kBAAAA,QAAU0B,MAAAA,CAAAA,GACpDI,GAAG9B,kBAAAA,QAAUwB,UAAU,CAACxB,kBAAAA,QAAUyB,QAAQzB,kBAAAA,QAAU0B,MAAAA,CAAAA,EAAAA,CAAAA,CAAAA,GAG5DkK,eAAe5L,kBAAAA,QAAUiC,KAAK/B,YAC9BL,OAAOG,kBAAAA,QAAUyB,OAAOvB,YACxB+M,WAAWjN,kBAAAA,QAAU0B,OAAOxB,WAAAA;AAGhC,IAAeC,SAAAA,cAAAA,MAAK6M,EAAAA;AAApB,ICpBMG,KAAQ,SAAHjO,IAAAA;AAA4C,MAAtC6N,KAAK7N,GAAL6N,OAAOnB,KAAa1M,GAAb0M,eAAezH,KAASjF,GAATiF;AACnC,SAAO4I,GACF1M,MAAM,CAAA,EACNgI,QAAAA,EACAxH,IAAI,SAAA8F,IAAAA;AAAA,QAAGrH,KAAEqH,GAAFrH,IAAIG,KAAIkH,GAAJlH,MAAMI,KAAK8G,GAAL9G;AAAK,eACnBT,oBAAAA,KAAC4N,IAAS,EAEN1N,IAAIA,IACJsB,QAAQnB,GAAKoB,IAAI,SAAA2F,IAAAA;AAAC,aAAIA,GAAEgF;IAAAA,CAAAA,GACxBI,eAAeA,IACf/L,OAAOA,IACPoN,WAAW9I,GAAAA,GALN7E,EAAAA;EAMP,CAAA;AAEd;AAEA6N,GAAMpN,YAAY,EACdgN,OAAO/M,kBAAAA,QAAUsB,QACbtB,kBAAAA,QAAUuB,MAAM,EACZjC,IAAIU,kBAAAA,QAAUwB,UAAU,CAACxB,kBAAAA,QAAUyB,QAAQzB,kBAAAA,QAAU0B,MAAAA,CAAAA,EAASxB,YAC9DL,OAAOG,kBAAAA,QAAUyB,OAAOvB,YACxBT,MAAMO,kBAAAA,QAAUsB,QACZtB,kBAAAA,QAAUuB,MAAM,EACZ9B,MAAMO,kBAAAA,QAAUuB,MAAM,EAClBI,GAAG3B,kBAAAA,QAAUwB,UAAU,CACnBxB,kBAAAA,QAAUyB,QACVzB,kBAAAA,QAAU0B,QACV1B,kBAAAA,QAAU4B,WAAWC,IAAAA,CAAAA,CAAAA,GAEzBC,GAAG9B,kBAAAA,QAAUwB,UAAU,CACnBxB,kBAAAA,QAAUyB,QACVzB,kBAAAA,QAAU0B,QACV1B,kBAAAA,QAAU4B,WAAWC,IAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAE1B3B,YACHsL,UAAUxL,kBAAAA,QAAUuB,MAAM,EACtBI,GAAG3B,kBAAAA,QAAU0B,QACbI,GAAG9B,kBAAAA,QAAU0B,OAAAA,CAAAA,EACdxB,WAAAA,CAAAA,CAAAA,EAETA,WAAAA,CAAAA,CAAAA,EAERA,YACFiE,WAAWnE,kBAAAA,QAAU0B,OAAOxB,YAC5B0L,eAAe5L,kBAAAA,QAAUiC,KAAK/B,WAAAA;AAGlC,IAAeC,SAAAA,cAAAA,MAAKgN,EAAAA;AAApB,IC/CMC,KAAa,SAAHlO,IAAAA;AAWV,MAVFmB,KAAKnB,GAALmB,OACAC,KAAIpB,GAAJoB,MACA+M,KAAKnO,GAALmO,OACArM,KAAO9B,GAAP8B,SACAsM,KAASpO,GAAToO,WACAC,KAAUrO,GAAVqO,YACAC,IAAYtO,GAAZsO,cACAC,IAAWvO,GAAXuO,aACAC,IAAYxO,GAAZwO,cACAC,KAAOzO,GAAPyO,SAEAC,KAA8CC,EAAAA,GAAtCC,IAAoBF,GAApBE,sBAAsBC,IAAWH,GAAXG,aAExBC,SAAmB9C,cAAAA,aACrB,SAAA+C,IAAAA;AACIH,UAAqBI,cAAAA,eAAclN,IAAS,EAAEX,OAAAA,IAAOC,MAAAA,GAAAA,CAAAA,GAAS2N,IAAO,OAAA,GACrEV,GAAWlN,EAAAA,GACXmN,KAAgBA,EAAanN,IAAO4N,EAAAA;EACvC,GACD,CAACH,GAAsB9M,IAASX,IAAOmN,CAAAA,CAAAA,GAGrCW,SAAkBjD,cAAAA,aACpB,SAAA+C,IAAAA;AACIH,UAAqBI,cAAAA,eAAclN,IAAS,EAAEX,OAAAA,IAAOC,MAAAA,GAAAA,CAAAA,GAAS2N,IAAO,OAAA,GACrER,KAAeA,EAAYpN,IAAO4N,EAAAA;EACrC,GACD,CAACH,GAAsB9M,IAASX,IAAOoN,CAAAA,CAAAA,GAGrCW,QAAmBlD,cAAAA,aACrB,SAAA+C,IAAAA;AACIF,MAAAA,GACAR,GAAW,IAAA,GACXG,KAAgBA,EAAarN,IAAO4N,EAAAA;EACvC,GACD,CAACF,GAAa1N,IAAOqN,CAAAA,CAAAA,GAGnBW,SAAcnD,cAAAA,aAChB,SAAA+C,IAAAA;AACIN,IAAAA,MAAWA,GAAQtN,IAAO4N,EAAAA;EAC9B,GACA,CAAC5N,IAAOsN,EAAAA,CAAAA;AAGZ,aACIvO,oBAAAA,KAAA,QAAA,EACIuC,GAAGtB,GAAM4H,IACTnG,GAAGzB,GAAMyG,IACTG,OAAO5G,GAAM4G,OACbC,QAAQ7G,GAAM6G,QACdgG,QAAO,OACPP,aAAaU,KAAQ,IAAI,GACzBiB,eAAe,MACftI,MAAK,OACL0G,aAAaY,MAAaD,KAAQ,OAAO,GACzCG,cAAcQ,IACdP,aAAaU,IACbT,cAAcU,GACdT,SAASU,IACT,eAAA,WAAsBhO,GAAMf,GAAAA,CAAAA;AAGxC;AAEA8N,GAAWrN,YAAY,EACnBM,OAAOL,kBAAAA,QAAUC,OAAOC,YACxBI,MAAMN,kBAAAA,QAAUoB,MAAM,CAAC,KAAK,GAAA,CAAA,EAAMlB,YAClCmN,OAAOrN,kBAAAA,QAAU6C,KAAK3C,YACtBgH,QAAQlH,kBAAAA,QAAU0B,OAAOxB,YACzBc,SAAShB,kBAAAA,QAAUwB,UAAU,CAACxB,kBAAAA,QAAUiC,MAAMjC,kBAAAA,QAAUC,MAAAA,CAAAA,GACxDqN,WAAWtN,kBAAAA,QAAU6C,KAAK3C,YAC1BqN,YAAYvN,kBAAAA,QAAUiC,KAAK/B,YAC3BsN,cAAcxN,kBAAAA,QAAUiC,MACxBwL,aAAazN,kBAAAA,QAAUiC,MACvByL,cAAc1N,kBAAAA,QAAUiC,MACxB0L,SAAS3N,kBAAAA,QAAUiC,KAAAA;AAGvB,IAAe9B,SAAAA,cAAAA,MAAKiN,EAAAA;AAApB,ICjFMmB,KAAS,SAAHrP,IAAAA;AAYN,MAXF8I,KAAM9I,GAAN8I,QACA1H,KAAIpB,GAAJoB,MACA+M,KAAKnO,GAALmO,OACAnG,KAAMhI,GAANgI,QACAlG,KAAO9B,GAAP8B,SACAwN,KAAOtP,GAAPsP,SACAjB,KAAUrO,GAAVqO,YACAC,KAAYtO,GAAZsO,cACAC,IAAWvO,GAAXuO,aACAC,IAAYxO,GAAZwO,cACAC,IAAOzO,GAAPyO;AAEA,SAAO3F,GAAOnH,IAAI,SAAAR,IAAAA;AAAK,eACnBjB,oBAAAA,KAACgO,IAAU,EAEP/M,OAAOA,IACPC,MAAMA,IACN+M,OAAOA,IACPnG,QAAQA,IACRlG,SAASA,IACTuM,YAAYA,IACZD,WAAuB,SAAZkB,MAAoBA,GAAQlP,OAAOe,GAAMf,IACpDkO,cAAcA,IACdC,aAAaA,GACbC,cAAcA,GACdC,SAASA,EAAAA,GAXJtN,GAAMf,EAAAA;EAYb,CAAA;AAEV;AAEAiP,GAAOxO,YAAY,EACfiI,QAAQhI,kBAAAA,QAAUsB,QACdtB,kBAAAA,QAAUuB,MAAM,EACZjC,IAAIU,kBAAAA,QAAUwB,UAAU,CACpBxB,kBAAAA,QAAU0B,QACV1B,kBAAAA,QAAUyB,QACVzB,kBAAAA,QAAU4B,WAAWC,IAAAA,CAAAA,CAAAA,EACtB3B,YACHyB,GAAG3B,kBAAAA,QAAU0B,OAAOxB,YACpB4B,GAAG9B,kBAAAA,QAAU0B,OAAOxB,YACpBU,QAAQZ,kBAAAA,QAAUsB,QAAQtB,kBAAAA,QAAUC,MAAAA,EAAQC,WAAAA,CAAAA,CAAAA,EAElDA,YACFI,MAAMN,kBAAAA,QAAUoB,MAAM,CAAC,KAAK,GAAA,CAAA,EAAMlB,YAClCmN,OAAOrN,kBAAAA,QAAU6C,KAAK3C,YACtBgH,QAAQlH,kBAAAA,QAAU0B,OAAOxB,YACzBc,SAAShB,kBAAAA,QAAUwB,UAAU,CAACxB,kBAAAA,QAAUiC,MAAMjC,kBAAAA,QAAUC,MAAAA,CAAAA,EAASC,YACjEsO,SAASxO,kBAAAA,QAAUC,QACnBsN,YAAYvN,kBAAAA,QAAUiC,KAAK/B,YAC3BsN,cAAcxN,kBAAAA,QAAUiC,MACxBwL,aAAazN,kBAAAA,QAAUiC,MACvByL,cAAc1N,kBAAAA,QAAUiC,MACxB0L,SAAS3N,kBAAAA,QAAUiC,KAAAA;AAGvB,IAAe9B,SAAAA,cAAAA,MAAKoO,EAAAA;AAApB,ICxDME,KAAS,SAAHvP,IAAAA;AAAgF,MAA1E0B,KAAM1B,GAAN0B,QAAQ8N,KAAMxP,GAANwP,QAAQC,KAAIzP,GAAJyP,MAAMC,KAAW1P,GAAX0P,aAAaC,KAAW3P,GAAX2P,aAAanE,KAAKxL,GAALwL,OAAOoE,KAAY5P,GAAZ4P,cAC/DvO,KAAQC,GAAAA,GACRuO,IAAWC,GAAkBtE,EAAAA,GAM7BuE,IAAerO,GAChBP,MAAM,CAAA,EACNgI,QAAAA,EACAxH,IAAI,SAAA1B,IAAAA;AAWD,WAVoB,EAChBG,IAAIH,GAAMG,IACVqC,GAAGxC,GAAMwC,GACTG,GAAG3C,GAAM2C,GACT6I,OAAOxL,GAAMM,MACbuG,MAAM7G,GAAMU,OACZqN,QAAQ/N,GAAMwM,aACdjB,OAAOmE,KAAcE,EAAS5P,GAAMM,IAAAA,IAAQ,KAAA;EAIpD,CAAA;AAEJ,aACIL,oBAAAA,KAAA,KAAA,EAAAI,UACKyP,EAAapO,IAAI,SAAA1B,IAAAA;AAAK,eACnBC,oBAAAA,KAAC8P,IAAQ,EAELvN,GAAGxC,GAAMwC,GACTG,GAAG3C,GAAM2C,GACT6I,OAAOxL,GAAMwL,OACb+D,QAAQA,IACRC,MAAMA,IACN9O,OAAOV,GAAM6G,MACb4I,aAAaA,IACbjD,aAAaxM,GAAM+N,QACnBxC,OAAOvL,GAAMuL,OACboE,cAAcA,IACdvO,OAAOA,GAAAA,GAXFpB,GAAMG,EAAAA;EAAAA,CAAAA,EAAAA,CAAAA;AAgB/B;AAEAmP,GAAO1O,YAAY,EACfa,QAAQZ,kBAAAA,QAAUsB,QAAQtB,kBAAAA,QAAUC,MAAAA,GACpCyO,QAAQ1O,kBAAAA,QAAUiC,MAClB0M,MAAM3O,kBAAAA,QAAU0B,OAAOxB,YACvBL,OAAOG,kBAAAA,QAAUiC,KAAK/B,YACtB0O,aAAa5O,kBAAAA,QAAU0B,OAAOxB,YAC9ByL,aAAa3L,kBAAAA,QAAUiC,KAAK/B,YAC5B2O,aAAa7O,kBAAAA,QAAU6C,KAAK3C,YAC5BwK,OAAO1K,kBAAAA,QAAUwB,UAAU,CAACxB,kBAAAA,QAAUyB,QAAQzB,kBAAAA,QAAUiC,IAAAA,CAAAA,EAAO/B,YAC/D4O,cAAc9O,kBAAAA,QAAU0B,OAAAA;AAG5B,IAAevB,SAAAA,cAAAA,MAAKsO,EAAAA;AAApB,IC1DMU,KAAO,SAAHjQ,IAAAA;AAYJ,MAXF0B,KAAM1B,GAAN0B,QACAqG,KAAK/H,GAAL+H,OACAC,KAAMhI,GAANgI,QACAkI,KAAMlQ,GAANkQ,QACA7B,KAAUrO,GAAVqO,YACAC,KAAYtO,GAAZsO,cACAC,IAAWvO,GAAXuO,aACAC,IAAYxO,GAAZwO,cACAC,IAAOzO,GAAPyO,SACA3M,KAAO9B,GAAP8B,SACAqM,KAAKnO,GAALmO,OAEAO,IAAuCC,EAAAA,GAA/BwB,IAAazB,EAAbyB,eAAetB,KAAWH,EAAXG,aAEjBC,SAAmB9C,cAAAA,aACrB,SAAC/L,IAAO8O,IAAAA;AACJoB,UACInB,cAAAA,eAAclN,IAAS,EAAE7B,OAAAA,GAAAA,CAAAA,GACzB,CAACA,GAAMwC,IAAIyN,GAAOE,MAAMnQ,GAAM2C,IAAIsN,GAAOG,GAAAA,GACzC,KAAA,GAEJhC,GAAWpO,EAAAA,GACXqO,MAAgBA,GAAarO,IAAO8O,EAAAA;EACxC,GACA,CAACV,IAAY8B,GAAerO,IAASwM,IAAc4B,EAAAA,CAAAA,GAGjDjB,QAAkBjD,cAAAA,aACpB,SAAC/L,IAAO8O,IAAAA;AACJoB,UACInB,cAAAA,eAAclN,IAAS,EAAE7B,OAAAA,GAAAA,CAAAA,GACzB,CAACA,GAAMwC,IAAIyN,GAAOE,MAAMnQ,GAAM2C,IAAIsN,GAAOG,GAAAA,GACzC,KAAA,GAEJhC,GAAWpO,EAAAA,GACXsO,KAAeA,EAAYtO,IAAO8O,EAAAA;EACrC,GACD,CAACV,IAAY8B,GAAerO,IAASyM,CAAAA,CAAAA,GAGnCW,SAAmBlD,cAAAA,aACrB,SAAC/L,IAAO8O,IAAAA;AACJF,IAAAA,GAAAA,GACAR,GAAW,IAAA,GACXG,KAAgBA,EAAavO,IAAO8O,EAAAA;EACvC,GACD,CAACF,IAAaR,IAAYG,CAAAA,CAAAA,GAGxBW,SAAcnD,cAAAA,aAChB,SAAC/L,IAAO8O,IAAAA;AACJN,SAAWA,EAAQxO,IAAO8O,EAAAA;EAC9B,GACA,CAACN,CAAAA,CAAAA;AAGL,aACIvO,oBAAAA,KAACoQ,GAAQ,EACLC,OAAO7O,IACPqG,OAAOA,IACPC,QAAQA,IACRsG,cAAcQ,IACdP,aAAaU,GACbT,cAAcU,IACdT,SAASU,IACThB,OAAOA,GAAAA,CAAAA;AAGnB;AAEA8B,GAAKpP,YAAY,EACba,QAAQZ,kBAAAA,QAAUsB,QAAQtB,kBAAAA,QAAUC,MAAAA,EAAQC,YAC5C+G,OAAOjH,kBAAAA,QAAU0B,OAAOxB,YACxBgH,QAAQlH,kBAAAA,QAAU0B,OAAOxB,YACzBkP,QAAQpP,kBAAAA,QAAUC,OAAOC,YACzBqN,YAAYvN,kBAAAA,QAAUiC,KAAK/B,YAC3BsN,cAAcxN,kBAAAA,QAAUiC,MACxBwL,aAAazN,kBAAAA,QAAUiC,MACvByL,cAAc1N,kBAAAA,QAAUiC,MACxB0L,SAAS3N,kBAAAA,QAAUiC,MACnBjB,SAAShB,kBAAAA,QAAUwB,UAAU,CAACxB,kBAAAA,QAAUiC,MAAMjC,kBAAAA,QAAUC,MAAAA,CAAAA,EAASC,YACjEmN,OAAOrN,kBAAAA,QAAU6C,KAAK3C,WAAAA;AAG1B,IAAeC,SAAAA,cAAAA,MAAKgP,EAAAA;AAApB,ICrEMO,MAAO,SAAAC,IAAAA;AACT,MACIlQ,KAkEAkQ,GAlEAlQ,MACQkJ,KAiERgH,GAjEA5N,QACAC,KAgEA2N,GAhEA3N,SACQ6G,KA+DR8G,GA/DAzN,QACAC,KA8DAwN,GA9DAxN,SACAC,KA6DAuN,GA7DAvN,QACAC,IA4DAsN,GA5DAtN,OACA6B,IA2DAyL,GA3DAzL,mBAEAN,KAyDA+L,GAzDA/L,QAEQgM,KAuDRD,GAvDAP,QACAnI,IAsDA0I,GAtDA1I,OACAC,IAqDAyI,GArDAzI,QAEA3E,KAmDAoN,GAnDApN,SACAE,KAkDAkN,GAlDAlN,WACAC,IAiDAiN,GAjDAjN,YACAC,KAgDAgN,GAhDAhN,UACAC,KA+CA+M,GA/CA/M,aACAE,KA8CA6M,GA9CA7M,aACAC,IA6CA4M,GA7CA5M,aACAC,KA4CA2M,GA5CA3M,aAEAmB,KA0CAwL,GA1CAxL,WACAL,KAyCA6L,GAzCA7L,YACAC,IAwCA4L,GAxCA5L,aACAC,KAuCA2L,GAvCA3L,eAEAf,IAqCA0M,GArCA1M,cACAC,KAoCAyM,GApCAzM,aACAC,IAmCAwM,GAnCAxM,WACAC,IAkCAuM,GAlCAvM,YACAE,KAiCAqM,GAjCArM,kBACAC,KAgCAoM,GAhCApM,kBACAC,KA+BAmM,GA/BAnM,kBACAC,KA8BAkM,GA9BAlM,YACAoM,IA6BAF,GA7BAE,mBAEA9J,IA2BA4J,GA3BA5J,MACAC,KA0BA2J,GA1BA3J,MAEAtC,KAwBAiM,GAxBAjM,SAEAU,IAsBAuL,GAtBAvL,SAEAE,IAoBAqL,GApBArL,eAEAU,IAkBA2K,GAlBA3K,SACAT,IAiBAoL,GAjBApL,WAEAiJ,KAeAmC,GAfAnC,cACAC,KAcAkC,GAdAlC,aACAC,MAaAiC,GAbAjC,cACAC,MAYAgC,GAZAhC,SAEA3M,MAUA2O,GAVA3O,SAEAwD,MAQAmL,GARAnL,cACAC,MAOAkL,GAPAlL,aACAC,MAMAiL,GANAjL,cAEAC,MAIAgL,GAJAhL,iBACAC,MAGA+K,GAHA/K,eAEAG,MACA4K,GADA5K,MAGJ+K,MAAqEC,GACjE9I,GACAC,GACA0I,EAAAA,GAHIR,MAAMU,IAANV,QAAQY,MAAUF,IAAVE,YAAYC,MAAWH,IAAXG,aAAaC,MAAUJ,IAAVI,YAAYC,MAAWL,IAAXK,aAMrDC,MAUI5H,GAAQ,EACR/I,MAAAA,IACAsC,QAAQ4G,IACR3G,SAAAA,IACAE,QAAQ2G,IACR1G,SAAAA,IACA8E,OAAO+I,KACP9I,QAAQ+I,KACRrM,QAAAA,IACAvB,OAAAA,GACA6B,mBAAAA,GACAd,YAAAA,GACAG,kBAAAA,IACAiB,cAAAA,IAAAA,CAAAA,GAtBAuG,MAAUqF,IAAVrF,YACAE,MAAWmF,IAAXnF,aACAW,MAAawE,IAAbxE,eACAC,MAAauE,IAAbvE,eACAtB,MAAM6F,IAAN7F,QACAxI,MAAMqO,IAANrO,QACAG,MAAMkO,IAANlO,QACA8F,MAAMoI,IAANpI,QACApH,MAAMwP,IAANxP,QAiBEL,MAAQC,GAAAA,GACRkJ,MAAgBC,GAAkBvG,GAAY7C,GAAAA,GAC9CqJ,MAAsBD,GAAkBpG,IAAkBhD,GAAAA,GAEhEsJ,UAAwCC,cAAAA,UAAS,IAAA,GAA1CuG,MAAYxG,IAAA,CAAA,GAAEyG,KAAezG,IAAA,CAAA,GACpC0G,SAAwCzG,cAAAA,UAAS,IAAA,GAA1C0G,MAAYD,GAAA,CAAA,GAAEE,KAAeF,GAAA,CAAA,GAE9BG,KAAY,EACdC,UACIvR,oBAAAA,KAACwR,GAAI,EAEDrQ,OAAOA,KACP0G,OAAO+I,KACP9I,QAAQ+I,KACRlO,QAAQa,KAAcb,MAAS,MAC/BG,QAAQY,KAAcZ,MAAS,MAC/B2O,SAAS9N,GACT+N,SAAS9N,GAAAA,GAPL,MAAA,GAUZU,aACItE,oBAAAA,KAAC2R,IAAgB,EAEbrN,SAASA,IACTuD,OAAO+I,KACP9I,QAAQ+I,KACRlO,QAAQA,KACRG,QAAQA,KACR3B,OAAOA,IAAAA,GANH,SAAA,GASZyQ,UACI5R,oBAAAA,KAAC6R,GAAI,EAEDlP,QAAQA,KACRG,QAAQA,KACR+E,OAAO+I,KACP9I,QAAQ+I,KACR1P,OAAOA,KACPgP,KAAKhN,IACL2O,OAAOzO,IACP0O,QAAQzO,GACR4M,MAAM3M,GAAAA,GATF,MAAA,GAYZyO,OAAO,MACPrE,WACI3N,oBAAAA,KAAC+N,IAAK,EAAaJ,OAAOxC,KAAQqB,eAAeA,KAAezH,WAAWA,GAAAA,GAAhE,OAAA,GAEf6D,QAAQ,MACRpH,QAAQ,MACRyQ,WAAW,MACXC,MAAM,MACNlN,SAASA,EAAQvD,IAAI,SAAC0Q,IAAQxJ,IAAAA;AAAC,eAC3B3I,oBAAAA,KAACoS,IAAY1M,GAAAA,CAAAA,GAELyM,IAAM,EACVE,gBAAgBzB,KAChB0B,iBAAiBzB,KACjBxQ,MAAM8R,GAAO9R,QAAQsL,KACrBxK,OAAOA,KACP0K,aAAasG,GAAOtG,cAAcA,MAAAA,OAAc0G,CAAAA,GAAU,YAN3C5J,EAAAA;EAAAA,CAAAA,EAAAA,GAWrB6J,KAAYC,GAAS9L,GAAMwE,KAAQvE,EAAAA;AAiGzC,SA/FIlC,OACA4M,GAAUU,YACNhS,oBAAAA,KAACyN,IAAK,EAEFhB,eAAeA,KACf9H,aAAaA,GACbC,eAAeA,IACf+I,OAAOxC,IAAAA,GAJH,OAAA,IASZjG,KAAAA,UAAiBE,QACjBkM,GAAU1I,aACN5I,oBAAAA,KAACmP,IAAM,EAEHvG,QAAQA,KACR1H,MAAMkE,KACN6I,OAAO5I,KACPyC,QAAQ+I,KACRjP,SAAS0D,KACT8J,SAASgC,KACTjD,YAAYkD,IACZjD,cAAcA,IACdC,aAAaA,IACbC,cAAcA,KACdC,SAASA,IAAAA,GAXL,QAAA,IAgBZ1K,MACAyN,GAAU9P,aACNxB,oBAAAA,KAACqP,IAAM,EAEH7N,QAAQA,KACR8N,QAAQxL,IACRyL,MAAMxL,GACNtD,OAAO6J,KACPkF,aAAatL,IACbqI,aAAa/B,KACbiF,aAAarL,IACbkH,OAAOjH,IACPqL,cAAce,EAAAA,GATV,QAAA,IAcZvL,KAAiBK,QACI,SAAjB0L,QACAK,GAAUW,gBACNjS,oBAAAA,KAAC0S,GAAS,EAEN7K,OAAO+I,KACP9I,QAAQ+I,KACRtO,GAAG0O,IAAa1O,GAChBG,GAAGuO,IAAavO,GAChBwD,MAAMV,IAAAA,GALF,WAAA,IASK,SAAjB4L,QACAE,GAAUW,gBACNjS,oBAAAA,KAAC0S,GAAS,EAEN7K,OAAO+I,KACP9I,QAAQ+I,KACRtO,GAAG6O,IAAa7O,GAChBG,GAAG0O,IAAa1O,GAChBwD,MAAMd,IAAAA,GALF,WAAA,KAWhBF,KAAiBU,KAAAA,UAAWR,QAC5BkM,GAAUY,WACNlS,oBAAAA,KAAC+P,IAAI,EAEDvO,QAAQA,KACRqG,OAAO+I,KACP9I,QAAQ+I,KACRb,QAAQA,KACRZ,SAAS6B,KACT9C,YAAY+C,IACZ9C,cAAcA,IACdC,aAAaA,IACbC,cAAcA,KACdC,SAASA,KACT3M,SAASA,KACTqM,OAAO9I,EAAAA,GAZH,MAAA,QAkBZnF,oBAAAA,KAAC2S,IAAU,EACPhM,MAAM6L,IACN3K,OAAOiJ,KACPhJ,QAAQiJ,KACRf,QAAQA,KACRrK,MAAMA,KAAKvF,UAEV4C,GAAOvB,IAAI,SAACmR,IAAOjK,IAAAA;AAChB,WAAqB,cAAA,OAAViK,SAEH5S,oBAAAA,KAAC6S,cAAAA,UAAQ,EAAAzS,UACJwS,GAAKlN,GAAAA,CAAAA,GACC6K,IAAK,EACRK,YAAAA,KACAC,aAAAA,KACA1F,QAAAA,KACAvC,QAAAA,KACApH,QAAAA,KACAmB,QAAAA,KACAG,QAAAA,KACA0J,eAAAA,KACAC,eAAAA,KACAwE,cAAAA,KACAC,iBAAAA,IACAE,cAAAA,KACAC,iBAAAA,GAAAA,CAAAA,CAAAA,EAAAA,GAfO1I,EAAAA,IAqBhB2I,GAAUsB,EAAAA;EAAAA,CAAAA,EAAAA,CAAAA;AAIjC;AAEAtC,IAAK3P,YAAY8E,IACjB6K,IAAKwC,eAAetM;AAEpB,IAAeuM,KAAAA,GAAczC,GAAAA;AAA7B,ICpUM0C,KAAiB,SAAAzC,IAAAA;AAAK,aACxBvQ,oBAAAA,KAACiT,IAAiB,EAAA7S,UACb,SAAAN,IAAAA;AAAA,QAAG+H,KAAK/H,GAAL+H,OAAOC,KAAMhI,GAANgI;AAAM,eAAO9H,oBAAAA,KAACsQ,IAAI5K,GAAA,EAACmC,OAAOA,IAAOC,QAAQA,GAAAA,GAAYyI,EAAAA,CAAAA;EAAS,EAAA,CAAA;AACzD;ADiUxB,IExTM2C,KAAa,SAAHpT,IAAAA;AAgDV,MA/CF+H,KAAK/H,GAAL+H,OACAC,KAAMhI,GAANgI,QACQ0I,KAAa1Q,GAArBkQ,QACAhK,IAAUlG,GAAVkG,YAEA3F,IAAIP,GAAJO,MACQkJ,KAAUzJ,GAAlB6C,QACAC,KAAO9C,GAAP8C,SACQ6G,IAAU3J,GAAlBgD,QACAC,IAAOjD,GAAPiD,SACAE,KAAKnD,GAALmD,OAEAD,KAAMlD,GAANkD,QAEAwB,IAAM1E,GAAN0E,QACAO,KAASjF,GAATiF,WAEAL,KAAU5E,GAAV4E,YACAI,IAAiBhF,GAAjBgF,mBACAH,KAAW7E,GAAX6E,aAEAd,IAAY/D,GAAZ+D,cACAE,KAASjE,GAATiE,WACAC,KAAUlE,GAAVkE,YACAE,KAAgBpE,GAAhBoE,kBACAC,IAAgBrE,GAAhBqE,kBAEAX,IAAW1D,GAAX0D,aACAG,IAAW7D,GAAX6D,aACAD,IAAW5D,GAAX4D,aACAE,KAAW9D,GAAX8D,aACAT,IAAOrD,GAAPqD,SACAE,IAASvD,GAATuD,WACAC,KAAUxD,GAAVwD,YACAC,KAAQzD,GAARyD,UAEAyB,IAAOlF,GAAPkF,SAEAE,KAAapF,GAAboF,eACAC,IAASrF,GAATqF,WAGAmJ,IAAYxO,GAAZwO,cACAC,KAAOzO,GAAPyO,SACA3M,KAAO9B,GAAP8B,SAEAuR,IAASrT,GAATqT,WAEMC,QAAWC,cAAAA,QAAO,IAAA,GACxB3C,IAAqEC,GACjE9I,IACAC,IACA0I,EAAAA,GAHIR,MAAMU,EAANV,QAAQY,MAAUF,EAAVE,YAAYC,MAAWH,EAAXG,aAAaC,MAAUJ,EAAVI,YAAYC,MAAWL,EAAXK,aAK/C5P,MAAQC,GAAAA,GACdqJ,UAAwCC,cAAAA,UAAS,IAAA,GAA1CuG,MAAYxG,IAAA,CAAA,GAAEyG,MAAezG,IAAA,CAAA,GAEpCuG,MAAyE5H,GAAQ,EAC7E/I,MAAAA,GACAsC,QAAQ4G,IACR3G,SAAAA,IACAE,QAAQ2G,GACR1G,SAAAA,GACA8E,OAAO+I,KACP9I,QAAQ+I,KACRrM,QAAAA,GACAvB,OAAAA,IACA6B,mBAAAA,GACAd,YAAAA,IACAG,kBAAAA,EAAAA,CAAAA,GAZIqI,MAAawE,IAAbxE,eAAeC,MAAauE,IAAbvE,eAAetB,MAAM6F,IAAN7F,QAAQxI,MAAMqO,IAANrO,QAAQG,MAAMkO,IAANlO,QAAQtB,MAAMwP,IAANxP,QAe9D8R,MAA8BC,EAAe,EACzC/R,QAAAA,KACAqG,OAAO+I,KACP9I,QAAQ+I,KACR5C,OAAO9I,EAAAA,CAAAA,GAJHqO,MAAQF,IAARE,UAAUC,MAAOH,IAAPG;AAOlBC,oBAAAA,WAAU,WAAA;AACFP,UACAA,EAAU/D,UAAUgE,EAAShE,UAGjCgE,EAAShE,QAAQvH,QAAQiJ,MAAa9K,GACtCoN,EAAShE,QAAQtH,SAASiJ,MAAc/K;AAExC,QAAM2N,KAAMP,EAAShE,QAAQwE,WAAW,IAAA;AAExCD,IAAAA,GAAIE,MAAM7N,GAAYA,CAAAA,GAEtB2N,GAAIG,YAAY3S,IAAM4S,YACtBJ,GAAIK,SAAS,GAAG,GAAGlD,KAAYC,GAAAA,GAC/B4C,GAAIM,UAAUjE,IAAOE,MAAMF,IAAOG,GAAAA,GAElCnN,GAAOgF,QAAQ,SAAA4K,IAAAA;AAuGX,UAtGqB,cAAA,OAAVA,MACPA,GAAM,EACFe,KAAAA,IACA/C,YAAAA,KACAC,aAAAA,KACA1F,QAAAA,KACA3J,QAAAA,KACAmB,QAAAA,KACAG,QAAAA,KACAiC,WAAAA,IACAyH,eAAAA,KACAC,eAAAA,KACAwE,cAAAA,KACAC,iBAAAA,IAAAA,CAAAA,GAIM,WAAV0B,MAAoBzR,IAAMoQ,KAAKrK,KAAKqG,cAAc,MAClDoG,GAAI5O,YAAY5D,IAAMoQ,KAAKrK,KAAKqG,aAChCoG,GAAIO,cAAc/S,IAAMoQ,KAAKrK,KAAK4G,QAElCtK,KACI2Q,EAAwBR,IAAK,EACzB9L,OAAO+I,KACP9I,QAAQ+I,KACRgD,OAAOlR,KACPzB,MAAM,KACNkT,QAAQzQ,EAAAA,CAAAA,GAGhBD,KACIyQ,EAAwBR,IAAK,EACzB9L,OAAO+I,KACP9I,QAAQ+I,KACRgD,OAAO/Q,KACP5B,MAAM,KACNkT,QAAQxQ,GAAAA,CAAAA,IAIN,WAAVgP,MACAyB,EAAmBV,IAAK,EACpBhR,QAAAA,KACAG,QAAAA,KACA+E,OAAO+I,KACP9I,QAAQ+I,KACRV,KAAKhN,GACL2O,OAAOzO,GACP0O,QAAQzO,IACR4M,MAAM3M,IACNpC,OAAAA,IAAAA,CAAAA,GAIM,YAAVyR,MAAAA,SAAqBlO,OACrBiP,GAAIW,KAAAA,GACJX,GAAIY,cAAc5P,IAElB8H,IAAc+H,QAAQb,EAAAA,GACtBxI,IAAOnD,QAAQ,SAAAyD,IAAAA;AACXkI,QAAAA,GAAIG,YAAYrI,GAAMhL,OACtBkT,GAAIc,UAAAA,GACJhI,IAAchB,GAAMpL,KAAKoB,IAAI,SAAA2F,IAAAA;AAAC,iBAAIA,GAAEgF;QAAQ,CAAA,CAAA,GAC5CuH,GAAI/M,KAAAA;MACR,CAAA,GAEA+M,GAAIe,QAAAA,IAGM,YAAV9B,OACApG,IAAcgI,QAAQb,EAAAA,GACtBxI,IAAOnD,QAAQ,SAAAyD,IAAAA;AACXkI,QAAAA,GAAIO,cAAczI,GAAMhL,OACxBkT,GAAI5O,YAAYA,IAChB4O,GAAIc,UAAAA,GACJjI,IAAcf,GAAMpL,KAAKoB,IAAI,SAAA2F,IAAAA;AAAC,iBAAIA,GAAEgF;QAAQ,CAAA,CAAA,GAC5CuH,GAAI7F,OAAAA;MACR,CAAA,IAGU,aAAV8E,MAAAA,SAAsB/O,KAAyBE,KAAY,KAC3DvC,IAAOwG,QAAQ,SAAAjI,IAAAA;AACX4T,QAAAA,GAAIG,YAAY/T,GAAMU,OACtBkT,GAAIc,UAAAA,GACJd,GAAIgB,IAAI5U,GAAMwC,GAAGxC,GAAM2C,GAAGqB,KAAY,GAAG,GAAG,IAAI6Q,KAAKC,EAAAA,GACrDlB,GAAI/M,KAAAA,GAEA1C,KAAmB,MACnByP,GAAIO,cAAcnU,GAAMwM,aACxBoH,GAAI5O,YAAYb,IAChByP,GAAI7F,OAAAA;MAEZ,CAAA,GAGU,WAAV8E,MAAAA,SAAoBzN,MACpB2P,GAAsBnB,IAAKF,GAAAA,GACvBxC,OACA8D,GAA0BpB,IAAKF,KAASxC,IAAa5E,KAAAA,IAI/C,cAAVuG,IAAqB;AACrB,YAAMjH,KAAaR,IACd1J,IAAI,SAAAgK,IAAAA;AAAK,iBAAK,EACXvL,IAAIuL,GAAMvL,IACVoL,OAAOG,GAAMvL,IACbO,OAAOgL,GAAMhL,MAAAA;QAChB,CAAA,EACAwI,QAAAA;AAELjE,UAAQgD,QAAQ,SAAAmK,IAAAA;AACZ6C,YAAqBrB,IAAGjO,GAAAA,CAAAA,GACjByM,IAAM,EACT9R,MAAM8R,GAAO9R,QAAQsL,IACrB0G,gBAAgBzB,KAChB0B,iBAAiBzB,KACjB1P,OAAAA,IAAAA,CAAAA,CAAAA;QAER,CAAA;MACJ;IACJ,CAAA;EACH,GAAE,CACCiS,GACAtC,KACAC,KACA/N,IACA7B,KACAqL,KACArB,KACAxI,KACAG,KACAU,GACAG,GACAD,GACAE,IACAT,GACAE,GACAC,IACAC,IACAyB,GACAxD,KACAqC,GACAE,IACAkN,GAAAA,CAAAA;AAGJ,MAAMgE,UAAyBnJ,cAAAA,aAC3B,SAAA+C,IAAAA;AACI,QAAAqG,KAAeC,GAAkB/B,EAAShE,SAASP,EAAAA,GAA5CtM,KAAC2S,GAAA,CAAA,GAAExS,KAACwS,GAAA,CAAA;AACX,QAAA,CAAKE,GAAepF,IAAOE,MAAMF,IAAOG,KAAKS,KAAYC,KAAatO,IAAGG,EAAAA;AAAI,aAAO;AAEpF,QAAM2S,KAAa7B,IAAShI,KAAKjJ,KAAIyN,IAAOE,MAAMxN,KAAIsN,IAAOG,GAAAA;AAC7D,WAAO3O,IAAO6T,EAAAA;EAClB,GACA,CAACjC,GAAUpD,KAAQY,KAAYC,KAAa2C,GAAAA,CAAAA,GAGhDhF,MAA8CC,EAAAA,GAAtCC,MAAoBF,IAApBE,sBAAsBC,MAAWH,IAAXG,aAExB2G,UAAmBxJ,cAAAA,aACrB,SAAA+C,IAAAA;AACI,QAAM9O,KAAQkV,IAAuBpG,EAAAA;AACrCqC,IAAAA,IAAgBnR,EAAAA,GAEZA,KACA2O,QAAqBI,cAAAA,eAAclN,IAAS,EAAE7B,OAAAA,GAAAA,CAAAA,GAAU8O,EAAAA,IAExDF,IAAAA;EAER,GACA,CAACsG,KAAwB/D,KAAiBxC,KAAsBC,KAAa/M,EAAAA,CAAAA,GAG3EoN,UAAmBlD,cAAAA,aACrB,SAAA+C,IAAAA;AACIF,IAAAA,IAAAA,GACAuC,IAAgB,IAAA,GAChBD,OAAgB3C,KAAgBA,EAAa2C,KAAcpC,EAAAA;EAC9D,GACD,CAACF,KAAauC,KAAiB5C,CAAAA,CAAAA,GAG7BW,UAAcnD,cAAAA,aAChB,SAAA+C,IAAAA;AACI,QAAIN,IAAS;AACT,UAAMxO,KAAQkV,IAAuBpG,EAAAA;AACrC9O,MAAAA,MAASwO,GAAQxO,IAAO8O,EAAAA;IAC5B;EACJ,GACA,CAACoG,KAAwB1G,EAAAA,CAAAA;AAG7B,aACIvO,oBAAAA,KAAA,UAAA,EACIuV,KAAKnC,GACLvL,OAAOiJ,MAAa9K,GACpB8B,QAAQiJ,MAAc/K,GACtBrE,OAAO,EACHkG,OAAOiJ,KACPhJ,QAAQiJ,KACRyE,QAAQtQ,KAAgB,SAAS,SAAA,GAErCkJ,cAAclJ,KAAgBoQ,MAAAA,QAC9BjH,aAAanJ,KAAgBoQ,MAAAA,QAC7BhH,cAAcpJ,KAAgB8J,MAAAA,QAC9BT,SAASrJ,KAAgB+J,MAAAA,OAAcsD,CAAAA;AAGnD;AAEAW,GAAWvS,YAAYoF,IACvBmN,GAAWJ,eAAejM;AAE1B,IAAM4O,KAA0B1C,GAAcG,EAAAA;AAA9C,IAEAwC,SAAeC,cAAAA,YAAW,SAACpF,IAAOgF,IAAAA;AAAG,aAAKvV,oBAAAA,KAACyV,IAAuB/P,GAAAA,CAAAA,GAAK6K,IAAK,EAAE4C,WAAWoC,GAAAA,CAAAA,CAAAA;AAAO,CAAA;AAFhG,IC5TeI,SAAAA,cAAAA,YANc,SAACpF,IAAOgF,IAAAA;AAAG,aACpCvV,oBAAAA,KAACiT,IAAiB,EAAA7S,UACb,SAAAN,IAAAA;AAAA,QAAG+H,KAAK/H,GAAL+H,OAAOC,KAAMhI,GAANgI;AAAM,eAAO9H,oBAAAA,KAACkT,IAAUxN,GAAA,EAACmC,OAAOA,IAAOC,QAAQA,GAAAA,GAAYyI,IAAK,EAAEgF,KAAKA,GAAAA,CAAAA,CAAAA;EAAO,EAAA,CAAA;AACzE,CAAA;", "names": ["n", "i", "p", "x", "i2", "r", "j", "k", "e", "t", "a", "b", "pr", "l", "x", "r", "w", "x", "i", "j", "n", "x", "t2", "x1", "y1", "x2", "t", "v", "p", "e", "P", "k", "p", "i", "a", "b", "x", "r", "j", "e", "f", "n", "collinear", "l", "t", "h", "t2", "getAccessor", "directive", "datum", "computeMeshPoints", "_ref", "points", "_ref$x", "x", "_ref$y", "y", "getX", "getY", "map", "point", "computeMesh", "_ref2", "width", "height", "debug", "delaunay", "Delaunay", "from", "voronoi", "useVoronoiMesh", "points2d", "useMemo", "<PERSON><PERSON>", "_ref", "nodes", "width", "height", "x", "y", "onMouseEnter", "onMouseMove", "onMouseLeave", "onClick", "debug", "elementRef", "useRef", "_useState", "useState", "currentIndex", "setCurrentIndex", "_useVoronoiMesh", "useVoronoiMesh", "points", "delaunay", "voronoi", "voronoiPath", "useMemo", "render", "getIndexAndNodeFromEvent", "useCallback", "event", "current", "_getRelativeCursor", "getRelativeCursor", "index", "find", "handleMouseEnter", "_getIndexAndNodeFromE", "node", "handleMouseMove", "_getIndexAndNodeFromE2", "handleMouseLeave", "previousNode", "handleClick", "_getIndexAndNodeFromE3", "_jsxs", "ref", "children", "_Fragment", "_jsx", "d", "stroke", "strokeWidth", "opacity", "fill", "renderCell", "style", "cursor", "renderVoronoiToCanvas", "ctx", "save", "globalAlpha", "beginPath", "strokeStyle", "lineWidth", "restore", "renderVoronoiCellToCanvas", "fillStyle", "LinePointTooltip", "_ref", "point", "_jsx", "BasicTooltip", "id", "_jsxs", "children", "data", "xFormatted", "yFormatted", "enableChip", "color", "serieColor", "propTypes", "PropTypes", "object", "isRequired", "memo", "SliceTooltip", "slice", "axis", "theme", "useTheme", "otherAxis", "TableTooltip", "rows", "points", "map", "Chip", "style", "tooltip", "chip", "serieId", "tableCellValue", "oneOf", "commonPropTypes", "arrayOf", "shape", "oneOfType", "string", "number", "x", "instanceOf", "Date", "y", "xScale", "xFormat", "func", "yScale", "yFormat", "layers", "curve", "lineCurvePropType", "axisTop", "axisPropType", "axisRight", "axisBottom", "axisLeft", "enableGridX", "bool", "enableGridY", "gridXValues", "gridYValues", "enablePoints", "pointSymbol", "pointSize", "pointColor", "any", "pointBorderWidth", "pointBorderColor", "enablePointLabel", "pointLabel", "markers", "value", "colors", "ordinalColorsPropType", "enableArea", "areaOpacity", "areaBlendMode", "blendModePropType", "areaBaselineValue", "lineWidth", "legends", "LegendPropShape", "isInteractive", "debug<PERSON><PERSON>", "enableSlices", "debugSlices", "sliceTooltip", "enableCrosshair", "crosshairType", "LinePropTypes", "_extends", "role", "<PERSON><PERSON><PERSON>", "motionPropTypes", "defsPropTypes", "LineCanvasPropTypes", "pixelRatio", "commonDefaultProps", "type", "min", "max", "from", "scheme", "PointTooltip", "LineDefaultProps", "animate", "motionConfig", "defs", "fill", "LineCanvasDefaultProps", "window", "devicePixelRatio", "useLineGenerator", "useMemo", "line", "defined", "d", "curveFromProp", "useAreaGenerator", "_ref2", "area", "y1", "y0", "useSlices", "_ref4", "width", "height", "Map", "for<PERSON>ach", "has", "get", "push", "set", "Array", "entries", "sort", "a", "b", "_ref5", "i", "slices", "x0", "slicePoints", "prevSlice", "nextSlice", "reverse", "_ref6", "sliceHeight", "useLine", "_ref7", "_ref7$xScale", "xScaleSpec", "_ref7$yScale", "yScaleSpec", "_ref7$colors", "_ref7$curve", "_ref7$areaBaselineVal", "_ref7$pointColor", "_ref7$pointBorderColo", "_ref7$enableSlices", "enableSlicesTooltip", "formatX", "useValueFormatter", "formatY", "getColor", "useOrdinalColorScale", "getPointColor", "useInheritedColor", "getPointBorderColor", "_useState", "useState", "hiddenIds", "setHiddenIds", "_useMemo", "computeXYScalesForSeries", "filter", "item", "indexOf", "rawSeries", "series", "_useMemo2", "dataWithColor", "label", "datum", "find", "serie", "Boolean", "legendData", "hidden", "toggleSerie", "useCallback", "state", "concat", "_ref3", "reduce", "acc", "position", "index", "length", "borderColor", "lineGenerator", "areaGenerator", "AreaPath", "path", "_useMotionConfig", "useMotionConfig", "springConfig", "config", "animatedPath", "useAnimatedPath", "animatedProps", "useSpring", "immediate", "animated", "fillOpacity", "strokeWidth", "mixBlendMode", "Areas", "computedLines", "lines", "LinesItem", "thickness", "stroke", "Lines", "SlicesItem", "debug", "isCurrent", "setCurrent", "onMouseEnter", "onMouseMove", "onMouseLeave", "onClick", "_useTooltip", "useTooltip", "showTooltipFromEvent", "hideTooltip", "handleMouseEnter", "event", "createElement", "handleMouseMove", "handleMouseLeave", "handleClick", "strokeOpacity", "Slices", "current", "Points", "symbol", "size", "borderWidth", "enableLabel", "labelYOffset", "get<PERSON><PERSON><PERSON>", "getLabelGenerator", "mappedPoints", "DotsItem", "<PERSON><PERSON>", "margin", "showTooltipAt", "left", "top", "BaseMesh", "nodes", "Line", "props", "<PERSON><PERSON><PERSON><PERSON>", "pointLabelYOffset", "_useDimensions", "useDimensions", "innerWidth", "innerHeight", "outerWidth", "outerHeight", "_useLine", "currentPoint", "setCurrentPoint", "_useState2", "currentSlice", "setCurrentSlice", "layerById", "grid", "Grid", "xValues", "yV<PERSON><PERSON>", "<PERSON><PERSON>ian<PERSON><PERSON><PERSON>", "axes", "Axes", "right", "bottom", "areas", "crosshair", "mesh", "legend", "BoxLegendSvg", "containerWidth", "containerHeight", "undefined", "boundDefs", "bindDefs", "<PERSON><PERSON><PERSON>", "SvgWrapper", "layer", "Fragment", "defaultProps", "<PERSON><PERSON><PERSON><PERSON>", "ResponsiveLine", "ResponsiveWrapper", "LineCanvas", "canvasRef", "canvasEl", "useRef", "_useVoronoiMesh", "useVoronoiMesh", "delaunay", "voronoi", "useEffect", "ctx", "getContext", "scale", "fillStyle", "background", "fillRect", "translate", "strokeStyle", "renderGridLinesToCanvas", "values", "renderAxesToCanvas", "save", "globalAlpha", "context", "beginPath", "restore", "arc", "Math", "PI", "renderVoronoiToCanvas", "renderVoronoiCellToCanvas", "renderLegendToCanvas", "getPointFromMouseEvent", "_getRelativeCursor", "getRelativeCursor", "isCursorInRect", "pointIndex", "handleMouseHover", "ref", "cursor", "LineCanvasWithContainer", "LineCanvas$1", "forwardRef"]}