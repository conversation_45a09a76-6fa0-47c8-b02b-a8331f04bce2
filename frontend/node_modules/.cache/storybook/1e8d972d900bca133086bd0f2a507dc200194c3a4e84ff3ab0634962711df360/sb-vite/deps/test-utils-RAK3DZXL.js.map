{"version": 3, "sources": ["../../../../../react-dom/cjs/react-dom-test-utils.development.js", "../../../../../react-dom/test-utils.js"], "sourcesContent": ["/**\n * @license React\n * react-dom-test-utils.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\nvar React = require('react');\nvar ReactDOM = require('react-dom');\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\n// by calls to these methods by a Babel plugin.\n//\n// In PROD (or in packages without access to React internals),\n// they are left as they are instead.\n\nfunction warn(format) {\n  {\n    {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n\n      printWarning('warn', format, args);\n    }\n  }\n}\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n/**\n * `ReactInstanceMap` maintains a mapping from a public facing stateful\n * instance (key) and the internal representation (value). This allows public\n * methods to accept the user facing instance as an argument and map them back\n * to internal methods.\n *\n * Note that this module is currently shared and assumed to be stateless.\n * If this becomes an actual Map, that will break.\n */\nfunction get(key) {\n  return key._reactInternals;\n}\n\nvar FunctionComponent = 0;\nvar ClassComponent = 1;\n\nvar HostRoot = 3; // Root of a host tree. Could be nested inside another node.\n\nvar HostComponent = 5;\nvar HostText = 6;\n\n// Don't change these two values. They're used by React Dev Tools.\nvar NoFlags =\n/*                      */\n0;\n\nvar Placement =\n/*                    */\n2;\nvar Hydrating =\n/*                    */\n4096;\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nfunction getNearestMountedFiber(fiber) {\n  var node = fiber;\n  var nearestMounted = fiber;\n\n  if (!fiber.alternate) {\n    // If there is no alternate, this might be a new tree that isn't inserted\n    // yet. If it is, then it will have a pending insertion effect on it.\n    var nextNode = node;\n\n    do {\n      node = nextNode;\n\n      if ((node.flags & (Placement | Hydrating)) !== NoFlags) {\n        // This is an insertion or in-progress hydration. The nearest possible\n        // mounted fiber is the parent but we need to continue to figure out\n        // if that one is still mounted.\n        nearestMounted = node.return;\n      }\n\n      nextNode = node.return;\n    } while (nextNode);\n  } else {\n    while (node.return) {\n      node = node.return;\n    }\n  }\n\n  if (node.tag === HostRoot) {\n    // TODO: Check if this was a nested HostRoot when used with\n    // renderContainerIntoSubtree.\n    return nearestMounted;\n  } // If we didn't hit the root, that means that we're in an disconnected tree\n  // that has been unmounted.\n\n\n  return null;\n}\n\nfunction assertIsMounted(fiber) {\n  if (getNearestMountedFiber(fiber) !== fiber) {\n    throw new Error('Unable to find node on an unmounted component.');\n  }\n}\n\nfunction findCurrentFiberUsingSlowPath(fiber) {\n  var alternate = fiber.alternate;\n\n  if (!alternate) {\n    // If there is no alternate, then we only need to check if it is mounted.\n    var nearestMounted = getNearestMountedFiber(fiber);\n\n    if (nearestMounted === null) {\n      throw new Error('Unable to find node on an unmounted component.');\n    }\n\n    if (nearestMounted !== fiber) {\n      return null;\n    }\n\n    return fiber;\n  } // If we have two possible branches, we'll walk backwards up to the root\n  // to see what path the root points to. On the way we may hit one of the\n  // special cases and we'll deal with them.\n\n\n  var a = fiber;\n  var b = alternate;\n\n  while (true) {\n    var parentA = a.return;\n\n    if (parentA === null) {\n      // We're at the root.\n      break;\n    }\n\n    var parentB = parentA.alternate;\n\n    if (parentB === null) {\n      // There is no alternate. This is an unusual case. Currently, it only\n      // happens when a Suspense component is hidden. An extra fragment fiber\n      // is inserted in between the Suspense fiber and its children. Skip\n      // over this extra fragment fiber and proceed to the next parent.\n      var nextParent = parentA.return;\n\n      if (nextParent !== null) {\n        a = b = nextParent;\n        continue;\n      } // If there's no parent, we're at the root.\n\n\n      break;\n    } // If both copies of the parent fiber point to the same child, we can\n    // assume that the child is current. This happens when we bailout on low\n    // priority: the bailed out fiber's child reuses the current child.\n\n\n    if (parentA.child === parentB.child) {\n      var child = parentA.child;\n\n      while (child) {\n        if (child === a) {\n          // We've determined that A is the current branch.\n          assertIsMounted(parentA);\n          return fiber;\n        }\n\n        if (child === b) {\n          // We've determined that B is the current branch.\n          assertIsMounted(parentA);\n          return alternate;\n        }\n\n        child = child.sibling;\n      } // We should never have an alternate for any mounting node. So the only\n      // way this could possibly happen is if this was unmounted, if at all.\n\n\n      throw new Error('Unable to find node on an unmounted component.');\n    }\n\n    if (a.return !== b.return) {\n      // The return pointer of A and the return pointer of B point to different\n      // fibers. We assume that return pointers never criss-cross, so A must\n      // belong to the child set of A.return, and B must belong to the child\n      // set of B.return.\n      a = parentA;\n      b = parentB;\n    } else {\n      // The return pointers point to the same fiber. We'll have to use the\n      // default, slow path: scan the child sets of each parent alternate to see\n      // which child belongs to which set.\n      //\n      // Search parent A's child set\n      var didFindChild = false;\n      var _child = parentA.child;\n\n      while (_child) {\n        if (_child === a) {\n          didFindChild = true;\n          a = parentA;\n          b = parentB;\n          break;\n        }\n\n        if (_child === b) {\n          didFindChild = true;\n          b = parentA;\n          a = parentB;\n          break;\n        }\n\n        _child = _child.sibling;\n      }\n\n      if (!didFindChild) {\n        // Search parent B's child set\n        _child = parentB.child;\n\n        while (_child) {\n          if (_child === a) {\n            didFindChild = true;\n            a = parentB;\n            b = parentA;\n            break;\n          }\n\n          if (_child === b) {\n            didFindChild = true;\n            b = parentB;\n            a = parentA;\n            break;\n          }\n\n          _child = _child.sibling;\n        }\n\n        if (!didFindChild) {\n          throw new Error('Child was not found in either parent set. This indicates a bug ' + 'in React related to the return pointer. Please file an issue.');\n        }\n      }\n    }\n\n    if (a.alternate !== b) {\n      throw new Error(\"Return fibers should always be each others' alternates. \" + 'This error is likely caused by a bug in React. Please file an issue.');\n    }\n  } // If the root is not a host container, we're in a disconnected tree. I.e.\n  // unmounted.\n\n\n  if (a.tag !== HostRoot) {\n    throw new Error('Unable to find node on an unmounted component.');\n  }\n\n  if (a.stateNode.current === a) {\n    // We've determined that A is the current branch.\n    return fiber;\n  } // Otherwise B has to be current branch.\n\n\n  return alternate;\n}\n\nvar assign = Object.assign;\n\n/**\n * `charCode` represents the actual \"character code\" and is safe to use with\n * `String.fromCharCode`. As such, only keys that correspond to printable\n * characters produce a valid `charCode`, the only exception to this is Enter.\n * The Tab-key is considered non-printable and does not have a `charCode`,\n * presumably because it does not produce a tab-character in browsers.\n *\n * @param {object} nativeEvent Native browser event.\n * @return {number} Normalized `charCode` property.\n */\nfunction getEventCharCode(nativeEvent) {\n  var charCode;\n  var keyCode = nativeEvent.keyCode;\n\n  if ('charCode' in nativeEvent) {\n    charCode = nativeEvent.charCode; // FF does not set `charCode` for the Enter-key, check against `keyCode`.\n\n    if (charCode === 0 && keyCode === 13) {\n      charCode = 13;\n    }\n  } else {\n    // IE8 does not implement `charCode`, but `keyCode` has the correct value.\n    charCode = keyCode;\n  } // IE and Edge (on Windows) and Chrome / Safari (on Windows and Linux)\n  // report Enter as charCode 10 when ctrl is pressed.\n\n\n  if (charCode === 10) {\n    charCode = 13;\n  } // Some non-printable keys are reported in `charCode`/`keyCode`, discard them.\n  // Must not discard the (non-)printable Enter-key.\n\n\n  if (charCode >= 32 || charCode === 13) {\n    return charCode;\n  }\n\n  return 0;\n}\n\nfunction functionThatReturnsTrue() {\n  return true;\n}\n\nfunction functionThatReturnsFalse() {\n  return false;\n} // This is intentionally a factory so that we have different returned constructors.\n// If we had a single constructor, it would be megamorphic and engines would deopt.\n\n\nfunction createSyntheticEvent(Interface) {\n  /**\n   * Synthetic events are dispatched by event plugins, typically in response to a\n   * top-level event delegation handler.\n   *\n   * These systems should generally use pooling to reduce the frequency of garbage\n   * collection. The system should check `isPersistent` to determine whether the\n   * event should be released into the pool after being dispatched. Users that\n   * need a persisted event should invoke `persist`.\n   *\n   * Synthetic events (and subclasses) implement the DOM Level 3 Events API by\n   * normalizing browser quirks. Subclasses do not necessarily have to implement a\n   * DOM interface; custom application-specific events can also subclass this.\n   */\n  function SyntheticBaseEvent(reactName, reactEventType, targetInst, nativeEvent, nativeEventTarget) {\n    this._reactName = reactName;\n    this._targetInst = targetInst;\n    this.type = reactEventType;\n    this.nativeEvent = nativeEvent;\n    this.target = nativeEventTarget;\n    this.currentTarget = null;\n\n    for (var _propName in Interface) {\n      if (!Interface.hasOwnProperty(_propName)) {\n        continue;\n      }\n\n      var normalize = Interface[_propName];\n\n      if (normalize) {\n        this[_propName] = normalize(nativeEvent);\n      } else {\n        this[_propName] = nativeEvent[_propName];\n      }\n    }\n\n    var defaultPrevented = nativeEvent.defaultPrevented != null ? nativeEvent.defaultPrevented : nativeEvent.returnValue === false;\n\n    if (defaultPrevented) {\n      this.isDefaultPrevented = functionThatReturnsTrue;\n    } else {\n      this.isDefaultPrevented = functionThatReturnsFalse;\n    }\n\n    this.isPropagationStopped = functionThatReturnsFalse;\n    return this;\n  }\n\n  assign(SyntheticBaseEvent.prototype, {\n    preventDefault: function () {\n      this.defaultPrevented = true;\n      var event = this.nativeEvent;\n\n      if (!event) {\n        return;\n      }\n\n      if (event.preventDefault) {\n        event.preventDefault(); // $FlowFixMe - flow is not aware of `unknown` in IE\n      } else if (typeof event.returnValue !== 'unknown') {\n        event.returnValue = false;\n      }\n\n      this.isDefaultPrevented = functionThatReturnsTrue;\n    },\n    stopPropagation: function () {\n      var event = this.nativeEvent;\n\n      if (!event) {\n        return;\n      }\n\n      if (event.stopPropagation) {\n        event.stopPropagation(); // $FlowFixMe - flow is not aware of `unknown` in IE\n      } else if (typeof event.cancelBubble !== 'unknown') {\n        // The ChangeEventPlugin registers a \"propertychange\" event for\n        // IE. This event does not support bubbling or cancelling, and\n        // any references to cancelBubble throw \"Member not found\".  A\n        // typeof check of \"unknown\" circumvents this issue (and is also\n        // IE specific).\n        event.cancelBubble = true;\n      }\n\n      this.isPropagationStopped = functionThatReturnsTrue;\n    },\n\n    /**\n     * We release all dispatched `SyntheticEvent`s after each event loop, adding\n     * them back into the pool. This allows a way to hold onto a reference that\n     * won't be added back into the pool.\n     */\n    persist: function () {// Modern event system doesn't use pooling.\n    },\n\n    /**\n     * Checks if this event should be released back into the pool.\n     *\n     * @return {boolean} True if this should not be released, false otherwise.\n     */\n    isPersistent: functionThatReturnsTrue\n  });\n  return SyntheticBaseEvent;\n}\n/**\n * @interface Event\n * @see http://www.w3.org/TR/DOM-Level-3-Events/\n */\n\n\nvar EventInterface = {\n  eventPhase: 0,\n  bubbles: 0,\n  cancelable: 0,\n  timeStamp: function (event) {\n    return event.timeStamp || Date.now();\n  },\n  defaultPrevented: 0,\n  isTrusted: 0\n};\nvar SyntheticEvent = createSyntheticEvent(EventInterface);\n\nvar UIEventInterface = assign({}, EventInterface, {\n  view: 0,\n  detail: 0\n});\n\nvar SyntheticUIEvent = createSyntheticEvent(UIEventInterface);\nvar lastMovementX;\nvar lastMovementY;\nvar lastMouseEvent;\n\nfunction updateMouseMovementPolyfillState(event) {\n  if (event !== lastMouseEvent) {\n    if (lastMouseEvent && event.type === 'mousemove') {\n      lastMovementX = event.screenX - lastMouseEvent.screenX;\n      lastMovementY = event.screenY - lastMouseEvent.screenY;\n    } else {\n      lastMovementX = 0;\n      lastMovementY = 0;\n    }\n\n    lastMouseEvent = event;\n  }\n}\n/**\n * @interface MouseEvent\n * @see http://www.w3.org/TR/DOM-Level-3-Events/\n */\n\n\nvar MouseEventInterface = assign({}, UIEventInterface, {\n  screenX: 0,\n  screenY: 0,\n  clientX: 0,\n  clientY: 0,\n  pageX: 0,\n  pageY: 0,\n  ctrlKey: 0,\n  shiftKey: 0,\n  altKey: 0,\n  metaKey: 0,\n  getModifierState: getEventModifierState,\n  button: 0,\n  buttons: 0,\n  relatedTarget: function (event) {\n    if (event.relatedTarget === undefined) return event.fromElement === event.srcElement ? event.toElement : event.fromElement;\n    return event.relatedTarget;\n  },\n  movementX: function (event) {\n    if ('movementX' in event) {\n      return event.movementX;\n    }\n\n    updateMouseMovementPolyfillState(event);\n    return lastMovementX;\n  },\n  movementY: function (event) {\n    if ('movementY' in event) {\n      return event.movementY;\n    } // Don't need to call updateMouseMovementPolyfillState() here\n    // because it's guaranteed to have already run when movementX\n    // was copied.\n\n\n    return lastMovementY;\n  }\n});\n\nvar SyntheticMouseEvent = createSyntheticEvent(MouseEventInterface);\n/**\n * @interface DragEvent\n * @see http://www.w3.org/TR/DOM-Level-3-Events/\n */\n\nvar DragEventInterface = assign({}, MouseEventInterface, {\n  dataTransfer: 0\n});\n\nvar SyntheticDragEvent = createSyntheticEvent(DragEventInterface);\n/**\n * @interface FocusEvent\n * @see http://www.w3.org/TR/DOM-Level-3-Events/\n */\n\nvar FocusEventInterface = assign({}, UIEventInterface, {\n  relatedTarget: 0\n});\n\nvar SyntheticFocusEvent = createSyntheticEvent(FocusEventInterface);\n/**\n * @interface Event\n * @see http://www.w3.org/TR/css3-animations/#AnimationEvent-interface\n * @see https://developer.mozilla.org/en-US/docs/Web/API/AnimationEvent\n */\n\nvar AnimationEventInterface = assign({}, EventInterface, {\n  animationName: 0,\n  elapsedTime: 0,\n  pseudoElement: 0\n});\n\nvar SyntheticAnimationEvent = createSyntheticEvent(AnimationEventInterface);\n/**\n * @interface Event\n * @see http://www.w3.org/TR/clipboard-apis/\n */\n\nvar ClipboardEventInterface = assign({}, EventInterface, {\n  clipboardData: function (event) {\n    return 'clipboardData' in event ? event.clipboardData : window.clipboardData;\n  }\n});\n\nvar SyntheticClipboardEvent = createSyntheticEvent(ClipboardEventInterface);\n/**\n * @interface Event\n * @see http://www.w3.org/TR/DOM-Level-3-Events/#events-compositionevents\n */\n\nvar CompositionEventInterface = assign({}, EventInterface, {\n  data: 0\n});\n\nvar SyntheticCompositionEvent = createSyntheticEvent(CompositionEventInterface);\n/**\n * Normalization of deprecated HTML5 `key` values\n * @see https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent#Key_names\n */\n\nvar normalizeKey = {\n  Esc: 'Escape',\n  Spacebar: ' ',\n  Left: 'ArrowLeft',\n  Up: 'ArrowUp',\n  Right: 'ArrowRight',\n  Down: 'ArrowDown',\n  Del: 'Delete',\n  Win: 'OS',\n  Menu: 'ContextMenu',\n  Apps: 'ContextMenu',\n  Scroll: 'ScrollLock',\n  MozPrintableKey: 'Unidentified'\n};\n/**\n * Translation from legacy `keyCode` to HTML5 `key`\n * Only special keys supported, all others depend on keyboard layout or browser\n * @see https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent#Key_names\n */\n\nvar translateToKey = {\n  '8': 'Backspace',\n  '9': 'Tab',\n  '12': 'Clear',\n  '13': 'Enter',\n  '16': 'Shift',\n  '17': 'Control',\n  '18': 'Alt',\n  '19': 'Pause',\n  '20': 'CapsLock',\n  '27': 'Escape',\n  '32': ' ',\n  '33': 'PageUp',\n  '34': 'PageDown',\n  '35': 'End',\n  '36': 'Home',\n  '37': 'ArrowLeft',\n  '38': 'ArrowUp',\n  '39': 'ArrowRight',\n  '40': 'ArrowDown',\n  '45': 'Insert',\n  '46': 'Delete',\n  '112': 'F1',\n  '113': 'F2',\n  '114': 'F3',\n  '115': 'F4',\n  '116': 'F5',\n  '117': 'F6',\n  '118': 'F7',\n  '119': 'F8',\n  '120': 'F9',\n  '121': 'F10',\n  '122': 'F11',\n  '123': 'F12',\n  '144': 'NumLock',\n  '145': 'ScrollLock',\n  '224': 'Meta'\n};\n/**\n * @param {object} nativeEvent Native browser event.\n * @return {string} Normalized `key` property.\n */\n\nfunction getEventKey(nativeEvent) {\n  if (nativeEvent.key) {\n    // Normalize inconsistent values reported by browsers due to\n    // implementations of a working draft specification.\n    // FireFox implements `key` but returns `MozPrintableKey` for all\n    // printable characters (normalized to `Unidentified`), ignore it.\n    var key = normalizeKey[nativeEvent.key] || nativeEvent.key;\n\n    if (key !== 'Unidentified') {\n      return key;\n    }\n  } // Browser does not implement `key`, polyfill as much of it as we can.\n\n\n  if (nativeEvent.type === 'keypress') {\n    var charCode = getEventCharCode(nativeEvent); // The enter-key is technically both printable and non-printable and can\n    // thus be captured by `keypress`, no other non-printable key should.\n\n    return charCode === 13 ? 'Enter' : String.fromCharCode(charCode);\n  }\n\n  if (nativeEvent.type === 'keydown' || nativeEvent.type === 'keyup') {\n    // While user keyboard layout determines the actual meaning of each\n    // `keyCode` value, almost all function keys have a universal value.\n    return translateToKey[nativeEvent.keyCode] || 'Unidentified';\n  }\n\n  return '';\n}\n/**\n * Translation from modifier key to the associated property in the event.\n * @see http://www.w3.org/TR/DOM-Level-3-Events/#keys-Modifiers\n */\n\n\nvar modifierKeyToProp = {\n  Alt: 'altKey',\n  Control: 'ctrlKey',\n  Meta: 'metaKey',\n  Shift: 'shiftKey'\n}; // Older browsers (Safari <= 10, iOS Safari <= 10.2) do not support\n// getModifierState. If getModifierState is not supported, we map it to a set of\n// modifier keys exposed by the event. In this case, Lock-keys are not supported.\n\nfunction modifierStateGetter(keyArg) {\n  var syntheticEvent = this;\n  var nativeEvent = syntheticEvent.nativeEvent;\n\n  if (nativeEvent.getModifierState) {\n    return nativeEvent.getModifierState(keyArg);\n  }\n\n  var keyProp = modifierKeyToProp[keyArg];\n  return keyProp ? !!nativeEvent[keyProp] : false;\n}\n\nfunction getEventModifierState(nativeEvent) {\n  return modifierStateGetter;\n}\n/**\n * @interface KeyboardEvent\n * @see http://www.w3.org/TR/DOM-Level-3-Events/\n */\n\n\nvar KeyboardEventInterface = assign({}, UIEventInterface, {\n  key: getEventKey,\n  code: 0,\n  location: 0,\n  ctrlKey: 0,\n  shiftKey: 0,\n  altKey: 0,\n  metaKey: 0,\n  repeat: 0,\n  locale: 0,\n  getModifierState: getEventModifierState,\n  // Legacy Interface\n  charCode: function (event) {\n    // `charCode` is the result of a KeyPress event and represents the value of\n    // the actual printable character.\n    // KeyPress is deprecated, but its replacement is not yet final and not\n    // implemented in any major browser. Only KeyPress has charCode.\n    if (event.type === 'keypress') {\n      return getEventCharCode(event);\n    }\n\n    return 0;\n  },\n  keyCode: function (event) {\n    // `keyCode` is the result of a KeyDown/Up event and represents the value of\n    // physical keyboard key.\n    // The actual meaning of the value depends on the users' keyboard layout\n    // which cannot be detected. Assuming that it is a US keyboard layout\n    // provides a surprisingly accurate mapping for US and European users.\n    // Due to this, it is left to the user to implement at this time.\n    if (event.type === 'keydown' || event.type === 'keyup') {\n      return event.keyCode;\n    }\n\n    return 0;\n  },\n  which: function (event) {\n    // `which` is an alias for either `keyCode` or `charCode` depending on the\n    // type of the event.\n    if (event.type === 'keypress') {\n      return getEventCharCode(event);\n    }\n\n    if (event.type === 'keydown' || event.type === 'keyup') {\n      return event.keyCode;\n    }\n\n    return 0;\n  }\n});\n\nvar SyntheticKeyboardEvent = createSyntheticEvent(KeyboardEventInterface);\n/**\n * @interface PointerEvent\n * @see http://www.w3.org/TR/pointerevents/\n */\n\nvar PointerEventInterface = assign({}, MouseEventInterface, {\n  pointerId: 0,\n  width: 0,\n  height: 0,\n  pressure: 0,\n  tangentialPressure: 0,\n  tiltX: 0,\n  tiltY: 0,\n  twist: 0,\n  pointerType: 0,\n  isPrimary: 0\n});\n\nvar SyntheticPointerEvent = createSyntheticEvent(PointerEventInterface);\n/**\n * @interface TouchEvent\n * @see http://www.w3.org/TR/touch-events/\n */\n\nvar TouchEventInterface = assign({}, UIEventInterface, {\n  touches: 0,\n  targetTouches: 0,\n  changedTouches: 0,\n  altKey: 0,\n  metaKey: 0,\n  ctrlKey: 0,\n  shiftKey: 0,\n  getModifierState: getEventModifierState\n});\n\nvar SyntheticTouchEvent = createSyntheticEvent(TouchEventInterface);\n/**\n * @interface Event\n * @see http://www.w3.org/TR/2009/WD-css3-transitions-20090320/#transition-events-\n * @see https://developer.mozilla.org/en-US/docs/Web/API/TransitionEvent\n */\n\nvar TransitionEventInterface = assign({}, EventInterface, {\n  propertyName: 0,\n  elapsedTime: 0,\n  pseudoElement: 0\n});\n\nvar SyntheticTransitionEvent = createSyntheticEvent(TransitionEventInterface);\n/**\n * @interface WheelEvent\n * @see http://www.w3.org/TR/DOM-Level-3-Events/\n */\n\nvar WheelEventInterface = assign({}, MouseEventInterface, {\n  deltaX: function (event) {\n    return 'deltaX' in event ? event.deltaX : // Fallback to `wheelDeltaX` for Webkit and normalize (right is positive).\n    'wheelDeltaX' in event ? -event.wheelDeltaX : 0;\n  },\n  deltaY: function (event) {\n    return 'deltaY' in event ? event.deltaY : // Fallback to `wheelDeltaY` for Webkit and normalize (down is positive).\n    'wheelDeltaY' in event ? -event.wheelDeltaY : // Fallback to `wheelDelta` for IE<9 and normalize (down is positive).\n    'wheelDelta' in event ? -event.wheelDelta : 0;\n  },\n  deltaZ: 0,\n  // Browsers without \"deltaMode\" is reporting in raw wheel delta where one\n  // notch on the scroll is always +/- 120, roughly equivalent to pixels.\n  // A good approximation of DOM_DELTA_LINE (1) is 5% of viewport size or\n  // ~40 pixels, for DOM_DELTA_SCREEN (2) it is 87.5% of viewport size.\n  deltaMode: 0\n});\n\nvar SyntheticWheelEvent = createSyntheticEvent(WheelEventInterface);\n\n/**\n * HTML nodeType values that represent the type of the node\n */\nvar ELEMENT_NODE = 1;\n\nfunction invokeGuardedCallbackProd(name, func, context, a, b, c, d, e, f) {\n  var funcArgs = Array.prototype.slice.call(arguments, 3);\n\n  try {\n    func.apply(context, funcArgs);\n  } catch (error) {\n    this.onError(error);\n  }\n}\n\nvar invokeGuardedCallbackImpl = invokeGuardedCallbackProd;\n\n{\n  // In DEV mode, we swap out invokeGuardedCallback for a special version\n  // that plays more nicely with the browser's DevTools. The idea is to preserve\n  // \"Pause on exceptions\" behavior. Because React wraps all user-provided\n  // functions in invokeGuardedCallback, and the production version of\n  // invokeGuardedCallback uses a try-catch, all user exceptions are treated\n  // like caught exceptions, and the DevTools won't pause unless the developer\n  // takes the extra step of enabling pause on caught exceptions. This is\n  // unintuitive, though, because even though React has caught the error, from\n  // the developer's perspective, the error is uncaught.\n  //\n  // To preserve the expected \"Pause on exceptions\" behavior, we don't use a\n  // try-catch in DEV. Instead, we synchronously dispatch a fake event to a fake\n  // DOM node, and call the user-provided callback from inside an event handler\n  // for that fake event. If the callback throws, the error is \"captured\" using\n  // a global event handler. But because the error happens in a different\n  // event loop context, it does not interrupt the normal program flow.\n  // Effectively, this gives us try-catch behavior without actually using\n  // try-catch. Neat!\n  // Check that the browser supports the APIs we need to implement our special\n  // DEV version of invokeGuardedCallback\n  if (typeof window !== 'undefined' && typeof window.dispatchEvent === 'function' && typeof document !== 'undefined' && typeof document.createEvent === 'function') {\n    var fakeNode = document.createElement('react');\n\n    invokeGuardedCallbackImpl = function invokeGuardedCallbackDev(name, func, context, a, b, c, d, e, f) {\n      // If document doesn't exist we know for sure we will crash in this method\n      // when we call document.createEvent(). However this can cause confusing\n      // errors: https://github.com/facebook/create-react-app/issues/3482\n      // So we preemptively throw with a better message instead.\n      if (typeof document === 'undefined' || document === null) {\n        throw new Error('The `document` global was defined when React was initialized, but is not ' + 'defined anymore. This can happen in a test environment if a component ' + 'schedules an update from an asynchronous callback, but the test has already ' + 'finished running. To solve this, you can either unmount the component at ' + 'the end of your test (and ensure that any asynchronous operations get ' + 'canceled in `componentWillUnmount`), or you can change the test itself ' + 'to be asynchronous.');\n      }\n\n      var evt = document.createEvent('Event');\n      var didCall = false; // Keeps track of whether the user-provided callback threw an error. We\n      // set this to true at the beginning, then set it to false right after\n      // calling the function. If the function errors, `didError` will never be\n      // set to false. This strategy works even if the browser is flaky and\n      // fails to call our global error handler, because it doesn't rely on\n      // the error event at all.\n\n      var didError = true; // Keeps track of the value of window.event so that we can reset it\n      // during the callback to let user code access window.event in the\n      // browsers that support it.\n\n      var windowEvent = window.event; // Keeps track of the descriptor of window.event to restore it after event\n      // dispatching: https://github.com/facebook/react/issues/13688\n\n      var windowEventDescriptor = Object.getOwnPropertyDescriptor(window, 'event');\n\n      function restoreAfterDispatch() {\n        // We immediately remove the callback from event listeners so that\n        // nested `invokeGuardedCallback` calls do not clash. Otherwise, a\n        // nested call would trigger the fake event handlers of any call higher\n        // in the stack.\n        fakeNode.removeEventListener(evtType, callCallback, false); // We check for window.hasOwnProperty('event') to prevent the\n        // window.event assignment in both IE <= 10 as they throw an error\n        // \"Member not found\" in strict mode, and in Firefox which does not\n        // support window.event.\n\n        if (typeof window.event !== 'undefined' && window.hasOwnProperty('event')) {\n          window.event = windowEvent;\n        }\n      } // Create an event handler for our fake event. We will synchronously\n      // dispatch our fake event using `dispatchEvent`. Inside the handler, we\n      // call the user-provided callback.\n\n\n      var funcArgs = Array.prototype.slice.call(arguments, 3);\n\n      function callCallback() {\n        didCall = true;\n        restoreAfterDispatch();\n        func.apply(context, funcArgs);\n        didError = false;\n      } // Create a global error event handler. We use this to capture the value\n      // that was thrown. It's possible that this error handler will fire more\n      // than once; for example, if non-React code also calls `dispatchEvent`\n      // and a handler for that event throws. We should be resilient to most of\n      // those cases. Even if our error event handler fires more than once, the\n      // last error event is always used. If the callback actually does error,\n      // we know that the last error event is the correct one, because it's not\n      // possible for anything else to have happened in between our callback\n      // erroring and the code that follows the `dispatchEvent` call below. If\n      // the callback doesn't error, but the error event was fired, we know to\n      // ignore it because `didError` will be false, as described above.\n\n\n      var error; // Use this to track whether the error event is ever called.\n\n      var didSetError = false;\n      var isCrossOriginError = false;\n\n      function handleWindowError(event) {\n        error = event.error;\n        didSetError = true;\n\n        if (error === null && event.colno === 0 && event.lineno === 0) {\n          isCrossOriginError = true;\n        }\n\n        if (event.defaultPrevented) {\n          // Some other error handler has prevented default.\n          // Browsers silence the error report if this happens.\n          // We'll remember this to later decide whether to log it or not.\n          if (error != null && typeof error === 'object') {\n            try {\n              error._suppressLogging = true;\n            } catch (inner) {// Ignore.\n            }\n          }\n        }\n      } // Create a fake event type.\n\n\n      var evtType = \"react-\" + (name ? name : 'invokeguardedcallback'); // Attach our event handlers\n\n      window.addEventListener('error', handleWindowError);\n      fakeNode.addEventListener(evtType, callCallback, false); // Synchronously dispatch our fake event. If the user-provided function\n      // errors, it will trigger our global error handler.\n\n      evt.initEvent(evtType, false, false);\n      fakeNode.dispatchEvent(evt);\n\n      if (windowEventDescriptor) {\n        Object.defineProperty(window, 'event', windowEventDescriptor);\n      }\n\n      if (didCall && didError) {\n        if (!didSetError) {\n          // The callback errored, but the error event never fired.\n          // eslint-disable-next-line react-internal/prod-error-codes\n          error = new Error('An error was thrown inside one of your components, but React ' + \"doesn't know what it was. This is likely due to browser \" + 'flakiness. React does its best to preserve the \"Pause on ' + 'exceptions\" behavior of the DevTools, which requires some ' + \"DEV-mode only tricks. It's possible that these don't work in \" + 'your browser. Try triggering the error in production mode, ' + 'or switching to a modern browser. If you suspect that this is ' + 'actually an issue with React, please file an issue.');\n        } else if (isCrossOriginError) {\n          // eslint-disable-next-line react-internal/prod-error-codes\n          error = new Error(\"A cross-origin error was thrown. React doesn't have access to \" + 'the actual error object in development. ' + 'See https://reactjs.org/link/crossorigin-error for more information.');\n        }\n\n        this.onError(error);\n      } // Remove our event listeners\n\n\n      window.removeEventListener('error', handleWindowError);\n\n      if (!didCall) {\n        // Something went really wrong, and our event was not dispatched.\n        // https://github.com/facebook/react/issues/16734\n        // https://github.com/facebook/react/issues/16585\n        // Fall back to the production implementation.\n        restoreAfterDispatch();\n        return invokeGuardedCallbackProd.apply(this, arguments);\n      }\n    };\n  }\n}\n\nvar invokeGuardedCallbackImpl$1 = invokeGuardedCallbackImpl;\n\nvar hasError = false;\nvar caughtError = null; // Used by event system to capture/rethrow the first error.\n\nvar hasRethrowError = false;\nvar rethrowError = null;\nvar reporter = {\n  onError: function (error) {\n    hasError = true;\n    caughtError = error;\n  }\n};\n/**\n * Call a function while guarding against errors that happens within it.\n * Returns an error if it throws, otherwise null.\n *\n * In production, this is implemented using a try-catch. The reason we don't\n * use a try-catch directly is so that we can swap out a different\n * implementation in DEV mode.\n *\n * @param {String} name of the guard to use for logging or debugging\n * @param {Function} func The function to invoke\n * @param {*} context The context to use when calling the function\n * @param {...*} args Arguments for function\n */\n\nfunction invokeGuardedCallback(name, func, context, a, b, c, d, e, f) {\n  hasError = false;\n  caughtError = null;\n  invokeGuardedCallbackImpl$1.apply(reporter, arguments);\n}\n/**\n * Same as invokeGuardedCallback, but instead of returning an error, it stores\n * it in a global so it can be rethrown by `rethrowCaughtError` later.\n * TODO: See if caughtError and rethrowError can be unified.\n *\n * @param {String} name of the guard to use for logging or debugging\n * @param {Function} func The function to invoke\n * @param {*} context The context to use when calling the function\n * @param {...*} args Arguments for function\n */\n\nfunction invokeGuardedCallbackAndCatchFirstError(name, func, context, a, b, c, d, e, f) {\n  invokeGuardedCallback.apply(this, arguments);\n\n  if (hasError) {\n    var error = clearCaughtError();\n\n    if (!hasRethrowError) {\n      hasRethrowError = true;\n      rethrowError = error;\n    }\n  }\n}\n/**\n * During execution of guarded functions we will capture the first error which\n * we will rethrow to be handled by the top level error handler.\n */\n\nfunction rethrowCaughtError() {\n  if (hasRethrowError) {\n    var error = rethrowError;\n    hasRethrowError = false;\n    rethrowError = null;\n    throw error;\n  }\n}\nfunction clearCaughtError() {\n  if (hasError) {\n    var error = caughtError;\n    hasError = false;\n    caughtError = null;\n    return error;\n  } else {\n    throw new Error('clearCaughtError was called but no error was captured. This error ' + 'is likely caused by a bug in React. Please file an issue.');\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\nvar SecretInternals = ReactDOM.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\nvar EventInternals = SecretInternals.Events;\nvar getInstanceFromNode = EventInternals[0];\nvar getNodeFromInstance = EventInternals[1];\nvar getFiberCurrentPropsFromNode = EventInternals[2];\nvar enqueueStateRestore = EventInternals[3];\nvar restoreStateIfNeeded = EventInternals[4];\nvar reactAct = React.unstable_act;\n\nfunction Event(suffix) {}\n\nvar hasWarnedAboutDeprecatedMockComponent = false;\n/**\n * @class ReactTestUtils\n */\n\nfunction findAllInRenderedFiberTreeInternal(fiber, test) {\n  if (!fiber) {\n    return [];\n  }\n\n  var currentParent = findCurrentFiberUsingSlowPath(fiber);\n\n  if (!currentParent) {\n    return [];\n  }\n\n  var node = currentParent;\n  var ret = [];\n\n  while (true) {\n    if (node.tag === HostComponent || node.tag === HostText || node.tag === ClassComponent || node.tag === FunctionComponent) {\n      var publicInst = node.stateNode;\n\n      if (test(publicInst)) {\n        ret.push(publicInst);\n      }\n    }\n\n    if (node.child) {\n      node.child.return = node;\n      node = node.child;\n      continue;\n    }\n\n    if (node === currentParent) {\n      return ret;\n    }\n\n    while (!node.sibling) {\n      if (!node.return || node.return === currentParent) {\n        return ret;\n      }\n\n      node = node.return;\n    }\n\n    node.sibling.return = node.return;\n    node = node.sibling;\n  }\n}\n\nfunction validateClassInstance(inst, methodName) {\n  if (!inst) {\n    // This is probably too relaxed but it's existing behavior.\n    return;\n  }\n\n  if (get(inst)) {\n    // This is a public instance indeed.\n    return;\n  }\n\n  var received;\n  var stringified = String(inst);\n\n  if (isArray(inst)) {\n    received = 'an array';\n  } else if (inst && inst.nodeType === ELEMENT_NODE && inst.tagName) {\n    received = 'a DOM node';\n  } else if (stringified === '[object Object]') {\n    received = 'object with keys {' + Object.keys(inst).join(', ') + '}';\n  } else {\n    received = stringified;\n  }\n\n  throw new Error(methodName + \"(...): the first argument must be a React class instance. \" + (\"Instead received: \" + received + \".\"));\n}\n/**\n * Utilities for making it easy to test React components.\n *\n * See https://reactjs.org/docs/test-utils.html\n *\n * Todo: Support the entire DOM.scry query syntax. For now, these simple\n * utilities will suffice for testing purposes.\n * @lends ReactTestUtils\n */\n\n\nvar didWarnAboutReactTestUtilsDeprecation = false;\n\nfunction renderIntoDocument(element) {\n  {\n    if (!didWarnAboutReactTestUtilsDeprecation) {\n      didWarnAboutReactTestUtilsDeprecation = true;\n\n      error('ReactDOMTestUtils is deprecated and will be removed in a future ' + 'major release, because it exposes internal implementation details ' + 'that are highly likely to change between releases. Upgrade to a ' + 'modern testing library, such as @testing-library/react. See ' + 'https://react.dev/warnings/react-dom-test-utils for more info.');\n    }\n  }\n\n  var div = document.createElement('div'); // None of our tests actually require attaching the container to the\n  // DOM, and doing so creates a mess that we rely on test isolation to\n  // clean up, so we're going to stop honoring the name of this method\n  // (and probably rename it eventually) if no problems arise.\n  // document.documentElement.appendChild(div);\n\n  return ReactDOM.render(element, div);\n}\n\nfunction isElement(element) {\n  return React.isValidElement(element);\n}\n\nfunction isElementOfType(inst, convenienceConstructor) {\n  return React.isValidElement(inst) && inst.type === convenienceConstructor;\n}\n\nfunction isDOMComponent(inst) {\n  return !!(inst && inst.nodeType === ELEMENT_NODE && inst.tagName);\n}\n\nfunction isDOMComponentElement(inst) {\n  return !!(inst && React.isValidElement(inst) && !!inst.tagName);\n}\n\nfunction isCompositeComponent(inst) {\n  if (isDOMComponent(inst)) {\n    // Accessing inst.setState warns; just return false as that'll be what\n    // this returns when we have DOM nodes as refs directly\n    return false;\n  }\n\n  return inst != null && typeof inst.render === 'function' && typeof inst.setState === 'function';\n}\n\nfunction isCompositeComponentWithType(inst, type) {\n  if (!isCompositeComponent(inst)) {\n    return false;\n  }\n\n  var internalInstance = get(inst);\n  var constructor = internalInstance.type;\n  return constructor === type;\n}\n\nfunction findAllInRenderedTree(inst, test) {\n  validateClassInstance(inst, 'findAllInRenderedTree');\n\n  if (!inst) {\n    return [];\n  }\n\n  var internalInstance = get(inst);\n  return findAllInRenderedFiberTreeInternal(internalInstance, test);\n}\n/**\n * Finds all instances of components in the rendered tree that are DOM\n * components with the class name matching `className`.\n * @return {array} an array of all the matches.\n */\n\n\nfunction scryRenderedDOMComponentsWithClass(root, classNames) {\n  validateClassInstance(root, 'scryRenderedDOMComponentsWithClass');\n  return findAllInRenderedTree(root, function (inst) {\n    if (isDOMComponent(inst)) {\n      var className = inst.className;\n\n      if (typeof className !== 'string') {\n        // SVG, probably.\n        className = inst.getAttribute('class') || '';\n      }\n\n      var classList = className.split(/\\s+/);\n\n      if (!isArray(classNames)) {\n        if (classNames === undefined) {\n          throw new Error('TestUtils.scryRenderedDOMComponentsWithClass expects a ' + 'className as a second argument.');\n        }\n\n        classNames = classNames.split(/\\s+/);\n      }\n\n      return classNames.every(function (name) {\n        return classList.indexOf(name) !== -1;\n      });\n    }\n\n    return false;\n  });\n}\n/**\n * Like scryRenderedDOMComponentsWithClass but expects there to be one result,\n * and returns that one result, or throws exception if there is any other\n * number of matches besides one.\n * @return {!ReactDOMComponent} The one match.\n */\n\n\nfunction findRenderedDOMComponentWithClass(root, className) {\n  validateClassInstance(root, 'findRenderedDOMComponentWithClass');\n  var all = scryRenderedDOMComponentsWithClass(root, className);\n\n  if (all.length !== 1) {\n    throw new Error('Did not find exactly one match (found: ' + all.length + ') ' + 'for class:' + className);\n  }\n\n  return all[0];\n}\n/**\n * Finds all instances of components in the rendered tree that are DOM\n * components with the tag name matching `tagName`.\n * @return {array} an array of all the matches.\n */\n\n\nfunction scryRenderedDOMComponentsWithTag(root, tagName) {\n  validateClassInstance(root, 'scryRenderedDOMComponentsWithTag');\n  return findAllInRenderedTree(root, function (inst) {\n    return isDOMComponent(inst) && inst.tagName.toUpperCase() === tagName.toUpperCase();\n  });\n}\n/**\n * Like scryRenderedDOMComponentsWithTag but expects there to be one result,\n * and returns that one result, or throws exception if there is any other\n * number of matches besides one.\n * @return {!ReactDOMComponent} The one match.\n */\n\n\nfunction findRenderedDOMComponentWithTag(root, tagName) {\n  validateClassInstance(root, 'findRenderedDOMComponentWithTag');\n  var all = scryRenderedDOMComponentsWithTag(root, tagName);\n\n  if (all.length !== 1) {\n    throw new Error('Did not find exactly one match (found: ' + all.length + ') ' + 'for tag:' + tagName);\n  }\n\n  return all[0];\n}\n/**\n * Finds all instances of components with type equal to `componentType`.\n * @return {array} an array of all the matches.\n */\n\n\nfunction scryRenderedComponentsWithType(root, componentType) {\n  validateClassInstance(root, 'scryRenderedComponentsWithType');\n  return findAllInRenderedTree(root, function (inst) {\n    return isCompositeComponentWithType(inst, componentType);\n  });\n}\n/**\n * Same as `scryRenderedComponentsWithType` but expects there to be one result\n * and returns that one result, or throws exception if there is any other\n * number of matches besides one.\n * @return {!ReactComponent} The one match.\n */\n\n\nfunction findRenderedComponentWithType(root, componentType) {\n  validateClassInstance(root, 'findRenderedComponentWithType');\n  var all = scryRenderedComponentsWithType(root, componentType);\n\n  if (all.length !== 1) {\n    throw new Error('Did not find exactly one match (found: ' + all.length + ') ' + 'for componentType:' + componentType);\n  }\n\n  return all[0];\n}\n/**\n * Pass a mocked component module to this method to augment it with\n * useful methods that allow it to be used as a dummy React component.\n * Instead of rendering as usual, the component will become a simple\n * <div> containing any provided children.\n *\n * @param {object} module the mock function object exported from a\n *                        module that defines the component to be mocked\n * @param {?string} mockTagName optional dummy root tag name to return\n *                              from render method (overrides\n *                              module.mockTagName if provided)\n * @return {object} the ReactTestUtils object (for chaining)\n */\n\n\nfunction mockComponent(module, mockTagName) {\n  {\n    if (!hasWarnedAboutDeprecatedMockComponent) {\n      hasWarnedAboutDeprecatedMockComponent = true;\n\n      warn('ReactTestUtils.mockComponent() is deprecated. ' + 'Use shallow rendering or jest.mock() instead.\\n\\n' + 'See https://reactjs.org/link/test-utils-mock-component for more information.');\n    }\n  }\n\n  mockTagName = mockTagName || module.mockTagName || 'div';\n  module.prototype.render.mockImplementation(function () {\n    return React.createElement(mockTagName, null, this.props.children);\n  });\n  return this;\n}\n\nfunction nativeTouchData(x, y) {\n  return {\n    touches: [{\n      pageX: x,\n      pageY: y\n    }]\n  };\n} // Start of inline: the below functions were inlined from\n// EventPropagator.js, as they deviated from ReactDOM's newer\n// implementations.\n\n/**\n * Dispatch the event to the listener.\n * @param {SyntheticEvent} event SyntheticEvent to handle\n * @param {function} listener Application-level callback\n * @param {*} inst Internal component instance\n */\n\n\nfunction executeDispatch(event, listener, inst) {\n  var type = event.type || 'unknown-event';\n  event.currentTarget = getNodeFromInstance(inst);\n  invokeGuardedCallbackAndCatchFirstError(type, listener, undefined, event);\n  event.currentTarget = null;\n}\n/**\n * Standard/simple iteration through an event's collected dispatches.\n */\n\n\nfunction executeDispatchesInOrder(event) {\n  var dispatchListeners = event._dispatchListeners;\n  var dispatchInstances = event._dispatchInstances;\n\n  if (isArray(dispatchListeners)) {\n    for (var i = 0; i < dispatchListeners.length; i++) {\n      if (event.isPropagationStopped()) {\n        break;\n      } // Listeners and Instances are two parallel arrays that are always in sync.\n\n\n      executeDispatch(event, dispatchListeners[i], dispatchInstances[i]);\n    }\n  } else if (dispatchListeners) {\n    executeDispatch(event, dispatchListeners, dispatchInstances);\n  }\n\n  event._dispatchListeners = null;\n  event._dispatchInstances = null;\n}\n/**\n * Dispatches an event and releases it back into the pool, unless persistent.\n *\n * @param {?object} event Synthetic event to be dispatched.\n * @private\n */\n\n\nvar executeDispatchesAndRelease = function (event) {\n  if (event) {\n    executeDispatchesInOrder(event);\n\n    if (!event.isPersistent()) {\n      event.constructor.release(event);\n    }\n  }\n};\n\nfunction isInteractive(tag) {\n  return tag === 'button' || tag === 'input' || tag === 'select' || tag === 'textarea';\n}\n\nfunction getParent(inst) {\n  do {\n    inst = inst.return; // TODO: If this is a HostRoot we might want to bail out.\n    // That is depending on if we want nested subtrees (layers) to bubble\n    // events to their parent. We could also go through parentNode on the\n    // host node but that wouldn't work for React Native and doesn't let us\n    // do the portal feature.\n  } while (inst && inst.tag !== HostComponent);\n\n  if (inst) {\n    return inst;\n  }\n\n  return null;\n}\n/**\n * Simulates the traversal of a two-phase, capture/bubble event dispatch.\n */\n\n\nfunction traverseTwoPhase(inst, fn, arg) {\n  var path = [];\n\n  while (inst) {\n    path.push(inst);\n    inst = getParent(inst);\n  }\n\n  var i;\n\n  for (i = path.length; i-- > 0;) {\n    fn(path[i], 'captured', arg);\n  }\n\n  for (i = 0; i < path.length; i++) {\n    fn(path[i], 'bubbled', arg);\n  }\n}\n\nfunction shouldPreventMouseEvent(name, type, props) {\n  switch (name) {\n    case 'onClick':\n    case 'onClickCapture':\n    case 'onDoubleClick':\n    case 'onDoubleClickCapture':\n    case 'onMouseDown':\n    case 'onMouseDownCapture':\n    case 'onMouseMove':\n    case 'onMouseMoveCapture':\n    case 'onMouseUp':\n    case 'onMouseUpCapture':\n    case 'onMouseEnter':\n      return !!(props.disabled && isInteractive(type));\n\n    default:\n      return false;\n  }\n}\n/**\n * @param {object} inst The instance, which is the source of events.\n * @param {string} registrationName Name of listener (e.g. `onClick`).\n * @return {?function} The stored callback.\n */\n\n\nfunction getListener(inst, registrationName) {\n  // TODO: shouldPreventMouseEvent is DOM-specific and definitely should not\n  // live here; needs to be moved to a better place soon\n  var stateNode = inst.stateNode;\n\n  if (!stateNode) {\n    // Work in progress (ex: onload events in incremental mode).\n    return null;\n  }\n\n  var props = getFiberCurrentPropsFromNode(stateNode);\n\n  if (!props) {\n    // Work in progress.\n    return null;\n  }\n\n  var listener = props[registrationName];\n\n  if (shouldPreventMouseEvent(registrationName, inst.type, props)) {\n    return null;\n  }\n\n  if (listener && typeof listener !== 'function') {\n    throw new Error(\"Expected `\" + registrationName + \"` listener to be a function, instead got a value of `\" + typeof listener + \"` type.\");\n  }\n\n  return listener;\n}\n\nfunction listenerAtPhase(inst, event, propagationPhase) {\n  var registrationName = event._reactName;\n\n  if (propagationPhase === 'captured') {\n    registrationName += 'Capture';\n  }\n\n  return getListener(inst, registrationName);\n}\n\nfunction accumulateDispatches(inst, ignoredDirection, event) {\n  if (inst && event && event._reactName) {\n    var registrationName = event._reactName;\n    var listener = getListener(inst, registrationName);\n\n    if (listener) {\n      if (event._dispatchListeners == null) {\n        event._dispatchListeners = [];\n      }\n\n      if (event._dispatchInstances == null) {\n        event._dispatchInstances = [];\n      }\n\n      event._dispatchListeners.push(listener);\n\n      event._dispatchInstances.push(inst);\n    }\n  }\n}\n\nfunction accumulateDirectionalDispatches(inst, phase, event) {\n  {\n    if (!inst) {\n      error('Dispatching inst must not be null');\n    }\n  }\n\n  var listener = listenerAtPhase(inst, event, phase);\n\n  if (listener) {\n    if (event._dispatchListeners == null) {\n      event._dispatchListeners = [];\n    }\n\n    if (event._dispatchInstances == null) {\n      event._dispatchInstances = [];\n    }\n\n    event._dispatchListeners.push(listener);\n\n    event._dispatchInstances.push(inst);\n  }\n}\n\nfunction accumulateDirectDispatchesSingle(event) {\n  if (event && event._reactName) {\n    accumulateDispatches(event._targetInst, null, event);\n  }\n}\n\nfunction accumulateTwoPhaseDispatchesSingle(event) {\n  if (event && event._reactName) {\n    traverseTwoPhase(event._targetInst, accumulateDirectionalDispatches, event);\n  }\n} // End of inline\n\n\nvar Simulate = {};\nvar directDispatchEventTypes = new Set(['mouseEnter', 'mouseLeave', 'pointerEnter', 'pointerLeave']);\n/**\n * Exports:\n *\n * - `Simulate.click(Element)`\n * - `Simulate.mouseMove(Element)`\n * - `Simulate.change(Element)`\n * - ... (All keys from event plugin `eventTypes` objects)\n */\n\nfunction makeSimulator(eventType) {\n  return function (domNode, eventData) {\n    if (React.isValidElement(domNode)) {\n      throw new Error('TestUtils.Simulate expected a DOM node as the first argument but received ' + 'a React element. Pass the DOM node you wish to simulate the event on instead. ' + 'Note that TestUtils.Simulate will not work if you are using shallow rendering.');\n    }\n\n    if (isCompositeComponent(domNode)) {\n      throw new Error('TestUtils.Simulate expected a DOM node as the first argument but received ' + 'a component instance. Pass the DOM node you wish to simulate the event on instead.');\n    }\n\n    var reactName = 'on' + eventType[0].toUpperCase() + eventType.slice(1);\n    var fakeNativeEvent = new Event();\n    fakeNativeEvent.target = domNode;\n    fakeNativeEvent.type = eventType.toLowerCase();\n    var targetInst = getInstanceFromNode(domNode);\n    var event = new SyntheticEvent(reactName, fakeNativeEvent.type, targetInst, fakeNativeEvent, domNode); // Since we aren't using pooling, always persist the event. This will make\n    // sure it's marked and won't warn when setting additional properties.\n\n    event.persist();\n    assign(event, eventData);\n\n    if (directDispatchEventTypes.has(eventType)) {\n      accumulateDirectDispatchesSingle(event);\n    } else {\n      accumulateTwoPhaseDispatchesSingle(event);\n    }\n\n    ReactDOM.unstable_batchedUpdates(function () {\n      // Normally extractEvent enqueues a state restore, but we'll just always\n      // do that since we're by-passing it here.\n      enqueueStateRestore(domNode);\n      executeDispatchesAndRelease(event);\n      rethrowCaughtError();\n    });\n    restoreStateIfNeeded();\n  };\n} // A one-time snapshot with no plans to update. We'll probably want to deprecate Simulate API.\n\n\nvar simulatedEventTypes = ['blur', 'cancel', 'click', 'close', 'contextMenu', 'copy', 'cut', 'auxClick', 'doubleClick', 'dragEnd', 'dragStart', 'drop', 'focus', 'input', 'invalid', 'keyDown', 'keyPress', 'keyUp', 'mouseDown', 'mouseUp', 'paste', 'pause', 'play', 'pointerCancel', 'pointerDown', 'pointerUp', 'rateChange', 'reset', 'resize', 'seeked', 'submit', 'touchCancel', 'touchEnd', 'touchStart', 'volumeChange', 'drag', 'dragEnter', 'dragExit', 'dragLeave', 'dragOver', 'mouseMove', 'mouseOut', 'mouseOver', 'pointerMove', 'pointerOut', 'pointerOver', 'scroll', 'toggle', 'touchMove', 'wheel', 'abort', 'animationEnd', 'animationIteration', 'animationStart', 'canPlay', 'canPlayThrough', 'durationChange', 'emptied', 'encrypted', 'ended', 'error', 'gotPointerCapture', 'load', 'loadedData', 'loadedMetadata', 'loadStart', 'lostPointerCapture', 'playing', 'progress', 'seeking', 'stalled', 'suspend', 'timeUpdate', 'transitionEnd', 'waiting', 'mouseEnter', 'mouseLeave', 'pointerEnter', 'pointerLeave', 'change', 'select', 'beforeInput', 'compositionEnd', 'compositionStart', 'compositionUpdate'];\n\nfunction buildSimulators() {\n  simulatedEventTypes.forEach(function (eventType) {\n    Simulate[eventType] = makeSimulator(eventType);\n  });\n}\n\nbuildSimulators();\nvar didWarnAboutUsingAct = false;\nvar act =  function actWithWarning(callback) {\n  {\n    if (!didWarnAboutUsingAct) {\n      didWarnAboutUsingAct = true;\n\n      error('`ReactDOMTestUtils.act` is deprecated in favor of `React.act`. ' + 'Import `act` from `react` instead of `react-dom/test-utils`. ' + 'See https://react.dev/warnings/react-dom-test-utils for more info.');\n    }\n  }\n\n  return reactAct(callback);\n} ;\n\nexports.Simulate = Simulate;\nexports.act = act;\nexports.findAllInRenderedTree = findAllInRenderedTree;\nexports.findRenderedComponentWithType = findRenderedComponentWithType;\nexports.findRenderedDOMComponentWithClass = findRenderedDOMComponentWithClass;\nexports.findRenderedDOMComponentWithTag = findRenderedDOMComponentWithTag;\nexports.isCompositeComponent = isCompositeComponent;\nexports.isCompositeComponentWithType = isCompositeComponentWithType;\nexports.isDOMComponent = isDOMComponent;\nexports.isDOMComponentElement = isDOMComponentElement;\nexports.isElement = isElement;\nexports.isElementOfType = isElementOfType;\nexports.mockComponent = mockComponent;\nexports.nativeTouchData = nativeTouchData;\nexports.renderIntoDocument = renderIntoDocument;\nexports.scryRenderedComponentsWithType = scryRenderedComponentsWithType;\nexports.scryRenderedDOMComponentsWithClass = scryRenderedDOMComponentsWithClass;\nexports.scryRenderedDOMComponentsWithTag = scryRenderedDOMComponentsWithTag;\nexports.traverseTwoPhase = traverseTwoPhase;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-dom-test-utils.production.min.js');\n} else {\n  module.exports = require('./cjs/react-dom-test-utils.development.js');\n}\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA;AAYA,QAAI,MAAuC;AACzC,OAAC,WAAW;AACd;AAEA,YAAI,QAAQ;AACZ,YAAI,WAAW;AAEf,YAAI,uBAAuB,MAAM;AAOjC,iBAAS,KAAK,QAAQ;AACpB;AACE;AACE,uBAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,qBAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,cACjC;AAEA,2BAAa,QAAQ,QAAQ,IAAI;AAAA,YACnC;AAAA,UACF;AAAA,QACF;AACA,iBAAS,MAAM,QAAQ;AACrB;AACE;AACE,uBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,qBAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,cACnC;AAEA,2BAAa,SAAS,QAAQ,IAAI;AAAA,YACpC;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,aAAa,OAAO,QAAQ,MAAM;AAGzC;AACE,gBAAI,yBAAyB,qBAAqB;AAClD,gBAAI,QAAQ,uBAAuB,iBAAiB;AAEpD,gBAAI,UAAU,IAAI;AAChB,wBAAU;AACV,qBAAO,KAAK,OAAO,CAAC,KAAK,CAAC;AAAA,YAC5B;AAGA,gBAAI,iBAAiB,KAAK,IAAI,SAAU,MAAM;AAC5C,qBAAO,OAAO,IAAI;AAAA,YACpB,CAAC;AAED,2BAAe,QAAQ,cAAc,MAAM;AAI3C,qBAAS,UAAU,MAAM,KAAK,QAAQ,KAAK,GAAG,SAAS,cAAc;AAAA,UACvE;AAAA,QACF;AAWA,iBAAS,IAAI,KAAK;AAChB,iBAAO,IAAI;AAAA,QACb;AAEA,YAAI,oBAAoB;AACxB,YAAI,iBAAiB;AAErB,YAAI,WAAW;AAEf,YAAI,gBAAgB;AACpB,YAAI,WAAW;AAGf,YAAI;AAAA;AAAA,UAEJ;AAAA;AAEA,YAAI;AAAA;AAAA,UAEJ;AAAA;AACA,YAAI;AAAA;AAAA,UAEJ;AAAA;AAEA,YAAI,oBAAoB,qBAAqB;AAC7C,iBAAS,uBAAuB,OAAO;AACrC,cAAI,OAAO;AACX,cAAI,iBAAiB;AAErB,cAAI,CAAC,MAAM,WAAW;AAGpB,gBAAI,WAAW;AAEf,eAAG;AACD,qBAAO;AAEP,mBAAK,KAAK,SAAS,YAAY,gBAAgB,SAAS;AAItD,iCAAiB,KAAK;AAAA,cACxB;AAEA,yBAAW,KAAK;AAAA,YAClB,SAAS;AAAA,UACX,OAAO;AACL,mBAAO,KAAK,QAAQ;AAClB,qBAAO,KAAK;AAAA,YACd;AAAA,UACF;AAEA,cAAI,KAAK,QAAQ,UAAU;AAGzB,mBAAO;AAAA,UACT;AAIA,iBAAO;AAAA,QACT;AAEA,iBAAS,gBAAgB,OAAO;AAC9B,cAAI,uBAAuB,KAAK,MAAM,OAAO;AAC3C,kBAAM,IAAI,MAAM,gDAAgD;AAAA,UAClE;AAAA,QACF;AAEA,iBAAS,8BAA8B,OAAO;AAC5C,cAAI,YAAY,MAAM;AAEtB,cAAI,CAAC,WAAW;AAEd,gBAAI,iBAAiB,uBAAuB,KAAK;AAEjD,gBAAI,mBAAmB,MAAM;AAC3B,oBAAM,IAAI,MAAM,gDAAgD;AAAA,YAClE;AAEA,gBAAI,mBAAmB,OAAO;AAC5B,qBAAO;AAAA,YACT;AAEA,mBAAO;AAAA,UACT;AAKA,cAAI,IAAI;AACR,cAAI,IAAI;AAER,iBAAO,MAAM;AACX,gBAAI,UAAU,EAAE;AAEhB,gBAAI,YAAY,MAAM;AAEpB;AAAA,YACF;AAEA,gBAAI,UAAU,QAAQ;AAEtB,gBAAI,YAAY,MAAM;AAKpB,kBAAI,aAAa,QAAQ;AAEzB,kBAAI,eAAe,MAAM;AACvB,oBAAI,IAAI;AACR;AAAA,cACF;AAGA;AAAA,YACF;AAKA,gBAAI,QAAQ,UAAU,QAAQ,OAAO;AACnC,kBAAI,QAAQ,QAAQ;AAEpB,qBAAO,OAAO;AACZ,oBAAI,UAAU,GAAG;AAEf,kCAAgB,OAAO;AACvB,yBAAO;AAAA,gBACT;AAEA,oBAAI,UAAU,GAAG;AAEf,kCAAgB,OAAO;AACvB,yBAAO;AAAA,gBACT;AAEA,wBAAQ,MAAM;AAAA,cAChB;AAIA,oBAAM,IAAI,MAAM,gDAAgD;AAAA,YAClE;AAEA,gBAAI,EAAE,WAAW,EAAE,QAAQ;AAKzB,kBAAI;AACJ,kBAAI;AAAA,YACN,OAAO;AAML,kBAAI,eAAe;AACnB,kBAAI,SAAS,QAAQ;AAErB,qBAAO,QAAQ;AACb,oBAAI,WAAW,GAAG;AAChB,iCAAe;AACf,sBAAI;AACJ,sBAAI;AACJ;AAAA,gBACF;AAEA,oBAAI,WAAW,GAAG;AAChB,iCAAe;AACf,sBAAI;AACJ,sBAAI;AACJ;AAAA,gBACF;AAEA,yBAAS,OAAO;AAAA,cAClB;AAEA,kBAAI,CAAC,cAAc;AAEjB,yBAAS,QAAQ;AAEjB,uBAAO,QAAQ;AACb,sBAAI,WAAW,GAAG;AAChB,mCAAe;AACf,wBAAI;AACJ,wBAAI;AACJ;AAAA,kBACF;AAEA,sBAAI,WAAW,GAAG;AAChB,mCAAe;AACf,wBAAI;AACJ,wBAAI;AACJ;AAAA,kBACF;AAEA,2BAAS,OAAO;AAAA,gBAClB;AAEA,oBAAI,CAAC,cAAc;AACjB,wBAAM,IAAI,MAAM,8HAAmI;AAAA,gBACrJ;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,EAAE,cAAc,GAAG;AACrB,oBAAM,IAAI,MAAM,8HAAmI;AAAA,YACrJ;AAAA,UACF;AAIA,cAAI,EAAE,QAAQ,UAAU;AACtB,kBAAM,IAAI,MAAM,gDAAgD;AAAA,UAClE;AAEA,cAAI,EAAE,UAAU,YAAY,GAAG;AAE7B,mBAAO;AAAA,UACT;AAGA,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,OAAO;AAYpB,iBAAS,iBAAiB,aAAa;AACrC,cAAI;AACJ,cAAI,UAAU,YAAY;AAE1B,cAAI,cAAc,aAAa;AAC7B,uBAAW,YAAY;AAEvB,gBAAI,aAAa,KAAK,YAAY,IAAI;AACpC,yBAAW;AAAA,YACb;AAAA,UACF,OAAO;AAEL,uBAAW;AAAA,UACb;AAIA,cAAI,aAAa,IAAI;AACnB,uBAAW;AAAA,UACb;AAIA,cAAI,YAAY,MAAM,aAAa,IAAI;AACrC,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,0BAA0B;AACjC,iBAAO;AAAA,QACT;AAEA,iBAAS,2BAA2B;AAClC,iBAAO;AAAA,QACT;AAIA,iBAAS,qBAAqB,WAAW;AAcvC,mBAAS,mBAAmB,WAAW,gBAAgB,YAAY,aAAa,mBAAmB;AACjG,iBAAK,aAAa;AAClB,iBAAK,cAAc;AACnB,iBAAK,OAAO;AACZ,iBAAK,cAAc;AACnB,iBAAK,SAAS;AACd,iBAAK,gBAAgB;AAErB,qBAAS,aAAa,WAAW;AAC/B,kBAAI,CAAC,UAAU,eAAe,SAAS,GAAG;AACxC;AAAA,cACF;AAEA,kBAAI,YAAY,UAAU,SAAS;AAEnC,kBAAI,WAAW;AACb,qBAAK,SAAS,IAAI,UAAU,WAAW;AAAA,cACzC,OAAO;AACL,qBAAK,SAAS,IAAI,YAAY,SAAS;AAAA,cACzC;AAAA,YACF;AAEA,gBAAI,mBAAmB,YAAY,oBAAoB,OAAO,YAAY,mBAAmB,YAAY,gBAAgB;AAEzH,gBAAI,kBAAkB;AACpB,mBAAK,qBAAqB;AAAA,YAC5B,OAAO;AACL,mBAAK,qBAAqB;AAAA,YAC5B;AAEA,iBAAK,uBAAuB;AAC5B,mBAAO;AAAA,UACT;AAEA,iBAAO,mBAAmB,WAAW;AAAA,YACnC,gBAAgB,WAAY;AAC1B,mBAAK,mBAAmB;AACxB,kBAAI,QAAQ,KAAK;AAEjB,kBAAI,CAAC,OAAO;AACV;AAAA,cACF;AAEA,kBAAI,MAAM,gBAAgB;AACxB,sBAAM,eAAe;AAAA,cACvB,WAAW,OAAO,MAAM,gBAAgB,WAAW;AACjD,sBAAM,cAAc;AAAA,cACtB;AAEA,mBAAK,qBAAqB;AAAA,YAC5B;AAAA,YACA,iBAAiB,WAAY;AAC3B,kBAAI,QAAQ,KAAK;AAEjB,kBAAI,CAAC,OAAO;AACV;AAAA,cACF;AAEA,kBAAI,MAAM,iBAAiB;AACzB,sBAAM,gBAAgB;AAAA,cACxB,WAAW,OAAO,MAAM,iBAAiB,WAAW;AAMlD,sBAAM,eAAe;AAAA,cACvB;AAEA,mBAAK,uBAAuB;AAAA,YAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAOA,SAAS,WAAY;AAAA,YACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAOA,cAAc;AAAA,UAChB,CAAC;AACD,iBAAO;AAAA,QACT;AAOA,YAAI,iBAAiB;AAAA,UACnB,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,WAAW,SAAU,OAAO;AAC1B,mBAAO,MAAM,aAAa,KAAK,IAAI;AAAA,UACrC;AAAA,UACA,kBAAkB;AAAA,UAClB,WAAW;AAAA,QACb;AACA,YAAI,iBAAiB,qBAAqB,cAAc;AAExD,YAAI,mBAAmB,OAAO,CAAC,GAAG,gBAAgB;AAAA,UAChD,MAAM;AAAA,UACN,QAAQ;AAAA,QACV,CAAC;AAED,YAAI,mBAAmB,qBAAqB,gBAAgB;AAC5D,YAAI;AACJ,YAAI;AACJ,YAAI;AAEJ,iBAAS,iCAAiC,OAAO;AAC/C,cAAI,UAAU,gBAAgB;AAC5B,gBAAI,kBAAkB,MAAM,SAAS,aAAa;AAChD,8BAAgB,MAAM,UAAU,eAAe;AAC/C,8BAAgB,MAAM,UAAU,eAAe;AAAA,YACjD,OAAO;AACL,8BAAgB;AAChB,8BAAgB;AAAA,YAClB;AAEA,6BAAiB;AAAA,UACnB;AAAA,QACF;AAOA,YAAI,sBAAsB,OAAO,CAAC,GAAG,kBAAkB;AAAA,UACrD,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,UACP,OAAO;AAAA,UACP,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,kBAAkB;AAAA,UAClB,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,eAAe,SAAU,OAAO;AAC9B,gBAAI,MAAM,kBAAkB;AAAW,qBAAO,MAAM,gBAAgB,MAAM,aAAa,MAAM,YAAY,MAAM;AAC/G,mBAAO,MAAM;AAAA,UACf;AAAA,UACA,WAAW,SAAU,OAAO;AAC1B,gBAAI,eAAe,OAAO;AACxB,qBAAO,MAAM;AAAA,YACf;AAEA,6CAAiC,KAAK;AACtC,mBAAO;AAAA,UACT;AAAA,UACA,WAAW,SAAU,OAAO;AAC1B,gBAAI,eAAe,OAAO;AACxB,qBAAO,MAAM;AAAA,YACf;AAKA,mBAAO;AAAA,UACT;AAAA,QACF,CAAC;AAED,YAAI,sBAAsB,qBAAqB,mBAAmB;AAMlE,YAAI,qBAAqB,OAAO,CAAC,GAAG,qBAAqB;AAAA,UACvD,cAAc;AAAA,QAChB,CAAC;AAED,YAAI,qBAAqB,qBAAqB,kBAAkB;AAMhE,YAAI,sBAAsB,OAAO,CAAC,GAAG,kBAAkB;AAAA,UACrD,eAAe;AAAA,QACjB,CAAC;AAED,YAAI,sBAAsB,qBAAqB,mBAAmB;AAOlE,YAAI,0BAA0B,OAAO,CAAC,GAAG,gBAAgB;AAAA,UACvD,eAAe;AAAA,UACf,aAAa;AAAA,UACb,eAAe;AAAA,QACjB,CAAC;AAED,YAAI,0BAA0B,qBAAqB,uBAAuB;AAM1E,YAAI,0BAA0B,OAAO,CAAC,GAAG,gBAAgB;AAAA,UACvD,eAAe,SAAU,OAAO;AAC9B,mBAAO,mBAAmB,QAAQ,MAAM,gBAAgB,OAAO;AAAA,UACjE;AAAA,QACF,CAAC;AAED,YAAI,0BAA0B,qBAAqB,uBAAuB;AAM1E,YAAI,4BAA4B,OAAO,CAAC,GAAG,gBAAgB;AAAA,UACzD,MAAM;AAAA,QACR,CAAC;AAED,YAAI,4BAA4B,qBAAqB,yBAAyB;AAM9E,YAAI,eAAe;AAAA,UACjB,KAAK;AAAA,UACL,UAAU;AAAA,UACV,MAAM;AAAA,UACN,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,UACL,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,iBAAiB;AAAA,QACnB;AAOA,YAAI,iBAAiB;AAAA,UACnB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAMA,iBAAS,YAAY,aAAa;AAChC,cAAI,YAAY,KAAK;AAKnB,gBAAI,MAAM,aAAa,YAAY,GAAG,KAAK,YAAY;AAEvD,gBAAI,QAAQ,gBAAgB;AAC1B,qBAAO;AAAA,YACT;AAAA,UACF;AAGA,cAAI,YAAY,SAAS,YAAY;AACnC,gBAAI,WAAW,iBAAiB,WAAW;AAG3C,mBAAO,aAAa,KAAK,UAAU,OAAO,aAAa,QAAQ;AAAA,UACjE;AAEA,cAAI,YAAY,SAAS,aAAa,YAAY,SAAS,SAAS;AAGlE,mBAAO,eAAe,YAAY,OAAO,KAAK;AAAA,UAChD;AAEA,iBAAO;AAAA,QACT;AAOA,YAAI,oBAAoB;AAAA,UACtB,KAAK;AAAA,UACL,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAIA,iBAAS,oBAAoB,QAAQ;AACnC,cAAI,iBAAiB;AACrB,cAAI,cAAc,eAAe;AAEjC,cAAI,YAAY,kBAAkB;AAChC,mBAAO,YAAY,iBAAiB,MAAM;AAAA,UAC5C;AAEA,cAAI,UAAU,kBAAkB,MAAM;AACtC,iBAAO,UAAU,CAAC,CAAC,YAAY,OAAO,IAAI;AAAA,QAC5C;AAEA,iBAAS,sBAAsB,aAAa;AAC1C,iBAAO;AAAA,QACT;AAOA,YAAI,yBAAyB,OAAO,CAAC,GAAG,kBAAkB;AAAA,UACxD,KAAK;AAAA,UACL,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,kBAAkB;AAAA;AAAA,UAElB,UAAU,SAAU,OAAO;AAKzB,gBAAI,MAAM,SAAS,YAAY;AAC7B,qBAAO,iBAAiB,KAAK;AAAA,YAC/B;AAEA,mBAAO;AAAA,UACT;AAAA,UACA,SAAS,SAAU,OAAO;AAOxB,gBAAI,MAAM,SAAS,aAAa,MAAM,SAAS,SAAS;AACtD,qBAAO,MAAM;AAAA,YACf;AAEA,mBAAO;AAAA,UACT;AAAA,UACA,OAAO,SAAU,OAAO;AAGtB,gBAAI,MAAM,SAAS,YAAY;AAC7B,qBAAO,iBAAiB,KAAK;AAAA,YAC/B;AAEA,gBAAI,MAAM,SAAS,aAAa,MAAM,SAAS,SAAS;AACtD,qBAAO,MAAM;AAAA,YACf;AAEA,mBAAO;AAAA,UACT;AAAA,QACF,CAAC;AAED,YAAI,yBAAyB,qBAAqB,sBAAsB;AAMxE,YAAI,wBAAwB,OAAO,CAAC,GAAG,qBAAqB;AAAA,UAC1D,WAAW;AAAA,UACX,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,oBAAoB;AAAA,UACpB,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,UACb,WAAW;AAAA,QACb,CAAC;AAED,YAAI,wBAAwB,qBAAqB,qBAAqB;AAMtE,YAAI,sBAAsB,OAAO,CAAC,GAAG,kBAAkB;AAAA,UACrD,SAAS;AAAA,UACT,eAAe;AAAA,UACf,gBAAgB;AAAA,UAChB,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU;AAAA,UACV,kBAAkB;AAAA,QACpB,CAAC;AAED,YAAI,sBAAsB,qBAAqB,mBAAmB;AAOlE,YAAI,2BAA2B,OAAO,CAAC,GAAG,gBAAgB;AAAA,UACxD,cAAc;AAAA,UACd,aAAa;AAAA,UACb,eAAe;AAAA,QACjB,CAAC;AAED,YAAI,2BAA2B,qBAAqB,wBAAwB;AAM5E,YAAI,sBAAsB,OAAO,CAAC,GAAG,qBAAqB;AAAA,UACxD,QAAQ,SAAU,OAAO;AACvB,mBAAO,YAAY,QAAQ,MAAM;AAAA;AAAA,cACjC,iBAAiB,QAAQ,CAAC,MAAM,cAAc;AAAA;AAAA,UAChD;AAAA,UACA,QAAQ,SAAU,OAAO;AACvB,mBAAO,YAAY,QAAQ,MAAM;AAAA;AAAA,cACjC,iBAAiB,QAAQ,CAAC,MAAM;AAAA;AAAA,gBAChC,gBAAgB,QAAQ,CAAC,MAAM,aAAa;AAAA;AAAA;AAAA,UAC9C;AAAA,UACA,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,UAKR,WAAW;AAAA,QACb,CAAC;AAED,YAAI,sBAAsB,qBAAqB,mBAAmB;AAKlE,YAAI,eAAe;AAEnB,iBAAS,0BAA0B,MAAM,MAAM,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACxE,cAAI,WAAW,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAEtD,cAAI;AACF,iBAAK,MAAM,SAAS,QAAQ;AAAA,UAC9B,SAASA,QAAO;AACd,iBAAK,QAAQA,MAAK;AAAA,UACpB;AAAA,QACF;AAEA,YAAI,4BAA4B;AAEhC;AAqBE,cAAI,OAAO,WAAW,eAAe,OAAO,OAAO,kBAAkB,cAAc,OAAO,aAAa,eAAe,OAAO,SAAS,gBAAgB,YAAY;AAChK,gBAAI,WAAW,SAAS,cAAc,OAAO;AAE7C,wCAA4B,SAAS,yBAAyB,MAAM,MAAM,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAKnG,kBAAI,OAAO,aAAa,eAAe,aAAa,MAAM;AACxD,sBAAM,IAAI,MAAM,scAAoe;AAAA,cACtf;AAEA,kBAAI,MAAM,SAAS,YAAY,OAAO;AACtC,kBAAI,UAAU;AAOd,kBAAI,WAAW;AAIf,kBAAI,cAAc,OAAO;AAGzB,kBAAI,wBAAwB,OAAO,yBAAyB,QAAQ,OAAO;AAE3E,uBAAS,uBAAuB;AAK9B,yBAAS,oBAAoB,SAAS,cAAc,KAAK;AAKzD,oBAAI,OAAO,OAAO,UAAU,eAAe,OAAO,eAAe,OAAO,GAAG;AACzE,yBAAO,QAAQ;AAAA,gBACjB;AAAA,cACF;AAKA,kBAAI,WAAW,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAEtD,uBAAS,eAAe;AACtB,0BAAU;AACV,qCAAqB;AACrB,qBAAK,MAAM,SAAS,QAAQ;AAC5B,2BAAW;AAAA,cACb;AAaA,kBAAIA;AAEJ,kBAAI,cAAc;AAClB,kBAAI,qBAAqB;AAEzB,uBAAS,kBAAkB,OAAO;AAChC,gBAAAA,SAAQ,MAAM;AACd,8BAAc;AAEd,oBAAIA,WAAU,QAAQ,MAAM,UAAU,KAAK,MAAM,WAAW,GAAG;AAC7D,uCAAqB;AAAA,gBACvB;AAEA,oBAAI,MAAM,kBAAkB;AAI1B,sBAAIA,UAAS,QAAQ,OAAOA,WAAU,UAAU;AAC9C,wBAAI;AACF,sBAAAA,OAAM,mBAAmB;AAAA,oBAC3B,SAAS,OAAO;AAAA,oBAChB;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAGA,kBAAI,UAAU,YAAY,OAAO,OAAO;AAExC,qBAAO,iBAAiB,SAAS,iBAAiB;AAClD,uBAAS,iBAAiB,SAAS,cAAc,KAAK;AAGtD,kBAAI,UAAU,SAAS,OAAO,KAAK;AACnC,uBAAS,cAAc,GAAG;AAE1B,kBAAI,uBAAuB;AACzB,uBAAO,eAAe,QAAQ,SAAS,qBAAqB;AAAA,cAC9D;AAEA,kBAAI,WAAW,UAAU;AACvB,oBAAI,CAAC,aAAa;AAGhB,kBAAAA,SAAQ,IAAI,MAAM,mdAAsf;AAAA,gBAC1gB,WAAW,oBAAoB;AAE7B,kBAAAA,SAAQ,IAAI,MAAM,4KAAsL;AAAA,gBAC1M;AAEA,qBAAK,QAAQA,MAAK;AAAA,cACpB;AAGA,qBAAO,oBAAoB,SAAS,iBAAiB;AAErD,kBAAI,CAAC,SAAS;AAKZ,qCAAqB;AACrB,uBAAO,0BAA0B,MAAM,MAAM,SAAS;AAAA,cACxD;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,8BAA8B;AAElC,YAAI,WAAW;AACf,YAAI,cAAc;AAElB,YAAI,kBAAkB;AACtB,YAAI,eAAe;AACnB,YAAI,WAAW;AAAA,UACb,SAAS,SAAUA,QAAO;AACxB,uBAAW;AACX,0BAAcA;AAAA,UAChB;AAAA,QACF;AAeA,iBAAS,sBAAsB,MAAM,MAAM,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACpE,qBAAW;AACX,wBAAc;AACd,sCAA4B,MAAM,UAAU,SAAS;AAAA,QACvD;AAYA,iBAAS,wCAAwC,MAAM,MAAM,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACtF,gCAAsB,MAAM,MAAM,SAAS;AAE3C,cAAI,UAAU;AACZ,gBAAIA,SAAQ,iBAAiB;AAE7B,gBAAI,CAAC,iBAAiB;AACpB,gCAAkB;AAClB,6BAAeA;AAAA,YACjB;AAAA,UACF;AAAA,QACF;AAMA,iBAAS,qBAAqB;AAC5B,cAAI,iBAAiB;AACnB,gBAAIA,SAAQ;AACZ,8BAAkB;AAClB,2BAAe;AACf,kBAAMA;AAAA,UACR;AAAA,QACF;AACA,iBAAS,mBAAmB;AAC1B,cAAI,UAAU;AACZ,gBAAIA,SAAQ;AACZ,uBAAW;AACX,0BAAc;AACd,mBAAOA;AAAA,UACT,OAAO;AACL,kBAAM,IAAI,MAAM,6HAAkI;AAAA,UACpJ;AAAA,QACF;AAEA,YAAI,cAAc,MAAM;AAExB,iBAAS,QAAQ,GAAG;AAClB,iBAAO,YAAY,CAAC;AAAA,QACtB;AAEA,YAAI,kBAAkB,SAAS;AAC/B,YAAI,iBAAiB,gBAAgB;AACrC,YAAI,sBAAsB,eAAe,CAAC;AAC1C,YAAI,sBAAsB,eAAe,CAAC;AAC1C,YAAI,+BAA+B,eAAe,CAAC;AACnD,YAAI,sBAAsB,eAAe,CAAC;AAC1C,YAAI,uBAAuB,eAAe,CAAC;AAC3C,YAAI,WAAW,MAAM;AAErB,iBAAS,MAAM,QAAQ;AAAA,QAAC;AAExB,YAAI,wCAAwC;AAK5C,iBAAS,mCAAmC,OAAO,MAAM;AACvD,cAAI,CAAC,OAAO;AACV,mBAAO,CAAC;AAAA,UACV;AAEA,cAAI,gBAAgB,8BAA8B,KAAK;AAEvD,cAAI,CAAC,eAAe;AAClB,mBAAO,CAAC;AAAA,UACV;AAEA,cAAI,OAAO;AACX,cAAI,MAAM,CAAC;AAEX,iBAAO,MAAM;AACX,gBAAI,KAAK,QAAQ,iBAAiB,KAAK,QAAQ,YAAY,KAAK,QAAQ,kBAAkB,KAAK,QAAQ,mBAAmB;AACxH,kBAAI,aAAa,KAAK;AAEtB,kBAAI,KAAK,UAAU,GAAG;AACpB,oBAAI,KAAK,UAAU;AAAA,cACrB;AAAA,YACF;AAEA,gBAAI,KAAK,OAAO;AACd,mBAAK,MAAM,SAAS;AACpB,qBAAO,KAAK;AACZ;AAAA,YACF;AAEA,gBAAI,SAAS,eAAe;AAC1B,qBAAO;AAAA,YACT;AAEA,mBAAO,CAAC,KAAK,SAAS;AACpB,kBAAI,CAAC,KAAK,UAAU,KAAK,WAAW,eAAe;AACjD,uBAAO;AAAA,cACT;AAEA,qBAAO,KAAK;AAAA,YACd;AAEA,iBAAK,QAAQ,SAAS,KAAK;AAC3B,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAEA,iBAAS,sBAAsB,MAAM,YAAY;AAC/C,cAAI,CAAC,MAAM;AAET;AAAA,UACF;AAEA,cAAI,IAAI,IAAI,GAAG;AAEb;AAAA,UACF;AAEA,cAAI;AACJ,cAAI,cAAc,OAAO,IAAI;AAE7B,cAAI,QAAQ,IAAI,GAAG;AACjB,uBAAW;AAAA,UACb,WAAW,QAAQ,KAAK,aAAa,gBAAgB,KAAK,SAAS;AACjE,uBAAW;AAAA,UACb,WAAW,gBAAgB,mBAAmB;AAC5C,uBAAW,uBAAuB,OAAO,KAAK,IAAI,EAAE,KAAK,IAAI,IAAI;AAAA,UACnE,OAAO;AACL,uBAAW;AAAA,UACb;AAEA,gBAAM,IAAI,MAAM,aAAa,gEAAgE,uBAAuB,WAAW,IAAI;AAAA,QACrI;AAYA,YAAI,wCAAwC;AAE5C,iBAAS,mBAAmB,SAAS;AACnC;AACE,gBAAI,CAAC,uCAAuC;AAC1C,sDAAwC;AAExC,oBAAM,8TAAkV;AAAA,YAC1V;AAAA,UACF;AAEA,cAAI,MAAM,SAAS,cAAc,KAAK;AAMtC,iBAAO,SAAS,OAAO,SAAS,GAAG;AAAA,QACrC;AAEA,iBAAS,UAAU,SAAS;AAC1B,iBAAO,MAAM,eAAe,OAAO;AAAA,QACrC;AAEA,iBAAS,gBAAgB,MAAM,wBAAwB;AACrD,iBAAO,MAAM,eAAe,IAAI,KAAK,KAAK,SAAS;AAAA,QACrD;AAEA,iBAAS,eAAe,MAAM;AAC5B,iBAAO,CAAC,EAAE,QAAQ,KAAK,aAAa,gBAAgB,KAAK;AAAA,QAC3D;AAEA,iBAAS,sBAAsB,MAAM;AACnC,iBAAO,CAAC,EAAE,QAAQ,MAAM,eAAe,IAAI,KAAK,CAAC,CAAC,KAAK;AAAA,QACzD;AAEA,iBAAS,qBAAqB,MAAM;AAClC,cAAI,eAAe,IAAI,GAAG;AAGxB,mBAAO;AAAA,UACT;AAEA,iBAAO,QAAQ,QAAQ,OAAO,KAAK,WAAW,cAAc,OAAO,KAAK,aAAa;AAAA,QACvF;AAEA,iBAAS,6BAA6B,MAAM,MAAM;AAChD,cAAI,CAAC,qBAAqB,IAAI,GAAG;AAC/B,mBAAO;AAAA,UACT;AAEA,cAAI,mBAAmB,IAAI,IAAI;AAC/B,cAAI,cAAc,iBAAiB;AACnC,iBAAO,gBAAgB;AAAA,QACzB;AAEA,iBAAS,sBAAsB,MAAM,MAAM;AACzC,gCAAsB,MAAM,uBAAuB;AAEnD,cAAI,CAAC,MAAM;AACT,mBAAO,CAAC;AAAA,UACV;AAEA,cAAI,mBAAmB,IAAI,IAAI;AAC/B,iBAAO,mCAAmC,kBAAkB,IAAI;AAAA,QAClE;AAQA,iBAAS,mCAAmC,MAAM,YAAY;AAC5D,gCAAsB,MAAM,oCAAoC;AAChE,iBAAO,sBAAsB,MAAM,SAAU,MAAM;AACjD,gBAAI,eAAe,IAAI,GAAG;AACxB,kBAAI,YAAY,KAAK;AAErB,kBAAI,OAAO,cAAc,UAAU;AAEjC,4BAAY,KAAK,aAAa,OAAO,KAAK;AAAA,cAC5C;AAEA,kBAAI,YAAY,UAAU,MAAM,KAAK;AAErC,kBAAI,CAAC,QAAQ,UAAU,GAAG;AACxB,oBAAI,eAAe,QAAW;AAC5B,wBAAM,IAAI,MAAM,wFAA6F;AAAA,gBAC/G;AAEA,6BAAa,WAAW,MAAM,KAAK;AAAA,cACrC;AAEA,qBAAO,WAAW,MAAM,SAAU,MAAM;AACtC,uBAAO,UAAU,QAAQ,IAAI,MAAM;AAAA,cACrC,CAAC;AAAA,YACH;AAEA,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AASA,iBAAS,kCAAkC,MAAM,WAAW;AAC1D,gCAAsB,MAAM,mCAAmC;AAC/D,cAAI,MAAM,mCAAmC,MAAM,SAAS;AAE5D,cAAI,IAAI,WAAW,GAAG;AACpB,kBAAM,IAAI,MAAM,4CAA4C,IAAI,SAAS,iBAAsB,SAAS;AAAA,UAC1G;AAEA,iBAAO,IAAI,CAAC;AAAA,QACd;AAQA,iBAAS,iCAAiC,MAAM,SAAS;AACvD,gCAAsB,MAAM,kCAAkC;AAC9D,iBAAO,sBAAsB,MAAM,SAAU,MAAM;AACjD,mBAAO,eAAe,IAAI,KAAK,KAAK,QAAQ,YAAY,MAAM,QAAQ,YAAY;AAAA,UACpF,CAAC;AAAA,QACH;AASA,iBAAS,gCAAgC,MAAM,SAAS;AACtD,gCAAsB,MAAM,iCAAiC;AAC7D,cAAI,MAAM,iCAAiC,MAAM,OAAO;AAExD,cAAI,IAAI,WAAW,GAAG;AACpB,kBAAM,IAAI,MAAM,4CAA4C,IAAI,SAAS,eAAoB,OAAO;AAAA,UACtG;AAEA,iBAAO,IAAI,CAAC;AAAA,QACd;AAOA,iBAAS,+BAA+B,MAAM,eAAe;AAC3D,gCAAsB,MAAM,gCAAgC;AAC5D,iBAAO,sBAAsB,MAAM,SAAU,MAAM;AACjD,mBAAO,6BAA6B,MAAM,aAAa;AAAA,UACzD,CAAC;AAAA,QACH;AASA,iBAAS,8BAA8B,MAAM,eAAe;AAC1D,gCAAsB,MAAM,+BAA+B;AAC3D,cAAI,MAAM,+BAA+B,MAAM,aAAa;AAE5D,cAAI,IAAI,WAAW,GAAG;AACpB,kBAAM,IAAI,MAAM,4CAA4C,IAAI,SAAS,yBAA8B,aAAa;AAAA,UACtH;AAEA,iBAAO,IAAI,CAAC;AAAA,QACd;AAgBA,iBAAS,cAAcC,SAAQ,aAAa;AAC1C;AACE,gBAAI,CAAC,uCAAuC;AAC1C,sDAAwC;AAExC,mBAAK,6KAAuL;AAAA,YAC9L;AAAA,UACF;AAEA,wBAAc,eAAeA,QAAO,eAAe;AACnD,UAAAA,QAAO,UAAU,OAAO,mBAAmB,WAAY;AACrD,mBAAO,MAAM,cAAc,aAAa,MAAM,KAAK,MAAM,QAAQ;AAAA,UACnE,CAAC;AACD,iBAAO;AAAA,QACT;AAEA,iBAAS,gBAAgB,GAAG,GAAG;AAC7B,iBAAO;AAAA,YACL,SAAS,CAAC;AAAA,cACR,OAAO;AAAA,cACP,OAAO;AAAA,YACT,CAAC;AAAA,UACH;AAAA,QACF;AAYA,iBAAS,gBAAgB,OAAO,UAAU,MAAM;AAC9C,cAAI,OAAO,MAAM,QAAQ;AACzB,gBAAM,gBAAgB,oBAAoB,IAAI;AAC9C,kDAAwC,MAAM,UAAU,QAAW,KAAK;AACxE,gBAAM,gBAAgB;AAAA,QACxB;AAMA,iBAAS,yBAAyB,OAAO;AACvC,cAAI,oBAAoB,MAAM;AAC9B,cAAI,oBAAoB,MAAM;AAE9B,cAAI,QAAQ,iBAAiB,GAAG;AAC9B,qBAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,kBAAI,MAAM,qBAAqB,GAAG;AAChC;AAAA,cACF;AAGA,8BAAgB,OAAO,kBAAkB,CAAC,GAAG,kBAAkB,CAAC,CAAC;AAAA,YACnE;AAAA,UACF,WAAW,mBAAmB;AAC5B,4BAAgB,OAAO,mBAAmB,iBAAiB;AAAA,UAC7D;AAEA,gBAAM,qBAAqB;AAC3B,gBAAM,qBAAqB;AAAA,QAC7B;AASA,YAAI,8BAA8B,SAAU,OAAO;AACjD,cAAI,OAAO;AACT,qCAAyB,KAAK;AAE9B,gBAAI,CAAC,MAAM,aAAa,GAAG;AACzB,oBAAM,YAAY,QAAQ,KAAK;AAAA,YACjC;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,cAAc,KAAK;AAC1B,iBAAO,QAAQ,YAAY,QAAQ,WAAW,QAAQ,YAAY,QAAQ;AAAA,QAC5E;AAEA,iBAAS,UAAU,MAAM;AACvB,aAAG;AACD,mBAAO,KAAK;AAAA,UAKd,SAAS,QAAQ,KAAK,QAAQ;AAE9B,cAAI,MAAM;AACR,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAMA,iBAAS,iBAAiB,MAAM,IAAI,KAAK;AACvC,cAAI,OAAO,CAAC;AAEZ,iBAAO,MAAM;AACX,iBAAK,KAAK,IAAI;AACd,mBAAO,UAAU,IAAI;AAAA,UACvB;AAEA,cAAI;AAEJ,eAAK,IAAI,KAAK,QAAQ,MAAM,KAAI;AAC9B,eAAG,KAAK,CAAC,GAAG,YAAY,GAAG;AAAA,UAC7B;AAEA,eAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAChC,eAAG,KAAK,CAAC,GAAG,WAAW,GAAG;AAAA,UAC5B;AAAA,QACF;AAEA,iBAAS,wBAAwB,MAAM,MAAM,OAAO;AAClD,kBAAQ,MAAM;AAAA,YACZ,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AACH,qBAAO,CAAC,EAAE,MAAM,YAAY,cAAc,IAAI;AAAA,YAEhD;AACE,qBAAO;AAAA,UACX;AAAA,QACF;AAQA,iBAAS,YAAY,MAAM,kBAAkB;AAG3C,cAAI,YAAY,KAAK;AAErB,cAAI,CAAC,WAAW;AAEd,mBAAO;AAAA,UACT;AAEA,cAAI,QAAQ,6BAA6B,SAAS;AAElD,cAAI,CAAC,OAAO;AAEV,mBAAO;AAAA,UACT;AAEA,cAAI,WAAW,MAAM,gBAAgB;AAErC,cAAI,wBAAwB,kBAAkB,KAAK,MAAM,KAAK,GAAG;AAC/D,mBAAO;AAAA,UACT;AAEA,cAAI,YAAY,OAAO,aAAa,YAAY;AAC9C,kBAAM,IAAI,MAAM,eAAe,mBAAmB,0DAA0D,OAAO,WAAW,SAAS;AAAA,UACzI;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,gBAAgB,MAAM,OAAO,kBAAkB;AACtD,cAAI,mBAAmB,MAAM;AAE7B,cAAI,qBAAqB,YAAY;AACnC,gCAAoB;AAAA,UACtB;AAEA,iBAAO,YAAY,MAAM,gBAAgB;AAAA,QAC3C;AAEA,iBAAS,qBAAqB,MAAM,kBAAkB,OAAO;AAC3D,cAAI,QAAQ,SAAS,MAAM,YAAY;AACrC,gBAAI,mBAAmB,MAAM;AAC7B,gBAAI,WAAW,YAAY,MAAM,gBAAgB;AAEjD,gBAAI,UAAU;AACZ,kBAAI,MAAM,sBAAsB,MAAM;AACpC,sBAAM,qBAAqB,CAAC;AAAA,cAC9B;AAEA,kBAAI,MAAM,sBAAsB,MAAM;AACpC,sBAAM,qBAAqB,CAAC;AAAA,cAC9B;AAEA,oBAAM,mBAAmB,KAAK,QAAQ;AAEtC,oBAAM,mBAAmB,KAAK,IAAI;AAAA,YACpC;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,gCAAgC,MAAM,OAAO,OAAO;AAC3D;AACE,gBAAI,CAAC,MAAM;AACT,oBAAM,mCAAmC;AAAA,YAC3C;AAAA,UACF;AAEA,cAAI,WAAW,gBAAgB,MAAM,OAAO,KAAK;AAEjD,cAAI,UAAU;AACZ,gBAAI,MAAM,sBAAsB,MAAM;AACpC,oBAAM,qBAAqB,CAAC;AAAA,YAC9B;AAEA,gBAAI,MAAM,sBAAsB,MAAM;AACpC,oBAAM,qBAAqB,CAAC;AAAA,YAC9B;AAEA,kBAAM,mBAAmB,KAAK,QAAQ;AAEtC,kBAAM,mBAAmB,KAAK,IAAI;AAAA,UACpC;AAAA,QACF;AAEA,iBAAS,iCAAiC,OAAO;AAC/C,cAAI,SAAS,MAAM,YAAY;AAC7B,iCAAqB,MAAM,aAAa,MAAM,KAAK;AAAA,UACrD;AAAA,QACF;AAEA,iBAAS,mCAAmC,OAAO;AACjD,cAAI,SAAS,MAAM,YAAY;AAC7B,6BAAiB,MAAM,aAAa,iCAAiC,KAAK;AAAA,UAC5E;AAAA,QACF;AAGA,YAAI,WAAW,CAAC;AAChB,YAAI,2BAA2B,oBAAI,IAAI,CAAC,cAAc,cAAc,gBAAgB,cAAc,CAAC;AAUnG,iBAAS,cAAc,WAAW;AAChC,iBAAO,SAAU,SAAS,WAAW;AACnC,gBAAI,MAAM,eAAe,OAAO,GAAG;AACjC,oBAAM,IAAI,MAAM,wOAAkP;AAAA,YACpQ;AAEA,gBAAI,qBAAqB,OAAO,GAAG;AACjC,oBAAM,IAAI,MAAM,8JAAmK;AAAA,YACrL;AAEA,gBAAI,YAAY,OAAO,UAAU,CAAC,EAAE,YAAY,IAAI,UAAU,MAAM,CAAC;AACrE,gBAAI,kBAAkB,IAAI,MAAM;AAChC,4BAAgB,SAAS;AACzB,4BAAgB,OAAO,UAAU,YAAY;AAC7C,gBAAI,aAAa,oBAAoB,OAAO;AAC5C,gBAAI,QAAQ,IAAI,eAAe,WAAW,gBAAgB,MAAM,YAAY,iBAAiB,OAAO;AAGpG,kBAAM,QAAQ;AACd,mBAAO,OAAO,SAAS;AAEvB,gBAAI,yBAAyB,IAAI,SAAS,GAAG;AAC3C,+CAAiC,KAAK;AAAA,YACxC,OAAO;AACL,iDAAmC,KAAK;AAAA,YAC1C;AAEA,qBAAS,wBAAwB,WAAY;AAG3C,kCAAoB,OAAO;AAC3B,0CAA4B,KAAK;AACjC,iCAAmB;AAAA,YACrB,CAAC;AACD,iCAAqB;AAAA,UACvB;AAAA,QACF;AAGA,YAAI,sBAAsB,CAAC,QAAQ,UAAU,SAAS,SAAS,eAAe,QAAQ,OAAO,YAAY,eAAe,WAAW,aAAa,QAAQ,SAAS,SAAS,WAAW,WAAW,YAAY,SAAS,aAAa,WAAW,SAAS,SAAS,QAAQ,iBAAiB,eAAe,aAAa,cAAc,SAAS,UAAU,UAAU,UAAU,eAAe,YAAY,cAAc,gBAAgB,QAAQ,aAAa,YAAY,aAAa,YAAY,aAAa,YAAY,aAAa,eAAe,cAAc,eAAe,UAAU,UAAU,aAAa,SAAS,SAAS,gBAAgB,sBAAsB,kBAAkB,WAAW,kBAAkB,kBAAkB,WAAW,aAAa,SAAS,SAAS,qBAAqB,QAAQ,cAAc,kBAAkB,aAAa,sBAAsB,WAAW,YAAY,WAAW,WAAW,WAAW,cAAc,iBAAiB,WAAW,cAAc,cAAc,gBAAgB,gBAAgB,UAAU,UAAU,eAAe,kBAAkB,oBAAoB,mBAAmB;AAE5kC,iBAAS,kBAAkB;AACzB,8BAAoB,QAAQ,SAAU,WAAW;AAC/C,qBAAS,SAAS,IAAI,cAAc,SAAS;AAAA,UAC/C,CAAC;AAAA,QACH;AAEA,wBAAgB;AAChB,YAAI,uBAAuB;AAC3B,YAAI,MAAO,SAAS,eAAe,UAAU;AAC3C;AACE,gBAAI,CAAC,sBAAsB;AACzB,qCAAuB;AAEvB,oBAAM,gMAA0M;AAAA,YAClN;AAAA,UACF;AAEA,iBAAO,SAAS,QAAQ;AAAA,QAC1B;AAEA,gBAAQ,WAAW;AACnB,gBAAQ,MAAM;AACd,gBAAQ,wBAAwB;AAChC,gBAAQ,gCAAgC;AACxC,gBAAQ,oCAAoC;AAC5C,gBAAQ,kCAAkC;AAC1C,gBAAQ,uBAAuB;AAC/B,gBAAQ,+BAA+B;AACvC,gBAAQ,iBAAiB;AACzB,gBAAQ,wBAAwB;AAChC,gBAAQ,YAAY;AACpB,gBAAQ,kBAAkB;AAC1B,gBAAQ,gBAAgB;AACxB,gBAAQ,kBAAkB;AAC1B,gBAAQ,qBAAqB;AAC7B,gBAAQ,iCAAiC;AACzC,gBAAQ,qCAAqC;AAC7C,gBAAQ,mCAAmC;AAC3C,gBAAQ,mBAAmB;AAAA,MACzB,GAAG;AAAA,IACL;AAAA;AAAA;;;ACluDA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;", "names": ["error", "module"]}