{"version": 3, "sources": ["../../../../../memoizerific/memoizerific.js"], "sourcesContent": ["(function(f){if(typeof exports===\"object\"&&typeof module!==\"undefined\"){module.exports=f()}else if(typeof define===\"function\"&&define.amd){define([],f)}else{var g;if(typeof window!==\"undefined\"){g=window}else if(typeof global!==\"undefined\"){g=global}else if(typeof self!==\"undefined\"){g=self}else{g=this}g.memoizerific = f()}})(function(){var define,module,exports;return (function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require==\"function\"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error(\"Cannot find module '\"+o+\"'\");throw f.code=\"MODULE_NOT_FOUND\",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require==\"function\"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(_dereq_,module,exports){\nmodule.exports = function(forceSimilar) {\n\tif (typeof Map !== 'function' || forceSimilar) {\n\t\tvar Similar = _dereq_('./similar');\n\t\treturn new Similar();\n\t}\n\telse {\n\t\treturn new Map();\n\t}\n}\n\n},{\"./similar\":2}],2:[function(_dereq_,module,exports){\nfunction Similar() {\n\tthis.list = [];\n\tthis.lastItem = undefined;\n\tthis.size = 0;\n\n\treturn this;\n}\n\nSimilar.prototype.get = function(key) {\n\tvar index;\n\n\tif (this.lastItem && this.isEqual(this.lastItem.key, key)) {\n\t\treturn this.lastItem.val;\n\t}\n\n\tindex = this.indexOf(key);\n\tif (index >= 0) {\n\t\tthis.lastItem = this.list[index];\n\t\treturn this.list[index].val;\n\t}\n\n\treturn undefined;\n};\n\nSimilar.prototype.set = function(key, val) {\n\tvar index;\n\n\tif (this.lastItem && this.isEqual(this.lastItem.key, key)) {\n\t\tthis.lastItem.val = val;\n\t\treturn this;\n\t}\n\n\tindex = this.indexOf(key);\n\tif (index >= 0) {\n\t\tthis.lastItem = this.list[index];\n\t\tthis.list[index].val = val;\n\t\treturn this;\n\t}\n\n\tthis.lastItem = { key: key, val: val };\n\tthis.list.push(this.lastItem);\n\tthis.size++;\n\n\treturn this;\n};\n\nSimilar.prototype.delete = function(key) {\n\tvar index;\n\n\tif (this.lastItem && this.isEqual(this.lastItem.key, key)) {\n\t\tthis.lastItem = undefined;\n\t}\n\n\tindex = this.indexOf(key);\n\tif (index >= 0) {\n\t\tthis.size--;\n\t\treturn this.list.splice(index, 1)[0];\n\t}\n\n\treturn undefined;\n};\n\n\n// important that has() doesn't use get() in case an existing key has a falsy value, in which case has() would return false\nSimilar.prototype.has = function(key) {\n\tvar index;\n\n\tif (this.lastItem && this.isEqual(this.lastItem.key, key)) {\n\t\treturn true;\n\t}\n\n\tindex = this.indexOf(key);\n\tif (index >= 0) {\n\t\tthis.lastItem = this.list[index];\n\t\treturn true;\n\t}\n\n\treturn false;\n};\n\nSimilar.prototype.forEach = function(callback, thisArg) {\n\tvar i;\n\tfor (i = 0; i < this.size; i++) {\n\t\tcallback.call(thisArg || this, this.list[i].val, this.list[i].key, this);\n\t}\n};\n\nSimilar.prototype.indexOf = function(key) {\n\tvar i;\n\tfor (i = 0; i < this.size; i++) {\n\t\tif (this.isEqual(this.list[i].key, key)) {\n\t\t\treturn i;\n\t\t}\n\t}\n\treturn -1;\n};\n\n// check if the numbers are equal, or whether they are both precisely NaN (isNaN returns true for all non-numbers)\nSimilar.prototype.isEqual = function(val1, val2) {\n\treturn val1 === val2 || (val1 !== val1 && val2 !== val2);\n};\n\nmodule.exports = Similar;\n},{}],3:[function(_dereq_,module,exports){\nvar MapOrSimilar = _dereq_('map-or-similar');\n\nmodule.exports = function (limit) {\n\tvar cache = new MapOrSimilar(undefined === 'true'),\n\t\tlru = [];\n\n\treturn function (fn) {\n\t\tvar memoizerific = function () {\n\t\t\tvar currentCache = cache,\n\t\t\t\tnewMap,\n\t\t\t\tfnResult,\n\t\t\t\targsLengthMinusOne = arguments.length - 1,\n\t\t\t\tlruPath = Array(argsLengthMinusOne + 1),\n\t\t\t\tisMemoized = true,\n\t\t\t\ti;\n\n\t\t\tif ((memoizerific.numArgs || memoizerific.numArgs === 0) && memoizerific.numArgs !== argsLengthMinusOne + 1) {\n\t\t\t\tthrow new Error('Memoizerific functions should always be called with the same number of arguments');\n\t\t\t}\n\n\t\t\t// loop through each argument to traverse the map tree\n\t\t\tfor (i = 0; i < argsLengthMinusOne; i++) {\n\t\t\t\tlruPath[i] = {\n\t\t\t\t\tcacheItem: currentCache,\n\t\t\t\t\targ: arguments[i]\n\t\t\t\t};\n\n\t\t\t\t// climb through the hierarchical map tree until the second-last argument has been found, or an argument is missing.\n\t\t\t\t// if all arguments up to the second-last have been found, this will potentially be a cache hit (determined later)\n\t\t\t\tif (currentCache.has(arguments[i])) {\n\t\t\t\t\tcurrentCache = currentCache.get(arguments[i]);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tisMemoized = false;\n\n\t\t\t\t// make maps until last value\n\t\t\t\tnewMap = new MapOrSimilar(undefined === 'true');\n\t\t\t\tcurrentCache.set(arguments[i], newMap);\n\t\t\t\tcurrentCache = newMap;\n\t\t\t}\n\n\t\t\t// we are at the last arg, check if it is really memoized\n\t\t\tif (isMemoized) {\n\t\t\t\tif (currentCache.has(arguments[argsLengthMinusOne])) {\n\t\t\t\t\tfnResult = currentCache.get(arguments[argsLengthMinusOne]);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tisMemoized = false;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (!isMemoized) {\n\t\t\t\tfnResult = fn.apply(null, arguments);\n\t\t\t\tcurrentCache.set(arguments[argsLengthMinusOne], fnResult);\n\t\t\t}\n\n\t\t\tif (limit > 0) {\n\t\t\t\tlruPath[argsLengthMinusOne] = {\n\t\t\t\t\tcacheItem: currentCache,\n\t\t\t\t\targ: arguments[argsLengthMinusOne]\n\t\t\t\t};\n\n\t\t\t\tif (isMemoized) {\n\t\t\t\t\tmoveToMostRecentLru(lru, lruPath);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tlru.push(lruPath);\n\t\t\t\t}\n\n\t\t\t\tif (lru.length > limit) {\n\t\t\t\t\tremoveCachedResult(lru.shift());\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tmemoizerific.wasMemoized = isMemoized;\n\t\t\tmemoizerific.numArgs = argsLengthMinusOne + 1;\n\n\t\t\treturn fnResult;\n\t\t};\n\n\t\tmemoizerific.limit = limit;\n\t\tmemoizerific.wasMemoized = false;\n\t\tmemoizerific.cache = cache;\n\t\tmemoizerific.lru = lru;\n\n\t\treturn memoizerific;\n\t};\n};\n\n// move current args to most recent position\nfunction moveToMostRecentLru(lru, lruPath) {\n\tvar lruLen = lru.length,\n\t\tlruPathLen = lruPath.length,\n\t\tisMatch,\n\t\ti, ii;\n\n\tfor (i = 0; i < lruLen; i++) {\n\t\tisMatch = true;\n\t\tfor (ii = 0; ii < lruPathLen; ii++) {\n\t\t\tif (!isEqual(lru[i][ii].arg, lruPath[ii].arg)) {\n\t\t\t\tisMatch = false;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tif (isMatch) {\n\t\t\tbreak;\n\t\t}\n\t}\n\n\tlru.push(lru.splice(i, 1)[0]);\n}\n\n// remove least recently used cache item and all dead branches\nfunction removeCachedResult(removedLru) {\n\tvar removedLruLen = removedLru.length,\n\t\tcurrentLru = removedLru[removedLruLen - 1],\n\t\ttmp,\n\t\ti;\n\n\tcurrentLru.cacheItem.delete(currentLru.arg);\n\n\t// walk down the tree removing dead branches (size 0) along the way\n\tfor (i = removedLruLen - 2; i >= 0; i--) {\n\t\tcurrentLru = removedLru[i];\n\t\ttmp = currentLru.cacheItem.get(currentLru.arg);\n\n\t\tif (!tmp || !tmp.size) {\n\t\t\tcurrentLru.cacheItem.delete(currentLru.arg);\n\t\t} else {\n\t\t\tbreak;\n\t\t}\n\t}\n}\n\n// check if the numbers are equal, or whether they are both precisely NaN (isNaN returns true for all non-numbers)\nfunction isEqual(val1, val2) {\n\treturn val1 === val2 || (val1 !== val1 && val2 !== val2);\n}\n},{\"map-or-similar\":1}]},{},[3])(3)\n});"], "mappings": ";;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE;AAAC,UAAG,OAAO,YAAU,YAAU,OAAO,WAAS,aAAY;AAAC,eAAO,UAAQ,EAAE;AAAA,MAAC,WAAS,OAAO,WAAS,cAAY,OAAO,KAAI;AAAC,eAAO,CAAC,GAAE,CAAC;AAAA,MAAC,OAAK;AAAC,YAAI;AAAE,YAAG,OAAO,WAAS,aAAY;AAAC,cAAE;AAAA,QAAM,WAAS,OAAO,WAAS,aAAY;AAAC,cAAE;AAAA,QAAM,WAAS,OAAO,SAAO,aAAY;AAAC,cAAE;AAAA,QAAI,OAAK;AAAC,cAAE;AAAA,QAAI;AAAC,UAAE,eAAe,EAAE;AAAA,MAAC;AAAA,IAAC,GAAG,WAAU;AAAC,UAAIA,SAAOC,SAAOC;AAAQ,aAAQ,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,iBAAS,EAAEC,IAAE,GAAE;AAAC,cAAG,CAAC,EAAEA,EAAC,GAAE;AAAC,gBAAG,CAAC,EAAEA,EAAC,GAAE;AAAC,kBAAI,IAAE,OAAO,aAAS,cAAY;AAAQ,kBAAG,CAAC,KAAG;AAAE,uBAAO,EAAEA,IAAE,IAAE;AAAE,kBAAG;AAAE,uBAAO,EAAEA,IAAE,IAAE;AAAE,kBAAI,IAAE,IAAI,MAAM,yBAAuBA,KAAE,GAAG;AAAE,oBAAM,EAAE,OAAK,oBAAmB;AAAA,YAAC;AAAC,gBAAI,IAAE,EAAEA,EAAC,IAAE,EAAC,SAAQ,CAAC,EAAC;AAAE,cAAEA,EAAC,EAAE,CAAC,EAAE,KAAK,EAAE,SAAQ,SAASC,IAAE;AAAC,kBAAIC,KAAE,EAAEF,EAAC,EAAE,CAAC,EAAEC,EAAC;AAAE,qBAAO,EAAEC,KAAEA,KAAED,EAAC;AAAA,YAAC,GAAE,GAAE,EAAE,SAAQ,GAAE,GAAE,GAAE,CAAC;AAAA,UAAC;AAAC,iBAAO,EAAED,EAAC,EAAE;AAAA,QAAO;AAAC,YAAI,IAAE,OAAO,aAAS,cAAY;AAAQ,iBAAQ,IAAE,GAAE,IAAE,EAAE,QAAO;AAAI,YAAE,EAAE,CAAC,CAAC;AAAE,eAAO;AAAA,MAAC,EAAG,EAAC,GAAE,CAAC,SAAS,SAAQF,SAAOC,UAAQ;AACn1B,QAAAD,QAAO,UAAU,SAAS,cAAc;AACvC,cAAI,OAAO,QAAQ,cAAc,cAAc;AAC9C,gBAAI,UAAU,QAAQ,WAAW;AACjC,mBAAO,IAAI,QAAQ;AAAA,UACpB,OACK;AACJ,mBAAO,oBAAI,IAAI;AAAA,UAChB;AAAA,QACD;AAAA,MAEA,GAAE,EAAC,aAAY,EAAC,CAAC,GAAE,GAAE,CAAC,SAAS,SAAQA,SAAOC,UAAQ;AACtD,iBAAS,UAAU;AAClB,eAAK,OAAO,CAAC;AACb,eAAK,WAAW;AAChB,eAAK,OAAO;AAEZ,iBAAO;AAAA,QACR;AAEA,gBAAQ,UAAU,MAAM,SAAS,KAAK;AACrC,cAAI;AAEJ,cAAI,KAAK,YAAY,KAAK,QAAQ,KAAK,SAAS,KAAK,GAAG,GAAG;AAC1D,mBAAO,KAAK,SAAS;AAAA,UACtB;AAEA,kBAAQ,KAAK,QAAQ,GAAG;AACxB,cAAI,SAAS,GAAG;AACf,iBAAK,WAAW,KAAK,KAAK,KAAK;AAC/B,mBAAO,KAAK,KAAK,KAAK,EAAE;AAAA,UACzB;AAEA,iBAAO;AAAA,QACR;AAEA,gBAAQ,UAAU,MAAM,SAAS,KAAK,KAAK;AAC1C,cAAI;AAEJ,cAAI,KAAK,YAAY,KAAK,QAAQ,KAAK,SAAS,KAAK,GAAG,GAAG;AAC1D,iBAAK,SAAS,MAAM;AACpB,mBAAO;AAAA,UACR;AAEA,kBAAQ,KAAK,QAAQ,GAAG;AACxB,cAAI,SAAS,GAAG;AACf,iBAAK,WAAW,KAAK,KAAK,KAAK;AAC/B,iBAAK,KAAK,KAAK,EAAE,MAAM;AACvB,mBAAO;AAAA,UACR;AAEA,eAAK,WAAW,EAAE,KAAU,IAAS;AACrC,eAAK,KAAK,KAAK,KAAK,QAAQ;AAC5B,eAAK;AAEL,iBAAO;AAAA,QACR;AAEA,gBAAQ,UAAU,SAAS,SAAS,KAAK;AACxC,cAAI;AAEJ,cAAI,KAAK,YAAY,KAAK,QAAQ,KAAK,SAAS,KAAK,GAAG,GAAG;AAC1D,iBAAK,WAAW;AAAA,UACjB;AAEA,kBAAQ,KAAK,QAAQ,GAAG;AACxB,cAAI,SAAS,GAAG;AACf,iBAAK;AACL,mBAAO,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,CAAC;AAAA,UACpC;AAEA,iBAAO;AAAA,QACR;AAIA,gBAAQ,UAAU,MAAM,SAAS,KAAK;AACrC,cAAI;AAEJ,cAAI,KAAK,YAAY,KAAK,QAAQ,KAAK,SAAS,KAAK,GAAG,GAAG;AAC1D,mBAAO;AAAA,UACR;AAEA,kBAAQ,KAAK,QAAQ,GAAG;AACxB,cAAI,SAAS,GAAG;AACf,iBAAK,WAAW,KAAK,KAAK,KAAK;AAC/B,mBAAO;AAAA,UACR;AAEA,iBAAO;AAAA,QACR;AAEA,gBAAQ,UAAU,UAAU,SAAS,UAAU,SAAS;AACvD,cAAI;AACJ,eAAK,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAC/B,qBAAS,KAAK,WAAW,MAAM,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,KAAK,CAAC,EAAE,KAAK,IAAI;AAAA,UACxE;AAAA,QACD;AAEA,gBAAQ,UAAU,UAAU,SAAS,KAAK;AACzC,cAAI;AACJ,eAAK,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAC/B,gBAAI,KAAK,QAAQ,KAAK,KAAK,CAAC,EAAE,KAAK,GAAG,GAAG;AACxC,qBAAO;AAAA,YACR;AAAA,UACD;AACA,iBAAO;AAAA,QACR;AAGA,gBAAQ,UAAU,UAAU,SAAS,MAAM,MAAM;AAChD,iBAAO,SAAS,QAAS,SAAS,QAAQ,SAAS;AAAA,QACpD;AAEA,QAAAD,QAAO,UAAU;AAAA,MACjB,GAAE,CAAC,CAAC,GAAE,GAAE,CAAC,SAAS,SAAQA,SAAOC,UAAQ;AACzC,YAAI,eAAe,QAAQ,gBAAgB;AAE3C,QAAAD,QAAO,UAAU,SAAU,OAAO;AACjC,cAAI,QAAQ,IAAI,aAAa,KAAoB,GAChD,MAAM,CAAC;AAER,iBAAO,SAAU,IAAI;AACpB,gBAAI,eAAe,WAAY;AAC9B,kBAAI,eAAe,OAClB,QACA,UACA,qBAAqB,UAAU,SAAS,GACxC,UAAU,MAAM,qBAAqB,CAAC,GACtC,aAAa,MACb;AAED,mBAAK,aAAa,WAAW,aAAa,YAAY,MAAM,aAAa,YAAY,qBAAqB,GAAG;AAC5G,sBAAM,IAAI,MAAM,kFAAkF;AAAA,cACnG;AAGA,mBAAK,IAAI,GAAG,IAAI,oBAAoB,KAAK;AACxC,wBAAQ,CAAC,IAAI;AAAA,kBACZ,WAAW;AAAA,kBACX,KAAK,UAAU,CAAC;AAAA,gBACjB;AAIA,oBAAI,aAAa,IAAI,UAAU,CAAC,CAAC,GAAG;AACnC,iCAAe,aAAa,IAAI,UAAU,CAAC,CAAC;AAC5C;AAAA,gBACD;AAEA,6BAAa;AAGb,yBAAS,IAAI,aAAa,KAAoB;AAC9C,6BAAa,IAAI,UAAU,CAAC,GAAG,MAAM;AACrC,+BAAe;AAAA,cAChB;AAGA,kBAAI,YAAY;AACf,oBAAI,aAAa,IAAI,UAAU,kBAAkB,CAAC,GAAG;AACpD,6BAAW,aAAa,IAAI,UAAU,kBAAkB,CAAC;AAAA,gBAC1D,OACK;AACJ,+BAAa;AAAA,gBACd;AAAA,cACD;AAEA,kBAAI,CAAC,YAAY;AAChB,2BAAW,GAAG,MAAM,MAAM,SAAS;AACnC,6BAAa,IAAI,UAAU,kBAAkB,GAAG,QAAQ;AAAA,cACzD;AAEA,kBAAI,QAAQ,GAAG;AACd,wBAAQ,kBAAkB,IAAI;AAAA,kBAC7B,WAAW;AAAA,kBACX,KAAK,UAAU,kBAAkB;AAAA,gBAClC;AAEA,oBAAI,YAAY;AACf,sCAAoB,KAAK,OAAO;AAAA,gBACjC,OACK;AACJ,sBAAI,KAAK,OAAO;AAAA,gBACjB;AAEA,oBAAI,IAAI,SAAS,OAAO;AACvB,qCAAmB,IAAI,MAAM,CAAC;AAAA,gBAC/B;AAAA,cACD;AAEA,2BAAa,cAAc;AAC3B,2BAAa,UAAU,qBAAqB;AAE5C,qBAAO;AAAA,YACR;AAEA,yBAAa,QAAQ;AACrB,yBAAa,cAAc;AAC3B,yBAAa,QAAQ;AACrB,yBAAa,MAAM;AAEnB,mBAAO;AAAA,UACR;AAAA,QACD;AAGA,iBAAS,oBAAoB,KAAK,SAAS;AAC1C,cAAI,SAAS,IAAI,QAChB,aAAa,QAAQ,QACrB,SACA,GAAG;AAEJ,eAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC5B,sBAAU;AACV,iBAAK,KAAK,GAAG,KAAK,YAAY,MAAM;AACnC,kBAAI,CAAC,QAAQ,IAAI,CAAC,EAAE,EAAE,EAAE,KAAK,QAAQ,EAAE,EAAE,GAAG,GAAG;AAC9C,0BAAU;AACV;AAAA,cACD;AAAA,YACD;AACA,gBAAI,SAAS;AACZ;AAAA,YACD;AAAA,UACD;AAEA,cAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC;AAAA,QAC7B;AAGA,iBAAS,mBAAmB,YAAY;AACvC,cAAI,gBAAgB,WAAW,QAC9B,aAAa,WAAW,gBAAgB,CAAC,GACzC,KACA;AAED,qBAAW,UAAU,OAAO,WAAW,GAAG;AAG1C,eAAK,IAAI,gBAAgB,GAAG,KAAK,GAAG,KAAK;AACxC,yBAAa,WAAW,CAAC;AACzB,kBAAM,WAAW,UAAU,IAAI,WAAW,GAAG;AAE7C,gBAAI,CAAC,OAAO,CAAC,IAAI,MAAM;AACtB,yBAAW,UAAU,OAAO,WAAW,GAAG;AAAA,YAC3C,OAAO;AACN;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAGA,iBAAS,QAAQ,MAAM,MAAM;AAC5B,iBAAO,SAAS,QAAS,SAAS,QAAQ,SAAS;AAAA,QACpD;AAAA,MACA,GAAE,EAAC,kBAAiB,EAAC,CAAC,EAAC,GAAE,CAAC,GAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AAAA,IAClC,CAAC;AAAA;AAAA;", "names": ["define", "module", "exports", "o", "e", "n"]}