import {
  entry_preview_docs_exports
} from "./chunk-OFJ6Z7MC.js";
import "./chunk-RR4G4Z3C.js";
import "./chunk-7N4LFK3R.js";
import {
  entry_preview_exports,
  renderToCanvas
} from "./chunk-ZDST3T7J.js";
import "./chunk-OJW7UZ5W.js";
import {
  require_react
} from "./chunk-JXCADB3Y.js";
import "./chunk-DMXPIZV6.js";
import {
  L
} from "./chunk-MX2IDHH2.js";
import "./chunk-QZPUQOOK.js";
import "./chunk-36BYAJZM.js";
import {
  require_global
} from "./chunk-BUYOXZCG.js";
import {
  require_preview_api
} from "./chunk-VTITOFDL.js";
import {
  __toESM
} from "./chunk-2LSFTFF7.js";

// node_modules/@storybook/react/dist/chunk-ZGTCCPPZ.mjs
function __definePreview(preview) {
  return L({ ...preview, addons: [entry_preview_exports, entry_preview_docs_exports, ...preview.addons ?? []] });
}

// node_modules/@storybook/react/dist/index.mjs
var import_global = __toESM(require_global(), 1);
var React = __toESM(require_react(), 1);
var import_preview_api = __toESM(require_preview_api(), 1);
var { window: globalWindow } = import_global.global;
globalWindow && (globalWindow.STORYBOOK_ENV = "react");
function setProjectAnnotations(projectAnnotations) {
  return (0, import_preview_api.setDefaultProjectAnnotations)(INTERNAL_DEFAULT_PROJECT_ANNOTATIONS), (0, import_preview_api.setProjectAnnotations)(projectAnnotations);
}
var INTERNAL_DEFAULT_PROJECT_ANNOTATIONS = { ...entry_preview_exports, renderToCanvas: async (renderContext, canvasElement) => {
  if (renderContext.storyContext.testingLibraryRender == null)
    return renderToCanvas(renderContext, canvasElement);
  let { storyContext: { context, unboundStoryFn: Story, testingLibraryRender: render } } = renderContext, { unmount } = render(React.createElement(Story, { ...context }), { container: context.canvasElement });
  return unmount;
} };
function composeStory(story, componentAnnotations, projectAnnotations, exportsName) {
  return (0, import_preview_api.composeStory)(story, componentAnnotations, projectAnnotations, globalThis.globalProjectAnnotations ?? INTERNAL_DEFAULT_PROJECT_ANNOTATIONS, exportsName);
}
function composeStories(csfExports, projectAnnotations) {
  return (0, import_preview_api.composeStories)(csfExports, projectAnnotations, composeStory);
}
var _a;
typeof module < "u" && ((_a = module == null ? void 0 : module.hot) == null ? void 0 : _a.decline());
export {
  INTERNAL_DEFAULT_PROJECT_ANNOTATIONS,
  __definePreview,
  composeStories,
  composeStory,
  setProjectAnnotations
};
//# sourceMappingURL=@storybook_react.js.map
