import {
  $r,
  Ai,
  <PERSON>i,
  <PERSON>t,
  <PERSON>,
  <PERSON>t,
  <PERSON>t,
  <PERSON>,
  <PERSON>i,
  <PERSON>,
  <PERSON>,
  <PERSON>t,
  <PERSON>t,
  Ti,
  <PERSON>,
  <PERSON>,
  X2,
  Zr,
  _i,
  animated,
  cn,
  diverging_default,
  j,
  k,
  li,
  ni,
  oi,
  pr,
  require_last,
  require_uniqBy,
  si,
  stack_default,
  to,
  useSpring,
  useTransition,
  w,
  z
} from "./chunk-E5QKIBDY.js";
import "./chunk-3ZRET7BV.js";
import "./chunk-Q7ZCQDVE.js";
import "./chunk-TTKHV656.js";
import {
  require_flatRest
} from "./chunk-AZWIPJ2R.js";
import "./chunk-XS2UDL7X.js";
import "./chunk-GQKKNNL7.js";
import "./chunk-D3GWGFQY.js";
import "./chunk-4BVSGRBS.js";
import {
  require_isPlainObject
} from "./chunk-576SI5Y5.js";
import "./chunk-QKAKBYX7.js";
import {
  require_baseClone
} from "./chunk-DQ6SG2RT.js";
import {
  require_getAllKeysIn
} from "./chunk-XNRSTCOD.js";
import {
  require_copyObject
} from "./chunk-3RUYYLMN.js";
import "./chunk-PBLA2M5Y.js";
import "./chunk-OGGZ2H5M.js";
import "./chunk-VANSMPSB.js";
import {
  require_baseEach
} from "./chunk-AFKAW5NK.js";
import "./chunk-L2MYVCJB.js";
import {
  require_baseIteratee
} from "./chunk-IMZCYE2V.js";
import {
  require_baseGet,
  require_castPath,
  require_toKey
} from "./chunk-EBHN46KS.js";
import "./chunk-A2RL5ZZB.js";
import "./chunk-5TUT4DVI.js";
import "./chunk-GZNJXC33.js";
import "./chunk-IYBXXNPR.js";
import "./chunk-AX2LFTCP.js";
import "./chunk-LBPJMYE3.js";
import {
  require_arrayFilter
} from "./chunk-CNJEIZNL.js";
import "./chunk-YWE747UV.js";
import "./chunk-HKPTUGQM.js";
import "./chunk-2HO6J6X4.js";
import "./chunk-WJPTHV5P.js";
import "./chunk-KHZOBGHN.js";
import "./chunk-KXJRCZQV.js";
import "./chunk-B5CG2ER2.js";
import "./chunk-37GKQVQ3.js";
import {
  require_baseSlice
} from "./chunk-JETE3AI2.js";
import {
  require_arrayMap
} from "./chunk-LD63QSJ3.js";
import "./chunk-Y3FQZQPT.js";
import {
  require_isArray
} from "./chunk-DH5RY6YN.js";
import {
  require_isObjectLike
} from "./chunk-URS5MDWH.js";
import {
  require_baseGetTag
} from "./chunk-LCZ7HEDH.js";
import {
  require_jsx_runtime
} from "./chunk-LHKICBWK.js";
import "./chunk-U62OR5NG.js";
import {
  require_react
} from "./chunk-JXCADB3Y.js";
import {
  __commonJS,
  __toESM
} from "./chunk-2LSFTFF7.js";

// node_modules/lodash/_baseFilter.js
var require_baseFilter = __commonJS({
  "node_modules/lodash/_baseFilter.js"(exports, module) {
    var baseEach = require_baseEach();
    function baseFilter(collection, predicate) {
      var result = [];
      baseEach(collection, function(value, index, collection2) {
        if (predicate(value, index, collection2)) {
          result.push(value);
        }
      });
      return result;
    }
    module.exports = baseFilter;
  }
});

// node_modules/lodash/filter.js
var require_filter = __commonJS({
  "node_modules/lodash/filter.js"(exports, module) {
    var arrayFilter = require_arrayFilter();
    var baseFilter = require_baseFilter();
    var baseIteratee = require_baseIteratee();
    var isArray = require_isArray();
    function filter(collection, predicate) {
      var func = isArray(collection) ? arrayFilter : baseFilter;
      return func(collection, baseIteratee(predicate, 3));
    }
    module.exports = filter;
  }
});

// node_modules/lodash/isNumber.js
var require_isNumber = __commonJS({
  "node_modules/lodash/isNumber.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isObjectLike = require_isObjectLike();
    var numberTag = "[object Number]";
    function isNumber(value) {
      return typeof value == "number" || isObjectLike(value) && baseGetTag(value) == numberTag;
    }
    module.exports = isNumber;
  }
});

// node_modules/lodash/_parent.js
var require_parent = __commonJS({
  "node_modules/lodash/_parent.js"(exports, module) {
    var baseGet = require_baseGet();
    var baseSlice = require_baseSlice();
    function parent(object, path) {
      return path.length < 2 ? object : baseGet(object, baseSlice(path, 0, -1));
    }
    module.exports = parent;
  }
});

// node_modules/lodash/_baseUnset.js
var require_baseUnset = __commonJS({
  "node_modules/lodash/_baseUnset.js"(exports, module) {
    var castPath = require_castPath();
    var last = require_last();
    var parent = require_parent();
    var toKey = require_toKey();
    function baseUnset(object, path) {
      path = castPath(path, object);
      object = parent(object, path);
      return object == null || delete object[toKey(last(path))];
    }
    module.exports = baseUnset;
  }
});

// node_modules/lodash/_customOmitClone.js
var require_customOmitClone = __commonJS({
  "node_modules/lodash/_customOmitClone.js"(exports, module) {
    var isPlainObject = require_isPlainObject();
    function customOmitClone(value) {
      return isPlainObject(value) ? void 0 : value;
    }
    module.exports = customOmitClone;
  }
});

// node_modules/lodash/omit.js
var require_omit = __commonJS({
  "node_modules/lodash/omit.js"(exports, module) {
    var arrayMap = require_arrayMap();
    var baseClone = require_baseClone();
    var baseUnset = require_baseUnset();
    var castPath = require_castPath();
    var copyObject = require_copyObject();
    var customOmitClone = require_customOmitClone();
    var flatRest = require_flatRest();
    var getAllKeysIn = require_getAllKeysIn();
    var CLONE_DEEP_FLAG = 1;
    var CLONE_FLAT_FLAG = 2;
    var CLONE_SYMBOLS_FLAG = 4;
    var omit = flatRest(function(object, paths) {
      var result = {};
      if (object == null) {
        return result;
      }
      var isDeep = false;
      paths = arrayMap(paths, function(path) {
        path = castPath(path, object);
        isDeep || (isDeep = path.length > 1);
        return path;
      });
      copyObject(object, getAllKeysIn(object), result);
      if (isDeep) {
        result = baseClone(result, CLONE_DEEP_FLAG | CLONE_FLAT_FLAG | CLONE_SYMBOLS_FLAG, customOmitClone);
      }
      var length = paths.length;
      while (length--) {
        baseUnset(result, paths[length]);
      }
      return result;
    });
    module.exports = omit;
  }
});

// node_modules/@nivo/annotations/dist/nivo-annotations.es.js
var import_react = __toESM(require_react());
var import_filter = __toESM(require_filter());
var import_isNumber = __toESM(require_isNumber());
var import_omit = __toESM(require_omit());
var import_jsx_runtime = __toESM(require_jsx_runtime());
function g() {
  return g = Object.assign ? Object.assign.bind() : function(t2) {
    for (var n2 = 1; n2 < arguments.length; n2++) {
      var i2 = arguments[n2];
      for (var o2 in i2)
        Object.prototype.hasOwnProperty.call(i2, o2) && (t2[o2] = i2[o2]);
    }
    return t2;
  }, g.apply(this, arguments);
}
var k2 = { dotSize: 4, noteWidth: 120, noteTextOffset: 8, animate: true };
var W = function(n2) {
  var i2 = typeof n2;
  return (0, import_react.isValidElement)(n2) || "string" === i2 || "function" === i2 || "object" === i2;
};
var v = function(t2) {
  var n2 = typeof t2;
  return "string" === n2 || "function" === n2;
};
var b = function(t2) {
  return "circle" === t2.type;
};
var w2 = function(t2) {
  return "dot" === t2.type;
};
var z2 = function(t2) {
  return "rect" === t2.type;
};
var P = function(t2) {
  var n2 = t2.data, i2 = t2.annotations, e3 = t2.getPosition, r = t2.getDimensions;
  return i2.reduce(function(t3, i3) {
    var s = i3.offset || 0;
    return [].concat(t3, (0, import_filter.default)(n2, i3.match).map(function(t4) {
      var n3 = e3(t4), o2 = r(t4);
      return (b(i3) || z2(i3)) && (o2.size = o2.size + 2 * s, o2.width = o2.width + 2 * s, o2.height = o2.height + 2 * s), g({}, (0, import_omit.default)(i3, ["match", "offset"]), n3, o2, { size: i3.size || o2.size, datum: t4 });
    }));
  }, []);
};
var C = function(t2, n2, i2, o2) {
  var e3 = Math.atan2(o2 - n2, i2 - t2);
  return si(oi(e3));
};
var O = function(t2) {
  var n2, i2, o2 = t2.x, a2 = t2.y, r = t2.noteX, s = t2.noteY, h = t2.noteWidth, d2 = void 0 === h ? k2.noteWidth : h, c2 = t2.noteTextOffset, f = void 0 === c2 ? k2.noteTextOffset : c2;
  if ((0, import_isNumber.default)(r))
    n2 = o2 + r;
  else {
    if (void 0 === r.abs)
      throw new Error("noteX should be either a number or an object containing an 'abs' property");
    n2 = r.abs;
  }
  if ((0, import_isNumber.default)(s))
    i2 = a2 + s;
  else {
    if (void 0 === s.abs)
      throw new Error("noteY should be either a number or an object containing an 'abs' property");
    i2 = s.abs;
  }
  var y = o2, x2 = a2, m2 = C(o2, a2, n2, i2);
  if (b(t2)) {
    var p2 = li(ni(m2), t2.size / 2);
    y += p2.x, x2 += p2.y;
  }
  if (z2(t2)) {
    var g2 = Math.round((m2 + 90) / 45) % 8;
    0 === g2 && (x2 -= t2.height / 2), 1 === g2 && (y += t2.width / 2, x2 -= t2.height / 2), 2 === g2 && (y += t2.width / 2), 3 === g2 && (y += t2.width / 2, x2 += t2.height / 2), 4 === g2 && (x2 += t2.height / 2), 5 === g2 && (y -= t2.width / 2, x2 += t2.height / 2), 6 === g2 && (y -= t2.width / 2), 7 === g2 && (y -= t2.width / 2, x2 -= t2.height / 2);
  }
  var W3 = n2, v2 = n2;
  return (m2 + 90) % 360 > 180 ? (W3 -= d2, v2 -= d2) : v2 += d2, { points: [[y, x2], [n2, i2], [v2, i2]], text: [W3, i2 - f], angle: m2 + 90 };
};
var S = function(t2) {
  var i2 = t2.data, o2 = t2.annotations, e3 = t2.getPosition, a2 = t2.getDimensions;
  return (0, import_react.useMemo)(function() {
    return P({ data: i2, annotations: o2, getPosition: e3, getDimensions: a2 });
  }, [i2, o2, e3, a2]);
};
var j2 = function(t2) {
  var i2 = t2.annotations;
  return (0, import_react.useMemo)(function() {
    return i2.map(function(t3) {
      return g({}, t3, { computed: O(g({}, t3)) });
    });
  }, [i2]);
};
var M = function(t2) {
  return (0, import_react.useMemo)(function() {
    return O(t2);
  }, [t2]);
};
var T = function(t2) {
  var n2 = t2.datum, o2 = t2.x, e3 = t2.y, r = t2.note, s = Et(), l = Zr(), u2 = l.animate, c2 = l.config, k3 = useSpring({ x: o2, y: e3, config: c2, immediate: !u2 });
  return "function" == typeof r ? (0, import_react.createElement)(r, { x: o2, y: e3, datum: n2 }) : (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [s.annotations.text.outlineWidth > 0 && (0, import_jsx_runtime.jsx)(animated.text, { x: k3.x, y: k3.y, style: g({}, s.annotations.text, { strokeLinejoin: "round", strokeWidth: 2 * s.annotations.text.outlineWidth, stroke: s.annotations.text.outlineColor }), children: r }), (0, import_jsx_runtime.jsx)(animated.text, { x: k3.x, y: k3.y, style: (0, import_omit.default)(s.annotations.text, ["outlineWidth", "outlineColor"]), children: r })] });
};
var E = function(t2) {
  var i2 = t2.points, o2 = t2.isOutline, e3 = void 0 !== o2 && o2, a2 = Et(), r = (0, import_react.useMemo)(function() {
    var t3 = i2[0];
    return i2.slice(1).reduce(function(t4, n2) {
      return t4 + " L" + n2[0] + "," + n2[1];
    }, "M" + t3[0] + "," + t3[1]);
  }, [i2]), s = $r(r);
  if (e3 && a2.annotations.link.outlineWidth <= 0)
    return null;
  var l = g({}, a2.annotations.link);
  return e3 && (l.strokeLinecap = "square", l.strokeWidth = a2.annotations.link.strokeWidth + 2 * a2.annotations.link.outlineWidth, l.stroke = a2.annotations.link.outlineColor, l.opacity = a2.annotations.link.outlineOpacity), (0, import_jsx_runtime.jsx)(animated.path, { fill: "none", d: s, style: l });
};
var I = function(t2) {
  var n2 = t2.x, i2 = t2.y, o2 = t2.size, e3 = Et(), a2 = Zr(), r = a2.animate, s = a2.config, l = useSpring({ x: n2, y: i2, radius: o2 / 2, config: s, immediate: !r });
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [e3.annotations.outline.outlineWidth > 0 && (0, import_jsx_runtime.jsx)(animated.circle, { cx: l.x, cy: l.y, r: l.radius, style: g({}, e3.annotations.outline, { fill: "none", strokeWidth: e3.annotations.outline.strokeWidth + 2 * e3.annotations.outline.outlineWidth, stroke: e3.annotations.outline.outlineColor, opacity: e3.annotations.outline.outlineOpacity }) }), (0, import_jsx_runtime.jsx)(animated.circle, { cx: l.x, cy: l.y, r: l.radius, style: e3.annotations.outline })] });
};
var D2 = function(t2) {
  var n2 = t2.x, i2 = t2.y, o2 = t2.size, e3 = void 0 === o2 ? k2.dotSize : o2, a2 = Et(), r = Zr(), s = r.animate, l = r.config, u2 = useSpring({ x: n2, y: i2, radius: e3 / 2, config: l, immediate: !s });
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [a2.annotations.outline.outlineWidth > 0 && (0, import_jsx_runtime.jsx)(animated.circle, { cx: u2.x, cy: u2.y, r: u2.radius, style: g({}, a2.annotations.outline, { fill: "none", strokeWidth: 2 * a2.annotations.outline.outlineWidth, stroke: a2.annotations.outline.outlineColor, opacity: a2.annotations.outline.outlineOpacity }) }), (0, import_jsx_runtime.jsx)(animated.circle, { cx: u2.x, cy: u2.y, r: u2.radius, style: a2.annotations.symbol })] });
};
var L = function(t2) {
  var n2 = t2.x, i2 = t2.y, o2 = t2.width, e3 = t2.height, a2 = t2.borderRadius, r = void 0 === a2 ? 6 : a2, s = Et(), l = Zr(), u2 = l.animate, c2 = l.config, k3 = useSpring({ x: n2 - o2 / 2, y: i2 - e3 / 2, width: o2, height: e3, config: c2, immediate: !u2 });
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [s.annotations.outline.outlineWidth > 0 && (0, import_jsx_runtime.jsx)(animated.rect, { x: k3.x, y: k3.y, rx: r, ry: r, width: k3.width, height: k3.height, style: g({}, s.annotations.outline, { fill: "none", strokeWidth: s.annotations.outline.strokeWidth + 2 * s.annotations.outline.outlineWidth, stroke: s.annotations.outline.outlineColor, opacity: s.annotations.outline.outlineOpacity }) }), (0, import_jsx_runtime.jsx)(animated.rect, { x: k3.x, y: k3.y, rx: r, ry: r, width: k3.width, height: k3.height, style: s.annotations.outline })] });
};
var R = function(t2) {
  var n2 = t2.datum, i2 = t2.x, o2 = t2.y, e3 = t2.note, a2 = M(t2);
  if (!W(e3))
    throw new Error("note should be a valid react element");
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [(0, import_jsx_runtime.jsx)(E, { points: a2.points, isOutline: true }), b(t2) && (0, import_jsx_runtime.jsx)(I, { x: i2, y: o2, size: t2.size }), w2(t2) && (0, import_jsx_runtime.jsx)(D2, { x: i2, y: o2, size: t2.size }), z2(t2) && (0, import_jsx_runtime.jsx)(L, { x: i2, y: o2, width: t2.width, height: t2.height, borderRadius: t2.borderRadius }), (0, import_jsx_runtime.jsx)(E, { points: a2.points }), (0, import_jsx_runtime.jsx)(T, { datum: n2, x: a2.text[0], y: a2.text[1], note: e3 })] });
};
var q = function(t2, n2) {
  n2.forEach(function(n3, i2) {
    var o2 = n3[0], e3 = n3[1];
    0 === i2 ? t2.moveTo(o2, e3) : t2.lineTo(o2, e3);
  });
};
var J = function(t2, n2) {
  var i2 = n2.annotations, o2 = n2.theme;
  0 !== i2.length && (t2.save(), i2.forEach(function(n3) {
    if (!v(n3.note))
      throw new Error("note is invalid for canvas implementation");
    o2.annotations.link.outlineWidth > 0 && (t2.lineCap = "square", t2.strokeStyle = o2.annotations.link.outlineColor, t2.lineWidth = o2.annotations.link.strokeWidth + 2 * o2.annotations.link.outlineWidth, t2.beginPath(), q(t2, n3.computed.points), t2.stroke(), t2.lineCap = "butt"), b(n3) && o2.annotations.outline.outlineWidth > 0 && (t2.strokeStyle = o2.annotations.outline.outlineColor, t2.lineWidth = o2.annotations.outline.strokeWidth + 2 * o2.annotations.outline.outlineWidth, t2.beginPath(), t2.arc(n3.x, n3.y, n3.size / 2, 0, 2 * Math.PI), t2.stroke()), w2(n3) && o2.annotations.symbol.outlineWidth > 0 && (t2.strokeStyle = o2.annotations.symbol.outlineColor, t2.lineWidth = 2 * o2.annotations.symbol.outlineWidth, t2.beginPath(), t2.arc(n3.x, n3.y, n3.size / 2, 0, 2 * Math.PI), t2.stroke()), z2(n3) && o2.annotations.outline.outlineWidth > 0 && (t2.strokeStyle = o2.annotations.outline.outlineColor, t2.lineWidth = o2.annotations.outline.strokeWidth + 2 * o2.annotations.outline.outlineWidth, t2.beginPath(), t2.rect(n3.x - n3.width / 2, n3.y - n3.height / 2, n3.width, n3.height), t2.stroke()), t2.strokeStyle = o2.annotations.link.stroke, t2.lineWidth = o2.annotations.link.strokeWidth, t2.beginPath(), q(t2, n3.computed.points), t2.stroke(), b(n3) && (t2.strokeStyle = o2.annotations.outline.stroke, t2.lineWidth = o2.annotations.outline.strokeWidth, t2.beginPath(), t2.arc(n3.x, n3.y, n3.size / 2, 0, 2 * Math.PI), t2.stroke()), w2(n3) && (t2.fillStyle = o2.annotations.symbol.fill, t2.beginPath(), t2.arc(n3.x, n3.y, n3.size / 2, 0, 2 * Math.PI), t2.fill()), z2(n3) && (t2.strokeStyle = o2.annotations.outline.stroke, t2.lineWidth = o2.annotations.outline.strokeWidth, t2.beginPath(), t2.rect(n3.x - n3.width / 2, n3.y - n3.height / 2, n3.width, n3.height), t2.stroke()), "function" == typeof n3.note ? n3.note(t2, { datum: n3.datum, x: n3.computed.text[0], y: n3.computed.text[1], theme: o2 }) : (t2.font = o2.annotations.text.fontSize + "px " + o2.annotations.text.fontFamily, t2.textAlign = "left", t2.textBaseline = "alphabetic", t2.fillStyle = o2.annotations.text.fill, t2.strokeStyle = o2.annotations.text.outlineColor, t2.lineWidth = 2 * o2.annotations.text.outlineWidth, o2.annotations.text.outlineWidth > 0 && (t2.lineJoin = "round", t2.strokeText(n3.note, n3.computed.text[0], n3.computed.text[1]), t2.lineJoin = "miter"), t2.fillText(n3.note, n3.computed.text[0], n3.computed.text[1]));
  }), t2.restore());
};

// node_modules/@nivo/bar/dist/nivo-bar.es.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var import_react2 = __toESM(require_react());
var import_uniqBy = __toESM(require_uniqBy());
function z3() {
  return z3 = Object.assign ? Object.assign.bind() : function(e3) {
    for (var a2 = 1; a2 < arguments.length; a2++) {
      var i2 = arguments[a2];
      for (var t2 in i2)
        Object.prototype.hasOwnProperty.call(i2, t2) && (e3[t2] = i2[t2]);
    }
    return e3;
  }, z3.apply(this, arguments);
}
function K(e3, a2) {
  if (null == e3)
    return {};
  var i2, t2, n2 = {}, r = Object.keys(e3);
  for (t2 = 0; t2 < r.length; t2++)
    i2 = r[t2], a2.indexOf(i2) >= 0 || (n2[i2] = e3[i2]);
  return n2;
}
var N;
var J2 = function(e3) {
  var a2 = e3.bars, i2 = e3.annotations, t2 = S({ data: a2, annotations: i2, getPosition: function(e4) {
    return { x: e4.x + e4.width / 2, y: e4.y + e4.height / 2 };
  }, getDimensions: function(e4) {
    var a3 = e4.height, i3 = e4.width;
    return { width: i3, height: a3, size: Math.max(i3, a3) };
  } });
  return (0, import_jsx_runtime2.jsx)(import_jsx_runtime2.Fragment, { children: t2.map(function(e4, a3) {
    return (0, import_jsx_runtime2.jsx)(R, z3({}, e4), a3);
  }) });
};
var Q = function(e3) {
  var a2 = e3.width, i2 = e3.height, t2 = e3.legends, n2 = e3.toggleSerie;
  return (0, import_jsx_runtime2.jsx)(import_jsx_runtime2.Fragment, { children: t2.map(function(e4, t3) {
    var r, l = e4[0], o2 = e4[1];
    return (0, import_jsx_runtime2.jsx)(X2, z3({}, l, { containerWidth: a2, containerHeight: i2, data: null != (r = l.data) ? r : o2, toggleSerie: l.toggleSerie && "keys" === l.dataFrom ? n2 : void 0 }), t3);
  }) });
};
var U = ["data"];
var Z = function(e3) {
  var a2, i2 = e3.bar, t2 = i2.data, n2 = K(i2, U), r = e3.style, l = r.borderColor, o2 = r.color, u2 = r.height, s = r.labelColor, h = r.labelOpacity, g2 = r.labelX, v2 = r.labelY, f = r.transform, m2 = r.width, p2 = e3.borderRadius, x2 = e3.borderWidth, y = e3.label, S2 = e3.shouldRenderLabel, k3 = e3.isInteractive, L2 = e3.onClick, w3 = e3.onMouseEnter, C2 = e3.onMouseLeave, B2 = e3.tooltip, I3 = e3.isFocusable, T3 = e3.ariaLabel, R3 = e3.ariaLabelledBy, H3 = e3.ariaDescribedBy, F = Et(), Y = k(), D3 = Y.showTooltipFromEvent, O2 = Y.showTooltipAt, G = Y.hideTooltip, A = (0, import_react2.useMemo)(function() {
    return function() {
      return (0, import_react2.createElement)(B2, z3({}, n2, t2));
    };
  }, [B2, n2, t2]), j3 = (0, import_react2.useCallback)(function(e4) {
    null == L2 || L2(z3({ color: n2.color }, t2), e4);
  }, [n2, t2, L2]), q3 = (0, import_react2.useCallback)(function(e4) {
    return D3(A(), e4);
  }, [D3, A]), N2 = (0, import_react2.useCallback)(function(e4) {
    null == w3 || w3(t2, e4), D3(A(), e4);
  }, [t2, w3, D3, A]), J3 = (0, import_react2.useCallback)(function(e4) {
    null == C2 || C2(t2, e4), G();
  }, [t2, G, C2]), Q2 = (0, import_react2.useCallback)(function() {
    O2(A(), [n2.absX + n2.width / 2, n2.absY]);
  }, [O2, A, n2]), Z2 = (0, import_react2.useCallback)(function() {
    G();
  }, [G]);
  return (0, import_jsx_runtime2.jsxs)(animated.g, { transform: f, children: [(0, import_jsx_runtime2.jsx)(animated.rect, { width: to(m2, function(e4) {
    return Math.max(e4, 0);
  }), height: to(u2, function(e4) {
    return Math.max(e4, 0);
  }), rx: p2, ry: p2, fill: null != (a2 = t2.fill) ? a2 : o2, strokeWidth: x2, stroke: l, focusable: I3, tabIndex: I3 ? 0 : void 0, "aria-label": T3 ? T3(t2) : void 0, "aria-labelledby": R3 ? R3(t2) : void 0, "aria-describedby": H3 ? H3(t2) : void 0, onMouseEnter: k3 ? N2 : void 0, onMouseMove: k3 ? q3 : void 0, onMouseLeave: k3 ? J3 : void 0, onClick: k3 ? j3 : void 0, onFocus: k3 && I3 ? Q2 : void 0, onBlur: k3 && I3 ? Z2 : void 0 }), S2 && (0, import_jsx_runtime2.jsx)(animated.text, { x: g2, y: v2, textAnchor: "middle", dominantBaseline: "central", fillOpacity: h, style: z3({}, F.labels.text, { pointerEvents: "none", fill: s }), children: y })] });
};
var $ = ["color", "label"];
var _ = function(e3) {
  var a2 = e3.color, i2 = e3.label, t2 = K(e3, $);
  return (0, import_jsx_runtime2.jsx)(w, { id: i2, value: t2.formattedValue, enableChip: true, color: a2 });
};
var ee = { indexBy: "id", keys: ["value"], groupMode: "stacked", layout: "vertical", reverse: false, minValue: "auto", maxValue: "auto", valueScale: { type: "linear" }, indexScale: { type: "band", round: true }, padding: 0.1, innerPadding: 0, axisBottom: {}, axisLeft: {}, enableGridX: false, enableGridY: true, enableLabel: true, label: "formattedValue", labelSkipWidth: 0, labelSkipHeight: 0, labelTextColor: { from: "theme", theme: "labels.text.fill" }, colorBy: "id", colors: { scheme: "nivo" }, borderRadius: 0, borderWidth: 0, borderColor: { from: "color" }, isInteractive: true, tooltip: _, tooltipLabel: function(e3) {
  return e3.id + " - " + e3.indexValue;
}, legends: [], initialHiddenIds: [], annotations: [], markers: [] };
var ae = z3({}, ee, { layers: ["grid", "axes", "bars", "markers", "legends", "annotations"], barComponent: Z, defs: [], fill: [], animate: true, motionConfig: "default", role: "img", isFocusable: false });
var ie = z3({}, ee, { layers: ["grid", "axes", "bars", "legends", "annotations"], pixelRatio: "undefined" != typeof window && null != (N = window.devicePixelRatio) ? N : 1 });
var te = function(e3, a2, i2, t2, n2, r) {
  return cn(t2, { all: e3.map(a2), min: 0, max: 0 }, n2, r).padding(i2);
};
var ne = function(e3, a2) {
  return e3.map(function(e4) {
    return z3({}, a2.reduce(function(e5, a3) {
      return e5[a3] = null, e5;
    }, {}), e4);
  });
};
var re = function(e3) {
  return Object.keys(e3).reduce(function(a2, i2) {
    return e3[i2] && (a2[i2] = e3[i2]), a2;
  }, {});
};
var le = function(e3) {
  return [e3, Number(e3)];
};
var oe = ["layout", "minValue", "maxValue", "reverse", "width", "height", "padding", "innerPadding", "valueScale", "indexScale", "hiddenIds"];
var de = function(e3, a2) {
  return e3 > a2;
};
var ue = function(e3, a2) {
  return e3 < a2;
};
var ce = function(e3, a2) {
  return Array.from(" ".repeat(a2 - e3), function(a3, i2) {
    return e3 + i2;
  });
};
var se = function(e3) {
  return de(e3, 0) ? 0 : e3;
};
var he = function(e3, a2, i2, t2) {
  var n2 = e3.data, r = e3.formatValue, l = e3.getColor, o2 = e3.getIndex, d2 = e3.getTooltipLabel, u2 = e3.innerPadding, c2 = void 0 === u2 ? 0 : u2, s = e3.keys, h = e3.xScale, b2 = e3.yScale, g2 = e3.margin, v2 = i2 ? ue : de, f = n2.map(re), m2 = [];
  return s.forEach(function(e4, i3) {
    return ce(0, h.domain().length).forEach(function(u3) {
      var s2, p2, x2, y = le(n2[u3][e4]), S2 = y[0], k3 = y[1], L2 = o2(n2[u3]), w3 = (null != (s2 = h(L2)) ? s2 : 0) + a2 * i3 + c2 * i3, C2 = v2(p2 = k3, 0) ? null != (x2 = b2(p2)) ? x2 : 0 : t2, V2 = function(e5, a3) {
        var i4;
        return v2(e5, 0) ? t2 - a3 : (null != (i4 = b2(e5)) ? i4 : 0) - t2;
      }(k3, C2), W3 = { id: e4, value: null === S2 ? S2 : k3, formattedValue: r(k3), hidden: false, index: u3, indexValue: L2, data: f[u3] };
      m2.push({ key: e4 + "." + W3.indexValue, index: m2.length, data: W3, x: w3, y: C2, absX: g2.left + w3, absY: g2.top + C2, width: a2, height: V2, color: l(W3), label: d2(W3) });
    });
  }), m2;
};
var be = function(e3, a2, i2, t2) {
  var n2 = e3.data, r = e3.formatValue, l = e3.getIndex, o2 = e3.getColor, d2 = e3.getTooltipLabel, u2 = e3.keys, c2 = e3.innerPadding, s = void 0 === c2 ? 0 : c2, h = e3.xScale, b2 = e3.yScale, g2 = e3.margin, v2 = i2 ? ue : de, f = n2.map(re), m2 = [];
  return u2.forEach(function(e4, i3) {
    return ce(0, b2.domain().length).forEach(function(u3) {
      var c3, p2, x2, y = le(n2[u3][e4]), S2 = y[0], k3 = y[1], L2 = l(n2[u3]), w3 = v2(p2 = k3, 0) ? t2 : null != (x2 = h(p2)) ? x2 : 0, C2 = (null != (c3 = b2(L2)) ? c3 : 0) + a2 * i3 + s * i3, V2 = function(e5, a3) {
        var i4;
        return v2(e5, 0) ? (null != (i4 = h(e5)) ? i4 : 0) - t2 : t2 - a3;
      }(k3, w3), W3 = { id: e4, value: null === S2 ? S2 : k3, formattedValue: r(k3), hidden: false, index: u3, indexValue: L2, data: f[u3] };
      m2.push({ key: e4 + "." + W3.indexValue, index: m2.length, data: W3, x: w3, y: C2, absX: g2.left + w3, absY: g2.top + C2, width: V2, height: a2, color: o2(W3), label: d2(W3) });
    });
  }), m2;
};
var ge = function(e3) {
  var a2, i2, t2 = e3.layout, n2 = e3.minValue, r = e3.maxValue, l = e3.reverse, o2 = e3.width, d2 = e3.height, u2 = e3.padding, c2 = void 0 === u2 ? 0 : u2, s = e3.innerPadding, h = void 0 === s ? 0 : s, b2 = e3.valueScale, g2 = e3.indexScale, v2 = e3.hiddenIds, f = void 0 === v2 ? [] : v2, m2 = K(e3, oe), p2 = m2.keys.filter(function(e4) {
    return !f.includes(e4);
  }), x2 = ne(m2.data, p2), y = "vertical" === t2 ? ["y", "x", o2] : ["x", "y", d2], S2 = y[0], k3 = y[1], L2 = y[2], w3 = te(x2, m2.getIndex, c2, g2, L2, k3), C2 = z3({ max: r, min: n2, reverse: l }, b2), V2 = "auto" === C2.min ? se : function(e4) {
    return e4;
  }, W3 = x2.reduce(function(e4, a3) {
    return [].concat(e4, p2.map(function(e5) {
      return a3[e5];
    }));
  }, []).filter(Boolean), M3 = V2(Math.min.apply(Math, W3)), B2 = (i2 = Math.max.apply(Math, W3), isFinite(i2) ? i2 : 0), I3 = cn(C2, { all: W3, min: M3, max: B2 }, "x" === S2 ? o2 : d2, S2), T3 = "vertical" === t2 ? [w3, I3] : [I3, w3], R3 = T3[0], H3 = T3[1], P2 = (w3.bandwidth() - h * (p2.length - 1)) / p2.length, E2 = [z3({}, m2, { data: x2, keys: p2, innerPadding: h, xScale: R3, yScale: H3 }), P2, C2.reverse, null != (a2 = I3(0)) ? a2 : 0];
  return { xScale: R3, yScale: H3, bars: P2 > 0 ? "vertical" === t2 ? he.apply(void 0, E2) : be.apply(void 0, E2) : [] };
};
var ve = ["data", "layout", "minValue", "maxValue", "reverse", "width", "height", "padding", "valueScale", "indexScale", "hiddenIds"];
var fe = function e2(a2) {
  var i2;
  return a2.some(Array.isArray) ? e2((i2 = []).concat.apply(i2, a2)) : a2;
};
var me = function(e3, a2, i2) {
  var t2 = e3.formatValue, n2 = e3.getColor, r = e3.getIndex, l = e3.getTooltipLabel, o2 = e3.innerPadding, d2 = e3.stackedData, u2 = e3.xScale, c2 = e3.yScale, s = e3.margin, h = [];
  return d2.forEach(function(e4) {
    return u2.domain().forEach(function(d3, b2) {
      var g2, v2, f = e4[b2], m2 = null != (g2 = u2(r(f.data))) ? g2 : 0, p2 = (null != (v2 = function(e5) {
        return c2(e5[i2 ? 0 : 1]);
      }(f)) ? v2 : 0) + 0.5 * o2, x2 = function(e5, a3) {
        var t3;
        return (null != (t3 = c2(e5[i2 ? 1 : 0])) ? t3 : 0) - a3;
      }(f, p2) - o2, y = le(f.data[e4.key]), S2 = y[0], k3 = y[1], L2 = { id: e4.key, value: null === S2 ? S2 : k3, formattedValue: t2(k3), hidden: false, index: b2, indexValue: d3, data: re(f.data) };
      h.push({ key: e4.key + "." + d3, index: h.length, data: L2, x: m2, y: p2, absX: s.left + m2, absY: s.top + p2, width: a2, height: x2, color: n2(L2), label: l(L2) });
    });
  }), h;
};
var pe = function(e3, a2, i2) {
  var t2 = e3.formatValue, n2 = e3.getColor, r = e3.getIndex, l = e3.getTooltipLabel, o2 = e3.innerPadding, d2 = e3.stackedData, u2 = e3.xScale, c2 = e3.yScale, s = e3.margin, h = [];
  return d2.forEach(function(e4) {
    return c2.domain().forEach(function(d3, b2) {
      var g2, v2, f = e4[b2], m2 = null != (g2 = c2(r(f.data))) ? g2 : 0, p2 = (null != (v2 = function(e5) {
        return u2(e5[i2 ? 1 : 0]);
      }(f)) ? v2 : 0) + 0.5 * o2, x2 = function(e5, a3) {
        var t3;
        return (null != (t3 = u2(e5[i2 ? 0 : 1])) ? t3 : 0) - a3;
      }(f, p2) - o2, y = le(f.data[e4.key]), S2 = y[0], k3 = y[1], L2 = { id: e4.key, value: null === S2 ? S2 : k3, formattedValue: t2(k3), hidden: false, index: b2, indexValue: d3, data: re(f.data) };
      h.push({ key: e4.key + "." + d3, index: h.length, data: L2, x: p2, y: m2, absX: s.left + p2, absY: s.top + m2, width: x2, height: a2, color: n2(L2), label: l(L2) });
    });
  }), h;
};
var xe = function(e3) {
  var a2, i2 = e3.data, t2 = e3.layout, n2 = e3.minValue, r = e3.maxValue, l = e3.reverse, o2 = e3.width, d2 = e3.height, u2 = e3.padding, c2 = void 0 === u2 ? 0 : u2, s = e3.valueScale, h = e3.indexScale, b2 = e3.hiddenIds, g2 = void 0 === b2 ? [] : b2, v2 = K(e3, ve), f = v2.keys.filter(function(e4) {
    return !g2.includes(e4);
  }), m2 = stack_default().keys(f).offset(diverging_default)(ne(i2, f)), p2 = "vertical" === t2 ? ["y", "x", o2] : ["x", "y", d2], x2 = p2[0], y = p2[1], S2 = p2[2], k3 = te(i2, v2.getIndex, c2, h, S2, y), L2 = z3({ max: r, min: n2, reverse: l }, s), w3 = (a2 = fe(m2), "log" === s.type ? a2.filter(function(e4) {
    return 0 !== e4;
  }) : a2), C2 = Math.min.apply(Math, w3), V2 = Math.max.apply(Math, w3), W3 = cn(L2, { all: w3, min: C2, max: V2 }, "x" === x2 ? o2 : d2, x2), M3 = "vertical" === t2 ? [k3, W3] : [W3, k3], B2 = M3[0], I3 = M3[1], T3 = v2.innerPadding > 0 ? v2.innerPadding : 0, R3 = k3.bandwidth(), H3 = [z3({}, v2, { innerPadding: T3, stackedData: m2, xScale: B2, yScale: I3 }), R3, L2.reverse];
  return { xScale: B2, yScale: I3, bars: R3 > 0 ? "vertical" === t2 ? me.apply(void 0, H3) : pe.apply(void 0, H3) : [] };
};
var ye = function(e3) {
  var a2 = e3.bars, i2 = e3.direction, t2 = e3.from, n2 = e3.groupMode, r = e3.layout, l = e3.legendLabel, o2 = e3.reverse, d2 = Bi(null != l ? l : "indexes" === t2 ? "indexValue" : "id");
  return "indexes" === t2 ? function(e4, a3, i3) {
    var t3 = (0, import_uniqBy.default)(e4.map(function(e5) {
      var a4, t4;
      return { id: null != (a4 = e5.data.indexValue) ? a4 : "", label: i3(e5.data), hidden: e5.data.hidden, color: null != (t4 = e5.color) ? t4 : "#000" };
    }), function(e5) {
      return e5.id;
    });
    return "horizontal" === a3 && t3.reverse(), t3;
  }(a2, r, d2) : function(e4, a3, i3, t3, n3, r2) {
    var l2 = (0, import_uniqBy.default)(e4.map(function(e5) {
      var a4;
      return { id: e5.data.id, label: r2(e5.data), hidden: e5.data.hidden, color: null != (a4 = e5.color) ? a4 : "#000" };
    }), function(e5) {
      return e5.id;
    });
    return ("vertical" === a3 && "stacked" === t3 && "column" === i3 && true !== n3 || "horizontal" === a3 && "stacked" === t3 && true === n3) && l2.reverse(), l2;
  }(a2, r, i2, n2, o2, d2);
};
var Se = function(e3) {
  var a2 = e3.indexBy, i2 = void 0 === a2 ? ee.indexBy : a2, t2 = e3.keys, n2 = void 0 === t2 ? ee.keys : t2, r = e3.label, l = void 0 === r ? ee.label : r, o2 = e3.tooltipLabel, d2 = void 0 === o2 ? ee.tooltipLabel : o2, u2 = e3.valueFormat, c2 = e3.colors, s = void 0 === c2 ? ee.colors : c2, h = e3.colorBy, g2 = void 0 === h ? ee.colorBy : h, m2 = e3.borderColor, p2 = void 0 === m2 ? ee.borderColor : m2, x2 = e3.labelTextColor, y = void 0 === x2 ? ee.labelTextColor : x2, S2 = e3.groupMode, k3 = void 0 === S2 ? ee.groupMode : S2, L2 = e3.layout, w3 = void 0 === L2 ? ee.layout : L2, C2 = e3.reverse, W3 = void 0 === C2 ? ee.reverse : C2, I3 = e3.data, T3 = e3.minValue, R3 = void 0 === T3 ? ee.minValue : T3, H3 = e3.maxValue, P2 = void 0 === H3 ? ee.maxValue : H3, E2 = e3.margin, F = e3.width, X3 = e3.height, Y = e3.padding, G = void 0 === Y ? ee.padding : Y, A = e3.innerPadding, j3 = void 0 === A ? ee.innerPadding : A, q3 = e3.valueScale, K2 = void 0 === q3 ? ee.valueScale : q3, N2 = e3.indexScale, J3 = void 0 === N2 ? ee.indexScale : N2, Q2 = e3.initialHiddenIds, U2 = void 0 === Q2 ? ee.initialHiddenIds : Q2, Z2 = e3.enableLabel, $2 = void 0 === Z2 ? ee.enableLabel : Z2, _2 = e3.labelSkipWidth, ae2 = void 0 === _2 ? ee.labelSkipWidth : _2, ie2 = e3.labelSkipHeight, te2 = void 0 === ie2 ? ee.labelSkipHeight : ie2, ne2 = e3.legends, re2 = void 0 === ne2 ? ee.legends : ne2, le2 = e3.legendLabel, oe2 = (0, import_react2.useState)(null != U2 ? U2 : []), de2 = oe2[0], ue2 = oe2[1], ce2 = (0, import_react2.useCallback)(function(e4) {
    ue2(function(a3) {
      return a3.indexOf(e4) > -1 ? a3.filter(function(a4) {
        return a4 !== e4;
      }) : [].concat(a3, [e4]);
    });
  }, []), se2 = Gi(i2), he2 = Gi(l), be2 = Gi(d2), ve2 = Dt(u2), fe2 = Et(), me2 = pr(s, g2), pe2 = We(p2, fe2), Se2 = We(y, fe2), ke2 = ("grouped" === k3 ? ge : xe)({ layout: w3, reverse: W3, data: I3, getIndex: se2, keys: n2, minValue: R3, maxValue: P2, width: F, height: X3, getColor: me2, padding: G, innerPadding: j3, valueScale: K2, indexScale: J3, hiddenIds: de2, formatValue: ve2, getTooltipLabel: be2, margin: E2 }), Le2 = ke2.bars, we2 = ke2.xScale, Ce2 = ke2.yScale, Ve2 = (0, import_react2.useMemo)(function() {
    return Le2.filter(function(e4) {
      return null !== e4.data.value;
    }).map(function(e4, a3) {
      return z3({}, e4, { index: a3 });
    });
  }, [Le2]), We3 = (0, import_react2.useCallback)(function(e4) {
    var a3 = e4.width, i3 = e4.height;
    return !!$2 && (!(ae2 > 0 && a3 < ae2) && !(te2 > 0 && i3 < te2));
  }, [$2, ae2, te2]), Me2 = (0, import_react2.useMemo)(function() {
    return n2.map(function(e4) {
      var a3 = Le2.find(function(a4) {
        return a4.data.id === e4;
      });
      return z3({}, a3, { data: z3({ id: e4 }, null == a3 ? void 0 : a3.data, { hidden: de2.includes(e4) }) });
    });
  }, [de2, n2, Le2]), Be2 = (0, import_react2.useMemo)(function() {
    return re2.map(function(e4) {
      return [e4, ye({ bars: "keys" === e4.dataFrom ? Me2 : Le2, direction: e4.direction, from: e4.dataFrom, groupMode: k3, layout: w3, legendLabel: le2, reverse: W3 })];
    });
  }, [re2, Me2, Le2, k3, w3, le2, W3]);
  return { bars: Le2, barsWithValue: Ve2, xScale: we2, yScale: Ce2, getIndex: se2, getLabel: he2, getTooltipLabel: be2, formatValue: ve2, getColor: me2, getBorderColor: pe2, getLabelColor: Se2, shouldRenderBarLabel: We3, hiddenIds: de2, toggleSerie: ce2, legendsWithData: Be2 };
};
var ke = ["isInteractive", "animate", "motionConfig", "theme", "renderWrapper"];
var Le = function(i2) {
  var t2 = i2.data, n2 = i2.indexBy, r = i2.keys, l = i2.margin, o2 = i2.width, u2 = i2.height, c2 = i2.groupMode, s = i2.layout, h = i2.reverse, b2 = i2.minValue, g2 = i2.maxValue, v2 = i2.valueScale, f = i2.indexScale, m2 = i2.padding, L2 = i2.innerPadding, w3 = i2.axisTop, C2 = i2.axisRight, M3 = i2.axisBottom, B2 = void 0 === M3 ? ae.axisBottom : M3, T3 = i2.axisLeft, R3 = void 0 === T3 ? ae.axisLeft : T3, H3 = i2.enableGridX, P2 = void 0 === H3 ? ae.enableGridX : H3, E2 = i2.enableGridY, X3 = void 0 === E2 ? ae.enableGridY : E2, Y = i2.gridXValues, D3 = i2.gridYValues, O2 = i2.layers, G = void 0 === O2 ? ae.layers : O2, A = i2.barComponent, j3 = void 0 === A ? ae.barComponent : A, q3 = i2.enableLabel, K2 = void 0 === q3 ? ae.enableLabel : q3, N2 = i2.label, U2 = i2.labelSkipWidth, Z2 = void 0 === U2 ? ae.labelSkipWidth : U2, $2 = i2.labelSkipHeight, _2 = void 0 === $2 ? ae.labelSkipHeight : $2, ee2 = i2.labelTextColor, ie2 = i2.markers, te2 = void 0 === ie2 ? ae.markers : ie2, ne2 = i2.colorBy, re2 = i2.colors, le2 = i2.defs, oe2 = void 0 === le2 ? ae.defs : le2, de2 = i2.fill, ue2 = void 0 === de2 ? ae.fill : de2, ce2 = i2.borderRadius, se2 = void 0 === ce2 ? ae.borderRadius : ce2, he2 = i2.borderWidth, be2 = void 0 === he2 ? ae.borderWidth : he2, ge2 = i2.borderColor, ve2 = i2.annotations, fe2 = void 0 === ve2 ? ae.annotations : ve2, me2 = i2.legendLabel, pe2 = i2.tooltipLabel, xe2 = i2.valueFormat, ye2 = i2.isInteractive, ke2 = void 0 === ye2 ? ae.isInteractive : ye2, Le2 = i2.tooltip, we2 = void 0 === Le2 ? ae.tooltip : Le2, Ce2 = i2.onClick, Ve2 = i2.onMouseEnter, We3 = i2.onMouseLeave, Me2 = i2.legends, Be2 = i2.role, Ie2 = void 0 === Be2 ? ae.role : Be2, Te = i2.ariaLabel, Re = i2.ariaLabelledBy, He = i2.ariaDescribedBy, Pe = i2.isFocusable, Ee = void 0 === Pe ? ae.isFocusable : Pe, Fe = i2.barAriaLabel, Xe = i2.barAriaLabelledBy, Ye = i2.barAriaDescribedBy, De = i2.initialHiddenIds, Oe = Zr(), Ge = Oe.animate, Ae = Oe.config, je = Bt(o2, u2, l), qe = je.outerWidth, ze = je.outerHeight, Ke = je.margin, Ne = je.innerWidth, Je = je.innerHeight, Qe = Se({ indexBy: n2, label: N2, tooltipLabel: pe2, valueFormat: xe2, colors: re2, colorBy: ne2, borderColor: ge2, labelTextColor: ee2, groupMode: c2, layout: s, reverse: h, data: t2, keys: r, minValue: b2, maxValue: g2, margin: Ke, width: Ne, height: Je, padding: m2, innerPadding: L2, valueScale: v2, indexScale: f, enableLabel: K2, labelSkipWidth: Z2, labelSkipHeight: _2, legends: Me2, legendLabel: me2, initialHiddenIds: De }), Ue = Qe.bars, Ze = Qe.barsWithValue, $e = Qe.xScale, _e = Qe.yScale, ea = Qe.getLabel, aa = Qe.getTooltipLabel, ia = Qe.getBorderColor, ta = Qe.getLabelColor, na = Qe.shouldRenderBarLabel, ra = Qe.toggleSerie, la = Qe.legendsWithData, oa = useTransition(Ze, { keys: function(e3) {
    return e3.key;
  }, from: function(e3) {
    return z3({ borderColor: ia(e3), color: e3.color, height: 0, labelColor: ta(e3), labelOpacity: 0, labelX: e3.width / 2, labelY: e3.height / 2, transform: "translate(" + e3.x + ", " + (e3.y + e3.height) + ")", width: e3.width }, "vertical" === s ? {} : { height: e3.height, transform: "translate(" + e3.x + ", " + e3.y + ")", width: 0 });
  }, enter: function(e3) {
    return { borderColor: ia(e3), color: e3.color, height: e3.height, labelColor: ta(e3), labelOpacity: 1, labelX: e3.width / 2, labelY: e3.height / 2, transform: "translate(" + e3.x + ", " + e3.y + ")", width: e3.width };
  }, update: function(e3) {
    return { borderColor: ia(e3), color: e3.color, height: e3.height, labelColor: ta(e3), labelOpacity: 1, labelX: e3.width / 2, labelY: e3.height / 2, transform: "translate(" + e3.x + ", " + e3.y + ")", width: e3.width };
  }, leave: function(e3) {
    return z3({ borderColor: ia(e3), color: e3.color, height: 0, labelColor: ta(e3), labelOpacity: 0, labelX: e3.width / 2, labelY: 0, transform: "translate(" + e3.x + ", " + (e3.y + e3.height) + ")", width: e3.width }, "vertical" === s ? {} : { labelX: 0, labelY: e3.height / 2, height: e3.height, transform: "translate(" + e3.x + ", " + e3.y + ")", width: 0 });
  }, config: Ae, immediate: !Ge }), da = (0, import_react2.useMemo)(function() {
    return { borderRadius: se2, borderWidth: be2, enableLabel: K2, isInteractive: ke2, labelSkipWidth: Z2, labelSkipHeight: _2, onClick: Ce2, onMouseEnter: Ve2, onMouseLeave: We3, getTooltipLabel: aa, tooltip: we2, isFocusable: Ee, ariaLabel: Fe, ariaLabelledBy: Xe, ariaDescribedBy: Ye };
  }, [se2, be2, K2, aa, ke2, _2, Z2, Ce2, Ve2, We3, we2, Ee, Fe, Xe, Ye]), ua = Hi(oe2, Ue, ue2, { dataKey: "data", targetKey: "data.fill" }), ca = { annotations: null, axes: null, bars: null, grid: null, legends: null, markers: null };
  G.includes("annotations") && (ca.annotations = (0, import_jsx_runtime2.jsx)(J2, { bars: Ue, annotations: fe2 }, "annotations")), G.includes("axes") && (ca.axes = (0, import_jsx_runtime2.jsx)(X, { xScale: $e, yScale: _e, width: Ne, height: Je, top: w3, right: C2, bottom: B2, left: R3 }, "axes")), G.includes("bars") && (ca.bars = (0, import_jsx_runtime2.jsx)(import_react2.Fragment, { children: oa(function(e3, a2) {
    return (0, import_react2.createElement)(j3, z3({}, da, { bar: a2, style: e3, shouldRenderLabel: na(a2), label: ea(a2.data) }));
  }) }, "bars")), G.includes("grid") && (ca.grid = (0, import_jsx_runtime2.jsx)(z, { width: Ne, height: Je, xScale: P2 ? $e : null, yScale: X3 ? _e : null, xValues: Y, yValues: D3 }, "grid")), G.includes("legends") && (ca.legends = (0, import_jsx_runtime2.jsx)(Q, { width: Ne, height: Je, legends: la, toggleSerie: ra }, "legends")), G.includes("markers") && (ca.markers = (0, import_jsx_runtime2.jsx)(Ti, { markers: te2, width: Ne, height: Je, xScale: $e, yScale: _e }, "markers"));
  var sa = (0, import_react2.useMemo)(function() {
    return z3({}, da, { margin: Ke, width: o2, height: u2, innerWidth: Ne, innerHeight: Je, bars: Ue, legendData: la, enableLabel: K2, xScale: $e, yScale: _e, tooltip: we2, getTooltipLabel: aa, onClick: Ce2, onMouseEnter: Ve2, onMouseLeave: We3 });
  }, [da, Ke, o2, u2, Ne, Je, Ue, la, K2, $e, _e, we2, aa, Ce2, Ve2, We3]);
  return (0, import_jsx_runtime2.jsx)(_i, { width: qe, height: ze, margin: Ke, defs: ua, role: Ie2, ariaLabel: Te, ariaLabelledBy: Re, ariaDescribedBy: He, isFocusable: Ee, children: G.map(function(e3, a2) {
    var i3;
    return "function" == typeof e3 ? (0, import_jsx_runtime2.jsx)(import_react2.Fragment, { children: (0, import_react2.createElement)(e3, sa) }, a2) : null != (i3 = null == ca ? void 0 : ca[e3]) ? i3 : null;
  }) });
};
var we = function(e3) {
  var a2 = e3.isInteractive, i2 = void 0 === a2 ? ae.isInteractive : a2, t2 = e3.animate, n2 = void 0 === t2 ? ae.animate : t2, r = e3.motionConfig, l = void 0 === r ? ae.motionConfig : r, o2 = e3.theme, u2 = e3.renderWrapper, c2 = K(e3, ke);
  return (0, import_jsx_runtime2.jsx)(Ht, { animate: n2, isInteractive: i2, motionConfig: l, renderWrapper: u2, theme: o2, children: (0, import_jsx_runtime2.jsx)(Le, z3({ isInteractive: i2 }, c2)) });
};
var Ce = ["isInteractive", "renderWrapper", "theme"];
var Ve = function(e3, a2, i2, t2) {
  return e3.find(function(e4) {
    return Ai(e4.x + a2.left, e4.y + a2.top, e4.width, e4.height, i2, t2);
  });
};
var We2 = function(e3) {
  var a2 = e3.data, r = e3.indexBy, u2 = e3.keys, c2 = e3.margin, s = e3.width, g2 = e3.height, v2 = e3.groupMode, f = e3.layout, m2 = e3.reverse, p2 = e3.minValue, y = e3.maxValue, S2 = e3.valueScale, k3 = e3.indexScale, w3 = e3.padding, C2 = e3.innerPadding, B2 = e3.axisTop, I3 = e3.axisRight, T3 = e3.axisBottom, P2 = void 0 === T3 ? ie.axisBottom : T3, E2 = e3.axisLeft, F = void 0 === E2 ? ie.axisLeft : E2, Y = e3.enableGridX, D3 = void 0 === Y ? ie.enableGridX : Y, O2 = e3.enableGridY, G = void 0 === O2 ? ie.enableGridY : O2, A = e3.gridXValues, j3 = e3.gridYValues, q3 = e3.layers, K2 = void 0 === q3 ? ie.layers : q3, N2 = e3.renderBar, J3 = void 0 === N2 ? function(e4, a3) {
    var i2 = a3.bar, t2 = i2.color, n2 = i2.height, r2 = i2.width, l = i2.x, o2 = i2.y, d2 = a3.borderColor, u3 = a3.borderRadius, c3 = a3.borderWidth, s2 = a3.label, h = a3.labelColor, b2 = a3.shouldRenderLabel;
    if (e4.fillStyle = t2, c3 > 0 && (e4.strokeStyle = d2, e4.lineWidth = c3), e4.beginPath(), u3 > 0) {
      var g3 = Math.min(u3, n2);
      e4.moveTo(l + g3, o2), e4.lineTo(l + r2 - g3, o2), e4.quadraticCurveTo(l + r2, o2, l + r2, o2 + g3), e4.lineTo(l + r2, o2 + n2 - g3), e4.quadraticCurveTo(l + r2, o2 + n2, l + r2 - g3, o2 + n2), e4.lineTo(l + g3, o2 + n2), e4.quadraticCurveTo(l, o2 + n2, l, o2 + n2 - g3), e4.lineTo(l, o2 + g3), e4.quadraticCurveTo(l, o2, l + g3, o2), e4.closePath();
    } else
      e4.rect(l, o2, r2, n2);
    e4.fill(), c3 > 0 && e4.stroke(), b2 && (e4.textBaseline = "middle", e4.textAlign = "center", e4.fillStyle = h, e4.fillText(s2, l + r2 / 2, o2 + n2 / 2));
  } : N2, Q2 = e3.enableLabel, U2 = void 0 === Q2 ? ie.enableLabel : Q2, Z2 = e3.label, $2 = e3.labelSkipWidth, _2 = void 0 === $2 ? ie.labelSkipWidth : $2, ee2 = e3.labelSkipHeight, ae2 = void 0 === ee2 ? ie.labelSkipHeight : ee2, te2 = e3.labelTextColor, ne2 = e3.colorBy, re2 = e3.colors, le2 = e3.borderRadius, oe2 = void 0 === le2 ? ie.borderRadius : le2, de2 = e3.borderWidth, ue2 = void 0 === de2 ? ie.borderWidth : de2, ce2 = e3.borderColor, se2 = e3.annotations, he2 = void 0 === se2 ? ie.annotations : se2, be2 = e3.legendLabel, ge2 = e3.tooltipLabel, ve2 = e3.valueFormat, fe2 = e3.isInteractive, me2 = void 0 === fe2 ? ie.isInteractive : fe2, pe2 = e3.tooltip, xe2 = void 0 === pe2 ? ie.tooltip : pe2, ye2 = e3.onClick, ke2 = e3.onMouseEnter, Le2 = e3.onMouseLeave, we2 = e3.legends, Ce2 = e3.pixelRatio, We3 = void 0 === Ce2 ? ie.pixelRatio : Ce2, Me2 = e3.canvasRef, Be2 = (0, import_react2.useRef)(null), Ie2 = Et(), Te = Bt(s, g2, c2), Re = Te.margin, He = Te.innerWidth, Pe = Te.innerHeight, Ee = Te.outerWidth, Fe = Te.outerHeight, Xe = Se({ indexBy: r, label: Z2, tooltipLabel: ge2, valueFormat: ve2, colors: re2, colorBy: ne2, borderColor: ce2, labelTextColor: te2, groupMode: v2, layout: f, reverse: m2, data: a2, keys: u2, minValue: p2, maxValue: y, margin: Re, width: He, height: Pe, padding: w3, innerPadding: C2, valueScale: S2, indexScale: k3, enableLabel: U2, labelSkipWidth: _2, labelSkipHeight: ae2, legends: we2, legendLabel: be2 }), Ye = Xe.bars, De = Xe.barsWithValue, Oe = Xe.xScale, Ge = Xe.yScale, Ae = Xe.getLabel, je = Xe.getTooltipLabel, qe = Xe.getBorderColor, ze = Xe.getLabelColor, Ke = Xe.shouldRenderBarLabel, Ne = Xe.legendsWithData, Je = k(), Qe = Je.showTooltipFromEvent, Ue = Je.hideTooltip, Ze = j2({ annotations: S({ data: Ye, annotations: he2, getPosition: function(e4) {
    return { x: e4.x, y: e4.y };
  }, getDimensions: function(e4) {
    var a3 = e4.width, i2 = e4.height;
    return { width: a3, height: i2, size: Math.max(a3, i2) };
  } }) }), $e = (0, import_react2.useMemo)(function() {
    return { borderRadius: oe2, borderWidth: ue2, isInteractive: me2, isFocusable: false, labelSkipWidth: _2, labelSkipHeight: ae2, margin: Re, width: s, height: g2, innerWidth: He, innerHeight: Pe, bars: Ye, legendData: Ne, enableLabel: U2, xScale: Oe, yScale: Ge, tooltip: xe2, getTooltipLabel: je, onClick: ye2, onMouseEnter: ke2, onMouseLeave: Le2 };
  }, [oe2, ue2, me2, _2, ae2, Re, s, g2, He, Pe, Ye, Ne, U2, Oe, Ge, xe2, je, ye2, ke2, Le2]);
  (0, import_react2.useEffect)(function() {
    var e4, a3 = null == (e4 = Be2.current) ? void 0 : e4.getContext("2d");
    Be2.current && a3 && (Be2.current.width = Ee * We3, Be2.current.height = Fe * We3, a3.scale(We3, We3), a3.fillStyle = Ie2.background, a3.fillRect(0, 0, Ee, Fe), a3.translate(Re.left, Re.top), K2.forEach(function(e5) {
      "grid" === e5 ? "number" == typeof Ie2.grid.line.strokeWidth && Ie2.grid.line.strokeWidth > 0 && (a3.lineWidth = Ie2.grid.line.strokeWidth, a3.strokeStyle = Ie2.grid.line.stroke, D3 && D(a3, { width: s, height: g2, scale: Oe, axis: "x", values: A }), G && D(a3, { width: s, height: g2, scale: Ge, axis: "y", values: j3 })) : "axes" === e5 ? j(a3, { xScale: Oe, yScale: Ge, width: He, height: Pe, top: B2, right: I3, bottom: P2, left: F, theme: Ie2 }) : "bars" === e5 ? De.forEach(function(e6) {
        J3(a3, { bar: e6, borderColor: qe(e6), borderRadius: oe2, borderWidth: ue2, label: Ae(e6.data), labelColor: ze(e6), shouldRenderLabel: Ke(e6) });
      }) : "legends" === e5 ? Ne.forEach(function(e6) {
        var i2 = e6[0], t2 = e6[1];
        H(a3, z3({}, i2, { data: t2, containerWidth: He, containerHeight: Pe, theme: Ie2 }));
      }) : "annotations" === e5 ? J(a3, { annotations: Ze, theme: Ie2 }) : "function" == typeof e5 && e5(a3, $e);
    }), a3.save());
  }, [P2, F, I3, B2, De, oe2, ue2, Ze, D3, G, qe, Ae, ze, A, j3, v2, g2, Pe, He, $e, K2, f, Ne, Re.left, Re.top, Fe, Ee, We3, J3, Oe, Ge, m2, Ke, Ie2, s]);
  var _e = (0, import_react2.useCallback)(function(e4) {
    if (Ye && Be2.current) {
      var a3 = Fi(Be2.current, e4), i2 = a3[0], t2 = a3[1], n2 = Ve(Ye, Re, i2, t2);
      void 0 !== n2 ? (Qe((0, import_react2.createElement)(xe2, z3({}, n2.data, { color: n2.color, label: n2.label, value: Number(n2.data.value) })), e4), "mouseenter" === e4.type && (null == ke2 || ke2(n2.data, e4))) : Ue();
    }
  }, [Ue, Re, ke2, Ye, Qe, xe2]), ea = (0, import_react2.useCallback)(function(e4) {
    if (Ye && Be2.current) {
      Ue();
      var a3 = Fi(Be2.current, e4), i2 = a3[0], t2 = a3[1], n2 = Ve(Ye, Re, i2, t2);
      n2 && (null == Le2 || Le2(n2.data, e4));
    }
  }, [Ue, Re, Le2, Ye]), aa = (0, import_react2.useCallback)(function(e4) {
    if (Ye && Be2.current) {
      var a3 = Fi(Be2.current, e4), i2 = a3[0], t2 = a3[1], n2 = Ve(Ye, Re, i2, t2);
      void 0 !== n2 && (null == ye2 || ye2(z3({}, n2.data, { color: n2.color }), e4));
    }
  }, [Re, ye2, Ye]);
  return (0, import_jsx_runtime2.jsx)("canvas", { ref: function(e4) {
    Be2.current = e4, Me2 && "current" in Me2 && (Me2.current = e4);
  }, width: Ee * We3, height: Fe * We3, style: { width: Ee, height: Fe, cursor: me2 ? "auto" : "normal" }, onMouseEnter: me2 ? _e : void 0, onMouseMove: me2 ? _e : void 0, onMouseLeave: me2 ? ea : void 0, onClick: me2 ? aa : void 0 });
};
var Me = (0, import_react2.forwardRef)(function(e3, a2) {
  var i2 = e3.isInteractive, t2 = e3.renderWrapper, n2 = e3.theme, r = K(e3, Ce);
  return (0, import_jsx_runtime2.jsx)(Ht, { isInteractive: i2, renderWrapper: t2, theme: n2, animate: false, children: (0, import_jsx_runtime2.jsx)(We2, z3({}, r, { canvasRef: a2 })) });
});
var Be = function(e3) {
  return (0, import_jsx_runtime2.jsx)(Jt, { children: function(a2) {
    var i2 = a2.width, t2 = a2.height;
    return (0, import_jsx_runtime2.jsx)(we, z3({ width: i2, height: t2 }, e3));
  } });
};
var Ie = (0, import_react2.forwardRef)(function(e3, a2) {
  return (0, import_jsx_runtime2.jsx)(Jt, { children: function(i2) {
    var t2 = i2.width, n2 = i2.height;
    return (0, import_jsx_runtime2.jsx)(Me, z3({ width: t2, height: n2 }, e3, { ref: a2 }));
  } });
});
export {
  we as Bar,
  Me as BarCanvas,
  Z as BarItem,
  _ as BarTooltip,
  Be as ResponsiveBar,
  Ie as ResponsiveBarCanvas,
  ie as canvasDefaultProps,
  ee as defaultProps,
  ae as svgDefaultProps
};
//# sourceMappingURL=@nivo_bar.js.map
