{"version": 3, "sources": ["../../../../../lodash/_baseFilter.js", "../../../../../lodash/filter.js", "../../../../../lodash/isNumber.js", "../../../../../lodash/_parent.js", "../../../../../lodash/_baseUnset.js", "../../../../../lodash/_customOmitClone.js", "../../../../../lodash/omit.js", "../../../../../@nivo/annotations/src/props.ts", "../../../../../@nivo/annotations/src/utils.ts", "../../../../../@nivo/annotations/src/compute.ts", "../../../../../@nivo/annotations/src/hooks.ts", "../../../../../@nivo/annotations/src/AnnotationNote.tsx", "../../../../../@nivo/annotations/src/AnnotationLink.tsx", "../../../../../@nivo/annotations/src/CircleAnnotationOutline.tsx", "../../../../../@nivo/annotations/src/DotAnnotationOutline.tsx", "../../../../../@nivo/annotations/src/RectAnnotationOutline.tsx", "../../../../../@nivo/annotations/src/Annotation.tsx", "../../../../../@nivo/annotations/src/canvas.ts", "../../../../../@nivo/bar/src/BarAnnotations.tsx", "../../../../../@nivo/bar/src/BarLegends.tsx", "../../../../../@nivo/bar/src/BarItem.tsx", "../../../../../@nivo/bar/src/BarTooltip.tsx", "../../../../../@nivo/bar/src/props.ts", "../../../../../@nivo/bar/src/compute/common.ts", "../../../../../@nivo/bar/src/compute/grouped.ts", "../../../../../@nivo/bar/src/compute/stacked.ts", "../../../../../@nivo/bar/src/compute/legends.ts", "../../../../../@nivo/bar/src/hooks.ts", "../../../../../@nivo/bar/src/Bar.tsx", "../../../../../@nivo/bar/src/BarCanvas.tsx", "../../../../../@nivo/bar/src/ResponsiveBar.tsx", "../../../../../@nivo/bar/src/ResponsiveBarCanvas.tsx"], "sourcesContent": ["var baseEach = require('./_baseEach');\n\n/**\n * The base implementation of `_.filter` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n */\nfunction baseFilter(collection, predicate) {\n  var result = [];\n  baseEach(collection, function(value, index, collection) {\n    if (predicate(value, index, collection)) {\n      result.push(value);\n    }\n  });\n  return result;\n}\n\nmodule.exports = baseFilter;\n", "var arrayFilter = require('./_arrayFilter'),\n    baseFilter = require('./_baseFilter'),\n    baseIteratee = require('./_baseIteratee'),\n    isArray = require('./isArray');\n\n/**\n * Iterates over elements of `collection`, returning an array of all elements\n * `predicate` returns truthy for. The predicate is invoked with three\n * arguments: (value, index|key, collection).\n *\n * **Note:** Unlike `_.remove`, this method returns a new array.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n * @see _.reject\n * @example\n *\n * var users = [\n *   { 'user': 'barney', 'age': 36, 'active': true },\n *   { 'user': 'fred',   'age': 40, 'active': false }\n * ];\n *\n * _.filter(users, function(o) { return !o.active; });\n * // => objects for ['fred']\n *\n * // The `_.matches` iteratee shorthand.\n * _.filter(users, { 'age': 36, 'active': true });\n * // => objects for ['barney']\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.filter(users, ['active', false]);\n * // => objects for ['fred']\n *\n * // The `_.property` iteratee shorthand.\n * _.filter(users, 'active');\n * // => objects for ['barney']\n *\n * // Combining several predicates using `_.overEvery` or `_.overSome`.\n * _.filter(users, _.overSome([{ 'age': 36 }, ['age', 40]]));\n * // => objects for ['fred', 'barney']\n */\nfunction filter(collection, predicate) {\n  var func = isArray(collection) ? arrayFilter : baseFilter;\n  return func(collection, baseIteratee(predicate, 3));\n}\n\nmodule.exports = filter;\n", "var baseGetTag = require('./_baseGetTag'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar numberTag = '[object Number]';\n\n/**\n * Checks if `value` is classified as a `Number` primitive or object.\n *\n * **Note:** To exclude `Infinity`, `-Infinity`, and `NaN`, which are\n * classified as numbers, use the `_.isFinite` method.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a number, else `false`.\n * @example\n *\n * _.isNumber(3);\n * // => true\n *\n * _.isNumber(Number.MIN_VALUE);\n * // => true\n *\n * _.isNumber(Infinity);\n * // => true\n *\n * _.isNumber('3');\n * // => false\n */\nfunction isNumber(value) {\n  return typeof value == 'number' ||\n    (isObjectLike(value) && baseGetTag(value) == numberTag);\n}\n\nmodule.exports = isNumber;\n", "var baseGet = require('./_baseGet'),\n    baseSlice = require('./_baseSlice');\n\n/**\n * Gets the parent value at `path` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} path The path to get the parent value of.\n * @returns {*} Returns the parent value.\n */\nfunction parent(object, path) {\n  return path.length < 2 ? object : baseGet(object, baseSlice(path, 0, -1));\n}\n\nmodule.exports = parent;\n", "var castPath = require('./_castPath'),\n    last = require('./last'),\n    parent = require('./_parent'),\n    toKey = require('./_toKey');\n\n/**\n * The base implementation of `_.unset`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The property path to unset.\n * @returns {boolean} Returns `true` if the property is deleted, else `false`.\n */\nfunction baseUnset(object, path) {\n  path = castPath(path, object);\n  object = parent(object, path);\n  return object == null || delete object[toKey(last(path))];\n}\n\nmodule.exports = baseUnset;\n", "var isPlainObject = require('./isPlainObject');\n\n/**\n * Used by `_.omit` to customize its `_.cloneDeep` use to only clone plain\n * objects.\n *\n * @private\n * @param {*} value The value to inspect.\n * @param {string} key The key of the property to inspect.\n * @returns {*} Returns the uncloned value or `undefined` to defer cloning to `_.cloneDeep`.\n */\nfunction customOmitClone(value) {\n  return isPlainObject(value) ? undefined : value;\n}\n\nmodule.exports = customOmitClone;\n", "var arrayMap = require('./_arrayMap'),\n    baseClone = require('./_baseClone'),\n    baseUnset = require('./_baseUnset'),\n    castPath = require('./_castPath'),\n    copyObject = require('./_copyObject'),\n    customOmitClone = require('./_customOmitClone'),\n    flatRest = require('./_flatRest'),\n    getAllKeysIn = require('./_getAllKeysIn');\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1,\n    CLONE_FLAT_FLAG = 2,\n    CLONE_SYMBOLS_FLAG = 4;\n\n/**\n * The opposite of `_.pick`; this method creates an object composed of the\n * own and inherited enumerable property paths of `object` that are not omitted.\n *\n * **Note:** This method is considerably slower than `_.pick`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [paths] The property paths to omit.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.omit(object, ['a', 'c']);\n * // => { 'b': '2' }\n */\nvar omit = flatRest(function(object, paths) {\n  var result = {};\n  if (object == null) {\n    return result;\n  }\n  var isDeep = false;\n  paths = arrayMap(paths, function(path) {\n    path = castPath(path, object);\n    isDeep || (isDeep = path.length > 1);\n    return path;\n  });\n  copyObject(object, getAllKeysIn(object), result);\n  if (isDeep) {\n    result = baseClone(result, CLONE_DEEP_FLAG | CLONE_FLAT_FLAG | CLONE_SYMBOLS_FLAG, customOmitClone);\n  }\n  var length = paths.length;\n  while (length--) {\n    baseUnset(result, paths[length]);\n  }\n  return result;\n});\n\nmodule.exports = omit;\n", "export const defaultProps = {\n    dotSize: 4,\n    noteWidth: 120,\n    noteTextOffset: 8,\n    animate: true,\n}\n", "import {\n    AnnotationSpec,\n    CircleAnnotationSpec,\n    DotAnnotationSpec,\n    Note,\n    NoteCanvas,\n    NoteSvg,\n    RectAnnotationSpec,\n} from './types'\nimport { isValidElement } from 'react'\n\nexport const isSvgNote = <Datum>(note: Note<Datum>): note is NoteSvg<Datum> => {\n    const noteType = typeof note\n\n    return (\n        isValidElement(note) ||\n        noteType === 'string' ||\n        noteType === 'function' ||\n        noteType === 'object'\n    )\n}\n\nexport const isCanvasNote = <Datum>(note: Note<Datum>): note is NoteCanvas<Datum> => {\n    const noteType = typeof note\n\n    return noteType === 'string' || noteType === 'function'\n}\n\nexport const isCircleAnnotation = <Datum>(\n    annotationSpec: AnnotationSpec<Datum>\n): annotationSpec is CircleAnnotationSpec<Datum> => annotationSpec.type === 'circle'\n\nexport const isDotAnnotation = <Datum>(\n    annotationSpec: AnnotationSpec<Datum>\n): annotationSpec is DotAnnotationSpec<Datum> => annotationSpec.type === 'dot'\n\nexport const isRectAnnotation = <Datum>(\n    annotationSpec: AnnotationSpec<Datum>\n): annotationSpec is RectAnnotationSpec<Datum> => annotationSpec.type === 'rect'\n", "import filter from 'lodash/filter'\nimport isNumber from 'lodash/isNumber'\nimport omit from 'lodash/omit'\nimport {\n    radiansToDegrees,\n    absoluteAngleDegrees,\n    degreesToRadians,\n    positionFromAngle,\n} from '@nivo/core'\nimport { defaultProps } from './props'\nimport {\n    AnnotationPositionGetter,\n    AnnotationDimensionsGetter,\n    BoundAnnotation,\n    AnnotationMatcher,\n    AnnotationInstructions,\n} from './types'\nimport { isCircleAnnotation, isRectAnnotation } from './utils'\n\nexport const bindAnnotations = <\n    Datum = {\n        x: number\n        y: number\n    }\n>({\n    data,\n    annotations,\n    getPosition,\n    getDimensions,\n}: {\n    data: Datum[]\n    annotations: AnnotationMatcher<Datum>[]\n    getPosition: AnnotationPositionGetter<Datum>\n    getDimensions: AnnotationDimensionsGetter<Datum>\n}): BoundAnnotation<Datum>[] =>\n    annotations.reduce((acc: BoundAnnotation<Datum>[], annotation) => {\n        const offset = annotation.offset || 0\n\n        return [\n            ...acc,\n            ...filter<Datum>(data, annotation.match).map(datum => {\n                const position = getPosition(datum)\n                const dimensions = getDimensions(datum)\n\n                if (isCircleAnnotation(annotation) || isRectAnnotation(annotation)) {\n                    dimensions.size = dimensions.size + offset * 2\n                    dimensions.width = dimensions.width + offset * 2\n                    dimensions.height = dimensions.height + offset * 2\n                }\n\n                // acc.push({\n                //     ...omit(annotation, ['match', 'offset']),\n                //     ...position,\n                //     ...dimensions,\n                //     size: annotation.size || dimensions.size,\n                //     datum,\n                // } as any)\n                // return [\n                //     ...acc,\n                //     {\n                //         ...omit(annotation, ['match', 'offset']),\n                //         ...position,\n                //         ...dimensions,\n                //         size: annotation.size || dimensions.size,\n                //         datum,\n                //     },\n                // ]\n                return {\n                    ...omit(annotation, ['match', 'offset']),\n                    ...position,\n                    ...dimensions,\n                    size: annotation.size || dimensions.size,\n                    datum,\n                } as Required<BoundAnnotation<Datum>>\n            }),\n        ]\n\n        // return acc\n    }, [])\n\nexport const getLinkAngle = (\n    sourceX: number,\n    sourceY: number,\n    targetX: number,\n    targetY: number\n) => {\n    const angle = Math.atan2(targetY - sourceY, targetX - sourceX)\n\n    return absoluteAngleDegrees(radiansToDegrees(angle))\n}\n\nexport const computeAnnotation = <Datum>(\n    annotation: BoundAnnotation<Datum>\n): AnnotationInstructions => {\n    const {\n        x,\n        y,\n        noteX,\n        noteY,\n        noteWidth = defaultProps.noteWidth,\n        noteTextOffset = defaultProps.noteTextOffset,\n    } = annotation\n\n    let computedNoteX: number\n    let computedNoteY: number\n\n    if (isNumber(noteX)) {\n        computedNoteX = x + noteX\n    } else if (noteX.abs !== undefined) {\n        computedNoteX = noteX.abs\n    } else {\n        throw new Error(`noteX should be either a number or an object containing an 'abs' property`)\n    }\n\n    if (isNumber(noteY)) {\n        computedNoteY = y + noteY\n    } else if (noteY.abs !== undefined) {\n        computedNoteY = noteY.abs\n    } else {\n        throw new Error(`noteY should be either a number or an object containing an 'abs' property`)\n    }\n\n    let computedX = x\n    let computedY = y\n\n    const angle = getLinkAngle(x, y, computedNoteX, computedNoteY)\n\n    if (isCircleAnnotation<Datum>(annotation)) {\n        const position = positionFromAngle(degreesToRadians(angle), annotation.size / 2)\n        computedX += position.x\n        computedY += position.y\n    }\n\n    if (isRectAnnotation<Datum>(annotation)) {\n        const eighth = Math.round((angle + 90) / 45) % 8\n        if (eighth === 0) {\n            computedY -= annotation.height / 2\n        }\n        if (eighth === 1) {\n            computedX += annotation.width / 2\n            computedY -= annotation.height / 2\n        }\n        if (eighth === 2) {\n            computedX += annotation.width / 2\n        }\n        if (eighth === 3) {\n            computedX += annotation.width / 2\n            computedY += annotation.height / 2\n        }\n        if (eighth === 4) {\n            computedY += annotation.height / 2\n        }\n        if (eighth === 5) {\n            computedX -= annotation.width / 2\n            computedY += annotation.height / 2\n        }\n        if (eighth === 6) {\n            computedX -= annotation.width / 2\n        }\n        if (eighth === 7) {\n            computedX -= annotation.width / 2\n            computedY -= annotation.height / 2\n        }\n    }\n\n    let textX = computedNoteX\n    const textY = computedNoteY - noteTextOffset\n\n    let noteLineX = computedNoteX\n    const noteLineY = computedNoteY\n\n    if ((angle + 90) % 360 > 180) {\n        textX -= noteWidth\n        noteLineX -= noteWidth\n    } else {\n        noteLineX += noteWidth\n    }\n\n    return {\n        points: [\n            [computedX, computedY],\n            [computedNoteX, computedNoteY],\n            [noteLineX, noteLineY],\n        ] as [number, number][],\n        text: [textX, textY],\n        angle: angle + 90,\n    }\n}\n", "import { useMemo } from 'react'\nimport { bindAnnotations, computeAnnotation } from './compute'\nimport {\n    AnnotationDimensionsGetter,\n    AnnotationMatcher,\n    AnnotationPositionGetter,\n    BoundAnnotation,\n} from './types'\n\n/**\n * Bind annotations to a dataset.\n */\nexport const useAnnotations = <Datum>({\n    data,\n    annotations,\n    getPosition,\n    getDimensions,\n}: {\n    data: Datum[]\n    annotations: AnnotationMatcher<Datum>[]\n    getPosition: AnnotationPositionGetter<Datum>\n    getDimensions: AnnotationDimensionsGetter<Datum>\n}) =>\n    useMemo(\n        () =>\n            bindAnnotations<Datum>({\n                data,\n                annotations,\n                getPosition,\n                getDimensions,\n            }),\n        [data, annotations, getPosition, getDimensions]\n    )\n\nexport const useComputedAnnotations = <Datum>({\n    annotations,\n}: {\n    annotations: BoundAnnotation<Datum>[]\n}) =>\n    useMemo(\n        () =>\n            annotations.map(annotation => ({\n                ...annotation,\n                computed: computeAnnotation<Datum>({\n                    ...annotation,\n                }),\n            })),\n        [annotations]\n    )\n\nexport const useComputedAnnotation = <Datum>(annotation: BoundAnnotation<Datum>) =>\n    useMemo(() => computeAnnotation<Datum>(annotation), [annotation])\n", "import { createElement } from 'react'\nimport omit from 'lodash/omit'\nimport { useSpring, animated } from '@react-spring/web'\nimport { useTheme, useMotionConfig } from '@nivo/core'\nimport { NoteSvg } from './types'\n\nexport const AnnotationNote = <Datum,>({\n    datum,\n    x,\n    y,\n    note,\n}: {\n    datum: Datum\n    x: number\n    y: number\n    note: NoteSvg<Datum>\n}) => {\n    const theme = useTheme()\n    const { animate, config: springConfig } = useMotionConfig()\n\n    const animatedProps = useSpring({\n        x,\n        y,\n        config: springConfig,\n        immediate: !animate,\n    })\n\n    if (typeof note === 'function') {\n        return createElement(note, { x, y, datum })\n    }\n\n    return (\n        <>\n            {theme.annotations.text.outlineWidth > 0 && (\n                <animated.text\n                    x={animatedProps.x}\n                    y={animatedProps.y}\n                    style={{\n                        ...theme.annotations.text,\n                        strokeLinejoin: 'round',\n                        strokeWidth: theme.annotations.text.outlineWidth * 2,\n                        stroke: theme.annotations.text.outlineColor,\n                    }}\n                >\n                    {note}\n                </animated.text>\n            )}\n            <animated.text\n                x={animatedProps.x}\n                y={animatedProps.y}\n                style={omit(theme.annotations.text, ['outlineWidth', 'outlineColor'])}\n            >\n                {note}\n            </animated.text>\n        </>\n    )\n}\n", "import { useMemo } from 'react'\nimport { animated } from '@react-spring/web'\nimport { useAnimatedPath, useTheme } from '@nivo/core'\n\nexport const AnnotationLink = ({\n    points,\n    isOutline = false,\n}: {\n    points: [number, number][]\n    isOutline?: boolean\n}) => {\n    const theme = useTheme()\n\n    const path = useMemo(() => {\n        const [firstPoint, ...otherPoints] = points\n\n        return otherPoints.reduce(\n            (acc, [x, y]) => `${acc} L${x},${y}`,\n            `M${firstPoint[0]},${firstPoint[1]}`\n        )\n    }, [points])\n\n    const animatedPath = useAnimatedPath(path)\n\n    if (isOutline && theme.annotations.link.outlineWidth <= 0) {\n        return null\n    }\n\n    const style = { ...theme.annotations.link }\n    if (isOutline) {\n        style.strokeLinecap = 'square'\n        style.strokeWidth =\n            theme.annotations.link.strokeWidth + theme.annotations.link.outlineWidth * 2\n        style.stroke = theme.annotations.link.outlineColor\n        style.opacity = theme.annotations.link.outlineOpacity\n    }\n\n    return <animated.path fill=\"none\" d={animatedPath} style={style} />\n}\n", "import { useSpring, animated } from '@react-spring/web'\nimport { useMotionConfig, useTheme } from '@nivo/core'\n\nexport const CircleAnnotationOutline = ({ x, y, size }: { x: number; y: number; size: number }) => {\n    const theme = useTheme()\n    const { animate, config: springConfig } = useMotionConfig()\n\n    const animatedProps = useSpring({\n        x,\n        y,\n        radius: size / 2,\n        config: springConfig,\n        immediate: !animate,\n    })\n\n    return (\n        <>\n            {theme.annotations.outline.outlineWidth > 0 && (\n                <animated.circle\n                    cx={animatedProps.x}\n                    cy={animatedProps.y}\n                    r={animatedProps.radius}\n                    style={{\n                        ...theme.annotations.outline,\n                        fill: 'none',\n                        strokeWidth:\n                            theme.annotations.outline.strokeWidth +\n                            theme.annotations.outline.outlineWidth * 2,\n                        stroke: theme.annotations.outline.outlineColor,\n                        opacity: theme.annotations.outline.outlineOpacity,\n                    }}\n                />\n            )}\n            <animated.circle\n                cx={animatedProps.x}\n                cy={animatedProps.y}\n                r={animatedProps.radius}\n                style={theme.annotations.outline}\n            />\n        </>\n    )\n}\n", "import { useSpring, animated } from '@react-spring/web'\nimport { useMotionConfig, useTheme } from '@nivo/core'\nimport { defaultProps } from './props'\n\nexport const DotAnnotationOutline = ({\n    x,\n    y,\n    size = defaultProps.dotSize,\n}: {\n    x: number\n    y: number\n    size?: number\n}) => {\n    const theme = useTheme()\n    const { animate, config: springConfig } = useMotionConfig()\n\n    const animatedProps = useSpring({\n        x,\n        y,\n        radius: size / 2,\n        config: springConfig,\n        immediate: !animate,\n    })\n\n    return (\n        <>\n            {theme.annotations.outline.outlineWidth > 0 && (\n                <animated.circle\n                    cx={animatedProps.x}\n                    cy={animatedProps.y}\n                    r={animatedProps.radius}\n                    style={{\n                        ...theme.annotations.outline,\n                        fill: 'none',\n                        strokeWidth: theme.annotations.outline.outlineWidth * 2,\n                        stroke: theme.annotations.outline.outlineColor,\n                        opacity: theme.annotations.outline.outlineOpacity,\n                    }}\n                />\n            )}\n            <animated.circle\n                cx={animatedProps.x}\n                cy={animatedProps.y}\n                r={animatedProps.radius}\n                style={theme.annotations.symbol}\n            />\n        </>\n    )\n}\n", "import { useSpring, animated } from '@react-spring/web'\nimport { useMotionConfig, useTheme } from '@nivo/core'\n\nexport const RectAnnotationOutline = ({\n    x,\n    y,\n    width,\n    height,\n    borderRadius = 6,\n}: {\n    x: number\n    y: number\n    width: number\n    height: number\n    borderRadius?: number\n}) => {\n    const theme = useTheme()\n    const { animate, config: springConfig } = useMotionConfig()\n\n    const animatedProps = useSpring({\n        x: x - width / 2,\n        y: y - height / 2,\n        width,\n        height,\n        config: springConfig,\n        immediate: !animate,\n    })\n\n    return (\n        <>\n            {theme.annotations.outline.outlineWidth > 0 && (\n                <animated.rect\n                    x={animatedProps.x}\n                    y={animatedProps.y}\n                    rx={borderRadius}\n                    ry={borderRadius}\n                    width={animatedProps.width}\n                    height={animatedProps.height}\n                    style={{\n                        ...theme.annotations.outline,\n                        fill: 'none',\n                        strokeWidth:\n                            theme.annotations.outline.strokeWidth +\n                            theme.annotations.outline.outlineWidth * 2,\n                        stroke: theme.annotations.outline.outlineColor,\n                        opacity: theme.annotations.outline.outlineOpacity,\n                    }}\n                />\n            )}\n            <animated.rect\n                x={animatedProps.x}\n                y={animatedProps.y}\n                rx={borderRadius}\n                ry={borderRadius}\n                width={animatedProps.width}\n                height={animatedProps.height}\n                style={theme.annotations.outline}\n            />\n        </>\n    )\n}\n", "import { useComputedAnnotation } from './hooks'\nimport { AnnotationNote } from './AnnotationNote'\nimport { AnnotationLink } from './AnnotationLink'\nimport { CircleAnnotationOutline } from './CircleAnnotationOutline'\nimport { DotAnnotationOutline } from './DotAnnotationOutline'\nimport { RectAnnotationOutline } from './RectAnnotationOutline'\nimport { BoundAnnotation } from './types'\nimport { isCircleAnnotation, isDotAnnotation, isRectAnnotation, isSvgNote } from './utils'\n\nexport const Annotation = <Datum,>(annotation: BoundAnnotation<Datum>) => {\n    const { datum, x, y, note } = annotation\n    const computed = useComputedAnnotation(annotation)\n\n    if (!isSvgNote(note)) {\n        throw new Error('note should be a valid react element')\n    }\n\n    return (\n        <>\n            <AnnotationLink points={computed.points} isOutline={true} />\n            {isCircleAnnotation(annotation) && (\n                <CircleAnnotationOutline x={x} y={y} size={annotation.size} />\n            )}\n            {isDotAnnotation(annotation) && (\n                <DotAnnotationOutline x={x} y={y} size={annotation.size} />\n            )}\n            {isRectAnnotation(annotation) && (\n                <RectAnnotationOutline\n                    x={x}\n                    y={y}\n                    width={annotation.width}\n                    height={annotation.height}\n                    borderRadius={annotation.borderRadius}\n                />\n            )}\n            <AnnotationLink points={computed.points} />\n            <AnnotationNote datum={datum} x={computed.text[0]} y={computed.text[1]} note={note} />\n        </>\n    )\n}\n", "import { CompleteTheme } from '@nivo/core'\nimport { ComputedAnnotation } from './types'\nimport { isCanvasNote, isCircleAnnotation, isDotAnnotation, isRectAnnotation } from './utils'\n\nconst drawPoints = (ctx: CanvasRenderingContext2D, points: [number, number][]) => {\n    points.forEach(([x, y], index) => {\n        if (index === 0) {\n            ctx.moveTo(x, y)\n        } else {\n            ctx.lineTo(x, y)\n        }\n    })\n}\n\nexport const renderAnnotationsToCanvas = <Datum>(\n    ctx: CanvasRenderingContext2D,\n    {\n        annotations,\n        theme,\n    }: {\n        annotations: ComputedAnnotation<Datum>[]\n        theme: CompleteTheme\n    }\n) => {\n    if (annotations.length === 0) return\n\n    ctx.save()\n    annotations.forEach(annotation => {\n        if (!isCanvasNote(annotation.note)) {\n            throw new Error('note is invalid for canvas implementation')\n        }\n\n        if (theme.annotations.link.outlineWidth > 0) {\n            ctx.lineCap = 'square'\n            ctx.strokeStyle = theme.annotations.link.outlineColor\n            ctx.lineWidth =\n                theme.annotations.link.strokeWidth + theme.annotations.link.outlineWidth * 2\n            ctx.beginPath()\n            drawPoints(ctx, annotation.computed.points)\n            ctx.stroke()\n            ctx.lineCap = 'butt'\n        }\n\n        if (isCircleAnnotation(annotation) && theme.annotations.outline.outlineWidth > 0) {\n            ctx.strokeStyle = theme.annotations.outline.outlineColor\n            ctx.lineWidth =\n                theme.annotations.outline.strokeWidth + theme.annotations.outline.outlineWidth * 2\n            ctx.beginPath()\n            ctx.arc(annotation.x, annotation.y, annotation.size / 2, 0, 2 * Math.PI)\n            ctx.stroke()\n        }\n\n        if (isDotAnnotation(annotation) && theme.annotations.symbol.outlineWidth > 0) {\n            ctx.strokeStyle = theme.annotations.symbol.outlineColor\n            ctx.lineWidth = theme.annotations.symbol.outlineWidth * 2\n            ctx.beginPath()\n            ctx.arc(annotation.x, annotation.y, annotation.size / 2, 0, 2 * Math.PI)\n            ctx.stroke()\n        }\n\n        if (isRectAnnotation(annotation) && theme.annotations.outline.outlineWidth > 0) {\n            ctx.strokeStyle = theme.annotations.outline.outlineColor\n            ctx.lineWidth =\n                theme.annotations.outline.strokeWidth + theme.annotations.outline.outlineWidth * 2\n            ctx.beginPath()\n            ctx.rect(\n                annotation.x - annotation.width / 2,\n                annotation.y - annotation.height / 2,\n                annotation.width,\n                annotation.height\n            )\n            ctx.stroke()\n        }\n\n        ctx.strokeStyle = theme.annotations.link.stroke\n        ctx.lineWidth = theme.annotations.link.strokeWidth\n        ctx.beginPath()\n        drawPoints(ctx, annotation.computed.points)\n        ctx.stroke()\n\n        if (isCircleAnnotation(annotation)) {\n            ctx.strokeStyle = theme.annotations.outline.stroke\n            ctx.lineWidth = theme.annotations.outline.strokeWidth\n            ctx.beginPath()\n            ctx.arc(annotation.x, annotation.y, annotation.size / 2, 0, 2 * Math.PI)\n            ctx.stroke()\n        }\n\n        if (isDotAnnotation(annotation)) {\n            ctx.fillStyle = theme.annotations.symbol.fill\n            ctx.beginPath()\n            ctx.arc(annotation.x, annotation.y, annotation.size / 2, 0, 2 * Math.PI)\n            ctx.fill()\n        }\n\n        if (isRectAnnotation(annotation)) {\n            ctx.strokeStyle = theme.annotations.outline.stroke\n            ctx.lineWidth = theme.annotations.outline.strokeWidth\n            ctx.beginPath()\n            ctx.rect(\n                annotation.x - annotation.width / 2,\n                annotation.y - annotation.height / 2,\n                annotation.width,\n                annotation.height\n            )\n            ctx.stroke()\n        }\n\n        if (typeof annotation.note === 'function') {\n            annotation.note(ctx, {\n                datum: annotation.datum,\n                x: annotation.computed.text[0],\n                y: annotation.computed.text[1],\n                theme,\n            })\n        } else {\n            ctx.font = `${theme.annotations.text.fontSize}px ${theme.annotations.text.fontFamily}`\n            ctx.textAlign = 'left'\n            ctx.textBaseline = 'alphabetic'\n\n            ctx.fillStyle = theme.annotations.text.fill\n            ctx.strokeStyle = theme.annotations.text.outlineColor\n            ctx.lineWidth = theme.annotations.text.outlineWidth * 2\n\n            if (theme.annotations.text.outlineWidth > 0) {\n                ctx.lineJoin = 'round'\n                ctx.strokeText(\n                    annotation.note,\n                    annotation.computed.text[0],\n                    annotation.computed.text[1]\n                )\n                ctx.lineJoin = 'miter'\n            }\n            ctx.fillText(annotation.note, annotation.computed.text[0], annotation.computed.text[1])\n        }\n    })\n    ctx.restore()\n}\n", "import { Annotation, useAnnotations } from '@nivo/annotations'\nimport { BarAnnotationsProps } from './types'\n\nexport const BarAnnotations = <RawDatum,>({ bars, annotations }: BarAnnotationsProps<RawDatum>) => {\n    const boundAnnotations = useAnnotations({\n        data: bars,\n        annotations,\n        getPosition: bar => ({\n            x: bar.x + bar.width / 2,\n            y: bar.y + bar.height / 2,\n        }),\n        getDimensions: ({ height, width }) => ({\n            width,\n            height,\n            size: Math.max(width, height),\n        }),\n    })\n\n    return (\n        <>\n            {boundAnnotations.map((annotation, i) => (\n                <Annotation key={i} {...annotation} />\n            ))}\n        </>\n    )\n}\n", "import { BoxLegendSvg } from '@nivo/legends'\nimport { BarLegendProps, LegendData } from './types'\n\ninterface BarLegendsProps {\n    width: number\n    height: number\n    legends: [BarLegendProps, LegendData[]][]\n    toggleSerie: (id: string | number) => void\n}\n\nexport const BarLegends = ({ width, height, legends, toggleSerie }: BarLegendsProps) => (\n    <>\n        {legends.map(([legend, data], i) => (\n            <BoxLegendSvg\n                key={i}\n                {...legend}\n                containerWidth={width}\n                containerHeight={height}\n                data={legend.data ?? data}\n                toggleSerie={\n                    legend.toggleSerie && legend.dataFrom === 'keys' ? toggleSerie : undefined\n                }\n            />\n        ))}\n    </>\n)\n", "import { createElement, MouseEvent, useCallback, useMemo } from 'react'\nimport { animated, to } from '@react-spring/web'\nimport { useTheme } from '@nivo/core'\nimport { useTooltip } from '@nivo/tooltip'\nimport { BarDatum, BarItemProps } from './types'\n\nexport const BarItem = <RawDatum extends BarDatum>({\n    bar: { data, ...bar },\n\n    style: {\n        borderColor,\n        color,\n        height,\n        labelColor,\n        labelOpacity,\n        labelX,\n        labelY,\n        transform,\n        width,\n    },\n\n    borderRadius,\n    borderWidth,\n\n    label,\n    shouldRenderLabel,\n\n    isInteractive,\n    onClick,\n    onMouseEnter,\n    onMouseLeave,\n\n    tooltip,\n\n    isFocusable,\n    ariaLabel,\n    ariaLabelledBy,\n    ariaDescribedBy,\n}: BarItemProps<RawDatum>) => {\n    const theme = useTheme()\n    const { showTooltipFromEvent, showTooltipAt, hideTooltip } = useTooltip()\n\n    const renderTooltip = useMemo(\n        () => () => createElement(tooltip, { ...bar, ...data }),\n        [tooltip, bar, data]\n    )\n\n    const handleClick = useCallback(\n        (event: MouseEvent<SVGRectElement>) => {\n            onClick?.({ color: bar.color, ...data }, event)\n        },\n        [bar, data, onClick]\n    )\n    const handleTooltip = useCallback(\n        (event: MouseEvent<SVGRectElement>) => showTooltipFromEvent(renderTooltip(), event),\n        [showTooltipFromEvent, renderTooltip]\n    )\n    const handleMouseEnter = useCallback(\n        (event: MouseEvent<SVGRectElement>) => {\n            onMouseEnter?.(data, event)\n            showTooltipFromEvent(renderTooltip(), event)\n        },\n        [data, onMouseEnter, showTooltipFromEvent, renderTooltip]\n    )\n    const handleMouseLeave = useCallback(\n        (event: MouseEvent<SVGRectElement>) => {\n            onMouseLeave?.(data, event)\n            hideTooltip()\n        },\n        [data, hideTooltip, onMouseLeave]\n    )\n\n    // extra handlers to allow keyboard navigation\n    const handleFocus = useCallback(() => {\n        showTooltipAt(renderTooltip(), [bar.absX + bar.width / 2, bar.absY])\n    }, [showTooltipAt, renderTooltip, bar])\n    const handleBlur = useCallback(() => {\n        hideTooltip()\n    }, [hideTooltip])\n\n    return (\n        <animated.g transform={transform}>\n            <animated.rect\n                width={to(width, value => Math.max(value, 0))}\n                height={to(height, value => Math.max(value, 0))}\n                rx={borderRadius}\n                ry={borderRadius}\n                fill={data.fill ?? color}\n                strokeWidth={borderWidth}\n                stroke={borderColor}\n                focusable={isFocusable}\n                tabIndex={isFocusable ? 0 : undefined}\n                aria-label={ariaLabel ? ariaLabel(data) : undefined}\n                aria-labelledby={ariaLabelledBy ? ariaLabelledBy(data) : undefined}\n                aria-describedby={ariaDescribedBy ? ariaDescribedBy(data) : undefined}\n                onMouseEnter={isInteractive ? handleMouseEnter : undefined}\n                onMouseMove={isInteractive ? handleTooltip : undefined}\n                onMouseLeave={isInteractive ? handleMouseLeave : undefined}\n                onClick={isInteractive ? handleClick : undefined}\n                onFocus={isInteractive && isFocusable ? handleFocus : undefined}\n                onBlur={isInteractive && isFocusable ? handleBlur : undefined}\n            />\n            {shouldRenderLabel && (\n                <animated.text\n                    x={labelX}\n                    y={labelY}\n                    textAnchor=\"middle\"\n                    dominantBaseline=\"central\"\n                    fillOpacity={labelOpacity}\n                    style={{\n                        ...theme.labels.text,\n                        pointerEvents: 'none',\n                        fill: labelColor,\n                    }}\n                >\n                    {label}\n                </animated.text>\n            )}\n        </animated.g>\n    )\n}\n", "import { BarTooltipProps } from './types'\nimport { BasicTooltip } from '@nivo/tooltip'\n\nexport const BarTooltip = <RawDatum,>({ color, label, ...data }: BarTooltipProps<RawDatum>) => {\n    return <BasicTooltip id={label} value={data.formattedValue} enableChip={true} color={color} />\n}\n", "import { BarItem } from './BarItem'\nimport { BarTooltip } from './BarTooltip'\nimport { ComputedDatum } from './types'\nimport { InheritedColorConfig, OrdinalColorScaleConfig } from '@nivo/colors'\nimport { ScaleBandSpec, ScaleSpec } from '@nivo/scales'\n\nexport const defaultProps = {\n    indexBy: 'id',\n    keys: ['value'],\n\n    groupMode: 'stacked' as const,\n    layout: 'vertical' as const,\n    reverse: false,\n\n    minValue: 'auto' as const,\n    maxValue: 'auto' as const,\n\n    valueScale: { type: 'linear' } as ScaleSpec,\n    indexScale: { type: 'band', round: true } as ScaleBandSpec,\n\n    padding: 0.1,\n    innerPadding: 0,\n\n    axisBottom: {},\n    axisLeft: {},\n    enableGridX: false,\n    enableGridY: true,\n\n    enableLabel: true,\n    label: 'formattedValue',\n    labelSkipWidth: 0,\n    labelSkipHeight: 0,\n    labelTextColor: { from: 'theme', theme: 'labels.text.fill' },\n\n    colorBy: 'id' as const,\n    colors: { scheme: 'nivo' } as OrdinalColorScaleConfig,\n\n    borderRadius: 0,\n    borderWidth: 0,\n    borderColor: { from: 'color' } as InheritedColorConfig<any>,\n\n    isInteractive: true,\n    tooltip: BarTooltip,\n    tooltipLabel: <RawDatum>(datum: ComputedDatum<RawDatum>) => `${datum.id} - ${datum.indexValue}`,\n\n    legends: [],\n    initialHiddenIds: [],\n    annotations: [],\n    markers: [],\n}\n\nexport const svgDefaultProps = {\n    ...defaultProps,\n    layers: ['grid', 'axes', 'bars', 'markers', 'legends', 'annotations'],\n    barComponent: BarItem,\n\n    defs: [],\n    fill: [],\n\n    animate: true,\n    motionConfig: 'default',\n\n    role: 'img',\n    isFocusable: false,\n}\n\nexport const canvasDefaultProps = {\n    ...defaultProps,\n    layers: ['grid', 'axes', 'bars', 'legends', 'annotations'],\n\n    pixelRatio: typeof window !== 'undefined' ? window.devicePixelRatio ?? 1 : 1,\n}\n", "import { ScaleBandSpec, ScaleBand, computeScale } from '@nivo/scales'\n\n/**\n * Generates indexed scale.\n */\nexport const getIndexScale = <RawDatum>(\n    data: RawDatum[],\n    getIndex: (datum: RawDatum) => string,\n    padding: number,\n    indexScale: ScaleBandSpec,\n    size: number,\n    axis: 'x' | 'y'\n) => {\n    return (\n        computeScale(\n            indexScale,\n            { all: data.map(getIndex), min: 0, max: 0 },\n            size,\n            axis\n        ) as ScaleBand<string>\n    ).padding(padding)\n}\n\n/**\n * This method ensures all the provided keys exist in the entire series.\n */\nexport const normalizeData = <RawDatum>(data: RawDatum[], keys: string[]) =>\n    data.map(\n        item =>\n            ({\n                ...keys.reduce<Record<string, unknown>>((acc, key) => {\n                    acc[key] = null\n                    return acc\n                }, {}),\n                ...item,\n            } as RawDatum)\n    )\n\nexport const filterNullValues = <RawDatum extends Record<string, unknown>>(data: RawDatum) =>\n    Object.keys(data).reduce<Record<string, unknown>>((acc, key) => {\n        if (data[key]) {\n            acc[key] = data[key]\n        }\n        return acc\n    }, {}) as Exclude<RawDatum, null | undefined | false | '' | 0>\n\nexport const coerceValue = <T>(value: T) => [value, Number(value)] as const\n", "import { Margin } from '@nivo/core'\nimport { OrdinalColorScale } from '@nivo/colors'\nimport { Scale, ScaleBand, computeScale } from '@nivo/scales'\nimport { BarDatum, BarSvgProps, ComputedBarDatum, ComputedDatum } from '../types'\nimport { coerceValue, filterNullValues, getIndexScale, normalizeData } from './common'\n\ntype Params<RawDatum, XScaleInput, YScaleInput> = {\n    data: RawDatum[]\n    formatValue: (value: number) => string\n    getColor: OrdinalColorScale<ComputedDatum<RawDatum>>\n    getIndex: (datum: RawDatum) => string\n    getTooltipLabel: (datum: ComputedDatum<RawDatum>) => string\n    innerPadding: number\n    keys: string[]\n    xScale: XScaleInput extends string ? ScaleBand<XScaleInput> : Scale<XScaleInput, number>\n    yScale: YScaleInput extends string ? ScaleBand<YScaleInput> : Scale<YScaleInput, number>\n    margin: Margin\n}\n\nconst gt = (value: number, other: number) => value > other\nconst lt = (value: number, other: number) => value < other\n\nconst range = (start: number, end: number) =>\n    Array.from(' '.repeat(end - start), (_, index) => start + index)\n\nconst clampToZero = (value: number) => (gt(value, 0) ? 0 : value)\nconst zeroIfNotFinite = (value: number) => (isFinite(value) ? value : 0)\n\n/**\n * Generates x/y scales & bars for vertical grouped bar chart.\n */\nconst generateVerticalGroupedBars = <RawDatum extends Record<string, unknown>>(\n    {\n        data,\n        formatValue,\n        getColor,\n        getIndex,\n        getTooltipLabel,\n        innerPadding = 0,\n        keys,\n        xScale,\n        yScale,\n        margin,\n    }: Params<RawDatum, string, number>,\n    barWidth: number,\n    reverse: boolean,\n    yRef: number\n): ComputedBarDatum<RawDatum>[] => {\n    const compare = reverse ? lt : gt\n    const getY = (d: number) => (compare(d, 0) ? yScale(d) ?? 0 : yRef)\n    const getHeight = (d: number, y: number) => (compare(d, 0) ? yRef - y : (yScale(d) ?? 0) - yRef)\n    const cleanedData = data.map(filterNullValues)\n\n    const bars: ComputedBarDatum<RawDatum>[] = []\n    keys.forEach((key, i) =>\n        range(0, xScale.domain().length).forEach(index => {\n            const [rawValue, value] = coerceValue(data[index][key])\n            const indexValue = getIndex(data[index])\n            const x = (xScale(indexValue) ?? 0) + barWidth * i + innerPadding * i\n            const y = getY(value)\n            const barHeight = getHeight(value, y)\n            const barData: ComputedDatum<RawDatum> = {\n                id: key,\n                value: rawValue === null ? rawValue : value,\n                formattedValue: formatValue(value),\n                hidden: false,\n                index,\n                indexValue,\n                data: cleanedData[index],\n            }\n\n            bars.push({\n                key: `${key}.${barData.indexValue}`,\n                index: bars.length,\n                data: barData,\n                x,\n                y,\n                absX: margin.left + x,\n                absY: margin.top + y,\n                width: barWidth,\n                height: barHeight,\n                color: getColor(barData),\n                label: getTooltipLabel(barData),\n            })\n        })\n    )\n\n    return bars\n}\n\n/**\n * Generates x/y scales & bars for horizontal grouped bar chart.\n */\nconst generateHorizontalGroupedBars = <RawDatum extends Record<string, unknown>>(\n    {\n        data,\n        formatValue,\n        getIndex,\n        getColor,\n        getTooltipLabel,\n        keys,\n        innerPadding = 0,\n        xScale,\n        yScale,\n        margin,\n    }: Params<RawDatum, number, string>,\n    barHeight: number,\n    reverse: boolean,\n    xRef: number\n): ComputedBarDatum<RawDatum>[] => {\n    const compare = reverse ? lt : gt\n    const getX = (d: number) => (compare(d, 0) ? xRef : xScale(d) ?? 0)\n    const getWidth = (d: number, x: number) => (compare(d, 0) ? (xScale(d) ?? 0) - xRef : xRef - x)\n    const cleanedData = data.map(filterNullValues)\n\n    const bars: ComputedBarDatum<RawDatum>[] = []\n    keys.forEach((key, i) =>\n        range(0, yScale.domain().length).forEach(index => {\n            const [rawValue, value] = coerceValue(data[index][key])\n            const indexValue = getIndex(data[index])\n            const x = getX(value)\n            const y = (yScale(indexValue) ?? 0) + barHeight * i + innerPadding * i\n            const barWidth = getWidth(value, x)\n            const barData: ComputedDatum<RawDatum> = {\n                id: key,\n                value: rawValue === null ? rawValue : value,\n                formattedValue: formatValue(value),\n                hidden: false,\n                index,\n                indexValue,\n                data: cleanedData[index],\n            }\n\n            bars.push({\n                key: `${key}.${barData.indexValue}`,\n                index: bars.length,\n                data: barData,\n                x,\n                y,\n                absX: margin.left + x,\n                absY: margin.top + y,\n                width: barWidth,\n                height: barHeight,\n                color: getColor(barData),\n                label: getTooltipLabel(barData),\n            })\n        })\n    )\n\n    return bars\n}\n\n/**\n * Generates x/y scales & bars for grouped bar chart.\n */\nexport const generateGroupedBars = <RawDatum extends BarDatum>({\n    layout,\n    minValue,\n    maxValue,\n    reverse,\n    width,\n    height,\n    padding = 0,\n    innerPadding = 0,\n    valueScale,\n    indexScale: indexScaleConfig,\n    hiddenIds = [],\n    ...props\n}: Pick<\n    Required<BarSvgProps<RawDatum>>,\n    | 'data'\n    | 'height'\n    | 'indexScale'\n    | 'innerPadding'\n    | 'keys'\n    | 'layout'\n    | 'maxValue'\n    | 'minValue'\n    | 'padding'\n    | 'reverse'\n    | 'valueScale'\n    | 'width'\n> & {\n    formatValue: (value: number) => string\n    getColor: OrdinalColorScale<ComputedDatum<RawDatum>>\n    getIndex: (datum: RawDatum) => string\n    getTooltipLabel: (datum: ComputedDatum<RawDatum>) => string\n    margin: Margin\n    hiddenIds?: (string | number)[]\n}) => {\n    const keys = props.keys.filter(key => !hiddenIds.includes(key))\n    const data = normalizeData(props.data, keys)\n    const [axis, otherAxis, size] =\n        layout === 'vertical' ? (['y', 'x', width] as const) : (['x', 'y', height] as const)\n    const indexScale = getIndexScale(\n        data,\n        props.getIndex,\n        padding,\n        indexScaleConfig,\n        size,\n        otherAxis\n    )\n\n    const scaleSpec = {\n        max: maxValue,\n        min: minValue,\n        reverse,\n        ...valueScale,\n    }\n\n    const clampMin = scaleSpec.min === 'auto' ? clampToZero : (value: number) => value\n\n    const values = data\n        .reduce<number[]>((acc, entry) => [...acc, ...keys.map(k => entry[k] as number)], [])\n        .filter(Boolean)\n    const min = clampMin(Math.min(...values))\n    const max = zeroIfNotFinite(Math.max(...values))\n\n    const scale = computeScale(\n        scaleSpec as any,\n        { all: values, min, max },\n        axis === 'x' ? width : height,\n        axis\n    )\n\n    const [xScale, yScale] = layout === 'vertical' ? [indexScale, scale] : [scale, indexScale]\n\n    const bandwidth = (indexScale.bandwidth() - innerPadding * (keys.length - 1)) / keys.length\n    const params = [\n        { ...props, data, keys, innerPadding, xScale, yScale } as Params<RawDatum, any, any>,\n        bandwidth,\n        scaleSpec.reverse,\n        scale(0) ?? 0,\n    ] as const\n\n    const bars: ComputedBarDatum<RawDatum>[] =\n        bandwidth > 0\n            ? layout === 'vertical'\n                ? generateVerticalGroupedBars(...params)\n                : generateHorizontalGroupedBars(...params)\n            : []\n\n    return { xScale, yScale, bars }\n}\n", "import { Margin } from '@nivo/core'\nimport { OrdinalColorScale } from '@nivo/colors'\nimport { Scale, ScaleBand, computeScale } from '@nivo/scales'\nimport { Series, SeriesPoint, stack, stackOffsetDiverging } from 'd3-shape'\nimport { BarDatum, BarSvgProps, ComputedBarDatum, ComputedDatum } from '../types'\nimport { coerceValue, filterNullValues, getIndexScale, normalizeData } from './common'\n\ntype StackDatum<RawDatum> = SeriesPoint<RawDatum>\n\ntype Params<RawDatum, XScaleInput, YScaleInput> = {\n    formatValue: (value: number) => string\n    getColor: OrdinalColorScale<ComputedDatum<RawDatum>>\n    getIndex: (datum: RawDatum) => string\n    getTooltipLabel: (datum: ComputedDatum<RawDatum>) => string\n    innerPadding: number\n    stackedData: Series<RawDatum, string>[]\n    xScale: XScaleInput extends string ? ScaleBand<XScaleInput> : Scale<XScaleInput, number>\n    yScale: YScaleInput extends string ? ScaleBand<YScaleInput> : Scale<YScaleInput, number>\n    margin: Margin\n}\n\nconst flattenDeep = <T>(arr: T[]): T =>\n    arr.some(Array.isArray) ? flattenDeep(([] as T[]).concat(...arr)) : (arr as unknown as T)\n\nconst filterZerosIfLog = (array: number[], type: string) =>\n    type === 'log' ? array.filter(num => num !== 0) : array\n\n/**\n * Generates x/y scales & bars for vertical stacked bar chart.\n */\nconst generateVerticalStackedBars = <RawDatum extends Record<string, unknown>>(\n    {\n        formatValue,\n        getColor,\n        getIndex,\n        getTooltipLabel,\n        innerPadding,\n        stackedData,\n        xScale,\n        yScale,\n        margin,\n    }: Params<RawDatum, string, number>,\n    barWidth: number,\n    reverse: boolean\n): ComputedBarDatum<RawDatum>[] => {\n    const getY = (d: StackDatum<RawDatum>) => yScale(d[reverse ? 0 : 1])\n    const getHeight = (d: StackDatum<RawDatum>, y: number) => (yScale(d[reverse ? 1 : 0]) ?? 0) - y\n\n    const bars: ComputedBarDatum<RawDatum>[] = []\n    stackedData.forEach(stackedDataItem =>\n        xScale.domain().forEach((index, i) => {\n            const d = stackedDataItem[i]\n            const x = xScale(getIndex(d.data)) ?? 0\n            const y = (getY(d) ?? 0) + innerPadding * 0.5\n            const barHeight = getHeight(d, y) - innerPadding\n            const [rawValue, value] = coerceValue(d.data[stackedDataItem.key])\n\n            const barData: ComputedDatum<RawDatum> = {\n                id: stackedDataItem.key,\n                value: rawValue === null ? rawValue : value,\n                formattedValue: formatValue(value),\n                hidden: false,\n                index: i,\n                indexValue: index,\n                data: filterNullValues(d.data),\n            }\n\n            bars.push({\n                key: `${stackedDataItem.key}.${index}`,\n                index: bars.length,\n                data: barData,\n                x,\n                y,\n                absX: margin.left + x,\n                absY: margin.top + y,\n                width: barWidth,\n                height: barHeight,\n                color: getColor(barData),\n                label: getTooltipLabel(barData),\n            })\n        })\n    )\n\n    return bars\n}\n\n/**\n * Generates x/y scales & bars for horizontal stacked bar chart.\n */\nconst generateHorizontalStackedBars = <RawDatum extends Record<string, unknown>>(\n    {\n        formatValue,\n        getColor,\n        getIndex,\n        getTooltipLabel,\n        innerPadding,\n        stackedData,\n        xScale,\n        yScale,\n        margin,\n    }: Params<RawDatum, number, string>,\n    barHeight: number,\n    reverse: boolean\n): ComputedBarDatum<RawDatum>[] => {\n    const getX = (d: StackDatum<RawDatum>) => xScale(d[reverse ? 1 : 0])\n    const getWidth = (d: StackDatum<RawDatum>, x: number) => (xScale(d[reverse ? 0 : 1]) ?? 0) - x\n\n    const bars: ComputedBarDatum<RawDatum>[] = []\n    stackedData.forEach(stackedDataItem =>\n        yScale.domain().forEach((index, i) => {\n            const d = stackedDataItem[i]\n            const y = yScale(getIndex(d.data)) ?? 0\n            const x = (getX(d) ?? 0) + innerPadding * 0.5\n            const barWidth = getWidth(d, x) - innerPadding\n            const [rawValue, value] = coerceValue(d.data[stackedDataItem.key])\n\n            const barData: ComputedDatum<RawDatum> = {\n                id: stackedDataItem.key,\n                value: rawValue === null ? rawValue : value,\n                formattedValue: formatValue(value),\n                hidden: false,\n                index: i,\n                indexValue: index,\n                data: filterNullValues(d.data),\n            }\n\n            bars.push({\n                key: `${stackedDataItem.key}.${index}`,\n                index: bars.length,\n                data: barData,\n                x,\n                y,\n                absX: margin.left + x,\n                absY: margin.top + y,\n                width: barWidth,\n                height: barHeight,\n                color: getColor(barData),\n                label: getTooltipLabel(barData),\n            })\n        })\n    )\n\n    return bars\n}\n\n/**\n * Generates x/y scales & bars for stacked bar chart.\n */\nexport const generateStackedBars = <RawDatum extends BarDatum>({\n    data,\n    layout,\n    minValue,\n    maxValue,\n    reverse,\n    width,\n    height,\n    padding = 0,\n    valueScale,\n    indexScale: indexScaleConfig,\n    hiddenIds = [],\n    ...props\n}: Pick<\n    Required<BarSvgProps<RawDatum>>,\n    | 'data'\n    | 'height'\n    | 'indexScale'\n    | 'innerPadding'\n    | 'keys'\n    | 'layout'\n    | 'maxValue'\n    | 'minValue'\n    | 'padding'\n    | 'reverse'\n    | 'valueScale'\n    | 'width'\n> & {\n    formatValue: (value: number) => string\n    getColor: OrdinalColorScale<ComputedDatum<RawDatum>>\n    getIndex: (datum: RawDatum) => string\n    getTooltipLabel: (datum: ComputedDatum<RawDatum>) => string\n    margin: Margin\n    hiddenIds?: (string | number)[]\n}) => {\n    const keys = props.keys.filter(key => !hiddenIds.includes(key))\n    const stackedData = stack<RawDatum, string>().keys(keys).offset(stackOffsetDiverging)(\n        normalizeData(data, keys)\n    )\n\n    const [axis, otherAxis, size] =\n        layout === 'vertical' ? (['y', 'x', width] as const) : (['x', 'y', height] as const)\n    const indexScale = getIndexScale(\n        data,\n        props.getIndex,\n        padding,\n        indexScaleConfig,\n        size,\n        otherAxis\n    )\n\n    const scaleSpec = {\n        max: maxValue,\n        min: minValue,\n        reverse,\n        ...valueScale,\n    }\n\n    const values = filterZerosIfLog(\n        flattenDeep(stackedData as unknown as number[][]),\n        valueScale.type\n    )\n    const min = Math.min(...values)\n    const max = Math.max(...values)\n\n    const scale = computeScale(\n        scaleSpec as any,\n        { all: values, min, max },\n        axis === 'x' ? width : height,\n        axis\n    )\n\n    const [xScale, yScale] = layout === 'vertical' ? [indexScale, scale] : [scale, indexScale]\n\n    const innerPadding = props.innerPadding > 0 ? props.innerPadding : 0\n    const bandwidth = indexScale.bandwidth()\n    const params = [\n        { ...props, innerPadding, stackedData, xScale, yScale } as Params<RawDatum, any, any>,\n        bandwidth,\n        scaleSpec.reverse,\n    ] as const\n\n    const bars: ComputedBarDatum<RawDatum>[] =\n        bandwidth > 0\n            ? layout === 'vertical'\n                ? generateVerticalStackedBars(...params)\n                : generateHorizontalStackedBars(...params)\n            : []\n\n    return { xScale, yScale, bars }\n}\n", "import {\n    BarDatum,\n    BarLegendProps,\n    BarSvgProps,\n    BarsWithHidden,\n    LegendData,\n    LegendLabelDatum,\n} from '../types'\nimport { getPropertyAccessor } from '@nivo/core'\nimport uniqBy from 'lodash/uniqBy'\n\nexport const getLegendDataForKeys = <RawDatum extends BarDatum>(\n    bars: BarsWithHidden<RawDatum>,\n    layout: NonNullable<BarSvgProps<RawDatum>['layout']>,\n    direction: BarLegendProps['direction'],\n    groupMode: NonNullable<BarSvgProps<RawDatum>['groupMode']>,\n    reverse: boolean,\n    getLegendLabel: (datum: LegendLabelDatum<RawDatum>) => string\n): LegendData[] => {\n    const data = uniqBy(\n        bars.map(bar => ({\n            id: bar.data.id,\n            label: getLegendLabel(bar.data),\n            hidden: bar.data.hidden,\n            color: bar.color ?? '#000',\n        })),\n        ({ id }) => id\n    )\n\n    if (\n        (layout === 'vertical' &&\n            groupMode === 'stacked' &&\n            direction === 'column' &&\n            reverse !== true) ||\n        (layout === 'horizontal' && groupMode === 'stacked' && reverse === true)\n    ) {\n        data.reverse()\n    }\n\n    return data\n}\n\nexport const getLegendDataForIndexes = <RawDatum extends BarDatum>(\n    bars: BarsWithHidden<RawDatum>,\n    layout: NonNullable<BarSvgProps<RawDatum>['layout']>,\n    getLegendLabel: (datum: LegendLabelDatum<RawDatum>) => string\n): LegendData[] => {\n    const data = uniqBy(\n        bars.map(bar => ({\n            id: bar.data.indexValue ?? '',\n            label: getLegendLabel(bar.data),\n            hidden: bar.data.hidden,\n            color: bar.color ?? '#000',\n        })),\n        ({ id }) => id\n    )\n\n    if (layout === 'horizontal') {\n        data.reverse()\n    }\n\n    return data\n}\n\nexport const getLegendData = <RawDatum extends BarDatum>({\n    bars,\n    direction,\n    from,\n    groupMode,\n    layout,\n    legendLabel,\n    reverse,\n}: Pick<Required<BarSvgProps<RawDatum>>, 'layout' | 'groupMode' | 'reverse'> & {\n    bars: BarsWithHidden<RawDatum>\n    direction: BarLegendProps['direction']\n    from: BarLegendProps['dataFrom']\n    legendLabel: BarSvgProps<RawDatum>['legendLabel']\n}) => {\n    const getLegendLabel = getPropertyAccessor(\n        legendLabel ?? (from === 'indexes' ? 'indexValue' : 'id')\n    )\n\n    if (from === 'indexes') {\n        return getLegendDataForIndexes(bars, layout, getLegendLabel)\n    }\n\n    return getLegendDataForKeys(bars, layout, direction, groupMode, reverse, getLegendLabel)\n}\n", "import { useCallback, useMemo, useState } from 'react'\nimport { useInheritedColor, useOrdinalColorScale } from '@nivo/colors'\nimport { usePropertyAccessor, useTheme, useValueFormatter, Margin } from '@nivo/core'\nimport {\n    DataProps,\n    BarCommonProps,\n    BarDatum,\n    ComputedBarDatumWithValue,\n    LegendData,\n    BarLegendProps,\n} from './types'\nimport { defaultProps } from './props'\nimport { generateGroupedBars, generateStackedBars, getLegendData } from './compute'\n\nexport const useBar = <RawDatum extends BarDatum>({\n    indexBy = defaultProps.indexBy,\n    keys = defaultProps.keys,\n    label = defaultProps.label,\n    tooltipLabel = defaultProps.tooltipLabel,\n    valueFormat,\n    colors = defaultProps.colors,\n    colorBy = defaultProps.colorBy,\n    borderColor = defaultProps.borderColor,\n    labelTextColor = defaultProps.labelTextColor,\n    groupMode = defaultProps.groupMode,\n    layout = defaultProps.layout,\n    reverse = defaultProps.reverse,\n    data,\n    minValue = defaultProps.minValue,\n    maxValue = defaultProps.maxValue,\n    margin,\n    width,\n    height,\n    padding = defaultProps.padding,\n    innerPadding = defaultProps.innerPadding,\n    valueScale = defaultProps.valueScale,\n    indexScale = defaultProps.indexScale,\n    initialHiddenIds = defaultProps.initialHiddenIds,\n    enableLabel = defaultProps.enableLabel,\n    labelSkipWidth = defaultProps.labelSkipWidth,\n    labelSkipHeight = defaultProps.labelSkipHeight,\n    legends = defaultProps.legends,\n    legendLabel,\n}: {\n    indexBy?: BarCommonProps<RawDatum>['indexBy']\n    label?: BarCommonProps<RawDatum>['label']\n    tooltipLabel?: BarCommonProps<RawDatum>['tooltipLabel']\n    valueFormat?: BarCommonProps<RawDatum>['valueFormat']\n    colors?: BarCommonProps<RawDatum>['colors']\n    colorBy?: BarCommonProps<RawDatum>['colorBy']\n    borderColor?: BarCommonProps<RawDatum>['borderColor']\n    labelTextColor?: BarCommonProps<RawDatum>['labelTextColor']\n    groupMode?: BarCommonProps<RawDatum>['groupMode']\n    layout?: BarCommonProps<RawDatum>['layout']\n    reverse?: BarCommonProps<RawDatum>['reverse']\n    data: DataProps<RawDatum>['data']\n    keys?: BarCommonProps<RawDatum>['keys']\n    minValue?: BarCommonProps<RawDatum>['minValue']\n    maxValue?: BarCommonProps<RawDatum>['maxValue']\n    margin: Margin\n    width: number\n    height: number\n    padding?: BarCommonProps<RawDatum>['padding']\n    innerPadding?: BarCommonProps<RawDatum>['innerPadding']\n    valueScale?: BarCommonProps<RawDatum>['valueScale']\n    indexScale?: BarCommonProps<RawDatum>['indexScale']\n    initialHiddenIds?: BarCommonProps<RawDatum>['initialHiddenIds']\n    enableLabel?: BarCommonProps<RawDatum>['enableLabel']\n    labelSkipWidth?: BarCommonProps<RawDatum>['labelSkipWidth']\n    labelSkipHeight?: BarCommonProps<RawDatum>['labelSkipHeight']\n    legends?: BarCommonProps<RawDatum>['legends']\n    legendLabel?: BarCommonProps<RawDatum>['legendLabel']\n}) => {\n    const [hiddenIds, setHiddenIds] = useState(initialHiddenIds ?? [])\n    const toggleSerie = useCallback((id: string | number) => {\n        setHiddenIds(state =>\n            state.indexOf(id) > -1 ? state.filter(item => item !== id) : [...state, id]\n        )\n    }, [])\n\n    const getIndex = usePropertyAccessor(indexBy)\n    const getLabel = usePropertyAccessor(label)\n    const getTooltipLabel = usePropertyAccessor(tooltipLabel)\n    const formatValue = useValueFormatter(valueFormat)\n\n    const theme = useTheme()\n    const getColor = useOrdinalColorScale(colors, colorBy)\n    const getBorderColor = useInheritedColor<ComputedBarDatumWithValue<RawDatum>>(\n        borderColor,\n        theme\n    )\n    const getLabelColor = useInheritedColor<ComputedBarDatumWithValue<RawDatum>>(\n        labelTextColor,\n        theme\n    )\n\n    const generateBars = groupMode === 'grouped' ? generateGroupedBars : generateStackedBars\n    const { bars, xScale, yScale } = generateBars({\n        layout,\n        reverse,\n        data,\n        getIndex,\n        keys,\n        minValue,\n        maxValue,\n        width,\n        height,\n        getColor,\n        padding,\n        innerPadding,\n        valueScale,\n        indexScale,\n        hiddenIds,\n        formatValue,\n        getTooltipLabel,\n        margin,\n    })\n\n    const barsWithValue = useMemo(\n        () =>\n            bars\n                .filter(\n                    (bar): bar is ComputedBarDatumWithValue<RawDatum> => bar.data.value !== null\n                )\n                .map((bar, index) => ({\n                    ...bar,\n                    index,\n                })),\n        [bars]\n    )\n\n    const shouldRenderBarLabel = useCallback(\n        ({ width, height }: { height: number; width: number }) => {\n            if (!enableLabel) return false\n            if (labelSkipWidth > 0 && width < labelSkipWidth) return false\n            if (labelSkipHeight > 0 && height < labelSkipHeight) return false\n            return true\n        },\n        [enableLabel, labelSkipWidth, labelSkipHeight]\n    )\n\n    const legendData = useMemo(\n        () =>\n            keys.map(key => {\n                const bar = bars.find(bar => bar.data.id === key)\n\n                return { ...bar, data: { id: key, ...bar?.data, hidden: hiddenIds.includes(key) } }\n            }),\n        [hiddenIds, keys, bars]\n    )\n\n    const legendsWithData: [BarLegendProps, LegendData[]][] = useMemo(\n        () =>\n            legends.map(legend => {\n                const data = getLegendData({\n                    bars: legend.dataFrom === 'keys' ? legendData : bars,\n                    direction: legend.direction,\n                    from: legend.dataFrom,\n                    groupMode,\n                    layout,\n                    legendLabel,\n                    reverse,\n                })\n\n                return [legend, data]\n            }),\n        [legends, legendData, bars, groupMode, layout, legendLabel, reverse]\n    )\n\n    return {\n        bars,\n        barsWithValue,\n        xScale,\n        yScale,\n        getIndex,\n        getLabel,\n        getTooltipLabel,\n        formatValue,\n        getColor,\n        getBorderColor,\n        getLabelColor,\n        shouldRenderBarLabel,\n        hiddenIds,\n        toggleSerie,\n        legendsWithData,\n    }\n}\n", "import { Axes, Grid } from '@nivo/axes'\nimport { BarAnnotations } from './BarAnnotations'\nimport {\n    BarCustomLayerProps,\n    BarDatum,\n    BarLayer,\n    BarLayerId,\n    BarSvgProps,\n    ComputedBarDatumWithValue,\n} from './types'\nimport { BarLegends } from './BarLegends'\nimport {\n    CartesianMarkers,\n    Container,\n    SvgWrapper,\n    // @ts-ignore\n    bindDefs,\n    useDimensions,\n    useMotionConfig,\n} from '@nivo/core'\nimport { Fragment, ReactNode, createElement, useMemo } from 'react'\nimport { svgDefaultProps } from './props'\nimport { useTransition } from '@react-spring/web'\nimport { useBar } from './hooks'\n\ntype InnerBarProps<RawDatum extends BarDatum> = Omit<\n    BarSvgProps<RawDatum>,\n    'animate' | 'motionConfig' | 'renderWrapper' | 'theme'\n>\n\nconst InnerBar = <RawDatum extends BarDatum>({\n    data,\n    indexBy,\n    keys,\n\n    margin: partialMargin,\n    width,\n    height,\n\n    groupMode,\n    layout,\n    reverse,\n    minValue,\n    maxValue,\n\n    valueScale,\n    indexScale,\n\n    padding,\n    innerPadding,\n\n    axisTop,\n    axisRight,\n    axisBottom = svgDefaultProps.axisBottom,\n    axisLeft = svgDefaultProps.axisLeft,\n    enableGridX = svgDefaultProps.enableGridX,\n    enableGridY = svgDefaultProps.enableGridY,\n    gridXValues,\n    gridYValues,\n\n    layers = svgDefaultProps.layers as BarLayer<RawDatum>[],\n    barComponent = svgDefaultProps.barComponent,\n\n    enableLabel = svgDefaultProps.enableLabel,\n    label,\n    labelSkipWidth = svgDefaultProps.labelSkipWidth,\n    labelSkipHeight = svgDefaultProps.labelSkipHeight,\n    labelTextColor,\n\n    markers = svgDefaultProps.markers,\n\n    colorBy,\n    colors,\n    defs = svgDefaultProps.defs,\n    fill = svgDefaultProps.fill,\n    borderRadius = svgDefaultProps.borderRadius,\n    borderWidth = svgDefaultProps.borderWidth,\n    borderColor,\n\n    annotations = svgDefaultProps.annotations,\n\n    legendLabel,\n    tooltipLabel,\n\n    valueFormat,\n\n    isInteractive = svgDefaultProps.isInteractive,\n    tooltip = svgDefaultProps.tooltip,\n    onClick,\n    onMouseEnter,\n    onMouseLeave,\n\n    legends,\n\n    role = svgDefaultProps.role,\n    ariaLabel,\n    ariaLabelledBy,\n    ariaDescribedBy,\n    isFocusable = svgDefaultProps.isFocusable,\n    barAriaLabel,\n    barAriaLabelledBy,\n    barAriaDescribedBy,\n\n    initialHiddenIds,\n}: InnerBarProps<RawDatum>) => {\n    const { animate, config: springConfig } = useMotionConfig()\n    const { outerWidth, outerHeight, margin, innerWidth, innerHeight } = useDimensions(\n        width,\n        height,\n        partialMargin\n    )\n\n    const {\n        bars,\n        barsWithValue,\n        xScale,\n        yScale,\n        getLabel,\n        getTooltipLabel,\n        getBorderColor,\n        getLabelColor,\n        shouldRenderBarLabel,\n        toggleSerie,\n        legendsWithData,\n    } = useBar<RawDatum>({\n        indexBy,\n        label,\n        tooltipLabel,\n        valueFormat,\n        colors,\n        colorBy,\n        borderColor,\n        labelTextColor,\n        groupMode,\n        layout,\n        reverse,\n        data,\n        keys,\n        minValue,\n        maxValue,\n        margin,\n        width: innerWidth,\n        height: innerHeight,\n        padding,\n        innerPadding,\n        valueScale,\n        indexScale,\n        enableLabel,\n        labelSkipWidth,\n        labelSkipHeight,\n        legends,\n        legendLabel,\n        initialHiddenIds,\n    })\n\n    const transition = useTransition<\n        ComputedBarDatumWithValue<RawDatum>,\n        {\n            borderColor: string\n            color: string\n            height: number\n            labelColor: string\n            labelOpacity: number\n            labelX: number\n            labelY: number\n            opacity: number\n            transform: string\n            width: number\n        }\n    >(barsWithValue, {\n        keys: bar => bar.key,\n        from: bar => ({\n            borderColor: getBorderColor(bar) as string,\n            color: bar.color,\n            height: 0,\n            labelColor: getLabelColor(bar) as string,\n            labelOpacity: 0,\n            labelX: bar.width / 2,\n            labelY: bar.height / 2,\n            transform: `translate(${bar.x}, ${bar.y + bar.height})`,\n            width: bar.width,\n            ...(layout === 'vertical'\n                ? {}\n                : {\n                      height: bar.height,\n                      transform: `translate(${bar.x}, ${bar.y})`,\n                      width: 0,\n                  }),\n        }),\n        enter: bar => ({\n            borderColor: getBorderColor(bar) as string,\n            color: bar.color,\n            height: bar.height,\n            labelColor: getLabelColor(bar) as string,\n            labelOpacity: 1,\n            labelX: bar.width / 2,\n            labelY: bar.height / 2,\n            transform: `translate(${bar.x}, ${bar.y})`,\n            width: bar.width,\n        }),\n        update: bar => ({\n            borderColor: getBorderColor(bar) as string,\n            color: bar.color,\n            height: bar.height,\n            labelColor: getLabelColor(bar) as string,\n            labelOpacity: 1,\n            labelX: bar.width / 2,\n            labelY: bar.height / 2,\n            transform: `translate(${bar.x}, ${bar.y})`,\n            width: bar.width,\n        }),\n        leave: bar => ({\n            borderColor: getBorderColor(bar) as string,\n            color: bar.color,\n            height: 0,\n            labelColor: getLabelColor(bar) as string,\n            labelOpacity: 0,\n            labelX: bar.width / 2,\n            labelY: 0,\n            transform: `translate(${bar.x}, ${bar.y + bar.height})`,\n            width: bar.width,\n            ...(layout === 'vertical'\n                ? {}\n                : {\n                      labelX: 0,\n                      labelY: bar.height / 2,\n                      height: bar.height,\n                      transform: `translate(${bar.x}, ${bar.y})`,\n                      width: 0,\n                  }),\n        }),\n        config: springConfig,\n        immediate: !animate,\n    })\n\n    const commonProps = useMemo(\n        () => ({\n            borderRadius,\n            borderWidth,\n            enableLabel,\n            isInteractive,\n            labelSkipWidth,\n            labelSkipHeight,\n            onClick,\n            onMouseEnter,\n            onMouseLeave,\n            getTooltipLabel,\n            tooltip,\n            isFocusable,\n            ariaLabel: barAriaLabel,\n            ariaLabelledBy: barAriaLabelledBy,\n            ariaDescribedBy: barAriaDescribedBy,\n        }),\n        [\n            borderRadius,\n            borderWidth,\n            enableLabel,\n            getTooltipLabel,\n            isInteractive,\n            labelSkipHeight,\n            labelSkipWidth,\n            onClick,\n            onMouseEnter,\n            onMouseLeave,\n            tooltip,\n            isFocusable,\n            barAriaLabel,\n            barAriaLabelledBy,\n            barAriaDescribedBy,\n        ]\n    )\n\n    const boundDefs = bindDefs(defs, bars, fill, {\n        dataKey: 'data',\n        targetKey: 'data.fill',\n    })\n\n    const layerById: Record<BarLayerId, ReactNode> = {\n        annotations: null,\n        axes: null,\n        bars: null,\n        grid: null,\n        legends: null,\n        markers: null,\n    }\n\n    if (layers.includes('annotations')) {\n        layerById.annotations = (\n            <BarAnnotations key=\"annotations\" bars={bars} annotations={annotations} />\n        )\n    }\n\n    if (layers.includes('axes')) {\n        layerById.axes = (\n            <Axes\n                key=\"axes\"\n                xScale={xScale}\n                yScale={yScale}\n                width={innerWidth}\n                height={innerHeight}\n                top={axisTop}\n                right={axisRight}\n                bottom={axisBottom}\n                left={axisLeft}\n            />\n        )\n    }\n\n    if (layers.includes('bars')) {\n        layerById.bars = (\n            <Fragment key=\"bars\">\n                {transition((style, bar) =>\n                    createElement(barComponent, {\n                        ...commonProps,\n                        bar,\n                        style,\n                        shouldRenderLabel: shouldRenderBarLabel(bar),\n                        label: getLabel(bar.data),\n                    })\n                )}\n            </Fragment>\n        )\n    }\n\n    if (layers.includes('grid')) {\n        layerById.grid = (\n            <Grid\n                key=\"grid\"\n                width={innerWidth}\n                height={innerHeight}\n                xScale={enableGridX ? xScale : null}\n                yScale={enableGridY ? yScale : null}\n                xValues={gridXValues}\n                yValues={gridYValues}\n            />\n        )\n    }\n\n    if (layers.includes('legends')) {\n        layerById.legends = (\n            <BarLegends\n                key=\"legends\"\n                width={innerWidth}\n                height={innerHeight}\n                legends={legendsWithData}\n                toggleSerie={toggleSerie}\n            />\n        )\n    }\n\n    if (layers.includes('markers')) {\n        layerById.markers = (\n            <CartesianMarkers<number | string, number>\n                key=\"markers\"\n                markers={markers as any[]}\n                width={innerWidth}\n                height={innerHeight}\n                xScale={xScale as (v: number | string) => number}\n                yScale={yScale as (v: number) => number}\n            />\n        )\n    }\n\n    const layerContext: BarCustomLayerProps<RawDatum> = useMemo(\n        () => ({\n            ...commonProps,\n            margin,\n            width,\n            height,\n            innerWidth,\n            innerHeight,\n            bars,\n            legendData: legendsWithData,\n            enableLabel,\n            xScale,\n            yScale,\n            tooltip,\n            getTooltipLabel,\n            onClick,\n            onMouseEnter,\n            onMouseLeave,\n        }),\n        [\n            commonProps,\n            margin,\n            width,\n            height,\n            innerWidth,\n            innerHeight,\n            bars,\n            legendsWithData,\n            enableLabel,\n            xScale,\n            yScale,\n            tooltip,\n            getTooltipLabel,\n            onClick,\n            onMouseEnter,\n            onMouseLeave,\n        ]\n    )\n\n    return (\n        <SvgWrapper\n            width={outerWidth}\n            height={outerHeight}\n            margin={margin}\n            defs={boundDefs}\n            role={role}\n            ariaLabel={ariaLabel}\n            ariaLabelledBy={ariaLabelledBy}\n            ariaDescribedBy={ariaDescribedBy}\n            isFocusable={isFocusable}\n        >\n            {layers.map((layer, i) => {\n                if (typeof layer === 'function') {\n                    return <Fragment key={i}>{createElement(layer, layerContext)}</Fragment>\n                }\n\n                return layerById?.[layer] ?? null\n            })}\n        </SvgWrapper>\n    )\n}\n\nexport const Bar = <RawDatum extends BarDatum>({\n    isInteractive = svgDefaultProps.isInteractive,\n    animate = svgDefaultProps.animate,\n    motionConfig = svgDefaultProps.motionConfig,\n    theme,\n    renderWrapper,\n    ...otherProps\n}: BarSvgProps<RawDatum>) => (\n    <Container\n        {...{\n            animate,\n            isInteractive,\n            motionConfig,\n            renderWrapper,\n            theme,\n        }}\n    >\n        <InnerBar<RawDatum> isInteractive={isInteractive} {...otherProps} />\n    </Container>\n)\n", "import {\n    BarCanvasCustomLayerProps,\n    BarCanvasLayer,\n    BarCanvasProps,\n    BarDatum,\n    ComputedBarDatum,\n} from './types'\nimport {\n    Container,\n    Margin,\n    getRelativeCursor,\n    isCursorInRect,\n    useDimensions,\n    useTheme,\n} from '@nivo/core'\nimport {\n    ForwardedRef,\n    createElement,\n    forwardRef,\n    useCallback,\n    useEffect,\n    useMemo,\n    useRef,\n} from 'react'\nimport { canvasDefaultProps } from './props'\nimport {\n    renderAnnotationsToCanvas,\n    useAnnotations,\n    useComputedAnnotations,\n} from '@nivo/annotations'\nimport { renderAxesToCanvas, renderGridLinesToCanvas } from '@nivo/axes'\nimport { renderLegendToCanvas } from '@nivo/legends'\nimport { useTooltip } from '@nivo/tooltip'\nimport { useBar } from './hooks'\n\ntype InnerBarCanvasProps<RawDatum extends BarDatum> = Omit<\n    BarCanvasProps<RawDatum>,\n    'renderWrapper' | 'theme'\n> & {\n    canvasRef: ForwardedRef<HTMLCanvasElement>\n}\n\nconst findBarUnderCursor = <RawDatum,>(\n    nodes: ComputedBarDatum<RawDatum>[],\n    margin: Margin,\n    x: number,\n    y: number\n) =>\n    nodes.find(node =>\n        isCursorInRect(node.x + margin.left, node.y + margin.top, node.width, node.height, x, y)\n    )\n\nconst isNumber = (value: unknown): value is number => typeof value === 'number'\n\nconst InnerBarCanvas = <RawDatum extends BarDatum>({\n    data,\n    indexBy,\n    keys,\n\n    margin: partialMargin,\n    width,\n    height,\n\n    groupMode,\n    layout,\n    reverse,\n    minValue,\n    maxValue,\n\n    valueScale,\n    indexScale,\n\n    padding,\n    innerPadding,\n\n    axisTop,\n    axisRight,\n    axisBottom = canvasDefaultProps.axisBottom,\n    axisLeft = canvasDefaultProps.axisLeft,\n    enableGridX = canvasDefaultProps.enableGridX,\n    enableGridY = canvasDefaultProps.enableGridY,\n    gridXValues,\n    gridYValues,\n\n    layers = canvasDefaultProps.layers as BarCanvasLayer<RawDatum>[],\n    renderBar = (\n        ctx,\n        {\n            bar: { color, height, width, x, y },\n\n            borderColor,\n            borderRadius,\n            borderWidth,\n            label,\n            labelColor,\n            shouldRenderLabel,\n        }\n    ) => {\n        ctx.fillStyle = color\n\n        if (borderWidth > 0) {\n            ctx.strokeStyle = borderColor\n            ctx.lineWidth = borderWidth\n        }\n\n        ctx.beginPath()\n\n        if (borderRadius > 0) {\n            const radius = Math.min(borderRadius, height)\n\n            ctx.moveTo(x + radius, y)\n            ctx.lineTo(x + width - radius, y)\n            ctx.quadraticCurveTo(x + width, y, x + width, y + radius)\n            ctx.lineTo(x + width, y + height - radius)\n            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)\n            ctx.lineTo(x + radius, y + height)\n            ctx.quadraticCurveTo(x, y + height, x, y + height - radius)\n            ctx.lineTo(x, y + radius)\n            ctx.quadraticCurveTo(x, y, x + radius, y)\n            ctx.closePath()\n        } else {\n            ctx.rect(x, y, width, height)\n        }\n\n        ctx.fill()\n\n        if (borderWidth > 0) {\n            ctx.stroke()\n        }\n\n        if (shouldRenderLabel) {\n            ctx.textBaseline = 'middle'\n            ctx.textAlign = 'center'\n            ctx.fillStyle = labelColor\n            ctx.fillText(label, x + width / 2, y + height / 2)\n        }\n    },\n\n    enableLabel = canvasDefaultProps.enableLabel,\n    label,\n    labelSkipWidth = canvasDefaultProps.labelSkipWidth,\n    labelSkipHeight = canvasDefaultProps.labelSkipHeight,\n    labelTextColor,\n\n    colorBy,\n    colors,\n    borderRadius = canvasDefaultProps.borderRadius,\n    borderWidth = canvasDefaultProps.borderWidth,\n    borderColor,\n\n    annotations = canvasDefaultProps.annotations,\n\n    legendLabel,\n    tooltipLabel,\n\n    valueFormat,\n\n    isInteractive = canvasDefaultProps.isInteractive,\n    tooltip = canvasDefaultProps.tooltip,\n    onClick,\n    onMouseEnter,\n    onMouseLeave,\n\n    legends,\n\n    pixelRatio = canvasDefaultProps.pixelRatio,\n\n    canvasRef,\n}: InnerBarCanvasProps<RawDatum>) => {\n    const canvasEl = useRef<HTMLCanvasElement | null>(null)\n\n    const theme = useTheme()\n    const { margin, innerWidth, innerHeight, outerWidth, outerHeight } = useDimensions(\n        width,\n        height,\n        partialMargin\n    )\n\n    const {\n        bars,\n        barsWithValue,\n        xScale,\n        yScale,\n        getLabel,\n        getTooltipLabel,\n        getBorderColor,\n        getLabelColor,\n        shouldRenderBarLabel,\n        legendsWithData,\n    } = useBar<RawDatum>({\n        indexBy,\n        label,\n        tooltipLabel,\n        valueFormat,\n        colors,\n        colorBy,\n        borderColor,\n        labelTextColor,\n        groupMode,\n        layout,\n        reverse,\n        data,\n        keys,\n        minValue,\n        maxValue,\n        margin,\n        width: innerWidth,\n        height: innerHeight,\n        padding,\n        innerPadding,\n        valueScale,\n        indexScale,\n        enableLabel,\n        labelSkipWidth,\n        labelSkipHeight,\n        legends,\n        legendLabel,\n    })\n\n    const { showTooltipFromEvent, hideTooltip } = useTooltip()\n\n    // Using any because return type isn't correct\n    const boundAnnotations: any = useComputedAnnotations({\n        annotations: useAnnotations({\n            data: bars,\n            annotations,\n            getPosition: node => ({\n                x: node.x,\n                y: node.y,\n            }),\n            getDimensions: ({ width, height }) => ({\n                width,\n                height,\n                size: Math.max(width, height),\n            }),\n        }),\n    })\n\n    // We use `any` here until we can figure out the best way to type xScale/yScale\n    const layerContext: BarCanvasCustomLayerProps<RawDatum> = useMemo(\n        () => ({\n            borderRadius,\n            borderWidth,\n            isInteractive,\n            isFocusable: false,\n            labelSkipWidth,\n            labelSkipHeight,\n            margin,\n            width,\n            height,\n            innerWidth,\n            innerHeight,\n            bars,\n            legendData: legendsWithData,\n            enableLabel,\n            xScale,\n            yScale,\n            tooltip,\n            getTooltipLabel,\n            onClick,\n            onMouseEnter,\n            onMouseLeave,\n        }),\n        [\n            borderRadius,\n            borderWidth,\n            isInteractive,\n            labelSkipWidth,\n            labelSkipHeight,\n            margin,\n            width,\n            height,\n            innerWidth,\n            innerHeight,\n            bars,\n            legendsWithData,\n            enableLabel,\n            xScale,\n            yScale,\n            tooltip,\n            getTooltipLabel,\n            onClick,\n            onMouseEnter,\n            onMouseLeave,\n        ]\n    )\n\n    useEffect(() => {\n        const ctx = canvasEl.current?.getContext('2d')\n\n        if (!canvasEl.current) return\n        if (!ctx) return\n\n        canvasEl.current.width = outerWidth * pixelRatio\n        canvasEl.current.height = outerHeight * pixelRatio\n\n        ctx.scale(pixelRatio, pixelRatio)\n\n        ctx.fillStyle = theme.background\n        ctx.fillRect(0, 0, outerWidth, outerHeight)\n        ctx.translate(margin.left, margin.top)\n\n        layers.forEach(layer => {\n            if (layer === 'grid') {\n                if (isNumber(theme.grid.line.strokeWidth) && theme.grid.line.strokeWidth > 0) {\n                    ctx.lineWidth = theme.grid.line.strokeWidth\n                    ctx.strokeStyle = theme.grid.line.stroke as string\n\n                    if (enableGridX) {\n                        renderGridLinesToCanvas<string | number>(ctx, {\n                            width,\n                            height,\n                            scale: xScale,\n                            axis: 'x',\n                            values: gridXValues,\n                        })\n                    }\n\n                    if (enableGridY) {\n                        renderGridLinesToCanvas<string | number>(ctx, {\n                            width,\n                            height,\n                            scale: yScale,\n                            axis: 'y',\n                            values: gridYValues,\n                        })\n                    }\n                }\n            } else if (layer === 'axes') {\n                renderAxesToCanvas(ctx, {\n                    xScale: xScale,\n                    yScale: yScale,\n                    width: innerWidth,\n                    height: innerHeight,\n                    top: axisTop,\n                    right: axisRight,\n                    bottom: axisBottom,\n                    left: axisLeft,\n                    theme,\n                })\n            } else if (layer === 'bars') {\n                barsWithValue.forEach(bar => {\n                    renderBar(ctx, {\n                        bar,\n                        borderColor: getBorderColor(bar) as string,\n                        borderRadius,\n                        borderWidth,\n                        label: getLabel(bar.data),\n                        labelColor: getLabelColor(bar) as string,\n                        shouldRenderLabel: shouldRenderBarLabel(bar),\n                    })\n                })\n            } else if (layer === 'legends') {\n                legendsWithData.forEach(([legend, data]) => {\n                    renderLegendToCanvas(ctx, {\n                        ...legend,\n                        data,\n                        containerWidth: innerWidth,\n                        containerHeight: innerHeight,\n                        theme,\n                    })\n                })\n            } else if (layer === 'annotations') {\n                renderAnnotationsToCanvas(ctx, { annotations: boundAnnotations, theme })\n            } else if (typeof layer === 'function') {\n                layer(ctx, layerContext)\n            }\n        })\n\n        ctx.save()\n    }, [\n        axisBottom,\n        axisLeft,\n        axisRight,\n        axisTop,\n        barsWithValue,\n        borderRadius,\n        borderWidth,\n        boundAnnotations,\n        enableGridX,\n        enableGridY,\n        getBorderColor,\n        getLabel,\n        getLabelColor,\n        gridXValues,\n        gridYValues,\n        groupMode,\n        height,\n        innerHeight,\n        innerWidth,\n        layerContext,\n        layers,\n        layout,\n        legendsWithData,\n        margin.left,\n        margin.top,\n        outerHeight,\n        outerWidth,\n        pixelRatio,\n        renderBar,\n        xScale,\n        yScale,\n        reverse,\n        shouldRenderBarLabel,\n        theme,\n        width,\n    ])\n\n    const handleMouseHover = useCallback(\n        (event: React.MouseEvent<HTMLCanvasElement>) => {\n            if (!bars) return\n            if (!canvasEl.current) return\n\n            const [x, y] = getRelativeCursor(canvasEl.current, event)\n            const bar = findBarUnderCursor(bars, margin, x, y)\n\n            if (bar !== undefined) {\n                showTooltipFromEvent(\n                    createElement(tooltip, {\n                        ...bar.data,\n                        color: bar.color,\n                        label: bar.label,\n                        value: Number(bar.data.value),\n                    }),\n                    event\n                )\n\n                if (event.type === 'mouseenter') {\n                    onMouseEnter?.(bar.data, event)\n                }\n            } else {\n                hideTooltip()\n            }\n        },\n        [hideTooltip, margin, onMouseEnter, bars, showTooltipFromEvent, tooltip]\n    )\n\n    const handleMouseLeave = useCallback(\n        (event: React.MouseEvent<HTMLCanvasElement>) => {\n            if (!bars) return\n            if (!canvasEl.current) return\n\n            hideTooltip()\n\n            const [x, y] = getRelativeCursor(canvasEl.current, event)\n            const bar = findBarUnderCursor(bars, margin, x, y)\n\n            if (bar) {\n                onMouseLeave?.(bar.data, event)\n            }\n        },\n        [hideTooltip, margin, onMouseLeave, bars]\n    )\n\n    const handleClick = useCallback(\n        (event: React.MouseEvent<HTMLCanvasElement>) => {\n            if (!bars) return\n            if (!canvasEl.current) return\n\n            const [x, y] = getRelativeCursor(canvasEl.current, event)\n            const bar = findBarUnderCursor(bars, margin, x, y)\n\n            if (bar !== undefined) {\n                onClick?.({ ...bar.data, color: bar.color }, event)\n            }\n        },\n        [margin, onClick, bars]\n    )\n\n    return (\n        <canvas\n            ref={canvas => {\n                canvasEl.current = canvas\n                if (canvasRef && 'current' in canvasRef) canvasRef.current = canvas\n            }}\n            width={outerWidth * pixelRatio}\n            height={outerHeight * pixelRatio}\n            style={{\n                width: outerWidth,\n                height: outerHeight,\n                cursor: isInteractive ? 'auto' : 'normal',\n            }}\n            onMouseEnter={isInteractive ? handleMouseHover : undefined}\n            onMouseMove={isInteractive ? handleMouseHover : undefined}\n            onMouseLeave={isInteractive ? handleMouseLeave : undefined}\n            onClick={isInteractive ? handleClick : undefined}\n        />\n    )\n}\n\nexport const BarCanvas = forwardRef(\n    <RawDatum extends BarDatum>(\n        { isInteractive, renderWrapper, theme, ...props }: BarCanvasProps<RawDatum>,\n        ref: ForwardedRef<HTMLCanvasElement>\n    ) => (\n        <Container {...{ isInteractive, renderWrapper, theme }} animate={false}>\n            <InnerBarCanvas<RawDatum> {...props} canvasRef={ref} />\n        </Container>\n    )\n)\n", "import { Bar } from './Bar'\nimport { BarDatum, BarSvgProps } from './types'\nimport { ResponsiveWrapper } from '@nivo/core'\n\nexport type ResponsiveBarSvgProps<RawDatum extends BarDatum> = Omit<\n    BarSvgProps<RawDatum>,\n    'height' | 'width'\n>\n\nexport const ResponsiveBar = <RawDatum extends BarDatum>(\n    props: ResponsiveBarSvgProps<RawDatum>\n) => (\n    <ResponsiveWrapper>\n        {({ width, height }) => <Bar<RawDatum> width={width} height={height} {...props} />}\n    </ResponsiveWrapper>\n)\n", "import { BarDatum, BarCanvasProps } from './types'\nimport { BarCanvas } from './BarCanvas'\nimport { ForwardedRef, forwardRef } from 'react'\nimport { ResponsiveWrapper } from '@nivo/core'\n\nexport type ResponsiveBarCanvasProps<RawDatum extends BarDatum> = Omit<\n    BarCanvasProps<RawDatum>,\n    'height' | 'width'\n>\n\nexport const ResponsiveBarCanvas = forwardRef(function ResponsiveBarCanvas<\n    RawDatum extends BarDatum\n>(props: ResponsiveBarCanvasProps<RawDatum>, ref: ForwardedRef<HTMLCanvasElement>) {\n    return (\n        <ResponsiveWrapper>\n            {({ width, height }) => (\n                <BarCanvas\n                    width={width}\n                    height={height}\n                    {...(props as Omit<BarCanvasProps<BarDatum>, 'height' | 'width'>)}\n                    ref={ref}\n                />\n            )}\n        </ResponsiveWrapper>\n    )\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAUf,aAAS,WAAW,YAAY,WAAW;AACzC,UAAI,SAAS,CAAC;AACd,eAAS,YAAY,SAAS,OAAO,OAAOA,aAAY;AACtD,YAAI,UAAU,OAAO,OAAOA,WAAU,GAAG;AACvC,iBAAO,KAAK,KAAK;AAAA,QACnB;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpBjB;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,aAAa;AADjB,QAEI,eAAe;AAFnB,QAGI,UAAU;AA2Cd,aAAS,OAAO,YAAY,WAAW;AACrC,UAAI,OAAO,QAAQ,UAAU,IAAI,cAAc;AAC/C,aAAO,KAAK,YAAY,aAAa,WAAW,CAAC,CAAC;AAAA,IACpD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnDjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,eAAe;AAGnB,QAAI,YAAY;AA4BhB,aAAS,SAAS,OAAO;AACvB,aAAO,OAAO,SAAS,YACpB,aAAa,KAAK,KAAK,WAAW,KAAK,KAAK;AAAA,IACjD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrCjB;AAAA;AAAA,QAAI,UAAU;AAAd,QACI,YAAY;AAUhB,aAAS,OAAO,QAAQ,MAAM;AAC5B,aAAO,KAAK,SAAS,IAAI,SAAS,QAAQ,QAAQ,UAAU,MAAM,GAAG,EAAE,CAAC;AAAA,IAC1E;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACfjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,OAAO;AADX,QAEI,SAAS;AAFb,QAGI,QAAQ;AAUZ,aAAS,UAAU,QAAQ,MAAM;AAC/B,aAAO,SAAS,MAAM,MAAM;AAC5B,eAAS,OAAO,QAAQ,IAAI;AAC5B,aAAO,UAAU,QAAQ,OAAO,OAAO,MAAM,KAAK,IAAI,CAAC,CAAC;AAAA,IAC1D;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnBjB;AAAA;AAAA,QAAI,gBAAgB;AAWpB,aAAS,gBAAgB,OAAO;AAC9B,aAAO,cAAc,KAAK,IAAI,SAAY;AAAA,IAC5C;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACfjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,YAAY;AADhB,QAEI,YAAY;AAFhB,QAGI,WAAW;AAHf,QAII,aAAa;AAJjB,QAKI,kBAAkB;AALtB,QAMI,WAAW;AANf,QAOI,eAAe;AAGnB,QAAI,kBAAkB;AAAtB,QACI,kBAAkB;AADtB,QAEI,qBAAqB;AAsBzB,QAAI,OAAO,SAAS,SAAS,QAAQ,OAAO;AAC1C,UAAI,SAAS,CAAC;AACd,UAAI,UAAU,MAAM;AAClB,eAAO;AAAA,MACT;AACA,UAAI,SAAS;AACb,cAAQ,SAAS,OAAO,SAAS,MAAM;AACrC,eAAO,SAAS,MAAM,MAAM;AAC5B,mBAAW,SAAS,KAAK,SAAS;AAClC,eAAO;AAAA,MACT,CAAC;AACD,iBAAW,QAAQ,aAAa,MAAM,GAAG,MAAM;AAC/C,UAAI,QAAQ;AACV,iBAAS,UAAU,QAAQ,kBAAkB,kBAAkB,oBAAoB,eAAe;AAAA,MACpG;AACA,UAAI,SAAS,MAAM;AACnB,aAAO,UAAU;AACf,kBAAU,QAAQ,MAAM,MAAM,CAAC;AAAA,MACjC;AACA,aAAO;AAAA,IACT,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;;;;;;;;;;;;;;;;;;ACxDV,IAAMC,KAAe,EACxBC,SAAS,GACTC,WAAW,KACXC,gBAAgB,GAChBC,SAAAA,KAAS;AAJN,ICWMC,IAAY,SAAQC,IAAAA;AAC7B,MAAMC,KAAAA,OAAkBD;AAExB,aACIE,aAAAA,gBAAeF,EAAAA,KACF,aAAbC,MACa,eAAbA,MACa,aAAbA;AAER;ADpBO,ICsBME,IAAe,SAAQH,IAAAA;AAChC,MAAMC,KAAAA,OAAkBD;AAExB,SAAoB,aAAbC,MAAsC,eAAbA;AACpC;AD1BO,IC4BMG,IAAqB,SAC9BC,IAAAA;AAAqC,SACmC,aAAxBA,GAAeC;AAAiB;AD9B7E,ICgCMC,KAAkB,SAC3BF,IAAAA;AAAqC,SACgC,UAAxBA,GAAeC;AAAc;ADlCvE,ICoCME,KAAmB,SAC5BH,IAAAA;AAAqC,SACiC,WAAxBA,GAAeC;AAAe;ADtCzE,IEmBMG,IAAkB,SAAHC,IAAAA;AAAA,MAMxBC,KAAID,GAAJC,MACAC,KAAWF,GAAXE,aACAC,KAAWH,GAAXG,aACAC,IAAaJ,GAAbI;AAAa,SAObF,GAAYG,OAAO,SAACC,IAA+BC,IAAAA;AAC/C,QAAMC,IAASD,GAAWC,UAAU;AAEpC,WAAA,CAAA,EAAAC,OACOH,QACAI,cAAAA,SAAcT,IAAMM,GAAWI,KAAAA,EAAOC,IAAI,SAAAC,IAAAA;AACzC,UAAMC,KAAWX,GAAYU,EAAAA,GACvBE,KAAaX,EAAcS,EAAAA;AAyBjC,cAvBInB,EAAmBa,EAAAA,KAAeT,GAAiBS,EAAAA,OACnDQ,GAAWC,OAAOD,GAAWC,OAAgB,IAATR,GACpCO,GAAWE,QAAQF,GAAWE,QAAiB,IAATT,GACtCO,GAAWG,SAASH,GAAWG,SAAkB,IAATV,IAoB5CW,EAAA,CAAA,OACOC,YAAAA,SAAKb,IAAY,CAAC,SAAS,QAAA,CAAA,GAC3BO,IACAC,IAAU,EACbC,MAAMT,GAAWS,QAAQD,GAAWC,MACpCH,OAAAA,GAAAA,CAAAA;IAEP,CAAA,CAAA;EAIR,GAAE,CAAA,CAAA;AAAG;AF9EH,IEgFMQ,IAAe,SACxBC,IACAC,IACAC,IACAC,IAAAA;AAEA,MAAMC,KAAQC,KAAKC,MAAMH,KAAUF,IAASC,KAAUF,EAAAA;AAEtD,SAAOO,GAAqBC,GAAiBJ,EAAAA,CAAAA;AACjD;AFzFO,IE2FMK,IAAoB,SAC7BxB,IAAAA;AAEA,MASIyB,IACAC,IATAC,KAMA3B,GANA2B,GACAC,KAKA5B,GALA4B,GACAC,IAIA7B,GAJA6B,OACAC,IAGA9B,GAHA8B,OAAKC,IAGL/B,GAFArB,WAAAA,KAAAA,WAASoD,IAAGtD,GAAaE,YAASoD,GAAAC,KAElChC,GADApB,gBAAAA,IAAAA,WAAcoD,KAAGvD,GAAaG,iBAAcoD;AAMhD,UAAIC,gBAAAA,SAASJ,CAAAA;AACTJ,IAAAA,KAAgBE,KAAIE;OACjB;AAAA,QAAA,WAAIA,EAAMK;AAGb,YAAM,IAAIC,MAAK,2EAAA;AAFfV,IAAAA,KAAgBI,EAAMK;EAG1B;AAEA,UAAID,gBAAAA,SAASH,CAAAA;AACTJ,IAAAA,KAAgBE,KAAIE;OACjB;AAAA,QAAA,WAAIA,EAAMI;AAGb,YAAM,IAAIC,MAAK,2EAAA;AAFfT,IAAAA,KAAgBI,EAAMI;EAG1B;AAEA,MAAIE,IAAYT,IACZU,KAAYT,IAEVT,KAAQL,EAAaa,IAAGC,IAAGH,IAAeC,EAAAA;AAEhD,MAAIvC,EAA0Ba,EAAAA,GAAa;AACvC,QAAMO,KAAW+B,GAAkBC,GAAiBpB,EAAAA,GAAQnB,GAAWS,OAAO,CAAA;AAC9E2B,SAAa7B,GAASoB,GACtBU,MAAa9B,GAASqB;EAC1B;AAEA,MAAIrC,GAAwBS,EAAAA,GAAa;AACrC,QAAMwC,KAASpB,KAAKqB,OAAOtB,KAAQ,MAAM,EAAA,IAAM;AAChC,UAAXqB,OACAH,MAAarC,GAAWW,SAAS,IAEtB,MAAX6B,OACAJ,KAAapC,GAAWU,QAAQ,GAChC2B,MAAarC,GAAWW,SAAS,IAEtB,MAAX6B,OACAJ,KAAapC,GAAWU,QAAQ,IAErB,MAAX8B,OACAJ,KAAapC,GAAWU,QAAQ,GAChC2B,MAAarC,GAAWW,SAAS,IAEtB,MAAX6B,OACAH,MAAarC,GAAWW,SAAS,IAEtB,MAAX6B,OACAJ,KAAapC,GAAWU,QAAQ,GAChC2B,MAAarC,GAAWW,SAAS,IAEtB,MAAX6B,OACAJ,KAAapC,GAAWU,QAAQ,IAErB,MAAX8B,OACAJ,KAAapC,GAAWU,QAAQ,GAChC2B,MAAarC,GAAWW,SAAS;EAEzC;AAEA,MAAI+B,KAAQjB,IAGRkB,KAAYlB;AAUhB,UAPKN,KAAQ,MAAM,MAAM,OACrBuB,MAAS/D,IACTgE,MAAahE,MAEbgE,MAAahE,IAGV,EACHiE,QAAQ,CACJ,CAACR,GAAWC,EAAAA,GACZ,CAACZ,IAAeC,EAAAA,GAChB,CAACiB,IAbSjB,EAAAA,CAAAA,GAedmB,MAAM,CAACH,IAlBGhB,KAAgB9C,CAAAA,GAmB1BuC,OAAOA,KAAQ,GAAA;AAEvB;AF3LO,IGYM2B,IAAiB,SAAHrD,IAAAA;AAAA,MACvBC,KAAID,GAAJC,MACAC,KAAWF,GAAXE,aACAC,KAAWH,GAAXG,aACAC,KAAaJ,GAAbI;AAAa,aAObkD,aAAAA,SACI,WAAA;AAAA,WACIvD,EAAuB,EACnBE,MAAAA,IACAC,aAAAA,IACAC,aAAAA,IACAC,eAAAA,GAAAA,CAAAA;EAER,GAAA,CAACH,IAAMC,IAAaC,IAAaC,EAAAA,CAAAA;AACpC;AHhCE,IGkCMmD,KAAyB,SAAHC,IAAAA;AAAA,MAC/BtD,KAAWsD,GAAXtD;AAAW,aAIXoD,aAAAA,SACI,WAAA;AAAA,WACIpD,GAAYU,IAAI,SAAAL,IAAAA;AAAU,aAAAY,EAAAA,CAAAA,GACnBZ,IAAU,EACbkD,UAAU1B,EAAiBZ,EAAA,CAAA,GACpBZ,EAAAA,CAAAA,EAAAA,CAAAA;IACL,CAAA;EAAA,GAEV,CAACL,EAAAA,CAAAA;AACJ;AHhDE,IGkDMwD,IAAwB,SAAQnD,IAAAA;AAAkC,aAC3E+C,aAAAA,SAAQ,WAAA;AAAA,WAAMvB,EAAyBxB,EAAAA;EAAAA,GAAa,CAACA,EAAAA,CAAAA;AAAY;AHnD9D,IIMMoD,IAAiB,SAAH3D,IAAAA;AAUrB,MATFa,KAAKb,GAALa,OACAqB,KAAClC,GAADkC,GACAC,KAACnC,GAADmC,GACA7C,IAAIU,GAAJV,MAOMsE,IAAQC,GAAAA,GACdC,IAA0CC,GAAAA,GAAlC3E,KAAO0E,EAAP1E,SAAiB4E,KAAYF,EAApBG,QAEXC,KAAgBC,UAAU,EAC5BjC,GAAAA,IACAC,GAAAA,IACA8B,QAAQD,IACRI,WAAAA,CAAYhF,GAAAA,CAAAA;AAGhB,SAAoB,cAAA,OAATE,QACA+E,aAAAA,eAAc/E,GAAM,EAAE4C,GAAAA,IAAGC,GAAAA,IAAGtB,OAAAA,GAAAA,CAAAA,QAInCyD,mBAAAA,MAAAC,mBAAAA,UAAA,EAAAC,UACKZ,CAAAA,EAAM1D,YAAYkD,KAAKqB,eAAe,SACnCC,mBAAAA,KAACC,SAASvB,MAAI,EACVlB,GAAGgC,GAAchC,GACjBC,GAAG+B,GAAc/B,GACjByC,OAAKzD,EAAA,CAAA,GACEyC,EAAM1D,YAAYkD,MAAI,EACzByB,gBAAgB,SAChBC,aAAmD,IAAtClB,EAAM1D,YAAYkD,KAAKqB,cACpCM,QAAQnB,EAAM1D,YAAYkD,KAAK4B,aAAAA,CAAAA,GACjCR,UAEDlF,EAAAA,CAAAA,OAGToF,mBAAAA,KAACC,SAASvB,MAAI,EACVlB,GAAGgC,GAAchC,GACjBC,GAAG+B,GAAc/B,GACjByC,WAAOxD,YAAAA,SAAKwC,EAAM1D,YAAYkD,MAAM,CAAC,gBAAgB,cAAA,CAAA,GAAiBoB,UAErElF,EAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAIjB;AJxDO,IKIM2F,IAAiB,SAAHjF,IAAAA;AAMrB,MALFmD,KAAMnD,GAANmD,QAAM+B,KAAAlF,GACNmF,WAAAA,KAAAA,WAASD,MAAQA,IAKXtB,KAAQC,GAAAA,GAERuB,QAAO9B,aAAAA,SAAQ,WAAA;AACjB,QAAO+B,KAA8BlC,GAAM,CAAA;AAE3C,WAFqCA,GAAMmC,MAAA,CAAA,EAExBjF,OACf,SAACC,IAAGkD,IAAAA;AAAO,aAASlD,KAAG,OAAfkD,GAAA,CAAA,IAAqB,MAAlBA,GAAA,CAAA;IAAA,GAAyB,MAChC6B,GAAW,CAAA,IAAA,MAAMA,GAAW,CAAA,CAAA;EAExC,GAAG,CAAClC,EAAAA,CAAAA,GAEEoC,IAAeC,GAAgBJ,CAAAA;AAErC,MAAID,MAAavB,GAAM1D,YAAYuF,KAAKhB,gBAAgB;AACpD,WAAO;AAGX,MAAMG,IAAKzD,EAAA,CAAA,GAAQyC,GAAM1D,YAAYuF,IAAAA;AASrC,SARIN,OACAP,EAAMc,gBAAgB,UACtBd,EAAME,cACFlB,GAAM1D,YAAYuF,KAAKX,cAAoD,IAAtClB,GAAM1D,YAAYuF,KAAKhB,cAChEG,EAAMG,SAASnB,GAAM1D,YAAYuF,KAAKT,cACtCJ,EAAMe,UAAU/B,GAAM1D,YAAYuF,KAAKG,qBAGpClB,mBAAAA,KAACC,SAASS,MAAI,EAACS,MAAK,QAAOC,GAAGP,GAAcX,OAAOA,EAAAA,CAAAA;AAC9D;ALtCO,IMGMmB,IAA0B,SAAH/F,IAAAA;AAA+D,MAAzDkC,KAAClC,GAADkC,GAAGC,KAACnC,GAADmC,GAAGnB,KAAIhB,GAAJgB,MACtC4C,KAAQC,GAAAA,GACdC,KAA0CC,GAAAA,GAAlC3E,IAAO0E,GAAP1E,SAAiB4E,IAAYF,GAApBG,QAEXC,IAAgBC,UAAU,EAC5BjC,GAAAA,IACAC,GAAAA,IACA6D,QAAQhF,KAAO,GACfiD,QAAQD,GACRI,WAAAA,CAAYhF,EAAAA,CAAAA;AAGhB,aACIkF,mBAAAA,MAAAC,mBAAAA,UAAA,EAAAC,UACKZ,CAAAA,GAAM1D,YAAY+F,QAAQxB,eAAe,SACtCC,mBAAAA,KAACC,SAASuB,QAAM,EACZC,IAAIjC,EAAchC,GAClBkE,IAAIlC,EAAc/B,GAClBkE,GAAGnC,EAAc8B,QACjBpB,OAAKzD,EAAA,CAAA,GACEyC,GAAM1D,YAAY+F,SAAO,EAC5BJ,MAAM,QACNf,aACIlB,GAAM1D,YAAY+F,QAAQnB,cACe,IAAzClB,GAAM1D,YAAY+F,QAAQxB,cAC9BM,QAAQnB,GAAM1D,YAAY+F,QAAQjB,cAClCW,SAAS/B,GAAM1D,YAAY+F,QAAQL,eAAAA,CAAAA,EAAAA,CAAAA,OAI/ClB,mBAAAA,KAACC,SAASuB,QAAM,EACZC,IAAIjC,EAAchC,GAClBkE,IAAIlC,EAAc/B,GAClBkE,GAAGnC,EAAc8B,QACjBpB,OAAOhB,GAAM1D,YAAY+F,QAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAIzC;ANzCO,IOIMK,KAAuB,SAAHtG,IAAAA;AAQ3B,MAPFkC,KAAClC,GAADkC,GACAC,KAACnC,GAADmC,GAACoE,KAAAvG,GACDgB,MAAAA,KAAAA,WAAIuF,KAAGvH,GAAaC,UAAOsH,IAMrB3C,KAAQC,GAAAA,GACdC,IAA0CC,GAAAA,GAAlC3E,IAAO0E,EAAP1E,SAAiB4E,IAAYF,EAApBG,QAEXC,KAAgBC,UAAU,EAC5BjC,GAAAA,IACAC,GAAAA,IACA6D,QAAQhF,KAAO,GACfiD,QAAQD,GACRI,WAAAA,CAAYhF,EAAAA,CAAAA;AAGhB,aACIkF,mBAAAA,MAAAC,mBAAAA,UAAA,EAAAC,UACKZ,CAAAA,GAAM1D,YAAY+F,QAAQxB,eAAe,SACtCC,mBAAAA,KAACC,SAASuB,QAAM,EACZC,IAAIjC,GAAchC,GAClBkE,IAAIlC,GAAc/B,GAClBkE,GAAGnC,GAAc8B,QACjBpB,OAAKzD,EAAA,CAAA,GACEyC,GAAM1D,YAAY+F,SAAO,EAC5BJ,MAAM,QACNf,aAAsD,IAAzClB,GAAM1D,YAAY+F,QAAQxB,cACvCM,QAAQnB,GAAM1D,YAAY+F,QAAQjB,cAClCW,SAAS/B,GAAM1D,YAAY+F,QAAQL,eAAAA,CAAAA,EAAAA,CAAAA,OAI/ClB,mBAAAA,KAACC,SAASuB,QAAM,EACZC,IAAIjC,GAAchC,GAClBkE,IAAIlC,GAAc/B,GAClBkE,GAAGnC,GAAc8B,QACjBpB,OAAOhB,GAAM1D,YAAYsG,OAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAIzC;APhDO,IQGMC,IAAwB,SAAHzG,IAAAA;AAY5B,MAXFkC,KAAClC,GAADkC,GACAC,KAACnC,GAADmC,GACAlB,KAAKjB,GAALiB,OACAC,KAAMlB,GAANkB,QAAMwF,KAAA1G,GACN2G,cAAAA,IAAAA,WAAYD,KAAG,IAACA,IAQV9C,IAAQC,GAAAA,GACdC,IAA0CC,GAAAA,GAAlC3E,KAAO0E,EAAP1E,SAAiB4E,KAAYF,EAApBG,QAEXC,KAAgBC,UAAU,EAC5BjC,GAAGA,KAAIjB,KAAQ,GACfkB,GAAGA,KAAIjB,KAAS,GAChBD,OAAAA,IACAC,QAAAA,IACA+C,QAAQD,IACRI,WAAAA,CAAYhF,GAAAA,CAAAA;AAGhB,aACIkF,mBAAAA,MAAAC,mBAAAA,UAAA,EAAAC,UACKZ,CAAAA,EAAM1D,YAAY+F,QAAQxB,eAAe,SACtCC,mBAAAA,KAACC,SAASiC,MAAI,EACV1E,GAAGgC,GAAchC,GACjBC,GAAG+B,GAAc/B,GACjB0E,IAAIF,GACJG,IAAIH,GACJ1F,OAAOiD,GAAcjD,OACrBC,QAAQgD,GAAchD,QACtB0D,OAAKzD,EAAA,CAAA,GACEyC,EAAM1D,YAAY+F,SAAO,EAC5BJ,MAAM,QACNf,aACIlB,EAAM1D,YAAY+F,QAAQnB,cACe,IAAzClB,EAAM1D,YAAY+F,QAAQxB,cAC9BM,QAAQnB,EAAM1D,YAAY+F,QAAQjB,cAClCW,SAAS/B,EAAM1D,YAAY+F,QAAQL,eAAAA,CAAAA,EAAAA,CAAAA,OAI/ClB,mBAAAA,KAACC,SAASiC,MAAI,EACV1E,GAAGgC,GAAchC,GACjBC,GAAG+B,GAAc/B,GACjB0E,IAAIF,GACJG,IAAIH,GACJ1F,OAAOiD,GAAcjD,OACrBC,QAAQgD,GAAchD,QACtB0D,OAAOhB,EAAM1D,YAAY+F,QAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAIzC;AR5DO,ISSMc,IAAa,SAASxG,IAAAA;AAC/B,MAAQM,KAAsBN,GAAtBM,OAAOqB,KAAe3B,GAAf2B,GAAGC,KAAY5B,GAAZ4B,GAAG7C,KAASiB,GAATjB,MACfmE,KAAWC,EAAsBnD,EAAAA;AAEvC,MAAA,CAAKlB,EAAUC,EAAAA;AACX,UAAM,IAAIoD,MAAM,sCAAA;AAGpB,aACI4B,mBAAAA,MAAAC,mBAAAA,UAAA,EAAAC,UAAA,KACIE,mBAAAA,KAACO,GAAc,EAAC9B,QAAQM,GAASN,QAAQgC,WAAAA,KAAW,CAAA,GACnDzF,EAAmBa,EAAAA,SAChBmE,mBAAAA,KAACqB,GAAuB,EAAC7D,GAAGA,IAAGC,GAAGA,IAAGnB,MAAMT,GAAWS,KAAAA,CAAAA,GAEzDnB,GAAgBU,EAAAA,SACbmE,mBAAAA,KAAC4B,IAAoB,EAACpE,GAAGA,IAAGC,GAAGA,IAAGnB,MAAMT,GAAWS,KAAAA,CAAAA,GAEtDlB,GAAiBS,EAAAA,SACdmE,mBAAAA,KAAC+B,GAAqB,EAClBvE,GAAGA,IACHC,GAAGA,IACHlB,OAAOV,GAAWU,OAClBC,QAAQX,GAAWW,QACnByF,cAAcpG,GAAWoG,aAAAA,CAAAA,OAGjCjC,mBAAAA,KAACO,GAAc,EAAC9B,QAAQM,GAASN,OAAAA,CAAAA,OACjCuB,mBAAAA,KAACf,GAAc,EAAC9C,OAAOA,IAAOqB,GAAGuB,GAASL,KAAK,CAAA,GAAIjB,GAAGsB,GAASL,KAAK,CAAA,GAAI9D,MAAMA,GAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAG1F;ATvCO,IUID0H,IAAa,SAACC,IAA+B9D,IAAAA;AAC/CA,EAAAA,GAAO+D,QAAQ,SAAAlH,IAASmH,IAAAA;AAAU,QAAjBjF,KAAClC,GAAA,CAAA,GAAEmC,KAACnC,GAAA,CAAA;AACH,UAAVmH,KACAF,GAAIG,OAAOlF,IAAGC,EAAAA,IAEd8E,GAAII,OAAOnF,IAAGC,EAAAA;EAEtB,CAAA;AACJ;AVZO,IUcMmF,IAA4B,SACrCL,IAA6BzD,IAAAA;AAQ5B,MANGtD,KAAWsD,GAAXtD,aACA0D,KAAKJ,GAALI;AAMuB,QAAvB1D,GAAYqH,WAEhBN,GAAIO,KAAAA,GACJtH,GAAYgH,QAAQ,SAAA3G,IAAAA;AAChB,QAAA,CAAKd,EAAac,GAAWjB,IAAAA;AACzB,YAAM,IAAIoD,MAAM,2CAAA;AAGhBkB,IAAAA,GAAM1D,YAAYuF,KAAKhB,eAAe,MACtCwC,GAAIQ,UAAU,UACdR,GAAIS,cAAc9D,GAAM1D,YAAYuF,KAAKT,cACzCiC,GAAIU,YACA/D,GAAM1D,YAAYuF,KAAKX,cAAoD,IAAtClB,GAAM1D,YAAYuF,KAAKhB,cAChEwC,GAAIW,UAAAA,GACJZ,EAAWC,IAAK1G,GAAWkD,SAASN,MAAAA,GACpC8D,GAAIlC,OAAAA,GACJkC,GAAIQ,UAAU,SAGd/H,EAAmBa,EAAAA,KAAeqD,GAAM1D,YAAY+F,QAAQxB,eAAe,MAC3EwC,GAAIS,cAAc9D,GAAM1D,YAAY+F,QAAQjB,cAC5CiC,GAAIU,YACA/D,GAAM1D,YAAY+F,QAAQnB,cAAuD,IAAzClB,GAAM1D,YAAY+F,QAAQxB,cACtEwC,GAAIW,UAAAA,GACJX,GAAIY,IAAItH,GAAW2B,GAAG3B,GAAW4B,GAAG5B,GAAWS,OAAO,GAAG,GAAG,IAAIW,KAAKmG,EAAAA,GACrEb,GAAIlC,OAAAA,IAGJlF,GAAgBU,EAAAA,KAAeqD,GAAM1D,YAAYsG,OAAO/B,eAAe,MACvEwC,GAAIS,cAAc9D,GAAM1D,YAAYsG,OAAOxB,cAC3CiC,GAAIU,YAAoD,IAAxC/D,GAAM1D,YAAYsG,OAAO/B,cACzCwC,GAAIW,UAAAA,GACJX,GAAIY,IAAItH,GAAW2B,GAAG3B,GAAW4B,GAAG5B,GAAWS,OAAO,GAAG,GAAG,IAAIW,KAAKmG,EAAAA,GACrEb,GAAIlC,OAAAA,IAGJjF,GAAiBS,EAAAA,KAAeqD,GAAM1D,YAAY+F,QAAQxB,eAAe,MACzEwC,GAAIS,cAAc9D,GAAM1D,YAAY+F,QAAQjB,cAC5CiC,GAAIU,YACA/D,GAAM1D,YAAY+F,QAAQnB,cAAuD,IAAzClB,GAAM1D,YAAY+F,QAAQxB,cACtEwC,GAAIW,UAAAA,GACJX,GAAIL,KACArG,GAAW2B,IAAI3B,GAAWU,QAAQ,GAClCV,GAAW4B,IAAI5B,GAAWW,SAAS,GACnCX,GAAWU,OACXV,GAAWW,MAAAA,GAEf+F,GAAIlC,OAAAA,IAGRkC,GAAIS,cAAc9D,GAAM1D,YAAYuF,KAAKV,QACzCkC,GAAIU,YAAY/D,GAAM1D,YAAYuF,KAAKX,aACvCmC,GAAIW,UAAAA,GACJZ,EAAWC,IAAK1G,GAAWkD,SAASN,MAAAA,GACpC8D,GAAIlC,OAAAA,GAEArF,EAAmBa,EAAAA,MACnB0G,GAAIS,cAAc9D,GAAM1D,YAAY+F,QAAQlB,QAC5CkC,GAAIU,YAAY/D,GAAM1D,YAAY+F,QAAQnB,aAC1CmC,GAAIW,UAAAA,GACJX,GAAIY,IAAItH,GAAW2B,GAAG3B,GAAW4B,GAAG5B,GAAWS,OAAO,GAAG,GAAG,IAAIW,KAAKmG,EAAAA,GACrEb,GAAIlC,OAAAA,IAGJlF,GAAgBU,EAAAA,MAChB0G,GAAIc,YAAYnE,GAAM1D,YAAYsG,OAAOX,MACzCoB,GAAIW,UAAAA,GACJX,GAAIY,IAAItH,GAAW2B,GAAG3B,GAAW4B,GAAG5B,GAAWS,OAAO,GAAG,GAAG,IAAIW,KAAKmG,EAAAA,GACrEb,GAAIpB,KAAAA,IAGJ/F,GAAiBS,EAAAA,MACjB0G,GAAIS,cAAc9D,GAAM1D,YAAY+F,QAAQlB,QAC5CkC,GAAIU,YAAY/D,GAAM1D,YAAY+F,QAAQnB,aAC1CmC,GAAIW,UAAAA,GACJX,GAAIL,KACArG,GAAW2B,IAAI3B,GAAWU,QAAQ,GAClCV,GAAW4B,IAAI5B,GAAWW,SAAS,GACnCX,GAAWU,OACXV,GAAWW,MAAAA,GAEf+F,GAAIlC,OAAAA,IAGuB,cAAA,OAApBxE,GAAWjB,OAClBiB,GAAWjB,KAAK2H,IAAK,EACjBpG,OAAON,GAAWM,OAClBqB,GAAG3B,GAAWkD,SAASL,KAAK,CAAA,GAC5BjB,GAAG5B,GAAWkD,SAASL,KAAK,CAAA,GAC5BQ,OAAAA,GAAAA,CAAAA,KAGJqD,GAAIe,OAAUpE,GAAM1D,YAAYkD,KAAK6E,WAAAA,QAAcrE,GAAM1D,YAAYkD,KAAK8E,YAC1EjB,GAAIkB,YAAY,QAChBlB,GAAImB,eAAe,cAEnBnB,GAAIc,YAAYnE,GAAM1D,YAAYkD,KAAKyC,MACvCoB,GAAIS,cAAc9D,GAAM1D,YAAYkD,KAAK4B,cACzCiC,GAAIU,YAAkD,IAAtC/D,GAAM1D,YAAYkD,KAAKqB,cAEnCb,GAAM1D,YAAYkD,KAAKqB,eAAe,MACtCwC,GAAIoB,WAAW,SACfpB,GAAIqB,WACA/H,GAAWjB,MACXiB,GAAWkD,SAASL,KAAK,CAAA,GACzB7C,GAAWkD,SAASL,KAAK,CAAA,CAAA,GAE7B6D,GAAIoB,WAAW,UAEnBpB,GAAIsB,SAAShI,GAAWjB,MAAMiB,GAAWkD,SAASL,KAAK,CAAA,GAAI7C,GAAWkD,SAASL,KAAK,CAAA,CAAA;EAE5F,CAAA,GACA6D,GAAIuB,QAAAA;AACR;;;;;;;;;;;;;;;;;;;;;;;;ACtIO,IAAA;AAAA,IAAMC,KAAiB,SAAHC,IAAAA;AAAwE,MAAvDC,KAAID,GAAJC,MAAMC,KAAWF,GAAXE,aACxCC,KAAmBC,EAAe,EACpCC,MAAMJ,IACNC,aAAAA,IACAI,aAAa,SAAAC,IAAAA;AAAG,WAAK,EACjBC,GAAGD,GAAIC,IAAID,GAAIE,QAAQ,GACvBC,GAAGH,GAAIG,IAAIH,GAAII,SAAS,EAAA;EAC1B,GACFC,eAAe,SAAAC,IAAAA;AAAA,QAAGF,KAAME,GAANF,QAAQF,KAAKI,GAALJ;AAAK,WAAQ,EACnCA,OAAAA,IACAE,QAAAA,IACAG,MAAMC,KAAKC,IAAIP,IAAOE,EAAAA,EAAAA;EACzB,EAAA,CAAA;AAGL,aACIM,oBAAAA,KAAAC,oBAAAA,UAAA,EAAAC,UACKhB,GAAiBiB,IAAI,SAACC,IAAYC,IAAAA;AAAC,eAChCL,oBAAAA,KAACM,GAAUC,GAAaH,CAAAA,GAAAA,EAAAA,GAAPC,EAAAA;EAAAA,CAAAA,EAAAA,CAAAA;AAIjC;AAtBO,ICOMG,IAAa,SAAHzB,IAAAA;AAAA,MAAMS,KAAKT,GAALS,OAAOE,KAAMX,GAANW,QAAQe,KAAO1B,GAAP0B,SAASC,KAAW3B,GAAX2B;AAAW,aAC5DV,oBAAAA,KAAAC,oBAAAA,UAAA,EAAAC,UACKO,GAAQN,IAAI,SAAAP,IAAiBS,IAAAA;AAAC,QAAAM,GAAhBC,IAAMhB,GAAA,CAAA,GAAER,KAAIQ,GAAA,CAAA;AAAA,eACvBI,oBAAAA,KAACa,IAAYN,GAAAA,CAAAA,GAELK,GAAM,EACVE,gBAAgBtB,IAChBuB,iBAAiBrB,IACjBN,MAAiB,SAAbuB,IAAEC,EAAOxB,QAAIuB,IAAIvB,IACrBsB,aACIE,EAAOF,eAAmC,WAApBE,EAAOI,WAAsBN,KAAAA,OAAcO,CAAAA,GANhEZ,EAAAA;EAAAA,CAAAA,EAAAA,CAAAA;AAUd;ADrBA,ICqBA,IAAA,CAAA,MAAA;ADrBA,IEGMa,IAAU,SAAHnC,IAAAA;AAgCU,MAAAoC,IAAAC,KAAArC,GA/B1BO,KAAOF,KAAIgC,GAAJhC,MAASE,KAAG+B,EAAAD,IAAAE,CAAAA,GAAAC,IAAAxC,GAEnByC,OACIC,IAAWF,EAAXE,aACAC,KAAKH,EAALG,OACAhC,KAAM6B,EAAN7B,QACAiC,IAAUJ,EAAVI,YACAC,IAAYL,EAAZK,cACAC,KAAMN,EAANM,QACAC,KAAMP,EAANO,QACAC,IAASR,EAATQ,WACAvC,KAAK+B,EAAL/B,OAGJwC,KAAYjD,GAAZiD,cACAC,KAAWlD,GAAXkD,aAEAC,IAAKnD,GAALmD,OACAC,KAAiBpD,GAAjBoD,mBAEAC,KAAarD,GAAbqD,eACAC,KAAOtD,GAAPsD,SACAC,KAAYvD,GAAZuD,cACAC,KAAYxD,GAAZwD,cAEAC,KAAOzD,GAAPyD,SAEAC,KAAW1D,GAAX0D,aACAC,KAAS3D,GAAT2D,WACAC,KAAc5D,GAAd4D,gBACAC,KAAe7D,GAAf6D,iBAEMC,IAAQC,GAAAA,GACdC,IAA6DC,EAAAA,GAArDC,KAAoBF,EAApBE,sBAAsBC,KAAaH,EAAbG,eAAeC,IAAWJ,EAAXI,aAEvCC,QAAgBC,cAAAA,SAClB,WAAA;AAAA,WAAM,WAAA;AAAA,iBAAMC,cAAAA,eAAcd,IAAOjC,GAAAA,CAAAA,GAAOjB,IAAQF,EAAAA,CAAAA;IAAO;EAAA,GACvD,CAACoD,IAASlD,IAAKF,EAAAA,CAAAA,GAGbmE,SAAcC,cAAAA,aAChB,SAACC,IAAAA;AACU,YAAPpB,MAAAA,GAAO9B,GAAA,EAAKmB,OAAOpC,GAAIoC,MAAAA,GAAUtC,EAAAA,GAAQqE,EAAAA;EAC5C,GACD,CAACnE,IAAKF,IAAMiD,EAAAA,CAAAA,GAEVqB,SAAgBF,cAAAA,aAClB,SAACC,IAAAA;AAAiC,WAAKR,GAAqBG,EAAAA,GAAiBK,EAAAA;EAAM,GACnF,CAACR,IAAsBG,CAAAA,CAAAA,GAErBO,SAAmBH,cAAAA,aACrB,SAACC,IAAAA;AAAAA,YACGnB,MAAAA,GAAelD,IAAMqE,EAAAA,GACrBR,GAAqBG,EAAAA,GAAiBK,EAAAA;EACzC,GACD,CAACrE,IAAMkD,IAAcW,IAAsBG,CAAAA,CAAAA,GAEzCQ,SAAmBJ,cAAAA,aACrB,SAACC,IAAAA;AAAAA,YACGlB,MAAAA,GAAenD,IAAMqE,EAAAA,GACrBN,EAAAA;EACH,GACD,CAAC/D,IAAM+D,GAAaZ,EAAAA,CAAAA,GAIlBsB,SAAcL,cAAAA,aAAY,WAAA;AAC5BN,IAAAA,GAAcE,EAAAA,GAAiB,CAAC9D,GAAIwE,OAAOxE,GAAIE,QAAQ,GAAGF,GAAIyE,IAAAA,CAAAA;EACjE,GAAE,CAACb,IAAeE,GAAe9D,EAAAA,CAAAA,GAC5B0E,SAAaR,cAAAA,aAAY,WAAA;AAC3BL,MAAAA;EACJ,GAAG,CAACA,CAAAA,CAAAA;AAEJ,aACIc,oBAAAA,MAACC,SAASC,GAAC,EAACpC,WAAWA,GAAU7B,UAC7BF,KAAAA,oBAAAA,KAACkE,SAASE,MAAI,EACV5E,OAAO6E,GAAG7E,IAAO,SAAA8E,IAAAA;AAAK,WAAIxE,KAAKC,IAAIuE,IAAO,CAAA;EAAA,CAAA,GAC1C5E,QAAQ2E,GAAG3E,IAAQ,SAAA4E,IAAAA;AAAK,WAAIxE,KAAKC,IAAIuE,IAAO,CAAA;EAAA,CAAA,GAC5CC,IAAIvC,IACJwC,IAAIxC,IACJyC,MAAe,SAAXtD,KAAE/B,GAAKqF,QAAItD,KAAIO,IACnBgD,aAAazC,IACb0C,QAAQlD,GACRmD,WAAWnC,IACXoC,UAAUpC,KAAc,IAAA,QACxB,cAAYC,KAAYA,GAAUtD,EAAAA,IAAAA,QAClC,mBAAiBuD,KAAiBA,GAAevD,EAAAA,IAAAA,QACjD,oBAAkBwD,KAAkBA,GAAgBxD,EAAAA,IAAAA,QACpDkD,cAAcF,KAAgBuB,KAAAA,QAC9BmB,aAAa1C,KAAgBsB,KAAAA,QAC7BnB,cAAcH,KAAgBwB,KAAAA,QAC9BvB,SAASD,KAAgBmB,KAAAA,QACzBwB,SAAS3C,MAAiBK,KAAcoB,KAAAA,QACxCmB,QAAQ5C,MAAiBK,KAAcuB,KAAAA,OAAa/C,CAAAA,GAEvDkB,UACGnC,oBAAAA,KAACkE,SAASe,MAAI,EACV1F,GAAGsC,IACHpC,GAAGqC,IACHoD,YAAW,UACXC,kBAAiB,WACjBC,aAAaxD,GACbJ,OAAKjB,GAAA,CAAA,GACEsC,EAAMwC,OAAOJ,MAAI,EACpBK,eAAe,QACfb,MAAM9C,EAAAA,CAAAA,GACRzB,UAEDgC,EAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAKrB;AFrHO,IEqHP,IAAA,CAAA,SAAA,OAAA;AFrHO,IGAMqD,IAAa,SAAHxG,IAAAA;AAAwE,MAAvD2C,KAAK3C,GAAL2C,OAAOQ,KAAKnD,GAALmD,OAAU9C,KAAIiC,EAAAtC,IAAAuC,CAAAA;AACzD,aAAOtB,oBAAAA,KAACwF,GAAY,EAACC,IAAIvD,IAAOoC,OAAOlF,GAAKsG,gBAAgBC,YAAAA,MAAkBjE,OAAOA,GAAAA,CAAAA;AACzF;AHFO,IIGMkE,KAAe,EACxBC,SAAS,MACTC,MAAM,CAAC,OAAA,GAEPC,WAAW,WACXC,QAAQ,YACRC,SAAAA,OAEAC,UAAU,QACVC,UAAU,QAEVC,YAAY,EAAEC,MAAM,SAAA,GACpBC,YAAY,EAAED,MAAM,QAAQE,OAAAA,KAAO,GAEnCC,SAAS,KACTC,cAAc,GAEdC,YAAY,CAAE,GACdC,UAAU,CAAE,GACZC,aAAAA,OACAC,aAAAA,MAEAC,aAAAA,MACA5E,OAAO,kBACP6E,gBAAgB,GAChBC,iBAAiB,GACjBC,gBAAgB,EAAEC,MAAM,SAASrE,OAAO,mBAAA,GAExCsE,SAAS,MACTC,QAAQ,EAAEC,QAAQ,OAAA,GAElBrF,cAAc,GACdC,aAAa,GACbR,aAAa,EAAEyF,MAAM,QAAA,GAErB9E,eAAAA,MACAI,SAAS+C,GACT+B,cAAc,SAAWC,IAAAA;AAA8B,SAAQA,GAAM9B,KAAQ8B,QAAAA,GAAMC;AAAY,GAE/F/G,SAAS,CAAA,GACTgH,kBAAkB,CAAA,GAClBxI,aAAa,CAAA,GACbyI,SAAS,CAAA,EAAA;AJ7CN,IIgDMC,KAAepH,GAAAA,CAAAA,GACrBqF,IAAY,EACfgC,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,WAAW,WAAW,aAAA,GACvDC,cAAc3G,GAEd4G,MAAM,CAAA,GACNrD,MAAM,CAAA,GAENsD,SAAAA,MACAC,cAAc,WAEdC,MAAM,OACNxF,aAAAA,MAAa,CAAA;AJ5DV,II+DMyF,KAAkB3H,GAAAA,CAAAA,GACxBqF,IAAY,EACfgC,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,WAAW,aAAA,GAE5CO,YAA8B,eAAA,OAAXC,UAAgD,SAA1BC,IAAGD,OAAOE,oBAAgBD,IAAQ,EAAA,CAAA;AJnExE,IKEME,KAAgB,SACzBnJ,IACAoJ,IACAhC,IACAF,IACAzG,IACA4I,GAAAA;AAEA,SACIC,GACIpC,IACA,EAAEqC,KAAKvJ,GAAKe,IAAIqI,EAAAA,GAAWI,KAAK,GAAG7I,KAAK,EAAA,GACxCF,IACA4I,CAAAA,EAENjC,QAAQA,EAAAA;AACd;ALlBO,IKuBMqC,KAAgB,SAAWzJ,IAAkB0G,IAAAA;AAAc,SACpE1G,GAAKe,IACD,SAAA2I,IAAAA;AAAI,WAAAvI,GAEOuF,CAAAA,GAAAA,GAAKiD,OAAgC,SAACC,IAAKC,IAAAA;AAE1C,aADAD,GAAIC,EAAAA,IAAO,MACJD;IACX,GAAG,CAAE,CAAA,GACFF,EAAAA;EAAI,CAAA;AAElB;ALjCE,IKmCMI,KAAmB,SAA2C9J,IAAAA;AAAc,SACrF+J,OAAOrD,KAAK1G,EAAAA,EAAM2J,OAAgC,SAACC,IAAKC,IAAAA;AAIpD,WAHI7J,GAAK6J,EAAAA,MACLD,GAAIC,EAAAA,IAAO7J,GAAK6J,EAAAA,IAEbD;EACV,GAAE,CAAE,CAAA;AAAC;ALzCH,IK2CMI,KAAc,SAAI9E,IAAAA;AAAQ,SAAK,CAACA,IAAO+E,OAAO/E,EAAAA,CAAAA;AAAO;AL3C3D,IK2C2D,KAAA,CAAA,UAAA,YAAA,YAAA,WAAA,SAAA,UAAA,WAAA,gBAAA,cAAA,cAAA,WAAA;AL3C3D,IMgBDgF,KAAK,SAAChF,IAAeiF,IAAAA;AAAa,SAAKjF,KAAQiF;AAAK;ANhBnD,IMiBDC,KAAK,SAAClF,IAAeiF,IAAAA;AAAa,SAAKjF,KAAQiF;AAAK;ANjBnD,IMmBDE,KAAQ,SAACC,IAAeC,IAAAA;AAAW,SACrCC,MAAM1C,KAAK,IAAI2C,OAAOF,KAAMD,EAAAA,GAAQ,SAACI,IAAGC,IAAAA;AAAK,WAAKL,KAAQK;EAAAA,CAAAA;AAAM;ANpB7D,IMsBDC,KAAc,SAAC1F,IAAAA;AAAa,SAAMgF,GAAGhF,IAAO,CAAA,IAAK,IAAIA;AAAK;ANtBzD,IM4BD2F,KAA8B,SAAHlL,IAa7BmL,IACAjE,IACAkE,IAAAA;AAC+B,MAd3B/K,KAAIL,GAAJK,MACAgL,IAAWrL,GAAXqL,aACAC,IAAQtL,GAARsL,UACA7B,KAAQzJ,GAARyJ,UACA8B,KAAevL,GAAfuL,iBAAeC,KAAAxL,GACf0H,cAAAA,KAAAA,WAAY8D,KAAG,IAACA,IAChBzE,IAAI/G,GAAJ+G,MACA0E,IAAMzL,GAANyL,QACAC,KAAM1L,GAAN0L,QACAC,KAAM3L,GAAN2L,QAMEC,KAAU1E,KAAUuD,KAAKF,IAGzBsB,IAAcxL,GAAKe,IAAI+I,EAAAA,GAEvBlK,KAAqC,CAAA;AAkC3C,SAjCA8G,EAAK+E,QAAQ,SAAC5B,IAAK5I,IAAAA;AAAC,WAChBoJ,GAAM,GAAGe,EAAOM,OAAAA,EAASC,MAAAA,EAAQF,QAAQ,SAAAd,IAAAA;AAAS,UAAAiB,IANxCC,IAASC,IAOfC,IAA0B/B,GAAYhK,GAAK2K,EAAAA,EAAOd,EAAAA,CAAAA,GAA3CmC,KAAQD,EAAA,CAAA,GAAE7G,KAAK6G,EAAA,CAAA,GAChB3D,KAAagB,GAASpJ,GAAK2K,EAAAA,CAAAA,GAC3BxK,MAAuB,SAAnByL,KAACR,EAAOhD,EAAAA,KAAWwD,KAAI,KAAKd,KAAW7J,KAAIoG,KAAepG,IAC9DZ,KAVekL,GAAfM,KAUS3G,IAViB,CAAA,IAAc,SAAZ4G,KAAGT,GAAOQ,EAAAA,KAAEC,KAAI,IAAIf,IAWhDkB,KAVI,SAACJ,IAAWxL,IAAAA;AAAS,YAAA6L;AAAA,eAAMX,GAAQM,IAAG,CAAA,IAAKd,KAAO1K,MAAAA,SAAI6L,KAACb,GAAOQ,EAAAA,KAAEK,KAAI,KAAKnB;MAAAA,EAUvD7F,IAAO7E,EAAAA,GAC7B8L,KAAmC,EACrC9F,IAAIwD,IACJ3E,OAAoB,SAAb8G,KAAoBA,KAAW9G,IACtCoB,gBAAgB0E,EAAY9F,EAAAA,GAC5BkH,QAAAA,OACAzB,OAAAA,IACAvC,YAAAA,IACApI,MAAMwL,EAAYb,EAAAA,EAAAA;AAGtB/K,MAAAA,GAAKyM,KAAK,EACNxC,KAAQA,KAAOsC,MAAAA,GAAQ/D,YACvBuC,OAAO/K,GAAK+L,QACZ3L,MAAMmM,IACNhM,GAAAA,IACAE,GAAAA,IACAqE,MAAM4G,GAAOgB,OAAOnM,IACpBwE,MAAM2G,GAAOiB,MAAMlM,IACnBD,OAAO0K,IACPxK,QAAQ2L,IACR3J,OAAO2I,EAASkB,EAAAA,GAChBrJ,OAAOoI,GAAgBiB,EAAAA,EAAAA,CAAAA;IAE/B,CAAA;EAAE,CAAA,GAGCvM;AACX;ANrFO,IM0FD4M,KAAgC,SAAHhM,IAa/ByL,IACApF,IACA4F,IAAAA;AAC+B,MAd3BzM,KAAIQ,GAAJR,MACAgL,IAAWxK,GAAXwK,aACA5B,IAAQ5I,GAAR4I,UACA6B,KAAQzK,GAARyK,UACAC,KAAe1K,GAAf0K,iBACAxE,KAAIlG,GAAJkG,MAAIgG,KAAAlM,GACJ6G,cAAAA,IAAAA,WAAYqF,KAAG,IAACA,IAChBtB,IAAM5K,GAAN4K,QACAC,KAAM7K,GAAN6K,QACAC,KAAM9K,GAAN8K,QAMEC,KAAU1E,KAAUuD,KAAKF,IAGzBsB,IAAcxL,GAAKe,IAAI+I,EAAAA,GAEvBlK,KAAqC,CAAA;AAkC3C,SAjCA8G,GAAK+E,QAAQ,SAAC5B,IAAK5I,IAAAA;AAAC,WAChBoJ,GAAM,GAAGgB,GAAOK,OAAAA,EAASC,MAAAA,EAAQF,QAAQ,SAAAd,IAAAA;AAAS,UAAAgC,IANxCd,IAASe,IAOfC,IAA0B7C,GAAYhK,GAAK2K,EAAAA,EAAOd,EAAAA,CAAAA,GAA3CmC,KAAQa,EAAA,CAAA,GAAE3H,KAAK2H,EAAA,CAAA,GAChBzE,KAAagB,EAASpJ,GAAK2K,EAAAA,CAAAA,GAC3BxK,KATeoL,GAAfM,KASS3G,IATiB,CAAA,IAAKuH,KAAgBG,SAAZA,KAAGxB,EAAOS,EAAAA,KAAEe,KAAI,GAUnDvM,MAAuB,SAAnBsM,KAACtB,GAAOjD,EAAAA,KAAWuE,KAAI,KAAKV,KAAYhL,KAAIoG,IAAepG,IAC/D6J,KAVG,SAACe,IAAW1L,IAAAA;AAAS,YAAA2M;AAAA,eAAMvB,GAAQM,IAAG,CAAA,KAAeiB,SAAVA,KAAC1B,EAAOS,EAAAA,KAAEiB,KAAI,KAAKL,KAAOA,KAAOtM;MAAAA,EAU3D+E,IAAO/E,EAAAA,GAC3BgM,KAAmC,EACrC9F,IAAIwD,IACJ3E,OAAoB,SAAb8G,KAAoBA,KAAW9G,IACtCoB,gBAAgB0E,EAAY9F,EAAAA,GAC5BkH,QAAAA,OACAzB,OAAAA,IACAvC,YAAAA,IACApI,MAAMwL,EAAYb,EAAAA,EAAAA;AAGtB/K,MAAAA,GAAKyM,KAAK,EACNxC,KAAQA,KAAOsC,MAAAA,GAAQ/D,YACvBuC,OAAO/K,GAAK+L,QACZ3L,MAAMmM,IACNhM,GAAAA,IACAE,GAAAA,IACAqE,MAAM4G,GAAOgB,OAAOnM,IACpBwE,MAAM2G,GAAOiB,MAAMlM,IACnBD,OAAO0K,IACPxK,QAAQ2L,IACR3J,OAAO2I,GAASkB,EAAAA,GAChBrJ,OAAOoI,GAAgBiB,EAAAA,EAAAA,CAAAA;IAE/B,CAAA;EAAE,CAAA,GAGCvM;AACX;ANnJO,IMwJMmN,KAAsB,SAAHC,IAAAA;AAkC1B,MAAAC,IAnKmB/H,IAkIrB0B,KAAMoG,GAANpG,QACAE,KAAQkG,GAARlG,UACAC,IAAQiG,GAARjG,UACAF,IAAOmG,GAAPnG,SACAzG,KAAK4M,GAAL5M,OACAE,KAAM0M,GAAN1M,QAAM4M,KAAAF,GACN5F,SAAAA,KAAAA,WAAO8F,KAAG,IAACA,IAAAC,IAAAH,GACX3F,cAAAA,IAAAA,WAAY8F,IAAG,IAACA,GAChBnG,KAAUgG,GAAVhG,YACYoG,KAAgBJ,GAA5B9F,YAAUmG,KAAAL,GACVM,WAAAA,IAAAA,WAASD,KAAG,CAAA,IAAEA,IACXE,KAAKtL,EAAA+K,IAAA9K,EAAAA,GAuBFwE,KAAO6G,GAAM7G,KAAK8G,OAAO,SAAA3D,IAAAA;AAAG,WAAA,CAAKyD,EAAUG,SAAS5D,EAAAA;EAAAA,CAAAA,GACpD7J,KAAOyJ,GAAc8D,GAAMvN,MAAM0G,EAAAA,GACvCgH,IACe,eAAX9G,KAAyB,CAAC,KAAK,KAAKxG,EAAAA,IAAoB,CAAC,KAAK,KAAKE,EAAAA,GADhE+I,KAAIqE,EAAA,CAAA,GAAEC,KAASD,EAAA,CAAA,GAAEjN,KAAIiN,EAAA,CAAA,GAEtBxG,KAAaiC,GACfnJ,IACAuN,GAAMnE,UACNhC,IACAgG,IACA3M,IACAkN,EAAAA,GAGEC,KAASzM,GAAA,EACXR,KAAKoG,GACLyC,KAAK1C,IACLD,SAAAA,EAAAA,GACGG,EAAAA,GAGD6G,KAA6B,WAAlBD,GAAUpE,MAAiBoB,KAAc,SAAC1F,IAAAA;AAAa,WAAKA;EAAK,GAE5E4I,KAAS9N,GACV2J,OAAiB,SAACC,IAAKmE,IAAAA;AAAK,WAAAC,CAAAA,EAAAA,OAASpE,IAAQlD,GAAK3F,IAAI,SAAAkN,IAAAA;AAAC,aAAIF,GAAME,EAAAA;IAAY,CAAA,CAAA;EAAE,GAAE,CAAA,CAAA,EACjFT,OAAOU,OAAAA,GACN1E,KAAMqE,GAASnN,KAAK8I,IAAG2E,MAARzN,MAAYoN,EAAAA,CAAAA,GAC3BnN,MA9LeuE,KA8LOxE,KAAKC,IAAGwN,MAARzN,MAAYoN,EAAAA,GA9LAM,SAASlJ,EAAAA,IAASA,KAAQ,IAgM5DmJ,KAAQ/E,GACVsE,IACA,EAAErE,KAAKuE,IAAQtE,KAAAA,IAAK7I,KAAAA,GAAAA,GACX,QAAT0I,KAAejJ,KAAQE,IACvB+I,EAAAA,GAGJiF,KAAoC,eAAX1H,KAAwB,CAACM,IAAYmH,EAAAA,IAAS,CAACA,IAAOnH,EAAAA,GAAxEkE,KAAMkD,GAAA,CAAA,GAAEjD,KAAMiD,GAAA,CAAA,GAEfC,MAAarH,GAAWqH,UAAAA,IAAclH,KAAgBX,GAAKiF,SAAS,MAAMjF,GAAKiF,QAC/E6C,KAAS,CAAArN,GAAAA,CAAAA,GACNoM,IAAK,EAAEvN,MAAAA,IAAM0G,MAAAA,IAAMW,cAAAA,GAAc+D,QAAAA,IAAQC,QAAAA,GAAAA,CAAAA,GAC9CkD,IACAX,GAAU/G,SACF,SADSoG,KACjBoB,GAAM,CAAA,KAAEpB,KAAI,CAAA;AAUhB,SAAO,EAAE7B,QAAAA,IAAQC,QAAAA,IAAQzL,MANrB2O,KAAY,IACK,eAAX3H,KACIiE,GAA2BsD,MAAAA,QAAIK,EAAAA,IAC/BhC,GAA6B2B,MAAAA,QAAIK,EAAAA,IACrC,CAAA,EAAA;AAGd;ANhPO,IMgPP,KAAA,CAAA,QAAA,UAAA,YAAA,YAAA,WAAA,SAAA,UAAA,WAAA,cAAA,cAAA,WAAA;ANhPO,IOkBDC,KAAc,SAAdA,GAAkBC,IAAAA;AAAQ,MAAA/O;AAAA,SAC5B+O,GAAIC,KAAKnE,MAAMoE,OAAAA,IAAWH,IAAY9O,KAAC,CAAA,GAAWqO,OAAMG,MAAAxO,IAAI+O,EAAAA,CAAAA,IAASA;AAAoB;APnBtF,IO2BDG,KAA8B,SAAHrO,IAY7BsK,IACAjE,IAAAA;AAC+B,MAZ3BmE,KAAWxK,GAAXwK,aACAC,KAAQzK,GAARyK,UACA7B,IAAQ5I,GAAR4I,UACA8B,IAAe1K,GAAf0K,iBACA7D,KAAY7G,GAAZ6G,cACAyH,KAAWtO,GAAXsO,aACA1D,KAAM5K,GAAN4K,QACAC,KAAM7K,GAAN6K,QACAC,IAAM9K,GAAN8K,QAQE1L,IAAqC,CAAA;AAmC3C,SAlCAkP,GAAYrD,QAAQ,SAAAsD,IAAAA;AAAe,WAC/B3D,GAAOM,OAAAA,EAASD,QAAQ,SAACd,IAAO1J,IAAAA;AAAM,UAAA2K,IAAAoD,IAC5BnD,IAAIkD,GAAgB9N,EAAAA,GACpBd,KAA4ByL,SAA3BA,KAAGR,GAAOhC,EAASyC,EAAE7L,IAAAA,CAAAA,KAAM4L,KAAI,GAChCvL,MAAY,SAAR2O,KARL,SAACnD,IAAAA;AAAuB,eAAKR,GAAOQ,GAAEhF,KAAU,IAAI,CAAA,CAAA;MAAG,EAQ5CgF,CAAAA,KAAEmD,KAAI,KAAoB,MAAf3H,IACrB4E,KARI,SAACJ,IAAyBxL,IAAAA;AAAS,YAAAyL;AAAA,gBAAgC,SAA3BA,KAACT,GAAOQ,GAAEhF,KAAU,IAAI,CAAA,CAAA,KAAGiF,KAAI,KAAKzL;MAAC,EAQ3DwL,GAAGxL,EAAAA,IAAKgH,IACpC0E,IAA0B/B,GAAY6B,EAAE7L,KAAK+O,GAAgBlF,GAAAA,CAAAA,GAAtDmC,KAAQD,EAAA,CAAA,GAAE7G,KAAK6G,EAAA,CAAA,GAEhBI,KAAmC,EACrC9F,IAAI0I,GAAgBlF,KACpB3E,OAAoB,SAAb8G,KAAoBA,KAAW9G,IACtCoB,gBAAgB0E,GAAY9F,EAAAA,GAC5BkH,QAAAA,OACAzB,OAAO1J,IACPmH,YAAYuC,IACZ3K,MAAM8J,GAAiB+B,EAAE7L,IAAAA,EAAAA;AAG7BJ,QAAKyM,KAAK,EACNxC,KAAQkF,GAAgBlF,MAAAA,MAAOc,IAC/BA,OAAO/K,EAAK+L,QACZ3L,MAAMmM,IACNhM,GAAAA,IACAE,GAAAA,IACAqE,MAAM4G,EAAOgB,OAAOnM,IACpBwE,MAAM2G,EAAOiB,MAAMlM,IACnBD,OAAO0K,IACPxK,QAAQ2L,IACR3J,OAAO2I,GAASkB,EAAAA,GAChBrJ,OAAOoI,EAAgBiB,EAAAA,EAAAA,CAAAA;IAE/B,CAAA;EAAE,CAAA,GAGCvM;AACX;APjFO,IOsFDqP,KAAgC,SAAHjC,IAY/Bf,IACApF,IAAAA;AAC+B,MAZ3BmE,KAAWgC,GAAXhC,aACAC,KAAQ+B,GAAR/B,UACA7B,IAAQ4D,GAAR5D,UACA8B,IAAe8B,GAAf9B,iBACA7D,KAAY2F,GAAZ3F,cACAyH,KAAW9B,GAAX8B,aACA1D,KAAM4B,GAAN5B,QACAC,KAAM2B,GAAN3B,QACAC,IAAM0B,GAAN1B,QAQE1L,IAAqC,CAAA;AAmC3C,SAlCAkP,GAAYrD,QAAQ,SAAAsD,IAAAA;AAAe,WAC/B1D,GAAOK,OAAAA,EAASD,QAAQ,SAACd,IAAO1J,IAAAA;AAAM,UAAAiL,IAAAgD,IAC5BrD,IAAIkD,GAAgB9N,EAAAA,GACpBZ,KAA4B6L,SAA3BA,KAAGb,GAAOjC,EAASyC,EAAE7L,IAAAA,CAAAA,KAAMkM,KAAI,GAChC/L,MAAY,SAAR+O,KARL,SAACrD,IAAAA;AAAuB,eAAKT,GAAOS,GAAEhF,KAAU,IAAI,CAAA,CAAA;MAAG,EAQ5CgF,CAAAA,KAAEqD,KAAI,KAAoB,MAAf7H,IACrByD,KARG,SAACe,IAAyB1L,IAAAA;AAAS,YAAAyM;AAAA,gBAAgC,SAA3BA,KAACxB,GAAOS,GAAEhF,KAAU,IAAI,CAAA,CAAA,KAAG+F,KAAI,KAAKzM;MAAC,EAQ5D0L,GAAG1L,EAAAA,IAAKkH,IAClCwF,IAA0B7C,GAAY6B,EAAE7L,KAAK+O,GAAgBlF,GAAAA,CAAAA,GAAtDmC,KAAQa,EAAA,CAAA,GAAE3H,KAAK2H,EAAA,CAAA,GAEhBV,KAAmC,EACrC9F,IAAI0I,GAAgBlF,KACpB3E,OAAoB,SAAb8G,KAAoBA,KAAW9G,IACtCoB,gBAAgB0E,GAAY9F,EAAAA,GAC5BkH,QAAAA,OACAzB,OAAO1J,IACPmH,YAAYuC,IACZ3K,MAAM8J,GAAiB+B,EAAE7L,IAAAA,EAAAA;AAG7BJ,QAAKyM,KAAK,EACNxC,KAAQkF,GAAgBlF,MAAAA,MAAOc,IAC/BA,OAAO/K,EAAK+L,QACZ3L,MAAMmM,IACNhM,GAAAA,IACAE,GAAAA,IACAqE,MAAM4G,EAAOgB,OAAOnM,IACpBwE,MAAM2G,EAAOiB,MAAMlM,IACnBD,OAAO0K,IACPxK,QAAQ2L,IACR3J,OAAO2I,GAASkB,EAAAA,GAChBrJ,OAAOoI,EAAgBiB,EAAAA,EAAAA,CAAAA;IAE/B,CAAA;EAAE,CAAA,GAGCvM;AACX;AP5IO,IOiJMuP,KAAsB,SAAHzB,IAAAA;AAkC1B,MA9JoB0B,IA6HtBpP,KAAI0N,GAAJ1N,MACA4G,KAAM8G,GAAN9G,QACAE,KAAQ4G,GAAR5G,UACAC,IAAQ2G,GAAR3G,UACAF,IAAO6G,GAAP7G,SACAzG,KAAKsN,GAALtN,OACAE,KAAMoN,GAANpN,QAAM+O,KAAA3B,GACNtG,SAAAA,KAAAA,WAAOiI,KAAG,IAACA,IACXrI,IAAU0G,GAAV1G,YACYoG,IAAgBM,GAA5BxG,YAAUoI,KAAA5B,GACVJ,WAAAA,KAAAA,WAASgC,KAAG,CAAA,IAAEA,IACX/B,KAAKtL,EAAAyL,IAAAxL,EAAAA,GAuBFwE,IAAO6G,GAAM7G,KAAK8G,OAAO,SAAA3D,IAAAA;AAAG,WAAA,CAAKyD,GAAUG,SAAS5D,EAAAA;EAAAA,CAAAA,GACpDiF,KAAcS,cAAAA,EAA0B7I,KAAKA,CAAAA,EAAM8I,OAAOC,iBAAAA,EAC5DhG,GAAczJ,IAAM0G,CAAAA,CAAAA,GAGxB4H,KACe,eAAX1H,KAAyB,CAAC,KAAK,KAAKxG,EAAAA,IAAoB,CAAC,KAAK,KAAKE,EAAAA,GADhE+I,KAAIiF,GAAA,CAAA,GAAEX,IAASW,GAAA,CAAA,GAAE7N,KAAI6N,GAAA,CAAA,GAEtBpH,KAAaiC,GACfnJ,IACAuN,GAAMnE,UACNhC,IACAgG,GACA3M,IACAkN,CAAAA,GAGEC,KAASzM,GAAA,EACXR,KAAKoG,GACLyC,KAAK1C,IACLD,SAAAA,EAAAA,GACGG,CAAAA,GAGD8G,MAtLgBsB,KAuLlBX,GAAYK,EAAAA,GAtLP,UAuLL9H,EAAWC,OAvLEmI,GAAM5B,OAAO,SAAAkC,IAAAA;AAAG,WAAY,MAARA;EAAS,CAAA,IAAIN,KAyL5C5F,KAAM9I,KAAK8I,IAAG2E,MAARzN,MAAYoN,EAAAA,GAClBnN,KAAMD,KAAKC,IAAGwN,MAARzN,MAAYoN,EAAAA,GAElBO,KAAQ/E,GACVsE,IACA,EAAErE,KAAKuE,IAAQtE,KAAAA,IAAK7I,KAAAA,GAAAA,GACX,QAAT0I,KAAejJ,KAAQE,IACvB+I,EAAAA,GAGJsG,KAAoC,eAAX/I,KAAwB,CAACM,IAAYmH,EAAAA,IAAS,CAACA,IAAOnH,EAAAA,GAAxEkE,KAAMuE,GAAA,CAAA,GAAEtE,KAAMsE,GAAA,CAAA,GAEftI,KAAekG,GAAMlG,eAAe,IAAIkG,GAAMlG,eAAe,GAC7DkH,KAAYrH,GAAWqH,UAAAA,GACvBC,KAAS,CAAArN,GAAAA,CAAAA,GACNoM,IAAK,EAAElG,cAAAA,IAAcyH,aAAAA,IAAa1D,QAAAA,IAAQC,QAAAA,GAAAA,CAAAA,GAC/CkD,IACAX,GAAU/G,OAAAA;AAUd,SAAO,EAAEuE,QAAAA,IAAQC,QAAAA,IAAQzL,MANrB2O,KAAY,IACK,eAAX3H,KACIiI,GAA2BV,MAAAA,QAAIK,EAAAA,IAC/BS,GAA6Bd,MAAAA,QAAIK,EAAAA,IACrC,CAAA,EAAA;AAGd;AP3OO,IQ6DMoB,KAAgB,SAAH5C,IAAAA;AAapB,MAZFpN,KAAIoN,GAAJpN,MACAiQ,KAAS7C,GAAT6C,WACA/H,KAAIkF,GAAJlF,MACAnB,KAASqG,GAATrG,WACAC,IAAMoG,GAANpG,QACAkJ,IAAW9C,GAAX8C,aACAjJ,KAAOmG,GAAPnG,SAOMkJ,KAAiBC,GACnBF,QAAAA,IAAAA,IAAyB,cAAThI,KAAqB,eAAe,IAAA;AAGxD,SAAa,cAATA,KAxC+B,SACnClI,IACAgH,IACAmJ,IAAAA;AAEA,QAAM/P,SAAOiQ,cAAAA,SACTrQ,GAAKmB,IAAI,SAAAb,IAAAA;AAAG,UAAAgQ,IAAAC;AAAA,aAAK,EACb9J,IAAuB,SAArB6J,KAAEhQ,GAAIF,KAAKoI,cAAU8H,KAAI,IAC3BpN,OAAOiN,GAAe7P,GAAIF,IAAAA,GAC1BoM,QAAQlM,GAAIF,KAAKoM,QACjB9J,OAAgB,SAAX6N,KAAEjQ,GAAIoC,SAAK6N,KAAI,OAAA;IAAA,CAAA,GAExB,SAAA3P,IAAAA;AAAK,aAAAA,GAAF6F;IAAW,CAAA;AAOlB,WAJe,iBAAXO,MACA5G,GAAK6G,QAAAA,GAGF7G;EACX,EAqBuCJ,IAAMgH,GAAQmJ,EAAAA,IAxEjB,SAChCnQ,IACAgH,IACAiJ,IACAlJ,IACAE,IACAkJ,IAAAA;AAEA,QAAM/P,SAAOiQ,cAAAA,SACTrQ,GAAKmB,IAAI,SAAAb,IAAAA;AAAG,UAAAkQ;AAAA,aAAK,EACb/J,IAAInG,GAAIF,KAAKqG,IACbvD,OAAOiN,GAAe7P,GAAIF,IAAAA,GAC1BoM,QAAQlM,GAAIF,KAAKoM,QACjB9J,OAAgB,SAAX8N,KAAElQ,GAAIoC,SAAK8N,KAAI,OAAA;IAAA,CAAA,GAExB,SAAAzQ,IAAAA;AAAK,aAAAA,GAAF0G;IAAW,CAAA;AAalB,YATgB,eAAXO,MACiB,cAAdD,MACc,aAAdkJ,MAAAA,SACAhJ,MACQ,iBAAXD,MAAyC,cAAdD,MAAAA,SAA2BE,OAEvD7G,GAAK6G,QAAAA,GAGF7G;EACX,EA8CgCJ,IAAMgH,GAAQiJ,IAAWlJ,IAAWE,IAASkJ,EAAAA;AAC7E;ARpFO,ISWMM,KAAS,SAAH1Q,IAAAA;AA0Db,MAAA2Q,KAAA3Q,GAzDF8G,SAAAA,KAAAA,WAAO6J,KAAG9J,GAAaC,UAAO6J,IAAAC,KAAA5Q,GAC9B+G,MAAAA,KAAAA,WAAI6J,KAAG/J,GAAaE,OAAI6J,IAAAC,IAAA7Q,GACxBmD,OAAAA,IAAAA,WAAK0N,IAAGhK,GAAa1D,QAAK0N,GAAAC,KAAA9Q,GAC1BuI,cAAAA,KAAAA,WAAYuI,KAAGjK,GAAa0B,eAAYuI,IACxCC,KAAW/Q,GAAX+Q,aAAWC,KAAAhR,GACXqI,QAAAA,IAAAA,WAAM2I,KAAGnK,GAAawB,SAAM2I,IAAAC,IAAAjR,GAC5BoI,SAAAA,KAAAA,WAAO6I,IAAGpK,GAAauB,UAAO6I,GAAAC,KAAAlR,GAC9B0C,aAAAA,KAAAA,WAAWwO,KAAGrK,GAAanE,cAAWwO,IAAAC,KAAAnR,GACtCkI,gBAAAA,IAAAA,WAAciJ,KAAGtK,GAAaqB,iBAAciJ,IAAAC,KAAApR,GAC5CgH,WAAAA,KAAAA,WAASoK,KAAGvK,GAAaG,YAASoK,IAAAC,KAAArR,GAClCiH,QAAAA,KAAAA,WAAMoK,KAAGxK,GAAaI,SAAMoK,IAAAC,KAAAtR,GAC5BkH,SAAAA,KAAAA,WAAOoK,KAAGzK,GAAaK,UAAOoK,IAC9BjR,KAAIL,GAAJK,MAAIkR,KAAAvR,GACJmH,UAAAA,KAAAA,WAAQoK,KAAG1K,GAAaM,WAAQoK,IAAAC,KAAAxR,GAChCoH,UAAAA,KAAAA,WAAQoK,KAAG3K,GAAaO,WAAQoK,IAChC7F,KAAM3L,GAAN2L,QACAlL,IAAKT,GAALS,OACAE,KAAMX,GAANW,QAAM8Q,IAAAzR,GACNyH,SAAAA,IAAAA,WAAOgK,IAAG5K,GAAaY,UAAOgK,GAAAjG,IAAAxL,GAC9B0H,cAAAA,KAAAA,WAAY8D,IAAG3E,GAAaa,eAAY8D,GAAAkG,KAAA1R,GACxCqH,YAAAA,KAAAA,WAAUqK,KAAG7K,GAAaQ,aAAUqK,IAAAC,KAAA3R,GACpCuH,YAAAA,KAAAA,WAAUoK,KAAG9K,GAAaU,aAAUoK,IAAAC,KAAA5R,GACpC0I,kBAAAA,KAAAA,WAAgBkJ,KAAG/K,GAAa6B,mBAAgBkJ,IAAAC,KAAA7R,GAChD+H,aAAAA,KAAAA,WAAW8J,KAAGhL,GAAakB,cAAW8J,IAAAC,KAAA9R,GACtCgI,gBAAAA,MAAAA,WAAc8J,KAAGjL,GAAamB,iBAAc8J,IAAAC,MAAA/R,GAC5CiI,iBAAAA,MAAAA,WAAe8J,MAAGlL,GAAaoB,kBAAe8J,KAAAC,MAAAhS,GAC9C0B,SAAAA,MAAAA,WAAOsQ,MAAGnL,GAAanF,UAAOsQ,KAC9B7B,MAAWnQ,GAAXmQ,aA+BA8B,UAAkCC,cAAAA,UAASxJ,QAAAA,KAAAA,KAAoB,CAAA,CAAA,GAAxDiF,MAASsE,IAAA,CAAA,GAAEE,MAAYF,IAAA,CAAA,GACxBtQ,UAAc8C,cAAAA,aAAY,SAACiC,IAAAA;AAC7ByL,IAAAA,IAAa,SAAAC,IAAAA;AAAK,aACdA,GAAMC,QAAQ3L,EAAAA,IAAAA,KAAW0L,GAAMvE,OAAO,SAAA9D,IAAAA;AAAI,eAAIA,OAASrD;MAAE,CAAA,IAAA,CAAA,EAAC2H,OAAO+D,IAAK,CAAE1L,EAAAA,CAAAA;IAAG,CAAA;EAElF,GAAE,CAAA,CAAA,GAEG+C,MAAW6I,GAAoBxL,EAAAA,GAC/ByL,MAAWD,GAAoBnP,CAAAA,GAC/BoI,MAAkB+G,GAAoB/J,EAAAA,GACtC8C,MAAcmH,GAAkBzB,EAAAA,GAEhCjN,MAAQC,GAAAA,GACRuH,MAAWmH,GAAqBpK,GAAQD,EAAAA,GACxCsK,MAAiBC,GACnBjQ,IACAoB,GAAAA,GAEE8O,MAAgBD,GAClBzK,GACApE,GAAAA,GAIJ+O,OADmC,cAAd7L,KAA0BoG,KAAsBoC,IACvB,EAC1CvI,QAAAA,IACAC,SAAAA,IACA7G,MAAAA,IACAoJ,UAAAA,KACA1C,MAAAA,IACAI,UAAAA,IACAC,UAAAA,IACA3G,OAAAA,GACAE,QAAAA,IACA2K,UAAAA,KACA7D,SAAAA,GACAC,cAAAA,IACAL,YAAAA,IACAE,YAAAA,IACAoG,WAAAA,KACAtC,aAAAA,KACAE,iBAAAA,KACAI,QAAAA,GAAAA,CAAAA,GAlBI1L,MAAI4S,IAAJ5S,MAAMwL,MAAMoH,IAANpH,QAAQC,MAAMmH,IAANnH,QAqBhBoH,UAAgBxO,cAAAA,SAClB,WAAA;AAAA,WACIrE,IACK4N,OACG,SAACtN,IAAAA;AAAG,aAAoE,SAAnBA,GAAIF,KAAKkF;IAClE,CAAA,EACCnE,IAAI,SAACb,IAAKyK,IAAAA;AAAK,aAAAxJ,GAAAA,CAAAA,GACTjB,IAAG,EACNyK,OAAAA,GAAAA,CAAAA;IAAK,CAAA;EAAA,GAEjB,CAAC/K,GAAAA,CAAAA,GAGC8S,UAAuBtO,cAAAA,aACzB,SAAA5D,IAAAA;AAA0D,QAAvDJ,KAAKI,GAALJ,OAAOE,KAAME,GAANF;AACN,WAAA,CAAA,CAAKoH,OAAAA,EACDC,MAAiB,KAAKvH,KAAQuH,QAAAA,EAC9BC,MAAkB,KAAKtH,KAASsH;EAEvC,GACD,CAACF,IAAaC,KAAgBC,GAAAA,CAAAA,GAG5B+K,UAAa1O,cAAAA,SACf,WAAA;AAAA,WACIyC,GAAK3F,IAAI,SAAA8I,IAAAA;AACL,UAAM3J,KAAMN,IAAKgT,KAAK,SAAA1S,IAAAA;AAAG,eAAIA,GAAIF,KAAKqG,OAAOwD;MAAAA,CAAAA;AAE7C,aAAA1I,GAAAA,CAAAA,GAAYjB,IAAG,EAAEF,MAAImB,GAAA,EAAIkF,IAAIwD,GAAAA,GAAW,QAAH3J,KAAAA,SAAAA,GAAKF,MAAI,EAAEoM,QAAQkB,IAAUG,SAAS5D,EAAAA,EAAAA,CAAAA,EAAAA,CAAAA;IAC/E,CAAA;EAAE,GACN,CAACyD,KAAW5G,IAAM9G,GAAAA,CAAAA,GAGhBiT,UAAoD5O,cAAAA,SACtD,WAAA;AAAA,WACI5C,IAAQN,IAAI,SAAAS,IAAAA;AAWR,aAAO,CAACA,IAVKoO,GAAc,EACvBhQ,MAA0B,WAApB4B,GAAOI,WAAsB+Q,MAAa/S,KAChDiQ,WAAWrO,GAAOqO,WAClB/H,MAAMtG,GAAOI,UACb+E,WAAAA,IACAC,QAAAA,IACAkJ,aAAAA,KACAjJ,SAAAA,GAAAA,CAAAA,CAAAA;IAIR,CAAA;EAAE,GACN,CAACxF,KAASsR,KAAY/S,KAAM+G,IAAWC,IAAQkJ,KAAajJ,EAAAA,CAAAA;AAGhE,SAAO,EACHjH,MAAAA,KACA6S,eAAAA,KACArH,QAAAA,KACAC,QAAAA,KACAjC,UAAAA,KACA8I,UAAAA,KACAhH,iBAAAA,KACAF,aAAAA,KACAC,UAAAA,KACAoH,gBAAAA,KACAE,eAAAA,KACAG,sBAAAA,KACApF,WAAAA,KACAhM,aAAAA,KACAuR,iBAAAA,IAAAA;AAER;ATvLO,ISuLP,KAAA,CAAA,iBAAA,WAAA,gBAAA,SAAA,eAAA;ATvLO,IU2BDC,KAAW,SAAHnT,IAAAA;AA0EiB,MAzE3BK,KAAIL,GAAJK,MACAyG,KAAO9G,GAAP8G,SACAC,IAAI/G,GAAJ+G,MAEQqM,IAAapT,GAArB2L,QACAlL,KAAKT,GAALS,OACAE,KAAMX,GAANW,QAEAqG,KAAShH,GAATgH,WACAC,IAAMjH,GAANiH,QACAC,IAAOlH,GAAPkH,SACAC,KAAQnH,GAARmH,UACAC,KAAQpH,GAARoH,UAEAC,KAAUrH,GAAVqH,YACAE,IAAUvH,GAAVuH,YAEAE,KAAOzH,GAAPyH,SACAC,KAAY1H,GAAZ0H,cAEA2L,KAAOrT,GAAPqT,SACAC,KAAStT,GAATsT,WAASC,KAAAvT,GACT2H,YAAAA,KAAAA,WAAU4L,KAAG3K,GAAgBjB,aAAU4L,IAAAC,KAAAxT,GACvC4H,UAAAA,KAAAA,WAAQ4L,KAAG5K,GAAgBhB,WAAQ4L,IAAAC,KAAAzT,GACnC6H,aAAAA,KAAAA,WAAW4L,KAAG7K,GAAgBf,cAAW4L,IAAAC,KAAA1T,GACzC8H,aAAAA,KAAAA,WAAW4L,KAAG9K,GAAgBd,cAAW4L,IACzCC,IAAW3T,GAAX2T,aACAC,KAAW5T,GAAX4T,aAAWC,KAAA7T,GAEX6I,QAAAA,IAAAA,WAAMgL,KAAGjL,GAAgBC,SAAMgL,IAAAC,IAAA9T,GAC/B8I,cAAAA,KAAAA,WAAYgL,IAAGlL,GAAgBE,eAAYgL,GAAAjC,KAAA7R,GAE3C+H,aAAAA,KAAAA,WAAW8J,KAAGjJ,GAAgBb,cAAW8J,IACzC1O,KAAKnD,GAALmD,OAAK2O,KAAA9R,GACLgI,gBAAAA,KAAAA,WAAc8J,KAAGlJ,GAAgBZ,iBAAc8J,IAAAC,KAAA/R,GAC/CiI,iBAAAA,KAAAA,WAAe8J,KAAGnJ,GAAgBX,kBAAe8J,IACjD7J,MAAclI,GAAdkI,gBAAc6L,MAAA/T,GAEd2I,SAAAA,MAAAA,WAAOoL,MAAGnL,GAAgBD,UAAOoL,KAEjC3L,MAAOpI,GAAPoI,SACAC,MAAMrI,GAANqI,QAAM2L,MAAAhU,GACN+I,MAAAA,MAAAA,WAAIiL,MAAGpL,GAAgBG,OAAIiL,KAAAC,MAAAjU,GAC3B0F,MAAAA,MAAAA,WAAIuO,MAAGrL,GAAgBlD,OAAIuO,KAAAC,MAAAlU,GAC3BiD,cAAAA,MAAAA,WAAYiR,MAAGtL,GAAgB3F,eAAYiR,KAAAC,MAAAnU,GAC3CkD,aAAAA,MAAAA,WAAWiR,MAAGvL,GAAgB1F,cAAWiR,KACzCzR,MAAW1C,GAAX0C,aAAW0R,MAAApU,GAEXE,aAAAA,MAAAA,WAAWkU,MAAGxL,GAAgB1I,cAAWkU,KAEzCjE,MAAWnQ,GAAXmQ,aACA5H,MAAYvI,GAAZuI,cAEAwI,MAAW/Q,GAAX+Q,aAAWsD,MAAArU,GAEXqD,eAAAA,MAAAA,WAAagR,MAAGzL,GAAgBvF,gBAAagR,KAAAC,MAAAtU,GAC7CyD,SAAAA,MAAAA,WAAO6Q,MAAG1L,GAAgBnF,UAAO6Q,KACjChR,MAAOtD,GAAPsD,SACAC,MAAYvD,GAAZuD,cACAC,MAAYxD,GAAZwD,cAEA9B,MAAO1B,GAAP0B,SAAO6S,MAAAvU,GAEPkJ,MAAAA,MAAAA,WAAIqL,MAAG3L,GAAgBM,OAAIqL,KAC3B5Q,KAAS3D,GAAT2D,WACAC,KAAc5D,GAAd4D,gBACAC,KAAe7D,GAAf6D,iBAAe2Q,KAAAxU,GACf0D,aAAAA,KAAAA,WAAW8Q,KAAG5L,GAAgBlF,cAAW8Q,IACzCC,KAAYzU,GAAZyU,cACAC,KAAiB1U,GAAjB0U,mBACAC,KAAkB3U,GAAlB2U,oBAEAjM,KAAgB1I,GAAhB0I,kBAEAkM,KAA0CC,GAAAA,GAAlC7L,KAAO4L,GAAP5L,SAAiB8L,KAAYF,GAApBG,QACjBC,KAAqEC,GACjExU,IACAE,IACAyS,CAAAA,GAHI8B,KAAUF,GAAVE,YAAYC,KAAWH,GAAXG,aAAaxJ,KAAMqJ,GAANrJ,QAAQyJ,KAAUJ,GAAVI,YAAYC,KAAWL,GAAXK,aAMrDC,KAYI5E,GAAiB,EACjB5J,SAAAA,IACA3D,OAAAA,IACAoF,cAAAA,KACAwI,aAAAA,KACA1I,QAAAA,KACAD,SAAAA,KACA1F,aAAAA,KACAwF,gBAAAA,KACAlB,WAAAA,IACAC,QAAAA,GACAC,SAAAA,GACA7G,MAAAA,IACA0G,MAAAA,GACAI,UAAAA,IACAC,UAAAA,IACAuE,QAAAA,IACAlL,OAAO2U,IACPzU,QAAQ0U,IACR5N,SAAAA,IACAC,cAAAA,IACAL,YAAAA,IACAE,YAAAA,GACAQ,aAAAA,IACAC,gBAAAA,IACAC,iBAAAA,IACAvG,SAAAA,KACAyO,aAAAA,KACAzH,kBAAAA,GAAAA,CAAAA,GAvCAzI,KAAIqV,GAAJrV,MACA6S,KAAawC,GAAbxC,eACArH,KAAM6J,GAAN7J,QACAC,KAAM4J,GAAN5J,QACA6G,KAAQ+C,GAAR/C,UACAhH,KAAe+J,GAAf/J,iBACAmH,KAAc4C,GAAd5C,gBACAE,KAAa0C,GAAb1C,eACAG,KAAoBuC,GAApBvC,sBACApR,KAAW2T,GAAX3T,aACAuR,KAAeoC,GAAfpC,iBAgCEqC,KAAaC,cAcjB1C,IAAe,EACb/L,MAAM,SAAAxG,IAAAA;AAAG,WAAIA,GAAI2J;EAAG,GACpB/B,MAAM,SAAA5H,IAAAA;AAAG,WAAAiB,GAAA,EACLkB,aAAagQ,GAAenS,EAAAA,GAC5BoC,OAAOpC,GAAIoC,OACXhC,QAAQ,GACRiC,YAAYgQ,GAAcrS,EAAAA,GAC1BsC,cAAc,GACdC,QAAQvC,GAAIE,QAAQ,GACpBsC,QAAQxC,GAAII,SAAS,GACrBqC,WAAwBzC,eAAAA,GAAIC,IAAMD,QAAAA,GAAIG,IAAIH,GAAII,UAAS,KACvDF,OAAOF,GAAIE,MAAAA,GACI,eAAXwG,IACE,CAAA,IACA,EACItG,QAAQJ,GAAII,QACZqC,WAAS,eAAezC,GAAIC,IAAAA,OAAMD,GAAIG,IAAI,KAC1CD,OAAO,EAAA,CAAA;EAEnB,GACFgV,OAAO,SAAAlV,IAAAA;AAAG,WAAK,EACXmC,aAAagQ,GAAenS,EAAAA,GAC5BoC,OAAOpC,GAAIoC,OACXhC,QAAQJ,GAAII,QACZiC,YAAYgQ,GAAcrS,EAAAA,GAC1BsC,cAAc,GACdC,QAAQvC,GAAIE,QAAQ,GACpBsC,QAAQxC,GAAII,SAAS,GACrBqC,WAAS,eAAezC,GAAIC,IAAAA,OAAMD,GAAIG,IAAI,KAC1CD,OAAOF,GAAIE,MAAAA;EACb,GACFiV,QAAQ,SAAAnV,IAAAA;AAAG,WAAK,EACZmC,aAAagQ,GAAenS,EAAAA,GAC5BoC,OAAOpC,GAAIoC,OACXhC,QAAQJ,GAAII,QACZiC,YAAYgQ,GAAcrS,EAAAA,GAC1BsC,cAAc,GACdC,QAAQvC,GAAIE,QAAQ,GACpBsC,QAAQxC,GAAII,SAAS,GACrBqC,WAAS,eAAezC,GAAIC,IAAAA,OAAMD,GAAIG,IAAI,KAC1CD,OAAOF,GAAIE,MAAAA;EACb,GACFkV,OAAO,SAAApV,IAAAA;AAAG,WAAAiB,GAAA,EACNkB,aAAagQ,GAAenS,EAAAA,GAC5BoC,OAAOpC,GAAIoC,OACXhC,QAAQ,GACRiC,YAAYgQ,GAAcrS,EAAAA,GAC1BsC,cAAc,GACdC,QAAQvC,GAAIE,QAAQ,GACpBsC,QAAQ,GACRC,WAAwBzC,eAAAA,GAAIC,IAAMD,QAAAA,GAAIG,IAAIH,GAAII,UAAS,KACvDF,OAAOF,GAAIE,MAAAA,GACI,eAAXwG,IACE,CAAA,IACA,EACInE,QAAQ,GACRC,QAAQxC,GAAII,SAAS,GACrBA,QAAQJ,GAAII,QACZqC,WAAS,eAAezC,GAAIC,IAAAA,OAAMD,GAAIG,IAAI,KAC1CD,OAAO,EAAA,CAAA;EAEnB,GACFsU,QAAQD,IACRc,WAAAA,CAAY5M,GAAAA,CAAAA,GAGV6M,SAAcvR,cAAAA,SAChB,WAAA;AAAA,WAAO,EACHrB,cAAAA,KACAC,aAAAA,KACA6E,aAAAA,IACA1E,eAAAA,KACA2E,gBAAAA,IACAC,iBAAAA,IACA3E,SAAAA,KACAC,cAAAA,KACAC,cAAAA,KACA+H,iBAAAA,IACA9H,SAAAA,KACAC,aAAAA,IACAC,WAAW8Q,IACX7Q,gBAAgB8Q,IAChB7Q,iBAAiB8Q,GAAAA;EACpB,GACD,CACI1R,KACAC,KACA6E,IACAwD,IACAlI,KACA4E,IACAD,IACA1E,KACAC,KACAC,KACAC,KACAC,IACA+Q,IACAC,IACAC,EAAAA,CAAAA,GAIFmB,KAAYC,GAAShN,KAAM9I,IAAMyF,KAAM,EACzCsQ,SAAS,QACTC,WAAW,YAAA,CAAA,GAGTC,KAA2C,EAC7ChW,aAAa,MACbiW,MAAM,MACNlW,MAAM,MACNmW,MAAM,MACN1U,SAAS,MACTiH,SAAS,KAAA;AAGTE,IAAOiF,SAAS,aAAA,MAChBoI,GAAUhW,kBACNe,oBAAAA,KAAClB,IAAc,EAAmBE,MAAMA,IAAMC,aAAaA,IAAAA,GAAvC,aAAA,IAIxB2I,EAAOiF,SAAS,MAAA,MAChBoI,GAAUC,WACNlV,oBAAAA,KAACoV,GAAI,EAED5K,QAAQA,IACRC,QAAQA,IACRjL,OAAO2U,IACPzU,QAAQ0U,IACRzI,KAAKyG,IACLiD,OAAOhD,IACPiD,QAAQ5O,IACRgF,MAAM/E,GAAAA,GARF,MAAA,IAaZiB,EAAOiF,SAAS,MAAA,MAChBoI,GAAUjW,WACNgB,oBAAAA,KAACuV,cAAAA,UAAQ,EAAArV,UACJoU,GAAW,SAAC9S,IAAOlC,IAAAA;AAAG,eACnBgE,cAAAA,eAAcuE,IAAYtH,GAAAA,CAAAA,GACnBqU,IAAW,EACdtV,KAAAA,IACAkC,OAAAA,IACAW,mBAAmB2P,GAAqBxS,EAAAA,GACxC4C,OAAOoP,GAAShS,GAAIF,IAAAA,EAAAA,CAAAA,CAAAA;EAAAA,CAAAA,EAAAA,GAPlB,MAAA,IAclBwI,EAAOiF,SAAS,MAAA,MAChBoI,GAAUE,WACNnV,oBAAAA,KAACwV,GAAI,EAEDhW,OAAO2U,IACPzU,QAAQ0U,IACR5J,QAAQ5D,KAAc4D,KAAS,MAC/BC,QAAQ5D,KAAc4D,KAAS,MAC/BgL,SAAS/C,GACTgD,SAAS/C,GAAAA,GANL,MAAA,IAWZ/K,EAAOiF,SAAS,SAAA,MAChBoI,GAAUxU,cACNT,oBAAAA,KAACQ,GAAU,EAEPhB,OAAO2U,IACPzU,QAAQ0U,IACR3T,SAASwR,IACTvR,aAAaA,GAAAA,GAJT,SAAA,IASZkH,EAAOiF,SAAS,SAAA,MAChBoI,GAAUvN,cACN1H,oBAAAA,KAAC2V,IAAgB,EAEbjO,SAASA,KACTlI,OAAO2U,IACPzU,QAAQ0U,IACR5J,QAAQA,IACRC,QAAQA,GAAAA,GALJ,SAAA;AAUhB,MAAMmL,SAA8CvS,cAAAA,SAChD,WAAA;AAAA,WAAA9C,GAAAA,CAAAA,GACOqU,IAAW,EACdlK,QAAAA,IACAlL,OAAAA,IACAE,QAAAA,IACAyU,YAAAA,IACAC,aAAAA,IACApV,MAAAA,IACA+S,YAAYE,IACZnL,aAAAA,IACA0D,QAAAA,IACAC,QAAAA,IACAjI,SAAAA,KACA8H,iBAAAA,IACAjI,SAAAA,KACAC,cAAAA,KACAC,cAAAA,IAAAA,CAAAA;EAAY,GAEhB,CACIqS,IACAlK,IACAlL,IACAE,IACAyU,IACAC,IACApV,IACAiT,IACAnL,IACA0D,IACAC,IACAjI,KACA8H,IACAjI,KACAC,KACAC,GAAAA,CAAAA;AAIR,aACIvC,oBAAAA,KAAC6V,IAAU,EACPrW,OAAOyU,IACPvU,QAAQwU,IACRxJ,QAAQA,IACR5C,MAAM+M,IACN5M,MAAMA,KACNvF,WAAWA,IACXC,gBAAgBA,IAChBC,iBAAiBA,IACjBH,aAAaA,IAAYvC,UAExB0H,EAAOzH,IAAI,SAAC2V,IAAOzV,IAAAA;AAAM,QAAA0V;AACtB,WAAqB,cAAA,OAAVD,SACA9V,oBAAAA,KAACuV,cAAAA,UAAQ,EAAArV,cAAUoD,cAAAA,eAAcwS,IAAOF,EAAAA,EAAAA,GAAzBvV,EAAAA,IAGD,SAAzB0V,KAAOd,QAAAA,KAAAA,SAAAA,GAAYa,EAAAA,KAAMC,KAAI;EAAA,CAAA,EAAA,CAAA;AAI7C;AVpaO,IUsaMC,KAAM,SAAHpW,IAAAA;AAAA,MAAAqW,KAAArW,GACZwC,eAAAA,KAAAA,WAAa6T,KAAGtO,GAAgBvF,gBAAa6T,IAAAC,KAAAtW,GAC7CmI,SAAAA,KAAAA,WAAOmO,KAAGvO,GAAgBI,UAAOmO,IAAAC,IAAAvW,GACjCoI,cAAAA,IAAAA,WAAYmO,IAAGxO,GAAgBK,eAAYmO,GAC3CtT,KAAKjD,GAALiD,OACAuT,KAAaxW,GAAbwW,eACGC,KAAUhV,EAAAzB,IAAA0B,EAAAA;AAAA,aAEbtB,oBAAAA,KAACsW,IAAS,EAEFvO,SAAAA,IACA3F,eAAAA,IACA4F,cAAAA,GACAoO,eAAAA,IACAvT,OAAAA,IAAK3C,cAGTF,oBAAAA,KAACkS,IAAQ3R,GAAA,EAAW6B,eAAeA,GAAAA,GAAmBiU,EAAAA,CAAAA,EAAAA,CAAAA;AAC9C;AVxbT,IUwbS,KAAA,CAAA,iBAAA,iBAAA,OAAA;AVxbT,IWuCDE,KAAqB,SACvBC,IACA9L,IACAnL,IACAE,IAAAA;AAAS,SAET+W,GAAMxE,KAAK,SAAAyE,IAAAA;AAAI,WACXC,GAAeD,GAAKlX,IAAImL,GAAOgB,MAAM+K,GAAKhX,IAAIiL,GAAOiB,KAAK8K,GAAKjX,OAAOiX,GAAK/W,QAAQH,IAAGE,EAAAA;EAAE,CAAA;AAC3F;AX/CE,IWmDDkX,MAAiB,SAAH5X,IAAAA;AAkHiB,MAjHjCK,KAAIL,GAAJK,MACAyG,IAAO9G,GAAP8G,SACAC,KAAI/G,GAAJ+G,MAEQqM,KAAapT,GAArB2L,QACAlL,IAAKT,GAALS,OACAE,KAAMX,GAANW,QAEAqG,KAAShH,GAATgH,WACAC,IAAMjH,GAANiH,QACAC,KAAOlH,GAAPkH,SACAC,KAAQnH,GAARmH,UACAC,IAAQpH,GAARoH,UAEAC,KAAUrH,GAAVqH,YACAE,KAAUvH,GAAVuH,YAEAE,KAAOzH,GAAPyH,SACAC,KAAY1H,GAAZ0H,cAEA2L,KAAOrT,GAAPqT,SACAC,KAAStT,GAATsT,WAASC,KAAAvT,GACT2H,YAAAA,KAAAA,WAAU4L,KAAGpK,GAAmBxB,aAAU4L,IAAAC,KAAAxT,GAC1C4H,UAAAA,IAAAA,WAAQ4L,KAAGrK,GAAmBvB,WAAQ4L,IAAAC,IAAAzT,GACtC6H,aAAAA,KAAAA,WAAW4L,IAAGtK,GAAmBtB,cAAW4L,GAAAC,KAAA1T,GAC5C8H,aAAAA,IAAAA,WAAW4L,KAAGvK,GAAmBrB,cAAW4L,IAC5CC,IAAW3T,GAAX2T,aACAC,KAAW5T,GAAX4T,aAAWC,KAAA7T,GAEX6I,QAAAA,KAAAA,WAAMgL,KAAG1K,GAAmBN,SAAMgL,IAAAgE,KAAA7X,GAClC8X,WAAAA,KAAAA,WAASD,KAAG,SACRE,IAAGlX,IAAAA;AAWF,QAAAmX,KAAAnX,GATGN,KAAOoC,KAAKqV,GAALrV,OAAOhC,KAAMqX,GAANrX,QAAQF,KAAKuX,GAALvX,OAAOD,IAACwX,GAADxX,GAAGE,KAACsX,GAADtX,GAEhCgC,KAAW7B,GAAX6B,aACAO,KAAYpC,GAAZoC,cACAC,KAAWrC,GAAXqC,aACAC,KAAKtC,GAALsC,OACAP,IAAU/B,GAAV+B,YACAQ,KAAiBvC,GAAjBuC;AAYJ,QATA2U,GAAIE,YAAYtV,IAEZO,KAAc,MACd6U,GAAIG,cAAcxV,IAClBqV,GAAII,YAAYjV,KAGpB6U,GAAIK,UAAAA,GAEAnV,KAAe,GAAG;AAClB,UAAMoV,KAAStX,KAAK8I,IAAI5G,IAActC,EAAAA;AAEtCoX,MAAAA,GAAIO,OAAO9X,IAAI6X,IAAQ3X,EAAAA,GACvBqX,GAAIQ,OAAO/X,IAAIC,KAAQ4X,IAAQ3X,EAAAA,GAC/BqX,GAAIS,iBAAiBhY,IAAIC,IAAOC,IAAGF,IAAIC,IAAOC,KAAI2X,EAAAA,GAClDN,GAAIQ,OAAO/X,IAAIC,IAAOC,KAAIC,KAAS0X,EAAAA,GACnCN,GAAIS,iBAAiBhY,IAAIC,IAAOC,KAAIC,IAAQH,IAAIC,KAAQ4X,IAAQ3X,KAAIC,EAAAA,GACpEoX,GAAIQ,OAAO/X,IAAI6X,IAAQ3X,KAAIC,EAAAA,GAC3BoX,GAAIS,iBAAiBhY,GAAGE,KAAIC,IAAQH,GAAGE,KAAIC,KAAS0X,EAAAA,GACpDN,GAAIQ,OAAO/X,GAAGE,KAAI2X,EAAAA,GAClBN,GAAIS,iBAAiBhY,GAAGE,IAAGF,IAAI6X,IAAQ3X,EAAAA,GACvCqX,GAAIU,UAAAA;IACR;AACIV,MAAAA,GAAI1S,KAAK7E,GAAGE,IAAGD,IAAOE,EAAAA;AAG1BoX,IAAAA,GAAIrS,KAAAA,GAEAxC,KAAc,KACd6U,GAAInS,OAAAA,GAGJxC,OACA2U,GAAIW,eAAe,UACnBX,GAAIY,YAAY,UAChBZ,GAAIE,YAAYrV,GAChBmV,GAAIa,SAASzV,IAAO3C,IAAIC,KAAQ,GAAGC,KAAIC,KAAS,CAAA;EAExD,IAACkX,IAAAhG,KAAA7R,GAED+H,aAAAA,KAAAA,WAAW8J,KAAG1I,GAAmBpB,cAAW8J,IAC5C1O,KAAKnD,GAALmD,OAAK2O,KAAA9R,GACLgI,gBAAAA,KAAAA,WAAc8J,KAAG3I,GAAmBnB,iBAAc8J,IAAAC,MAAA/R,GAClDiI,iBAAAA,MAAAA,WAAe8J,MAAG5I,GAAmBlB,kBAAe8J,KACpD7J,MAAclI,GAAdkI,gBAEAE,MAAOpI,GAAPoI,SACAC,MAAMrI,GAANqI,QAAM6L,MAAAlU,GACNiD,cAAAA,MAAAA,WAAYiR,MAAG/K,GAAmBlG,eAAYiR,KAAAC,MAAAnU,GAC9CkD,aAAAA,MAAAA,WAAWiR,MAAGhL,GAAmBjG,cAAWiR,KAC5CzR,MAAW1C,GAAX0C,aAAW0R,MAAApU,GAEXE,aAAAA,MAAAA,WAAWkU,MAAGjL,GAAmBjJ,cAAWkU,KAE5CjE,MAAWnQ,GAAXmQ,aACA5H,MAAYvI,GAAZuI,cAEAwI,MAAW/Q,GAAX+Q,aAAWsD,MAAArU,GAEXqD,eAAAA,MAAAA,WAAagR,MAAGlL,GAAmB9F,gBAAagR,KAAAC,MAAAtU,GAChDyD,SAAAA,MAAAA,WAAO6Q,MAAGnL,GAAmB1F,UAAO6Q,KACpChR,MAAOtD,GAAPsD,SACAC,MAAYvD,GAAZuD,cACAC,MAAYxD,GAAZwD,cAEA9B,MAAO1B,GAAP0B,SAAOmX,MAAA7Y,GAEPoJ,YAAAA,MAAAA,WAAUyP,MAAG1P,GAAmBC,aAAUyP,KAE1CC,MAAS9Y,GAAT8Y,WAEMC,UAAWC,cAAAA,QAAiC,IAAA,GAE5ClV,MAAQC,GAAAA,GACdiR,KAAqEC,GACjExU,GACAE,IACAyS,EAAAA,GAHIzH,KAAMqJ,GAANrJ,QAAQyJ,KAAUJ,GAAVI,YAAYC,KAAWL,GAAXK,aAAaH,KAAUF,GAAVE,YAAYC,KAAWH,GAAXG,aAMrDG,KAWI5E,GAAiB,EACjB5J,SAAAA,GACA3D,OAAAA,IACAoF,cAAAA,KACAwI,aAAAA,KACA1I,QAAAA,KACAD,SAAAA,KACA1F,aAAAA,KACAwF,gBAAAA,KACAlB,WAAAA,IACAC,QAAAA,GACAC,SAAAA,IACA7G,MAAAA,IACA0G,MAAAA,IACAI,UAAAA,IACAC,UAAAA,GACAuE,QAAAA,IACAlL,OAAO2U,IACPzU,QAAQ0U,IACR5N,SAAAA,IACAC,cAAAA,IACAL,YAAAA,IACAE,YAAAA,IACAQ,aAAAA,IACAC,gBAAAA,IACAC,iBAAAA,KACAvG,SAAAA,KACAyO,aAAAA,IAAAA,CAAAA,GArCAlQ,KAAIqV,GAAJrV,MACA6S,KAAawC,GAAbxC,eACArH,KAAM6J,GAAN7J,QACAC,KAAM4J,GAAN5J,QACA6G,KAAQ+C,GAAR/C,UACAhH,KAAe+J,GAAf/J,iBACAmH,KAAc4C,GAAd5C,gBACAE,KAAa0C,GAAb1C,eACAG,KAAoBuC,GAApBvC,sBACAG,KAAeoC,GAAfpC,iBA+BJlP,KAA8CC,EAAAA,GAAtCC,KAAoBF,GAApBE,sBAAsBE,KAAWJ,GAAXI,aAGxBjE,KAAwB8Y,GAAuB,EACjD/Y,aAAaE,EAAe,EACxBC,MAAMJ,IACNC,aAAAA,KACAI,aAAa,SAAAoX,IAAAA;AAAI,WAAK,EAClBlX,GAAGkX,GAAKlX,GACRE,GAAGgX,GAAKhX,EAAAA;EACV,GACFE,eAAe,SAAAyM,IAAAA;AAAA,QAAG5M,KAAK4M,GAAL5M,OAAOE,KAAM0M,GAAN1M;AAAM,WAAQ,EACnCF,OAAAA,IACAE,QAAAA,IACAG,MAAMC,KAAKC,IAAIP,IAAOE,EAAAA,EAAAA;EACzB,EAAA,CAAA,EAAA,CAAA,GAKHkW,SAAoDvS,cAAAA,SACtD,WAAA;AAAA,WAAO,EACHrB,cAAAA,KACAC,aAAAA,KACAG,eAAAA,KACAK,aAAAA,OACAsE,gBAAAA,IACAC,iBAAAA,KACA0D,QAAAA,IACAlL,OAAAA,GACAE,QAAAA,IACAyU,YAAAA,IACAC,aAAAA,IACApV,MAAAA,IACA+S,YAAYE,IACZnL,aAAAA,IACA0D,QAAAA,IACAC,QAAAA,IACAjI,SAAAA,KACA8H,iBAAAA,IACAjI,SAAAA,KACAC,cAAAA,KACAC,cAAAA,IAAAA;EACH,GACD,CACIP,KACAC,KACAG,KACA2E,IACAC,KACA0D,IACAlL,GACAE,IACAyU,IACAC,IACApV,IACAiT,IACAnL,IACA0D,IACAC,IACAjI,KACA8H,IACAjI,KACAC,KACAC,GAAAA,CAAAA;AAIR0V,oBAAAA,WAAU,WAAA;AAAM,QAAAC,IACNpB,KAAsB,SAAnBoB,KAAGJ,IAASK,WAAAA,SAATD,GAAkBE,WAAW,IAAA;AAEpCN,IAAAA,IAASK,WACTrB,OAELgB,IAASK,QAAQ3Y,QAAQyU,KAAa9L,KACtC2P,IAASK,QAAQzY,SAASwU,KAAc/L,KAExC2O,GAAIrJ,MAAMtF,KAAYA,GAAAA,GAEtB2O,GAAIE,YAAYnU,IAAMwV,YACtBvB,GAAIwB,SAAS,GAAG,GAAGrE,IAAYC,EAAAA,GAC/B4C,GAAIyB,UAAU7N,GAAOgB,MAAMhB,GAAOiB,GAAAA,GAElC/D,GAAOiD,QAAQ,SAAAiL,IAAAA;AACG,iBAAVA,KA3PuD,YAAA,OA4P1CjT,IAAMsS,KAAKqD,KAAK9T,eAAgB7B,IAAMsS,KAAKqD,KAAK9T,cAAc,MACvEoS,GAAII,YAAYrU,IAAMsS,KAAKqD,KAAK9T,aAChCoS,GAAIG,cAAcpU,IAAMsS,KAAKqD,KAAK7T,QAE9BiC,MACA6R,EAAyC3B,IAAK,EAC1CtX,OAAAA,GACAE,QAAAA,IACA+N,OAAOjD,IACP/B,MAAM,KACNyE,QAAQwF,EAAAA,CAAAA,GAIZ7L,KACA4R,EAAyC3B,IAAK,EAC1CtX,OAAAA,GACAE,QAAAA,IACA+N,OAAOhD,IACPhC,MAAM,KACNyE,QAAQyF,GAAAA,CAAAA,KAIH,WAAVmD,KACP4C,EAAmB5B,IAAK,EACpBtM,QAAQA,IACRC,QAAQA,IACRjL,OAAO2U,IACPzU,QAAQ0U,IACRzI,KAAKyG,IACLiD,OAAOhD,IACPiD,QAAQ5O,IACRgF,MAAM/E,GACN9D,OAAAA,IAAAA,CAAAA,IAEa,WAAViT,KACPjE,GAAchH,QAAQ,SAAAvL,IAAAA;AAClBuX,QAAAA,GAAUC,IAAK,EACXxX,KAAAA,IACAmC,aAAagQ,GAAenS,EAAAA,GAC5B0C,cAAAA,KACAC,aAAAA,KACAC,OAAOoP,GAAShS,GAAIF,IAAAA,GACpBuC,YAAYgQ,GAAcrS,EAAAA,GAC1B6C,mBAAmB2P,GAAqBxS,EAAAA,EAAAA,CAAAA;MAEhD,CAAA,IACiB,cAAVwW,KACP7D,GAAgBpH,QAAQ,SAAAiC,IAAAA;AAAoB,YAAlBlM,KAAMkM,GAAA,CAAA,GAAE1N,KAAI0N,GAAA,CAAA;AAClC6L,UAAqB7B,IAAGvW,GAAAA,CAAAA,GACjBK,IAAM,EACTxB,MAAAA,IACA0B,gBAAgBqT,IAChBpT,iBAAiBqT,IACjBvR,OAAAA,IAAAA,CAAAA,CAAAA;MAER,CAAA,IACiB,kBAAViT,KACP8C,EAA0B9B,IAAK,EAAE7X,aAAaC,IAAkB2D,OAAAA,IAAAA,CAAAA,IACxC,cAAA,OAAViT,MACdA,GAAMgB,IAAKlB,EAAAA;IAEnB,CAAA,GAEAkB,GAAI+B,KAAAA;EACR,GAAG,CACCnS,IACAC,GACA0L,IACAD,IACAP,IACA7P,KACAC,KACA/C,IACA0H,IACAC,GACA4K,IACAH,IACAK,IACAe,GACAC,IACA5M,IACArG,IACA0U,IACAD,IACAyB,IACAhO,IACA5B,GACAiM,IACAvH,GAAOgB,MACPhB,GAAOiB,KACPuI,IACAD,IACA9L,KACA0O,IACArM,IACAC,IACAxE,IACA6L,IACAjP,KACArD,CAAAA,CAAAA;AAGJ,MAAMsZ,SAAmBtV,cAAAA,aACrB,SAACC,IAAAA;AACG,QAAKzE,MACA8Y,IAASK,SAAd;AAEA,UAAAY,KAAeC,GAAkBlB,IAASK,SAAS1U,EAAAA,GAA5ClE,KAACwZ,GAAA,CAAA,GAAEtZ,KAACsZ,GAAA,CAAA,GACLzZ,KAAMiX,GAAmBvX,IAAM0L,IAAQnL,IAAGE,EAAAA;AAAAA,iBAE5CH,MACA2D,OACIK,cAAAA,eAAcd,KAAOjC,GAAA,CAAA,GACdjB,GAAIF,MAAI,EACXsC,OAAOpC,GAAIoC,OACXQ,OAAO5C,GAAI4C,OACXoC,OAAO+E,OAAO/J,GAAIF,KAAKkF,KAAAA,EAAAA,CAAAA,CAAAA,GAE3Bb,EAAAA,GAGe,iBAAfA,GAAM4C,SACM,QAAZ/D,OAAAA,IAAehD,GAAIF,MAAMqE,EAAAA,MAG7BN,GAAAA;IApBmB;EAsB3B,GACA,CAACA,IAAauH,IAAQpI,KAActD,IAAMiE,IAAsBT,GAAAA,CAAAA,GAG9DoB,SAAmBJ,cAAAA,aACrB,SAACC,IAAAA;AACG,QAAKzE,MACA8Y,IAASK,SAAd;AAEAhV,SAAAA;AAEA,UAAA8V,KAAeD,GAAkBlB,IAASK,SAAS1U,EAAAA,GAA5ClE,KAAC0Z,GAAA,CAAA,GAAExZ,KAACwZ,GAAA,CAAA,GACL3Z,KAAMiX,GAAmBvX,IAAM0L,IAAQnL,IAAGE,EAAAA;AAE5CH,MAAAA,OACY,QAAZiD,OAAAA,IAAejD,GAAIF,MAAMqE,EAAAA;IARN;EAU1B,GACD,CAACN,IAAauH,IAAQnI,KAAcvD,EAAAA,CAAAA,GAGlCuE,SAAcC,cAAAA,aAChB,SAACC,IAAAA;AACG,QAAKzE,MACA8Y,IAASK,SAAd;AAEA,UAAAe,KAAeF,GAAkBlB,IAASK,SAAS1U,EAAAA,GAA5ClE,KAAC2Z,GAAA,CAAA,GAAEzZ,KAACyZ,GAAA,CAAA,GACL5Z,KAAMiX,GAAmBvX,IAAM0L,IAAQnL,IAAGE,EAAAA;AAAAA,iBAE5CH,OAAAA,QACA+C,OAAAA,IAAO9B,GAAQjB,CAAAA,GAAAA,GAAIF,MAAI,EAAEsC,OAAOpC,GAAIoC,MAAAA,CAAAA,GAAS+B,EAAAA;IAN1B;EAQ1B,GACD,CAACiH,IAAQrI,KAASrD,EAAAA,CAAAA;AAGtB,aACIgB,oBAAAA,KAAA,UAAA,EACImZ,KAAK,SAAAC,IAAAA;AACDtB,IAAAA,IAASK,UAAUiB,IACfvB,OAAa,aAAaA,QAAWA,IAAUM,UAAUiB;EAC/D,GACF5Z,OAAOyU,KAAa9L,KACpBzI,QAAQwU,KAAc/L,KACtB3G,OAAO,EACHhC,OAAOyU,IACPvU,QAAQwU,IACRmF,QAAQjX,MAAgB,SAAS,SAAA,GAErCE,cAAcF,MAAgB0W,KAAAA,QAC9BhU,aAAa1C,MAAgB0W,KAAAA,QAC7BvW,cAAcH,MAAgBwB,KAAAA,QAC9BvB,SAASD,MAAgBmB,KAAAA,OAActC,CAAAA;AAGnD;AXreO,IWueMqY,SAAYC,cAAAA,YACrB,SAAA7L,IAEIyL,IAAAA;AAAoC,MADlC/W,KAAasL,GAAbtL,eAAegU,KAAa1I,GAAb0I,eAAevT,KAAK6K,GAAL7K,OAAU8J,IAAKtL,EAAAqM,IAAApM,EAAAA;AAAA,aAG/CtB,oBAAAA,KAACsW,IAAS,EAAOlU,eAAAA,IAAegU,eAAAA,IAAevT,OAAAA,IAASkF,SAAAA,OAAe7H,cACnEF,oBAAAA,KAAC2W,KAAcpW,GAAAA,CAAAA,GAAeoM,GAAK,EAAEkL,WAAWsB,GAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AACxC,CAAA;AX9eb,IYMMK,KAAgB,SACzB7M,IAAAA;AAAsC,aAEtC3M,oBAAAA,KAACyZ,IAAiB,EAAAvZ,UACb,SAAAnB,IAAAA;AAAA,QAAGS,KAAKT,GAALS,OAAOE,KAAMX,GAANW;AAAM,eAAOM,oBAAAA,KAACgW,IAAGzV,GAAA,EAAWf,OAAOA,IAAOE,QAAQA,GAAAA,GAAYiN,EAAAA,CAAAA;EAAS,EAAA,CAAA;AAClE;AZXjB,IaOM+M,SAAsBH,cAAAA,YAAW,SAE5C5M,IAA2CwM,IAAAA;AACzC,aACInZ,oBAAAA,KAACyZ,IAAiB,EAAAvZ,UACb,SAAAnB,IAAAA;AAAA,QAAGS,KAAKT,GAALS,OAAOE,KAAMX,GAANW;AAAM,eACbM,oBAAAA,KAACsZ,IAAS/Y,GAAA,EACNf,OAAOA,IACPE,QAAQA,GAAAA,GACHiN,IAAK,EACVwM,KAAKA,GAAAA,CAAAA,CAAAA;EACP,EAAA,CAAA;AAIlB,CAAA;", "names": ["collection", "defaultProps", "dotSize", "noteWidth", "noteTextOffset", "animate", "isSvgNote", "note", "noteType", "isValidElement", "isCanvasNote", "isCircleAnnotation", "annotationSpec", "type", "isDotAnnotation", "isRectAnnotation", "bindAnnotations", "_ref", "data", "annotations", "getPosition", "getDimensions", "reduce", "acc", "annotation", "offset", "concat", "filter", "match", "map", "datum", "position", "dimensions", "size", "width", "height", "_extends", "omit", "getLinkAngle", "sourceX", "sourceY", "targetX", "targetY", "angle", "Math", "atan2", "absoluteAngleDegrees", "radiansToDegrees", "computeAnnotation", "computedNoteX", "computedNoteY", "x", "y", "noteX", "noteY", "_annotation$noteWidth", "_annotation$noteTextO", "isNumber", "abs", "Error", "computedX", "computedY", "positionFromAngle", "degreesToRadians", "eighth", "round", "textX", "noteLineX", "points", "text", "useAnnotations", "useMemo", "useComputedAnnotations", "_ref2", "computed", "useComputedAnnotation", "AnnotationNote", "theme", "useTheme", "_useMotionConfig", "useMotionConfig", "springConfig", "config", "animatedProps", "useSpring", "immediate", "createElement", "_jsxs", "_Fragment", "children", "outlineWidth", "_jsx", "animated", "style", "strokeLinejoin", "strokeWidth", "stroke", "outlineColor", "AnnotationLink", "_ref$isOutline", "isOutline", "path", "firstPoint", "slice", "animatedPath", "useAnimatedPath", "link", "strokeLinecap", "opacity", "outlineOpacity", "fill", "d", "CircleAnnotationOutline", "radius", "outline", "circle", "cx", "cy", "r", "DotAnnotationOutline", "_ref$size", "symbol", "RectAnnotationOutline", "_ref$borderRadius", "borderRadius", "rect", "rx", "ry", "Annotation", "drawPoints", "ctx", "for<PERSON>ach", "index", "moveTo", "lineTo", "renderAnnotationsToCanvas", "length", "save", "lineCap", "strokeStyle", "lineWidth", "beginPath", "arc", "PI", "fillStyle", "font", "fontSize", "fontFamily", "textAlign", "textBaseline", "lineJoin", "strokeText", "fillText", "restore", "BarAnnotations", "_ref", "bars", "annotations", "boundAnnotations", "useAnnotations", "data", "getPosition", "bar", "x", "width", "y", "height", "getDimensions", "_ref2", "size", "Math", "max", "_jsx", "_Fragment", "children", "map", "annotation", "i", "Annotation", "_extends", "BarLegends", "legends", "toggleSerie", "_legend$data", "legend", "BoxLegendSvg", "containerWidth", "containerHeight", "dataFrom", "undefined", "BarItem", "_data$fill", "_ref$bar", "_objectWithoutPropertiesLoose", "_excluded", "_ref$style", "style", "borderColor", "color", "labelColor", "labelOpacity", "labelX", "labelY", "transform", "borderRadius", "borderWidth", "label", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isInteractive", "onClick", "onMouseEnter", "onMouseLeave", "tooltip", "isFocusable", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "ariaDescribedBy", "theme", "useTheme", "_useTooltip", "useTooltip", "showTooltipFromEvent", "showTooltipAt", "hideTooltip", "renderTooltip", "useMemo", "createElement", "handleClick", "useCallback", "event", "handleTooltip", "handleMouseEnter", "handleMouseLeave", "handleFocus", "absX", "absY", "handleBlur", "_jsxs", "animated", "g", "rect", "to", "value", "rx", "ry", "fill", "strokeWidth", "stroke", "focusable", "tabIndex", "onMouseMove", "onFocus", "onBlur", "text", "textAnchor", "dominantBaseline", "fillOpacity", "labels", "pointerEvents", "BarTooltip", "BasicTooltip", "id", "formattedValue", "enableChip", "defaultProps", "indexBy", "keys", "groupMode", "layout", "reverse", "minValue", "maxValue", "valueScale", "type", "indexScale", "round", "padding", "innerPadding", "axisBottom", "axisLeft", "enableGridX", "enableGridY", "enableLabel", "labelSkipWidth", "labelSkipHeight", "labelTextColor", "from", "colorBy", "colors", "scheme", "tooltipLabel", "datum", "indexValue", "initialHiddenIds", "markers", "svgDefaultProps", "layers", "barComponent", "defs", "animate", "motionConfig", "role", "canvasDefaultProps", "pixelRatio", "window", "_window$devicePixelRa", "devicePixelRatio", "getIndexScale", "getIndex", "axis", "computeScale", "all", "min", "normalizeData", "item", "reduce", "acc", "key", "filterNullV<PERSON>ues", "Object", "coerce<PERSON><PERSON><PERSON>", "Number", "gt", "other", "lt", "range", "start", "end", "Array", "repeat", "_", "index", "clampToZero", "generateVerticalGroupedBars", "<PERSON><PERSON><PERSON><PERSON>", "yRef", "formatValue", "getColor", "getTooltipLabel", "_ref$innerPadding", "xScale", "yScale", "margin", "compare", "cleanedData", "for<PERSON>ach", "domain", "length", "_xScale", "d", "_yScale", "_coerceValue", "rawValue", "barHeight", "_yScale2", "barData", "hidden", "push", "left", "top", "generateHorizontalGroupedBars", "xRef", "_ref2$innerPadding", "_yScale3", "_xScale2", "_coerceValue2", "_xScale3", "generateGroupedBars", "_ref3", "_scale", "_ref3$padding", "_ref3$innerPadding", "indexScaleConfig", "_ref3$hiddenIds", "hiddenIds", "props", "filter", "includes", "_ref4", "otherAxis", "scaleSpec", "clampMin", "values", "entry", "concat", "k", "Boolean", "apply", "isFinite", "scale", "_ref5", "bandwidth", "params", "flattenDeep", "arr", "some", "isArray", "generateVerticalStackedBars", "stackedData", "stackedDataItem", "_getY", "generateHorizontalStackedBars", "_getX", "generateStackedBars", "array", "_ref4$padding", "_ref4$hiddenIds", "stack", "offset", "stackOffsetDiverging", "num", "_ref6", "getLegendData", "direction", "<PERSON><PERSON><PERSON><PERSON>", "getLegendLabel", "getPropertyAccessor", "uniqBy", "_bar$data$indexValue", "_bar$color2", "_bar$color", "useBar", "_ref$indexBy", "_ref$keys", "_ref$label", "_ref$tooltipLabel", "valueFormat", "_ref$colors", "_ref$colorBy", "_ref$borderColor", "_ref$labelTextColor", "_ref$groupMode", "_ref$layout", "_ref$reverse", "_ref$minValue", "_ref$maxValue", "_ref$padding", "_ref$valueScale", "_ref$indexScale", "_ref$initialHiddenIds", "_ref$enableLabel", "_ref$labelSkipWidth", "_ref$labelSkipHeight", "_ref$legends", "_useState", "useState", "setHiddenIds", "state", "indexOf", "usePropertyAccessor", "get<PERSON><PERSON><PERSON>", "useValueFormatter", "useOrdinalColorScale", "getBorderColor", "useInheritedColor", "getLabelColor", "_generateBars", "barsWithValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendData", "find", "legendsWithData", "InnerBar", "<PERSON><PERSON><PERSON><PERSON>", "axisTop", "axisRight", "_ref$axisBottom", "_ref$axisLeft", "_ref$enableGridX", "_ref$enableGridY", "gridXValues", "gridYValues", "_ref$layers", "_ref$barComponent", "_ref$markers", "_ref$defs", "_ref$fill", "_ref$borderRadius", "_ref$borderWidth", "_ref$annotations", "_ref$isInteractive", "_ref$tooltip", "_ref$role", "_ref$isFocusable", "barAriaLabel", "barAriaLabelledBy", "barAriaDescribedBy", "_useMotionConfig", "useMotionConfig", "springConfig", "config", "_useDimensions", "useDimensions", "outerWidth", "outerHeight", "innerWidth", "innerHeight", "_useBar", "transition", "useTransition", "enter", "update", "leave", "immediate", "commonProps", "boundDefs", "bindDefs", "dataKey", "<PERSON><PERSON><PERSON>", "layerById", "axes", "grid", "Axes", "right", "bottom", "Fragment", "Grid", "xValues", "yV<PERSON><PERSON>", "<PERSON><PERSON>ian<PERSON><PERSON><PERSON>", "layerContext", "SvgWrapper", "layer", "_layerById$layer", "Bar", "_ref2$isInteractive", "_ref2$animate", "_ref2$motionConfig", "renderWrapper", "otherProps", "Container", "findBarUnderCursor", "nodes", "node", "isCursorInRect", "InnerBarCanvas", "_ref$renderBar", "renderBar", "ctx", "_ref2$bar", "fillStyle", "strokeStyle", "lineWidth", "beginPath", "radius", "moveTo", "lineTo", "quadraticCurveTo", "closePath", "textBaseline", "textAlign", "fillText", "_ref$pixelRatio", "canvasRef", "canvasEl", "useRef", "useComputedAnnotations", "useEffect", "_canvasEl$current", "current", "getContext", "background", "fillRect", "translate", "line", "renderGridLinesToCanvas", "renderAxesToCanvas", "renderLegendToCanvas", "renderAnnotationsToCanvas", "save", "handleMouseHover", "_getRelativeCursor", "getRelativeCursor", "_getRelativeCursor2", "_getRelativeCursor3", "ref", "canvas", "cursor", "BarCanvas", "forwardRef", "ResponsiveBar", "ResponsiveWrapper", "ResponsiveBarCanvas"]}