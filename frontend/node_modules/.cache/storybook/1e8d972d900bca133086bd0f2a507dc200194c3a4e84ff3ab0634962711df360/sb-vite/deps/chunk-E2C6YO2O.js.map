{"version": 3, "sources": ["../../../../../@mdx-js/react/lib/index.js", "../../../../../@mdx-js/react/index.js"], "sourcesContent": ["/**\n * @import {MDXComponents} from 'mdx/types.js'\n * @import {Component, ReactElement, ReactNode} from 'react'\n */\n\n/**\n * @callback MergeComponents\n *   Custom merge function.\n * @param {Readonly<MDXComponents>} currentComponents\n *   Current components from the context.\n * @returns {MDXComponents}\n *   Additional components.\n *\n * @typedef Props\n *   Configuration for `MDXProvider`.\n * @property {ReactNode | null | undefined} [children]\n *   Children (optional).\n * @property {Readonly<MDXComponents> | MergeComponents | null | undefined} [components]\n *   Additional components to use or a function that creates them (optional).\n * @property {boolean | null | undefined} [disableParentContext=false]\n *   Turn off outer component context (default: `false`).\n */\n\nimport React from 'react'\n\n/** @type {Readonly<MDXComponents>} */\nconst emptyComponents = {}\n\nconst MDXContext = React.createContext(emptyComponents)\n\n/**\n * Get current components from the MDX Context.\n *\n * @param {Readonly<MDXComponents> | MergeComponents | null | undefined} [components]\n *   Additional components to use or a function that creates them (optional).\n * @returns {MDXComponents}\n *   Current components.\n */\nexport function useMDXComponents(components) {\n  const contextComponents = React.useContext(MDXContext)\n\n  // Memoize to avoid unnecessary top-level context changes\n  return React.useMemo(\n    function () {\n      // Custom merge via a function prop\n      if (typeof components === 'function') {\n        return components(contextComponents)\n      }\n\n      return {...contextComponents, ...components}\n    },\n    [contextComponents, components]\n  )\n}\n\n/**\n * Provider for MDX context.\n *\n * @param {Readonly<Props>} properties\n *   Properties.\n * @returns {ReactElement}\n *   Element.\n * @satisfies {Component}\n */\nexport function MDXProvider(properties) {\n  /** @type {Readonly<MDXComponents>} */\n  let allComponents\n\n  if (properties.disableParentContext) {\n    allComponents =\n      typeof properties.components === 'function'\n        ? properties.components(emptyComponents)\n        : properties.components || emptyComponents\n  } else {\n    allComponents = useMDXComponents(properties.components)\n  }\n\n  return React.createElement(\n    MDXContext.Provider,\n    {value: allComponents},\n    properties.children\n  )\n}\n", "export {MDXProvider, useMDXComponents} from './lib/index.js'\n"], "mappings": ";;;;;;;;;;AAsCO,SAAS,iBAAiB,YAAY;AAC3C,QAAM,oBAAoB,aAAAA,QAAM,WAAW,UAAU;AAGrD,SAAO,aAAAA,QAAM;AAAA,IACX,WAAY;AAEV,UAAI,OAAO,eAAe,YAAY;AACpC,eAAO,WAAW,iBAAiB;AAAA,MACrC;AAEA,aAAO,EAAC,GAAG,mBAAmB,GAAG,WAAU;AAAA,IAC7C;AAAA,IACA,CAAC,mBAAmB,UAAU;AAAA,EAChC;AACF;AAWO,SAAS,YAAY,YAAY;AAEtC,MAAI;AAEJ,MAAI,WAAW,sBAAsB;AACnC,oBACE,OAAO,WAAW,eAAe,aAC7B,WAAW,WAAW,eAAe,IACrC,WAAW,cAAc;AAAA,EACjC,OAAO;AACL,oBAAgB,iBAAiB,WAAW,UAAU;AAAA,EACxD;AAEA,SAAO,aAAAA,QAAM;AAAA,IACX,WAAW;AAAA,IACX,EAAC,OAAO,cAAa;AAAA,IACrB,WAAW;AAAA,EACb;AACF;AAlFA,IAuBA,cAGM,iBAEA;AA5BN;AAAA;AAuBA,mBAAkB;AAGlB,IAAM,kBAAkB,CAAC;AAEzB,IAAM,aAAa,aAAAA,QAAM,cAAc,eAAe;AAAA;AAAA;;;AC5BtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;", "names": ["React"]}