try{
(()=>{var y=__STORYBOOK_API__,{ActiveTabs:E,Consumer:T,ManagerContext:h,Provider:v,RequestResponseError:A,addons:a,combineParameters:b,controlOrMetaKey:O,controlOrMetaSymbol:k,eventMatchesShortcut:R,eventToShortcut:g,experimental_MockUniversalStore:x,experimental_UniversalStore:I,experimental_requestResponse:M,experimental_useUniversalStore:C,isMacLike:P,isShortcutTaken:U,keyToSymbol:f,merge:q,mockChannel:D,optionOrAltSymbol:G,shortcutMatchesShortcut:K,shortcutToHumanString:V,types:$,useAddonState:B,useArgTypes:N,useArgs:Q,useChannel:Y,useGlobalTypes:H,useGlobals:L,useParameter:j,useSharedState:w,useStoryPrepared:z,useStorybookApi:F,useStorybookState:J}=__STORYBOOK_API__;var e="storybook/links",n={NAVIGATE:`${e}/navigate`,REQUEST:`${e}/request`,RECEIVE:`${e}/receive`};a.register(e,t=>{t.on(n.REQUEST,({kind:u,name:l})=>{let i=t.storyId(u,l);t.emit(n.RECEIVE,i)})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
