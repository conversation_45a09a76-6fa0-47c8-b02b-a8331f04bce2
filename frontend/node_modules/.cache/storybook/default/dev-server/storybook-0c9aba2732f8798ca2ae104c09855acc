{"key": "lastEvents", "content": {"boot": {"body": {"eventType": "boot", "eventId": "TVZlFRfjam3dbdXpGK98e", "sessionId": "HknrkA32Lm4FpK-Qezu4k", "payload": {"eventType": "dev"}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "23.6.1", "cliVersion": "8.6.14"}}, "timestamp": 1750918633252}, "init-step": {"body": {"eventType": "init-step", "eventId": "9-2mr0iBiMIcMVcUG4fJV", "sessionId": "HknrkA32Lm4FpK-Qezu4k", "metadata": {"generatedAt": 1750918430216, "userSince": 1750918417044, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": true, "storybookVersionSpecifier": "9.0.13", "language": "typescript"}, "payload": {"step": "new-user-check", "newUser": true}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "23.6.1", "storybookVersion": "9.0.13", "cliVersion": "9.0.13", "anonymousId": "7262709bd24d187a783172d60c4f9c14f1a5ac2e781eff4a9b7c57c875b2b48c"}}, "timestamp": 1750918430759}, "error": {"body": {"eventType": "error", "eventId": "b1UhpZXMAGrXcOotWKIkf", "sessionId": "HknrkA32Lm4FpK-Qezu4k", "metadata": {"generatedAt": 1750918438479, "userSince": 1750918417044, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": true, "storybookVersionSpecifier": "9.0.13", "language": "typescript"}, "payload": {"name": "Error", "eventType": "init", "error": {"handled": true, "cause": {"shortMessage": "Command failed with exit code 1: npm install -D storybook@^9.0.13 @storybook/react-vite@^9.0.13 @chromatic-com/storybook@^4.0.1 @storybook/addon-docs@^9.0.13 @storybook/addon-onboarding@^9.0.13 eslint-plugin-storybook@^9.0.13", "command": "npm install -D storybook@^9.0.13 @storybook/react-vite@^9.0.13 @chromatic-com/storybook@^4.0.1 @storybook/addon-docs@^9.0.13 @storybook/addon-onboarding@^9.0.13 eslint-plugin-storybook@^9.0.13", "escapedCommand": "\"npm install -D storybook@^9.0.13 @storybook/react-vite@^9.0.13 @chromatic-com/storybook@^4.0.1 @storybook/addon-docs@^9.0.13 @storybook/addon-onboarding@^9.0.13 eslint-plugin-storybook@^9.0.13\"", "exitCode": 1, "cwd": "$SNIP", "failed": true, "timedOut": false, "isCanceled": false, "killed": false}, "message": "Error: Command failed with exit code 1: npm install -D storybook@^9.0.13 @storybook/react-vite@^9.0.13 @chromatic-com/storybook@^4.0.1 @storybook/addon-docs@^9.0.13 @storybook/addon-onboarding@^9.0.13 eslint-plugin-storybook@^9.0.13", "stack": "Error: Error: Command failed with exit code 1: npm install -D storybook@^9.0.13 @storybook/react-vite@^9.0.13 @chromatic-com/storybook@^4.0.1 @storybook/addon-docs@^9.0.13 @storybook/addon-onboarding@^9.0.13 eslint-plugin-storybook@^9.0.13\n    at installStorybook (file://$SNIP/.npm/_npx/2e6d93afebb95389/node_modules/create-storybook/dist/chunk-CLCYJ4O2.js:1840:53)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async doInitiate (file://$SNIP/.npm/_npx/2e6d93afebb95389/node_modules/create-storybook/dist/chunk-CLCYJ4O2.js:1857:310)\n    at async withTelemetry (file://$SNIP/.npm/_npx/2e6d93afebb95389/node_modules/create-storybook/dist/chunk-CLCYJ4O2.js:1739:2189)\n    at async initiate (file://$SNIP/.npm/_npx/2e6d93afebb95389/node_modules/create-storybook/dist/chunk-CLCYJ4O2.js:1895:236)\n    at async _Command.<anonymous> (file://$SNIP/.npm/_npx/2e6d93afebb95389/node_modules/create-storybook/dist/bin/index.js:31:3392)", "name": "Error"}, "errorHash": "4825fb496a706ca7f260df0b6cf59250acb9ba2187b992e5f7a9c6f7b11c2d34", "isErrorInstance": true}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "23.6.1", "storybookVersion": "9.0.13", "anonymousId": "7262709bd24d187a783172d60c4f9c14f1a5ac2e781eff4a9b7c57c875b2b48c"}}, "timestamp": 1750918439076}, "version-update": {"body": {"eventType": "version-update", "eventId": "wm6nutWRj_RUXgvSfodYs", "sessionId": "HknrkA32Lm4FpK-Qezu4k", "metadata": {"generatedAt": 1750918634044, "userSince": 1750918417044, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": true, "typescriptOptions": {"check": false, "reactDocgen": "react-docgen-typescript", "reactDocgenTypescriptOptions": {"shouldExtractLiteralValuesFromEnum": true}}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 22, "language": "typescript", "storybookPackages": {"@storybook/blocks": {"version": "8.6.14"}, "@storybook/react-vite": {"version": "8.6.14"}, "storybook": {"version": "8.6.14"}}, "addons": {"@storybook/addon-links": {"version": "8.6.14"}, "@storybook/addon-essentials": {"version": "8.6.14"}, "@storybook/addon-interactions": {"version": "8.6.14"}}}, "payload": {}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "23.6.1", "cliVersion": "8.6.14", "anonymousId": "7262709bd24d187a783172d60c4f9c14f1a5ac2e781eff4a9b7c57c875b2b48c"}}, "timestamp": 1750918636517}, "dev": {"body": {"eventType": "dev", "eventId": "TLpgPmBCCrJvdchPZ0E5l", "sessionId": "HknrkA32Lm4FpK-Qezu4k", "metadata": {"generatedAt": 1750918634044, "userSince": 1750918417044, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": true, "typescriptOptions": {"check": false, "reactDocgen": "react-docgen-typescript", "reactDocgenTypescriptOptions": {"shouldExtractLiteralValuesFromEnum": true}}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 22, "language": "typescript", "storybookPackages": {"@storybook/blocks": {"version": "8.6.14"}, "@storybook/react-vite": {"version": "8.6.14"}, "storybook": {"version": "8.6.14"}}, "addons": {"@storybook/addon-links": {"version": "8.6.14"}, "@storybook/addon-essentials": {"version": "8.6.14"}, "@storybook/addon-interactions": {"version": "8.6.14"}}}, "payload": {"versionStatus": "success", "storyIndex": {"storyCount": 28, "componentCount": 3, "pageStoryCount": 0, "playStoryCount": 0, "autodocsCount": 3, "mdxCount": 0, "exampleStoryCount": 0, "exampleDocsCount": 0, "onboardingStoryCount": 0, "onboardingDocsCount": 0, "version": 5}, "storyStats": {"factory": 0, "play": 0, "render": 5, "loaders": 0, "beforeEach": 0, "globals": 0, "tags": 28, "storyFn": 0, "mount": 0, "moduleMock": 0}}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "23.6.1", "cliVersion": "8.6.14", "anonymousId": "7262709bd24d187a783172d60c4f9c14f1a5ac2e781eff4a9b7c57c875b2b48c"}}, "timestamp": 1750918641795}}}