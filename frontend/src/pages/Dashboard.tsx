import { useQuery } from '@tanstack/react-query'
import { dashboard<PERSON>pi, agendaApi, financeiroApi } from '@/services/api'
import KPICard from '@/components/common/KPICard'
import AIInsightCard from '@/components/common/AIInsightCard'
import Line<PERSON>hart from '@/components/charts/LineChart'
import BarChart from '@/components/charts/BarChart'
import AdvancedChart from '@/components/analytics/AdvancedChart'
import YearOver<PERSON><PERSON><PERSON><PERSON> from '@/components/charts/YearOverYearChart'
import SpecialtyHeatmap from '@/components/charts/SpecialtyHeatmap'
import FilterContainer, { FilterGroup, FilterSeparator } from '@/components/common/FilterContainer'
import { usePeriodFilter } from '@/components/common/PeriodFilter'
import { useMetricFilter, revenueMetricOptions } from '@/components/common/MetricFilter'
import { TrendingUp, Users, DollarSign, Calendar, AlertTriangle, Activity } from 'lucide-react'

export default function Dashboard() {
  // Filtros
  const { period, PeriodFilter } = usePeriodFilter('last_90_days')
  const { metric, MetricFilter } = useMetricFilter(revenueMetricOptions, 'receita')

  const { data: summary, isLoading: isLoadingSummary } = useQuery({
    queryKey: ['dashboard-summary', period],
    queryFn: () => dashboardApi.getSummary(period),
  })

  // Buscar dados reais do banco via API
  const { data: performanceData, isLoading: isLoadingPerformance, error: performanceError } = useQuery({
    queryKey: ['dashboard-performance', period],
    queryFn: () => dashboardApi.getSummary(period),
  })

  const { data: agendamentosData, isLoading: isLoadingAgendamentos, error: agendamentosError } = useQuery({
    queryKey: ['agenda-weekly-overview', period],
    queryFn: () => agendaApi.getOcupacaoSemanal(period),
  })

  const { data: receitaData, isLoading: isLoadingReceita, error: receitaError } = useQuery({
    queryKey: ['financeiro-revenue-evolution', period],
    queryFn: () => financeiroApi.getRevenueEvolution(period),
  })

  // Insights removidos temporariamente - endpoint não implementado
  const performanceInsights = []
  const isLoadingInsights = false
  const insightsError = null

  // Novos gráficos avançados
  const { data: yearOverYearData, isLoading: isLoadingYoY, error: yoyError } = useQuery({
    queryKey: ['dashboard-year-over-year', metric],
    queryFn: () => dashboardApi.getYearOverYear(metric),
  })

  const { data: heatmapData, isLoading: isLoadingHeatmap, error: heatmapError } = useQuery({
    queryKey: ['dashboard-heatmap', period],
    queryFn: () => dashboardApi.getHeatmap(period),
  })

  // Estados de loading e erro
  const isLoading = isLoadingSummary || isLoadingPerformance || isLoadingAgendamentos || isLoadingReceita || isLoadingInsights || isLoadingYoY || isLoadingHeatmap
  const hasError = performanceError || agendamentosError || receitaError || insightsError || yoyError || heatmapError

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Carregando dashboard...</div>
      </div>
    )
  }

  // Componente de erro quando não há dados
  if (hasError) {
    return (
      <div className="space-y-8">
        <div className="bg-white rounded-view p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-black mb-2">Dashboard Executivo</h1>
              <p className="text-gray-600">Visão geral dos indicadores da clínica</p>
            </div>
            <Activity className="w-12 h-12 text-blue-600" />
          </div>
        </div>

        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="w-6 h-6 text-red-600" />
            <div>
              <h3 className="text-lg font-semibold text-red-800">Dados Indisponíveis</h3>
              <p className="text-red-700">
                Não foi possível carregar os dados do dashboard. Verifique se o banco de dados está conectado e contém informações.
              </p>
              <p className="text-sm text-red-600 mt-2">
                Erro técnico: {hasError?.message || 'Falha na conexão com o banco de dados'}
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="bg-white rounded-view p-8 border border-gray-200 relative overflow-hidden">
        <div className="relative z-10">
          <h1 className="text-3xl font-bold text-black mb-2">
            Dashboard Executivo
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            Visão geral da clínica com métricas integradas de todos os módulos
          </p>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl">
            <div className="flex flex-col items-center bg-gray-50 hover:bg-white border border-gray-200 text-black px-4 py-4 rounded-lg transition duration-150 ease-in-out group">
              <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mb-2 group-hover:bg-gray-50 shadow-sm">
                <Calendar className="w-5 h-5 text-blue-600" />
              </div>
              <span className="text-xs font-medium">Agendamentos</span>
            </div>

            <div className="flex flex-col items-center bg-gray-50 hover:bg-white border border-gray-200 text-black px-4 py-4 rounded-lg transition duration-150 ease-in-out group">
              <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mb-2 group-hover:bg-gray-50 shadow-sm">
                <DollarSign className="w-5 h-5 text-blue-600" />
              </div>
              <span className="text-xs font-medium">Financeiro</span>
            </div>

            <div className="flex flex-col items-center bg-gray-50 hover:bg-white border border-gray-200 text-black px-4 py-4 rounded-lg transition duration-150 ease-in-out group">
              <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mb-2 group-hover:bg-gray-50 shadow-sm">
                <Users className="w-5 h-5 text-blue-600" />
              </div>
              <span className="text-xs font-medium">Pacientes</span>
            </div>

            <div className="flex flex-col items-center bg-gray-50 hover:bg-white border border-gray-200 text-black px-4 py-4 rounded-lg transition duration-150 ease-in-out group">
              <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mb-2 group-hover:bg-gray-50 shadow-sm">
                <TrendingUp className="w-5 h-5 text-blue-600" />
              </div>
              <span className="text-xs font-medium">Amigo Care+</span>
            </div>
          </div>
        </div>
        
        {/* Background decorative elements */}
        <div className="absolute top-0 right-0 w-full h-full overflow-hidden opacity-30">
          <div className="absolute top-[-80px] right-[-80px] w-[400px] h-[400px] rounded-full bg-gray-100"></div>
          <div className="absolute bottom-[-100px] left-[-100px] w-[300px] h-[300px] rounded-full bg-gray-100"></div>
        </div>
      </div>

      {/* Filtros */}
      <FilterContainer
        title="Filtros de Análise"
        onReset={() => {
          // Reset filters to default
          window.location.reload()
        }}
      >
        <FilterGroup label="Período">
          <PeriodFilter />
        </FilterGroup>

        <FilterSeparator />

        <FilterGroup label="Métrica Y/Y">
          <MetricFilter />
        </FilterGroup>
      </FilterContainer>

      {/* KPI Cards Executivos */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {isLoadingSummary ? (
          // Loading skeleton
          Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="bg-white rounded-lg p-6 border border-gray-200 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/4"></div>
            </div>
          ))
        ) : summary?.kpis?.map((kpi) => (
          <KPICard
            key={kpi.id || kpi.name}
            metric={kpi}
            target={kpi.name === 'Receita Total' ? 60000 : kpi.name === 'Taxa Cancelamento' ? 20 : undefined}
            status={
              kpi.name === 'Taxa Cancelamento' && kpi.value > 25 ? 'danger' :
              kpi.trend_direction === 'up' && kpi.name !== 'Taxa Cancelamento' ? 'success' :
              'info'
            }
            comparison={{
              period: 'Mesmo período ano anterior',
              value: kpi.value * 0.85,
              label: 'Ano passado'
            }}
          />
        )) || (
          <div className="col-span-full text-center py-8 text-gray-500">
            <p>KPIs não disponíveis no momento</p>
          </div>
        )}
      </div>

      {/* Análise Year over Year - Posição Principal */}
      <div className="bg-white rounded-view p-6 border border-gray-200">
        {yearOverYearData && yearOverYearData.chart_data && yearOverYearData.anos_disponiveis ? (
          <YearOverYearChart
            data={yearOverYearData.chart_data}
            anos={yearOverYearData.anos_disponiveis}
            metric={metric}
            title={`Análise Year over Year - ${revenueMetricOptions.find(opt => opt.value === metric)?.label || 'Receita'}`}
            height={700}
            showTrendLine={true}
            showMovingAverage={true}
          />
        ) : (
          <div>
            <h3 className="text-lg font-semibold text-black mb-4">Análise Year over Year</h3>
            <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
              <div className="text-center">
                <AlertTriangle className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600">Dados Y/Y indisponíveis</p>
                <p className="text-sm text-gray-500">Aguardando dados do banco</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Análise Executiva Avançada */}
      {performanceData && performanceData.length > 0 ? (
        <AdvancedChart
          title="Performance Operacional Integrada"
          subtitle="Análise multidimensional de atendimentos, receita e eficiência operacional"
          data={performanceData}
          type="performance"
          primaryMetric="atendimentos"
          secondaryMetric="receita"
          targetValue={200}
          benchmarkValue={175}
          insights={performanceInsights || []}
          height={400}
          showBrush={true}
        />
      ) : (
        <div className="bg-white rounded-view p-6 border border-gray-200">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="w-6 h-6 text-amber-600" />
            <div>
              <h3 className="text-lg font-semibold text-black">Dados de Performance Indisponíveis</h3>
              <p className="text-gray-600">Não há dados suficientes no banco para gerar análise de performance.</p>
            </div>
          </div>
        </div>
      )}

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-view p-6 border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-black">Agendamentos Semanais</h3>
            <div className="flex items-center space-x-2">
              {agendamentosData?.data && agendamentosData.data.length > 0 ? (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                  Dados Reais
                </span>
              ) : (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
                  Sem Dados
                </span>
              )}
            </div>
          </div>

          {agendamentosData?.data && Array.isArray(agendamentosData.data) && agendamentosData.data.length > 0 ? (
            <>
              <LineChart
                data={agendamentosData.data.map(item => ({
                  id: item.dia_semana,
                  data: [
                    { x: item.dia_semana, y: item.taxa_ocupacao }
                  ]
                }))}
                height={300}
                enableArea={true}
                enablePoints={true}
              />
              <div className="mt-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex items-start space-x-2">
                  <TrendingUp className="w-4 h-4 text-blue-600 mt-0.5" />
                  <div className="text-sm text-gray-700">
                    <strong>Análise:</strong> Dados baseados em agendamentos reais do sistema.
                  </div>
                </div>
              </div>
            </>
          ) : (
            <div className="h-[300px] flex items-center justify-center bg-gray-50 rounded-lg">
              <div className="text-center">
                <AlertTriangle className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600">Nenhum dado de agendamento encontrado</p>
                <p className="text-sm text-gray-500">Verifique se há agendamentos cadastrados no sistema</p>
              </div>
            </div>
          )}
        </div>

        <div className="bg-white rounded-view p-6 border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-black">Evolução da Receita</h3>
            <div className="flex items-center space-x-2">
              {receitaData?.data && receitaData.data.length > 0 ? (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                  Dados Reais
                </span>
              ) : (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
                  Sem Dados
                </span>
              )}
            </div>
          </div>

          {receitaData?.data && receitaData.data.length > 0 ? (
            <>
              <BarChart
                data={receitaData.data}
                keys={['receita']}
                indexBy="periodo"
                height={300}
                colors={['#2563eb']}
              />
              <div className="mt-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex items-start space-x-2">
                  <TrendingUp className="w-4 h-4 text-green-600 mt-0.5" />
                  <div className="text-sm text-gray-700">
                    <strong>Análise:</strong> Receita baseada em dados reais de atendimentos e transações.
                  </div>
                </div>
              </div>
            </>
          ) : (
            <div className="h-[300px] flex items-center justify-center bg-gray-50 rounded-lg">
              <div className="text-center">
                <AlertTriangle className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600">Nenhum dado de receita encontrado</p>
                <p className="text-sm text-gray-500">Verifique se há transações financeiras cadastradas</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Heatmap de Especialidades - Largura Total */}
      <div className="bg-white rounded-view p-6 border border-gray-200">
        {heatmapData && heatmapData.heatmap_data ? (
          <SpecialtyHeatmap
            data={heatmapData.heatmap_data}
            title="Performance por Especialidade"
            height={400}
            fullWidth={true}
          />
        ) : (
          <div>
            <h3 className="text-lg font-semibold text-black mb-4">Performance por Especialidade</h3>
            <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
              <div className="text-center">
                <AlertTriangle className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600">Dados de especialidades indisponíveis</p>
                <p className="text-sm text-gray-500">Aguardando dados do banco</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* AI Insights */}
      <div>
        <h2 className="text-xl font-semibold text-black mb-6">Insights Inteligentes</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {summary?.insights?.map((insight) => (
            <AIInsightCard key={insight.id || insight.title} insight={insight} />
          )) || (
            <div className="col-span-full text-center py-8 text-gray-500">
              <p>Insights não disponíveis no momento</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
