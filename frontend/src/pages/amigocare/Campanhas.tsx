import { useQuery } from '@tanstack/react-query'
import { amigoCareApi } from '@/services/api'
import KPICard from '@/components/common/KPICard'
import { MessageCircle } from 'lucide-react'

export default function Campanhas() {
  const { data: campanhas, isLoading } = useQuery({
    queryKey: ['campanhas'],
    queryFn: amigoCareApi.getCampanhas,
  })

  const kpis = [
    { name: 'Campanhas Ativas', value: campanhas?.campaigns?.filter((c: any) => c.status === 'Ativa').length || 0, unit: '', category: 'amigocare', trend_value: 2, trend_direction: 'up' as const, description: 'Campanhas em execução' },
    { name: 'Orçamento Total', value: 15000, unit: 'R$', category: 'amigocare', trend_value: 8.5, trend_direction: 'up' as const, description: 'Orçamento total das campanhas' },
    { name: 'Gasto Atual', value: 8450, unit: 'R$', category: 'amigocare', trend_value: 12.3, trend_direction: 'up' as const, description: 'Valor gasto até agora' },
    { name: 'RO<PERSON> Médio', value: 3.2, unit: 'x', category: 'amigocare', trend_value: 0.5, trend_direction: 'up' as const, description: 'Retorno sobre investimento' }
  ]

  if (isLoading) return <div className="flex items-center justify-center h-64"><div className="text-lg text-label-secondary">Carregando...</div></div>

  return (
    <div className="space-y-8">
      <div className="bg-white rounded-view p-6 border border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Campanhas</h1>
            <p className="text-label-secondary">Acompanhe campanhas de marketing e publicidade</p>
          </div>
          <MessageCircle className="w-12 h-12 text-green-600" />
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpis.map((kpi) => <KPICard key={kpi.name} metric={kpi} />)}
      </div>

      <div className="bg-white rounded-view border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Campanhas Ativas</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Nome</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Tipo</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Orçamento</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Gasto</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Impressões</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Conversões</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {campanhas?.campaigns?.slice(0, 10).map((campanha: any, index: number) => (
                <tr key={campanha.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{campanha.nome}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{campanha.tipo}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">R$ {campanha.custo.toFixed(2)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">R$ {campanha.receita.toFixed(2)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{campanha.leads_gerados}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{campanha.conversoes}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      campanha.status === 'Ativa' ? 'bg-green-100 text-green-800' :
                      campanha.status === 'Planejada' ? 'bg-blue-100 text-blue-800' :
                      campanha.status === 'Concluída' ? 'bg-gray-100 text-gray-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {campanha.status}
                    </span>
                  </td>
                </tr>
              )) || (
                <tr>
                  <td colSpan={7} className="px-6 py-8 text-center text-gray-500">
                    <p>Nenhuma campanha encontrada</p>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
