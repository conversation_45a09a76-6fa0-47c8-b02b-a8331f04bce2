import { useQuery } from '@tanstack/react-query'
import { Link } from 'react-router-dom'
import { amigoCareApi, dashboardApi } from '@/services/api'
import KPICard from '@/components/common/KPICard'
import AIInsightCard from '@/components/common/AIInsightCard'
import FilterContainer, { FilterGroup } from '@/components/common/FilterContainer'
import { usePeriodFilter } from '@/components/common/PeriodFilter'
import LineChart from '@/components/charts/LineChart'
import Pie<PERSON>hart from '@/components/charts/PieChart'
import BarChart from '@/components/charts/BarChart'
import { Heart, Star, Users, TrendingUp, MessageCircle, Target, AlertTriangle } from 'lucide-react'

export default function AmigoCareIndex() {
  // Filtros
  const { period } = usePeriodFilter('last_90_days')

  const { data: amigoCareOverview } = useQuery({
    queryKey: ['amigocare-overview', period],
    queryFn: () => amigoCareApi.getOverview(period),
  })

  const { data: campanhasData } = useQuery({
    queryKey: ['campanhas-performance', period],
    queryFn: () => amigoCareApi.getCampanhas(period),
  })

  const { data: funilData } = useQuery({
    queryKey: ['funil-vendas', period],
    queryFn: () => amigoCareApi.getFunilVendas(period),
  })

  const { data: insights } = useQuery({
    queryKey: ['insights-amigocare'],
    queryFn: () => dashboardApi.getInsights('amigocare'),
  })

  // KPIs específicos do AmigoCare com dados reais
  const amigoCareKPIs = [
    {
      id: 'total_leads',
      name: 'Total Leads',
      value: amigoCareOverview?.total_leads ?? 0,
      unit: '',
      category: 'amigocare',
      trend_value: amigoCareOverview?.crescimento_leads ?? 0,
      trend_direction: (amigoCareOverview?.crescimento_leads ?? 0) >= 0 ? 'up' as const : 'down' as const,
      description: 'Total de leads no período'
    },
    {
      id: 'leads_convertidos',
      name: 'Leads Convertidos',
      value: amigoCareOverview?.leads_convertidos ?? 0,
      unit: '',
      category: 'amigocare',
      trend_value: 15,
      trend_direction: 'up' as const,
      description: 'Leads convertidos em pacientes'
    },
    {
      id: 'taxa_conversao',
      name: 'Taxa de Conversão',
      value: amigoCareOverview?.taxa_conversao ?? 0,
      unit: '%',
      category: 'amigocare',
      trend_value: amigoCareOverview?.crescimento_conversao ?? 0,
      trend_direction: (amigoCareOverview?.crescimento_conversao ?? 0) >= 0 ? 'up' as const : 'down' as const,
      description: 'Conversão de leads em pacientes'
    },
    {
      id: 'roi_total',
      name: 'ROI Total',
      value: amigoCareOverview?.roi_total ?? 0,
      unit: '%',
      category: 'amigocare',
      trend_value: 2,
      trend_direction: 'up' as const,
      description: 'Retorno sobre investimento'
    }
  ]

  // Mock data for charts
  const npsEvolution = [
    {
      id: 'nps',
      data: [
        { x: 'Jan', y: 8.1 },
        { x: 'Fev', y: 8.3 },
        { x: 'Mar', y: 8.0 },
        { x: 'Abr', y: 8.4 },
        { x: 'Mai', y: 8.6 },
        { x: 'Jun', y: 8.7 },
      ]
    }
  ]

  const leadsPorOrigem = [
    { id: 'Google', label: 'Google', value: 35, color: '#007AFF' },
    { id: 'Facebook', label: 'Facebook', value: 28, color: '#34C759' },
    { id: 'Instagram', label: 'Instagram', value: 20, color: '#7B61FF' },
    { id: 'Indicação', label: 'Indicação', value: 17, color: '#FF9500' },
  ]

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="bg-white rounded-view p-6 border border-gray-200">
        <div className="flex flex-col md:flex-row items-center">
          <div className="md:w-2/3 mb-6 md:mb-0 md:pr-6">
            <h1 className="text-2xl font-bold text-label-DEFAULT mb-2">Bem-vindo ao Amigo Care+</h1>
            <p className="text-label-secondary mb-4">
              O módulo Amigo Care+ oferece ferramentas avançadas para acompanhamento da satisfação dos pacientes, 
              gestão de leads e campanhas de marketing, permitindo melhorar a experiência do paciente e impulsionar 
              o crescimento da clínica.
            </p>
            <div className="flex flex-wrap gap-3">
              <Link
                to="/amigocare/avaliacao-nps"
                className="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm inline-flex items-center"
              >
                <Star className="w-4 h-4 mr-2" />
                Avaliação NPS
              </Link>
              <Link
                to="/amigocare/leads"
                className="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm inline-flex items-center"
              >
                <Users className="w-4 h-4 mr-2" />
                Leads
              </Link>
              <Link
                to="/amigocare/campanhas"
                className="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm inline-flex items-center"
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Campanhas
              </Link>
            </div>
          </div>
          <div className="md:w-1/3">
            <div className="bg-systemGray-ultralight p-4 rounded-view">
              <div className="flex items-center mb-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                  <Heart className="w-5 h-5 text-systemBlue" />
                </div>
                <div>
                  <h3 className="text-sm font-semibold">Dica Rápida</h3>
                  <p className="text-xs text-label-secondary">Melhore seus resultados</p>
                </div>
              </div>
              <p className="text-sm text-label-DEFAULT">
                Mantenha um NPS acima de 8.0 para garantir alta satisfação dos pacientes e aumentar as indicações.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* KPI Cards */}
      {/* Filtros */}
      <FilterContainer>
        <FilterGroup>
          <PeriodFilter />
        </FilterGroup>
      </FilterContainer>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {amigoCareKPIs.map((kpi) => (
          <KPICard key={kpi.name} metric={kpi} />
        ))}
      </div>

      {/* Análises de Marketing Avançadas */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Funil de Vendas */}
        <div className="bg-white rounded-view p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-black mb-4">Funil de Vendas</h3>
          <p className="text-gray-600 mb-4">Conversão por etapa do funil de marketing</p>

          {funilData?.data && funilData.data.length > 0 ? (
            <div className="h-80">
              <BarChart
                data={funilData.data}
                keys={['leads']}
                indexBy="etapa"
                height={300}
                colors={['#2563eb']}
                layout="horizontal"
              />
            </div>
          ) : (
            <div className="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
              <div className="text-center">
                <AlertTriangle className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <div className="text-lg font-medium text-gray-900">Dados do Funil Indisponíveis</div>
                <div className="text-sm text-gray-500">Aguardando dados do banco</div>
              </div>
            </div>
          )}
        </div>

        {/* Performance de Campanhas */}
        <div className="bg-white rounded-view p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-black mb-4">Performance de Campanhas</h3>
          <p className="text-gray-600 mb-4">ROI e conversão por campanha</p>

          {campanhasData?.data && campanhasData.data.length > 0 ? (
            <div className="h-80">
              <LineChart
                data={[
                  {
                    id: 'ROI',
                    data: campanhasData.data.map(item => ({
                      x: item.campanha,
                      y: item.roi
                    }))
                  },
                  {
                    id: 'Conversão',
                    data: campanhasData.data.map(item => ({
                      x: item.campanha,
                      y: item.taxa_conversao
                    }))
                  }
                ]}
                height={300}
                enableArea={true}
                enablePoints={true}
                colors={['#10b981', '#f59e0b']}
                axisLeft={{
                  legend: 'Performance (%)',
                  legendOffset: -40,
                  legendPosition: 'middle'
                }}
                axisBottom={{
                  legend: 'Campanhas',
                  legendOffset: 36,
                  legendPosition: 'middle'
                }}
              />
            </div>
          ) : (
            <div className="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
              <div className="text-center">
                <AlertTriangle className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <div className="text-lg font-medium text-gray-900">Dados de Campanhas Indisponíveis</div>
                <div className="text-sm text-gray-500">Aguardando dados do banco</div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Métricas de Marketing */}
      <div className="bg-white rounded-view p-6 border border-gray-200">
        <h3 className="text-lg font-semibold text-black mb-4">Métricas de Marketing</h3>
        <p className="text-gray-600 mb-4">Indicadores chave de performance de marketing</p>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold text-blue-900">Valor Potencial</h4>
              <Target className="w-5 h-5 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-blue-600">
              R$ {(amigoCareOverview?.valor_potencial_total ?? 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </div>
            <div className="text-sm text-blue-700">Total em pipeline</div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold text-green-900">Receita Campanhas</h4>
              <TrendingUp className="w-5 h-5 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-green-600">
              R$ {(amigoCareOverview?.receita_campanhas ?? 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </div>
            <div className="text-sm text-green-700">Receita gerada</div>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold text-purple-900">Campanhas Ativas</h4>
              <Star className="w-5 h-5 text-purple-600" />
            </div>
            <div className="text-2xl font-bold text-purple-600">
              {amigoCareOverview?.campanhas_ativas ?? 0}
            </div>
            <div className="text-sm text-purple-700">Campanhas em execução</div>
          </div>

          <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold text-orange-900">Probabilidade Média</h4>
              <Heart className="w-5 h-5 text-orange-600" />
            </div>
            <div className="text-2xl font-bold text-orange-600">
              {(amigoCareOverview?.probabilidade_media ?? 0).toFixed(1)}%
            </div>
            <div className="text-sm text-orange-700">Chance de conversão</div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Link
          to="/amigocare/avaliacao-nps"
          className="bg-white rounded-view p-6 border border-gray-200 hover:shadow-md hover:border-blue-300 transition-all duration-300 group"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 group-hover:text-blue-600 transition-colors duration-300">
              Avaliação NPS
            </h3>
            <Star className="w-6 h-6 text-yellow-500" />
          </div>
          <p className="text-sm text-label-secondary mb-2">
            Monitore a satisfação dos pacientes
          </p>
          <div className="text-2xl font-bold text-gray-900">{npsData?.score_atual || 8.7}</div>
          <div className="text-xs text-label-secondary">score atual</div>
        </Link>

        <Link
          to="/amigocare/leads"
          className="bg-white rounded-view p-6 border border-gray-200 hover:shadow-md hover:border-blue-300 transition-all duration-300 group"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 group-hover:text-blue-600 transition-colors duration-300">
              Leads
            </h3>
            <Users className="w-6 h-6 text-blue-600" />
          </div>
          <p className="text-sm text-label-secondary mb-2">
            Gerencie leads e oportunidades
          </p>
          <div className="text-2xl font-bold text-gray-900">{leadsSummary?.total || 125}</div>
          <div className="text-xs text-label-secondary">leads ativos</div>
        </Link>

        <Link
          to="/amigocare/campanhas"
          className="bg-white rounded-view p-6 border border-gray-200 hover:shadow-md hover:border-blue-300 transition-all duration-300 group"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 group-hover:text-blue-600 transition-colors duration-300">
              Campanhas
            </h3>
            <MessageCircle className="w-6 h-6 text-green-600" />
          </div>
          <p className="text-sm text-label-secondary mb-2">
            Acompanhe campanhas de marketing
          </p>
          <div className="text-2xl font-bold text-gray-900">8</div>
          <div className="text-xs text-label-secondary">campanhas ativas</div>
        </Link>

        <Link
          to="/amigocare/funil-vendas"
          className="bg-white rounded-view p-6 border border-gray-200 hover:shadow-md hover:border-blue-300 transition-all duration-300 group"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 group-hover:text-blue-600 transition-colors duration-300">
              Funil de Vendas
            </h3>
            <Target className="w-6 h-6 text-purple-600" />
          </div>
          <p className="text-sm text-label-secondary mb-2">
            Analise o funil de conversão
          </p>
          <div className="text-2xl font-bold text-gray-900">23.8%</div>
          <div className="text-xs text-label-secondary">taxa de conversão</div>
        </Link>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-view p-6 border border-gray-200">
          <h3 className="text-lg font-semibold mb-4">Evolução do NPS</h3>
          <LineChart 
            data={npsEvolution} 
            height={300}
            enableArea={true}
            enablePoints={true}
            colors={['#34C759']}
          />
        </div>
        
        <div className="bg-white rounded-view p-6 border border-gray-200">
          <h3 className="text-lg font-semibold mb-4">Leads por Origem</h3>
          <PieChart 
            data={leadsPorOrigem}
            height={300}
            innerRadius={0.5}
          />
        </div>
      </div>

      {/* AI Insights */}
      {insights && insights.length > 0 && (
        <div>
          <h2 className="text-xl font-semibold mb-6">Insights do Amigo Care+</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {insights.map((insight) => (
              <AIInsightCard key={insight.id || insight.title} insight={insight} />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
