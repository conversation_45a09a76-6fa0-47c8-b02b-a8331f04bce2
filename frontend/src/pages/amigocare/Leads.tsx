import { useQuery } from '@tanstack/react-query'
import { amigoCareApi } from '@/services/api'
import KPICard from '@/components/common/KPICard'
import PieChart from '@/components/charts/PieChart'
import { Users } from 'lucide-react'

export default function Leads() {
  const { data: leads, isLoading } = useQuery({
    queryKey: ['leads'],
    queryFn: amigoCareApi.getLeads,
  })

  const { data: leadsSummary } = useQuery({
    queryKey: ['leads-summary'],
    queryFn: amigoCareApi.getLeadsSummary,
  })

  const kpis = [
    { name: 'Total Leads', value: leadsSummary?.total || 125, unit: '', category: 'amigocare', trend_value: 15, trend_direction: 'up' as const, description: 'Total de leads' },
    { name: 'Novos Leads', value: leadsSummary?.novos || 28, unit: '', category: 'amigocare', trend_value: 8, trend_direction: 'up' as const, description: 'Leads novos' },
    { name: 'Taxa Conversão', value: leadsSummary?.taxa_conversao || 23.8, unit: '%', category: 'amigocare', trend_value: -2.1, trend_direction: 'down' as const, description: 'Taxa de conversão' },
    { name: 'Valor Potencial', value: leadsSummary?.valor_potencial_total || 45230, unit: 'R$', category: 'amigocare', trend_value: 18.5, trend_direction: 'up' as const, description: 'Valor potencial total' }
  ]

  const leadsPorOrigem = leadsSummary?.por_origem?.map((origem: any) => ({
    id: origem.origem,
    label: origem.origem,
    value: origem.quantidade,
    color: ['#007AFF', '#34C759', '#7B61FF', '#FF9500'][Math.floor(Math.random() * 4)]
  })) || []

  if (isLoading) return <div className="flex items-center justify-center h-64"><div className="text-lg text-label-secondary">Carregando...</div></div>

  return (
    <div className="space-y-8">
      <div className="bg-white rounded-view p-6 border border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Leads</h1>
            <p className="text-label-secondary">Gerencie e acompanhe leads e oportunidades</p>
          </div>
          <Users className="w-12 h-12 text-systemBlue" />
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpis.map((kpi) => <KPICard key={kpi.name} metric={kpi} />)}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-view p-6 border border-gray-200">
          <h3 className="text-lg font-semibold mb-4">Leads por Origem</h3>
          <PieChart data={leadsPorOrigem} height={300} innerRadius={0.5} />
        </div>
        
        <div className="bg-white rounded-view border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold">Performance por Origem</h3>
          </div>
          <div className="p-6">
            {leadsSummary?.por_origem?.map((origem: any, index: number) => (
              <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                <span className="text-sm font-medium text-gray-900">{origem.origem}</span>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">{origem.quantidade} leads</div>
                  <div className="text-xs text-green-600">{origem.conversao}% conversão</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="bg-white rounded-view border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Lista de Leads</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Nome</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Origem</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Interesse</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Valor Potencial</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                  <p>Lista detalhada de leads não disponível</p>
                  <p className="text-sm mt-1">Use os dados de resumo acima para análise</p>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
