import { useQuery } from '@tanstack/react-query'
import { Link } from 'react-router-dom'
import { pacienteApi, dashboardApi } from '@/services/api'
import KPICard from '@/components/common/KPICard'
import AIInsightCard from '@/components/common/AIInsightCard'
import FilterContainer, { FilterGroup } from '@/components/common/FilterContainer'
import { usePeriodFilter } from '@/components/common/PeriodFilter'
import Pie<PERSON>hart from '@/components/charts/PieChart'
import BarChart from '@/components/charts/BarChart'
import { Users, Heart, FileText, CreditCard, AlertTriangle } from 'lucide-react'

export default function PacienteIndex() {
  // Filtros
  const { period, PeriodFilter } = usePeriodFilter('last_90_days')

  const { data: pacienteOverview, isLoading } = useQuery({
    queryKey: ['paciente-overview', period],
    queryFn: () => pacienteApi.getOverview(period),
  })

  const { data: demografiaData } = useQuery({
    queryKey: ['paciente-demografia', period],
    queryFn: () => pacienteApi.getDemografico(period),
  })

  const { data: fidelidadeData } = useQuery({
    queryKey: ['paciente-fidelidade', period],
    queryFn: () => pacienteApi.getFidelidade(period),
  })

  const { data: insights } = useQuery({
    queryKey: ['insights-pacientes'],
    queryFn: () => dashboardApi.getInsights('paciente'),
  })

  const pacienteKPIs = [
    {
      id: 'pacientes_atendidos',
      name: 'Pacientes Atendidos',
      value: pacienteOverview?.pacientes_atendidos ?? 0,
      unit: '',
      category: 'paciente',
      trend_value: pacienteOverview?.crescimento_pacientes ?? 0,
      trend_direction: (pacienteOverview?.crescimento_pacientes ?? 0) >= 0 ? 'up' as const : 'down' as const,
      description: 'Pacientes únicos atendidos'
    },
    {
      id: 'pacientes_novos',
      name: 'Pacientes Novos',
      value: pacienteOverview?.pacientes_novos ?? 0,
      unit: '',
      category: 'paciente',
      trend_value: pacienteOverview?.crescimento_pacientes ?? 0,
      trend_direction: (pacienteOverview?.crescimento_pacientes ?? 0) >= 0 ? 'up' as const : 'down' as const,
      description: 'Novos pacientes no período'
    },
    {
      id: 'taxa_retencao',
      name: 'Taxa Retenção',
      value: pacienteOverview?.taxa_retencao ?? 0,
      unit: '%',
      category: 'paciente',
      trend_value: 8,
      trend_direction: (pacienteOverview?.taxa_retencao ?? 0) > 30 ? 'up' as const : 'down' as const,
      description: 'Taxa de retenção de pacientes'
    },
    {
      id: 'ticket_medio',
      name: 'Ticket Médio',
      value: pacienteOverview?.ticket_medio_paciente ?? 0,
      unit: 'R$',
      category: 'paciente',
      trend_value: 3.2,
      trend_direction: 'up' as const,
      description: 'Valor médio por paciente'
    }
  ]

  return (
    <div className="space-y-8">
      <div className="bg-white rounded-view p-8 border border-gray-200">
        <div className="flex flex-col md:flex-row items-center">
          <div className="md:w-2/3">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Módulo Paciente</h1>
            <p className="text-lg text-label-secondary mb-6">Acompanhe atendimentos realizados, créditos disponíveis e gerencie orçamentos.</p>
            <div className="flex space-x-4">
              <Link to="/paciente/atendimentos-realizados" className="bg-systemBlue hover:bg-blue-600 text-white px-6 py-2 rounded-full transition duration-150 ease-in-out">Atendimentos</Link>
              <Link to="/paciente/creditos-disponiveis" className="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-6 py-2 rounded-full transition duration-150 ease-in-out border border-systemGray-lightest">Créditos</Link>
            </div>
          </div>
          <div className="md:w-1/3 mt-6 md:mt-0 flex justify-center">
            <div className="w-48 h-48 bg-blue-100 rounded-full flex items-center justify-center">
              <Users className="w-24 h-24 text-systemBlue" />
            </div>
          </div>
        </div>
      </div>

      {/* Filtros */}
      <FilterContainer>
        <FilterGroup>
          <PeriodFilter />
        </FilterGroup>
      </FilterContainer>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {pacienteKPIs.map((kpi) => <KPICard key={kpi.id} metric={kpi} />)}
      </div>

      {/* Análises Demográficas e Fidelidade */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Demografia de Pacientes */}
        <div className="bg-white rounded-view p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-black mb-4">Demografia de Pacientes</h3>
          <p className="text-gray-600 mb-4">Distribuição por faixa etária e gênero</p>

          {demografiaData?.data && demografiaData.data.length > 0 ? (
            <div className="h-80">
              <PieChart
                data={demografiaData.data}
                height={300}
                innerRadius={0.6}
                colors={['#2563eb', '#10b981', '#f59e0b', '#ef4444']}
              />
            </div>
          ) : (
            <div className="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
              <div className="text-center">
                <AlertTriangle className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <div className="text-lg font-medium text-gray-900">Dados Demográficos Indisponíveis</div>
                <div className="text-sm text-gray-500">Aguardando dados do banco</div>
              </div>
            </div>
          )}
        </div>

        {/* Análise de Fidelidade */}
        <div className="bg-white rounded-view p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-black mb-4">Análise de Fidelidade</h3>
          <p className="text-gray-600 mb-4">Frequência de retorno e valor por paciente</p>

          {fidelidadeData?.data && fidelidadeData.data.length > 0 ? (
            <div className="h-80">
              <BarChart
                data={fidelidadeData.data}
                keys={['pacientes']}
                indexBy="categoria"
                height={300}
                colors={['#2563eb']}
                layout="vertical"
              />
            </div>
          ) : (
            <div className="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
              <div className="text-center">
                <AlertTriangle className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <div className="text-lg font-medium text-gray-900">Dados de Fidelidade Indisponíveis</div>
                <div className="text-sm text-gray-500">Aguardando dados do banco</div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Segmentação de Pacientes */}
      <div className="bg-white rounded-view p-6 border border-gray-200">
        <h3 className="text-lg font-semibold text-black mb-4">Segmentação de Pacientes</h3>
        <p className="text-gray-600 mb-4">Análise de valor e comportamento dos pacientes</p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold text-blue-900">Pacientes Retornaram</h4>
              <Users className="w-5 h-5 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {pacienteOverview?.pacientes_retornaram ?? 0}
            </div>
            <div className="text-sm text-blue-700">Pacientes que retornaram</div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold text-green-900">Total Atendimentos</h4>
              <Users className="w-5 h-5 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-green-600">
              {pacienteOverview?.total_atendimentos ?? 0}
            </div>
            <div className="text-sm text-green-700">Atendimentos realizados</div>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold text-yellow-900">Atend./Paciente</h4>
              <Users className="w-5 h-5 text-yellow-600" />
            </div>
            <div className="text-2xl font-bold text-yellow-600">
              {pacienteOverview?.atendimentos_por_paciente ?? 0}
            </div>
            <div className="text-sm text-yellow-700">Média por paciente</div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Link to="/paciente/atendimentos-realizados" className="bg-white rounded-view p-6 border border-gray-200 hover:shadow-md hover:border-blue-300 transition-all duration-300 group">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 group-hover:text-blue-600 transition-colors duration-300">Atendimentos</h3>
            <Heart className="w-6 h-6 text-red-500" />
          </div>
          <p className="text-sm text-label-secondary mb-2">Atendimentos realizados</p>
          <div className="text-2xl font-bold text-gray-900">156</div>
          <div className="text-xs text-label-secondary">este mês</div>
        </Link>

        <Link to="/paciente/creditos-disponiveis" className="bg-white rounded-view p-6 border border-gray-200 hover:shadow-md hover:border-blue-300 transition-all duration-300 group">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 group-hover:text-blue-600 transition-colors duration-300">Créditos</h3>
            <CreditCard className="w-6 h-6 text-green-600" />
          </div>
          <p className="text-sm text-label-secondary mb-2">Créditos disponíveis</p>
          <div className="text-2xl font-bold text-gray-900">R$ 6.230,00</div>
          <div className="text-xs text-label-secondary">valor total</div>
        </Link>

        <Link to="/paciente/orcamentos-abertos" className="bg-white rounded-view p-6 border border-gray-200 hover:shadow-md hover:border-blue-300 transition-all duration-300 group">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 group-hover:text-blue-600 transition-colors duration-300">Orçamentos Abertos</h3>
            <FileText className="w-6 h-6 text-blue-600" />
          </div>
          <p className="text-sm text-label-secondary mb-2">Orçamentos pendentes</p>
          <div className="text-2xl font-bold text-gray-900">23</div>
          <div className="text-xs text-label-secondary">aguardando aprovação</div>
        </Link>

        <Link to="/paciente/orcamentos-fechados" className="bg-white rounded-view p-6 border border-gray-200 hover:shadow-md hover:border-blue-300 transition-all duration-300 group">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 group-hover:text-blue-600 transition-colors duration-300">Orçamentos Fechados</h3>
            <FileText className="w-6 h-6 text-purple-600" />
          </div>
          <p className="text-sm text-label-secondary mb-2">Orçamentos finalizados</p>
          <div className="text-2xl font-bold text-gray-900">18</div>
          <div className="text-xs text-label-secondary">este mês</div>
        </Link>
      </div>
    </div>
  )
}
