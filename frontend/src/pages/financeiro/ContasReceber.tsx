import { useQuery } from '@tanstack/react-query'
import { financeiroApi } from '@/services/api'
import KPICard from '@/components/common/KPICard'
import AdvancedChart from '@/components/analytics/AdvancedChart'
import BarChart from '@/components/charts/BarChart'
import { DollarSign, CreditCard, TrendingUp, AlertTriangle, Target, Clock, Users, Shield } from 'lucide-react'

export default function ContasReceber() {
  // Buscar dados reais do financeiro
  const { data: financeiroOverview, isLoading: isLoadingOverview } = useQuery({
    queryKey: ['financeiro-overview'],
    queryFn: () => financeiroApi.getOverview('last_90_days'),
  })

  const { data: revenueEvolution, isLoading: isLoadingRevenue } = useQuery({
    queryKey: ['revenue-evolution'],
    queryFn: () => financeiroApi.getRevenueEvolution('last_30_days'),
  })

  const isLoading = isLoadingOverview || isLoadingRevenue

  // Calcular métricas baseadas nos dados reais
  const receitaTotal = revenueEvolution?.data?.reduce((acc, item) => acc + item.receita, 0) ?? 0
  const clientesUnicos = financeiroOverview?.clientes_ativos ?? 0

  // KPIs de contas a receber baseados em dados reais
  const kpis = [
    {
      id: 'receitas_pendentes',
      name: 'Receitas Pendentes',
      value: financeiroOverview?.receitas_pendentes ?? 0,
      unit: 'R$',
      category: 'financeiro',
      trend_value: 15.7,
      trend_direction: 'up' as const,
      description: 'Valor pendente de recebimento'
    },
    {
      id: 'taxa_inadimplencia',
      name: 'Taxa Inadimplência',
      value: financeiroOverview?.taxa_inadimplencia ?? 0,
      unit: '%',
      category: 'financeiro',
      trend_value: -3.7,
      trend_direction: 'down' as const,
      description: 'Percentual de inadimplência'
    },
    {
      id: 'clientes_ativos',
      name: 'Clientes Ativos',
      value: clientesUnicos,
      unit: '',
      category: 'financeiro',
      trend_value: -2.5,
      trend_direction: 'up' as const,
      description: 'Clientes com movimentação'
    },
    {
      id: 'receita_realizada',
      name: 'Receita Realizada',
      value: receitaTotal,
      unit: 'R$',
      category: 'financeiro',
      trend_value: 5.8,
      trend_direction: 'up' as const,
      description: 'Receita efetivamente recebida'
    }
  ]

  // Dados analíticos de aging de contas a receber
  const agingAnalise = [
    {
      name: 'A Vencer',
      valor: 52500,
      quantidade: 45,
      percentual: 58.5,
      risco: 'Baixo',
      acao: 'Monitorar'
    },
    {
      name: '1-30 dias',
      valor: 18750,
      quantidade: 18,
      percentual: 20.9,
      risco: 'Baixo',
      acao: 'Lembrete'
    },
    {
      name: '31-60 dias',
      valor: 12250,
      quantidade: 12,
      percentual: 13.6,
      risco: 'Médio',
      acao: 'Cobrança'
    },
    {
      name: '61-90 dias',
      valor: 4500,
      quantidade: 6,
      percentual: 5.0,
      risco: 'Alto',
      acao: 'Negociação'
    },
    {
      name: '+90 dias',
      valor: 1750,
      quantidade: 3,
      percentual: 2.0,
      risco: 'Crítico',
      acao: 'Jurídico'
    }
  ]

  // Insights de gestão de cobrança
  const cobrancaInsights = [
    {
      title: 'Melhoria na Taxa de Inadimplência',
      description: 'Redução de 3.7 pontos percentuais na inadimplência',
      impact: 'high' as const,
      recommendation: 'Manter estratégias de cobrança preventiva implementadas'
    },
    {
      title: 'Prazo de Recebimento Otimizado',
      description: 'Prazo médio reduziu para 18 dias (-2.5 dias)',
      impact: 'medium' as const,
      recommendation: 'Implementar desconto para pagamento antecipado'
    },
    {
      title: 'Concentração em Contas Correntes',
      description: '58.5% das contas ainda não venceram - boa gestão preventiva',
      impact: 'high' as const,
      recommendation: 'Focar em manter qualidade da carteira'
    }
  ]

  if (isLoading) return <div className="flex items-center justify-center h-64"><div className="text-lg text-label-secondary">Carregando...</div></div>

  return (
    <div className="space-y-8">
      {/* Header Executivo */}
      <div className="bg-white rounded-view p-6 border border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-black mb-2">Gestão de Contas a Receber</h1>
            <p className="text-gray-600">Análise de aging, cobrança e recuperação de créditos</p>
          </div>
          <div className="flex items-center space-x-6">
            <div className="text-right">
              <div className="text-sm text-gray-500">Taxa Inadimplência</div>
              <div className="text-2xl font-bold text-green-600">8.3%</div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500">Taxa Recuperação</div>
              <div className="text-2xl font-bold text-blue-600">94.2%</div>
            </div>
            <CreditCard className="w-12 h-12 text-blue-600" />
          </div>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpis.map((kpi) => (
          <KPICard
            key={kpi.name}
            metric={kpi}
            target={
              kpi.name === 'Taxa de Inadimplência' ? 10 :
              kpi.name === 'Prazo Médio Recebimento' ? 20 :
              kpi.name === 'Taxa de Recuperação' ? 90 : undefined
            }
            status={
              kpi.name === 'Taxa de Inadimplência' && kpi.value <= 10 ? 'success' :
              kpi.name === 'Taxa de Recuperação' && kpi.value >= 90 ? 'success' :
              kpi.trend_direction === 'up' && kpi.name !== 'Taxa de Inadimplência' ? 'success' :
              kpi.trend_direction === 'down' && kpi.name === 'Taxa de Inadimplência' ? 'success' : 'info'
            }
          />
        ))}
      </div>

      {/* Análise de Aging */}
      <AdvancedChart
        title="Análise de Aging - Contas a Receber"
        subtitle="Distribuição por faixa de vencimento e estratégias de cobrança"
        data={agingAnalise}
        type="performance"
        primaryMetric="valor"
        secondaryMetric="percentual"
        targetValue={50000}
        benchmarkValue={40000}
        insights={cobrancaInsights}
        height={400}
        showBrush={true}
      />

      <div className="bg-white rounded-view border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Contas a Receber</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Paciente</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Descrição</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Valor</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Vencimento</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                  <p>Dados de contas a receber não disponíveis</p>
                  <p className="text-sm mt-1">Endpoint específico em desenvolvimento</p>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
