import axios, { AxiosError } from 'axios'
import type { DashboardSummary, KPIMetric, AIInsight, ChartData, Agendamento, Lead, NPSData } from '@/types'

const api = axios.create({
  baseURL: '/api/v1',
  timeout: 15000, // Aumentado para 15 segundos
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
})

// Interceptor para requests
api.interceptors.request.use(
  (config) => {
    // Log requests em desenvolvimento
    if (import.meta.env.DEV) {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, config.params)
    }
    return config
  },
  (error) => {
    console.error('❌ Request Error:', error)
    return Promise.reject(error)
  }
)

// Interceptor para responses
api.interceptors.response.use(
  (response) => {
    // Log responses em desenvolvimento
    if (import.meta.env.DEV) {
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, response.data)
    }
    return response
  },
  (error: AxiosError) => {
    // Log errors
    console.error('❌ API Error:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      message: error.message,
      data: error.response?.data
    })

    // Tratamento específico para diferentes tipos de erro
    if (error.response?.status === 503) {
      console.warn('🔧 Serviço temporariamente indisponível')
    } else if (error.code === 'ERR_NETWORK') {
      console.warn('🌐 Erro de rede - verificar conexão')
    } else if (error.code === 'ECONNABORTED') {
      console.warn('⏱️ Timeout na requisição')
    }

    return Promise.reject(error)
  }
)

// Dashboard API
export const dashboardApi = {
  getSummary: (periodType = 'last_90_days'): Promise<DashboardSummary> =>
    api.get('/dashboard/overview', { params: { period_type: periodType } }).then(res => res.data),

  getKPIs: (periodType = 'last_90_days'): Promise<KPIMetric[]> =>
    api.get('/dashboard/overview', { params: { period_type: periodType } }).then(res => res.data.kpis),

  getInsights: (_module?: string, _page?: string): Promise<AIInsight[]> =>
    api.get('/dashboard/overview', { params: { period_type: 'last_90_days' } }).then(res => res.data.insights ?? []),

  getYearOverYear: (metric = 'receita') =>
    api.get('/dashboard/year-over-year', { params: { metric } }).then(res => res.data),

  getHeatmap: (periodType = 'last_90_days') =>
    api.get('/dashboard/heatmap', { params: { period_type: periodType } }).then(res => res.data),

  getCharts: (_module?: string, _page?: string): Promise<ChartData[]> =>
    api.get('/dashboard/overview').then(_res => []), // Placeholder para compatibilidade
}

// Agenda API
export const agendaApi = {
  getOverview: (periodType = 'last_30_days') =>
    api.get('/agenda/overview', { params: { period_type: periodType } }).then(res => res.data),

  getAgendamentos: (periodType = 'last_30_days'): Promise<Agendamento[]> =>
    api.get('/agenda/overview', { params: { period_type: periodType } }).then(_res => []), // Placeholder

  getOcupacaoSemanal: (periodType = 'last_30_days') =>
    api.get('/agenda/analytics/ocupacao-semanal', { params: { period_type: periodType } }).then(res => res.data),

  getRankingProfissionais: (periodType = 'last_90_days', limit = 20) =>
    api.get('/agenda/ranking-profissionais', { params: { period_type: periodType, limit } }).then(res => res.data),

  getCancelamentos: (periodType = 'last_90_days') =>
    api.get('/agenda/cancelamentos', { params: { period_type: periodType } }).then(res => res.data),

  // Métodos legados para compatibilidade
  getAgendamentosHoje: () =>
    api.get('/agenda/overview', { params: { period_type: 'today' } }).then(res => res.data),

  getProducaoMedica: () =>
    api.get('/agenda/ranking-profissionais', { params: { period_type: 'last_90_days' } }).then(res => res.data),

  getTempoAtendimento: () =>
    api.get('/agenda/overview', { params: { period_type: 'last_30_days' } }).then(res => res.data),
}

// AmigoCare API
export const amigoCareApi = {
  getOverview: (periodType = 'last_90_days') =>
    api.get('/amigocare', { params: { period_type: periodType } }).then(res => res.data),

  getFunilVendas: (periodType = 'last_90_days') =>
    api.get('/amigocare/funil-vendas', { params: { period_type: periodType } }).then(res => res.data),

  getCampanhas: (periodType = 'last_90_days') =>
    api.get('/amigocare/campanhas', { params: { period_type: periodType } }).then(res => res.data),

  getFontesLeads: (periodType = 'last_90_days') =>
    api.get('/amigocare/fontes-leads', { params: { period_type: periodType } }).then(res => res.data),

  getTimelineConversoes: (periodType = 'last_12_months') =>
    api.get('/amigocare/timeline-conversoes', { params: { period_type: periodType } }).then(res => res.data),

  // Métodos legados para compatibilidade
  getLeads: (periodType = 'last_90_days'): Promise<Lead[]> =>
    api.get('/amigocare', { params: { period_type: periodType } }).then(res => res.data),

  getLeadsSummary: () =>
    api.get('/amigocare', { params: { period_type: 'last_90_days' } }).then(res => res.data),

  getNPSData: (): Promise<NPSData> =>
    api.get('/amigocare/overview', { params: { period_type: 'last_90_days' } }).then(_res => ({ nps_score: 0, total_respostas: 0, promotores: 0, neutros: 0, detratores: 0 })), // Placeholder

  getAcompanhamentoPacientes: () =>
    api.get('/amigocare/overview', { params: { period_type: 'last_90_days' } }).then(res => res.data),
}

// Financeiro API
export const financeiroApi = {
  getOverview: (periodType = 'last_90_days') =>
    api.get('/financeiro', { params: { period_type: periodType } }).then(res => res.data),

  getFluxoCaixa: (periodType = 'last_12_months') =>
    api.get('/financeiro/fluxo-caixa', { params: { period_type: periodType } }).then(res => res.data),

  getRevenueEvolution: (periodType = 'last_90_days') =>
    api.get('/financeiro/analytics/revenue-evolution', { params: { period_type: periodType } }).then(res => res.data),

  getContasReceberAging: (referenceDate?: string) =>
    api.get('/financeiro/contas-receber/aging', { params: { reference_date: referenceDate } }).then(res => res.data),

  getFormasPagamento: (periodType = 'last_90_days') =>
    api.get('/financeiro/formas-pagamento', { params: { period_type: periodType } }).then(res => res.data),

  // Métodos legados para compatibilidade
  getContasReceber: () =>
    api.get('/financeiro/contas-receber/aging').then(res => res.data),

  getContasPagar: () =>
    api.get('/financeiro', { params: { period_type: 'last_90_days' } }).then(res => res.data),

  getFechamentoCaixa: () =>
    api.get('/financeiro', { params: { period_type: 'today' } }).then(res => res.data),
}

// Paciente API
export const pacienteApi = {
  getOverview: (periodType = 'last_90_days') =>
    api.get('/pacientes/overview', { params: { period_type: periodType } }).then(res => res.data),

  getDemografico: (periodType = 'last_90_days') =>
    api.get('/pacientes/demografico', { params: { period_type: periodType } }).then(res => res.data),

  getFidelidade: (periodType = 'last_12_months') =>
    api.get('/pacientes/fidelidade', { params: { period_type: periodType } }).then(res => res.data),

  getRanking: (periodType = 'last_12_months', limit = 50) =>
    api.get('/pacientes/ranking', { params: { period_type: periodType, limit } }).then(res => res.data),

  // Métodos legados para compatibilidade
  getAtendimentosRealizados: () =>
    api.get('/pacientes/overview', { params: { period_type: 'last_90_days' } }).then(res => res.data),

  getCreditosDisponiveis: () =>
    api.get('/pacientes/overview', { params: { period_type: 'last_90_days' } }).then(_res => ({ total_creditos: 0 })), // Placeholder

  getOrcamentosAbertos: () =>
    api.get('/pacientes/overview', { params: { period_type: 'last_90_days' } }).then(_res => ({ total: 0, valor_total: 0 })), // Placeholder

  getOrcamentosFechados: () =>
    api.get('/pacientes/overview', { params: { period_type: 'last_90_days' } }).then(_res => ({ total_mes: 0, valor_aprovado: 0 })), // Placeholder
}
