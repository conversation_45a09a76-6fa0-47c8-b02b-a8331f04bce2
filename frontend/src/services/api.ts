import axios from 'axios'
import type { DashboardSummary, KPIMetric, AIInsight, ChartData, Agendamento, Lead, NPSData } from '@/types'

const api = axios.create({
  baseURL: '/api/v1',
  timeout: 10000,
})

// Dashboard API
export const dashboardApi = {
  getSummary: (periodType = 'last_90_days'): Promise<DashboardSummary> =>
    api.get('/dashboard/overview', { params: { period_type: periodType } }).then(res => res.data),

  getKPIs: (periodType = 'last_90_days'): Promise<KPIMetric[]> =>
    api.get('/dashboard/overview', { params: { period_type: periodType } }).then(res => res.data.kpis),

  getInsights: (module?: string, page?: string): Promise<AIInsight[]> =>
    api.get('/dashboard/overview', { params: { period_type: 'last_90_days' } }).then(res => res.data.insights ?? []),

  getYearOverYear: (metric = 'receita') =>
    api.get('/dashboard/year-over-year', { params: { metric } }).then(res => res.data),

  getHeatmap: (periodType = 'last_90_days') =>
    api.get('/dashboard/heatmap', { params: { period_type: periodType } }).then(res => res.data),

  getCharts: (module?: string, page?: string): Promise<ChartData[]> =>
    api.get('/dashboard/overview').then(res => []), // Placeholder para compatibilidade
}

// Agenda API
export const agendaApi = {
  getOverview: (periodType = 'last_30_days') =>
    api.get('/agenda/overview', { params: { period_type: periodType } }).then(res => res.data),

  getAgendamentos: (periodType = 'last_30_days'): Promise<Agendamento[]> =>
    api.get('/agenda/overview', { params: { period_type: periodType } }).then(res => []), // Placeholder

  getOcupacaoSemanal: (periodType = 'last_30_days') =>
    api.get('/agenda/analytics/ocupacao-semanal', { params: { period_type: periodType } }).then(res => res.data),

  getRankingProfissionais: (periodType = 'last_90_days', limit = 20) =>
    api.get('/agenda/ranking-profissionais', { params: { period_type: periodType, limit } }).then(res => res.data),

  getCancelamentos: (periodType = 'last_90_days') =>
    api.get('/agenda/cancelamentos', { params: { period_type: periodType } }).then(res => res.data),

  // Métodos legados para compatibilidade
  getAgendamentosHoje: () =>
    api.get('/agenda/overview', { params: { period_type: 'today' } }).then(res => res.data),

  getProducaoMedica: () =>
    api.get('/agenda/ranking-profissionais', { params: { period_type: 'last_90_days' } }).then(res => res.data),

  getTempoAtendimento: () =>
    api.get('/agenda/overview', { params: { period_type: 'last_30_days' } }).then(res => res.data),
}

// AmigoCare API
export const amigoCareApi = {
  getOverview: (periodType = 'last_90_days') =>
    api.get('/amigocare', { params: { period_type: periodType } }).then(res => res.data),

  getFunilVendas: (periodType = 'last_90_days') =>
    api.get('/amigocare/funil-vendas', { params: { period_type: periodType } }).then(res => res.data),

  getCampanhas: (periodType = 'last_90_days') =>
    api.get('/amigocare/campanhas', { params: { period_type: periodType } }).then(res => res.data),

  getFontesLeads: (periodType = 'last_90_days') =>
    api.get('/amigocare/fontes-leads', { params: { period_type: periodType } }).then(res => res.data),

  getTimelineConversoes: (periodType = 'last_12_months') =>
    api.get('/amigocare/timeline-conversoes', { params: { period_type: periodType } }).then(res => res.data),

  // Métodos legados para compatibilidade
  getLeads: (periodType = 'last_90_days'): Promise<Lead[]> =>
    api.get('/amigocare', { params: { period_type: periodType } }).then(res => res.data),

  getLeadsSummary: () =>
    api.get('/amigocare', { params: { period_type: 'last_90_days' } }).then(res => res.data),

  getNPSData: (): Promise<NPSData> =>
    api.get('/amigocare/overview', { params: { period_type: 'last_90_days' } }).then(res => ({ nps_score: 0, total_respostas: 0, promotores: 0, neutros: 0, detratores: 0 })), // Placeholder

  getAcompanhamentoPacientes: () =>
    api.get('/amigocare/overview', { params: { period_type: 'last_90_days' } }).then(res => res.data),
}

// Financeiro API
export const financeiroApi = {
  getOverview: (periodType = 'last_90_days') =>
    api.get('/financeiro', { params: { period_type: periodType } }).then(res => res.data),

  getFluxoCaixa: (periodType = 'last_12_months') =>
    api.get('/financeiro/fluxo-caixa', { params: { period_type: periodType } }).then(res => res.data),

  getRevenueEvolution: (periodType = 'last_90_days') =>
    api.get('/financeiro/revenue-evolution', { params: { period_type: periodType } }).then(res => res.data),

  getContasReceberAging: (referenceDate?: string) =>
    api.get('/financeiro/contas-receber/aging', { params: { reference_date: referenceDate } }).then(res => res.data),

  getFormasPagamento: (periodType = 'last_90_days') =>
    api.get('/financeiro/formas-pagamento', { params: { period_type: periodType } }).then(res => res.data),

  // Métodos legados para compatibilidade
  getContasReceber: () =>
    api.get('/financeiro/contas-receber/aging').then(res => res.data),

  getContasPagar: () =>
    api.get('/financeiro', { params: { period_type: 'last_90_days' } }).then(res => res.data),

  getFechamentoCaixa: () =>
    api.get('/financeiro', { params: { period_type: 'today' } }).then(res => res.data),
}

// Paciente API
export const pacienteApi = {
  getOverview: (periodType = 'last_90_days') =>
    api.get('/pacientes/overview', { params: { period_type: periodType } }).then(res => res.data),

  getDemografico: (periodType = 'last_90_days') =>
    api.get('/pacientes/demografico', { params: { period_type: periodType } }).then(res => res.data),

  getFidelidade: (periodType = 'last_12_months') =>
    api.get('/pacientes/fidelidade', { params: { period_type: periodType } }).then(res => res.data),

  getRanking: (periodType = 'last_12_months', limit = 50) =>
    api.get('/pacientes/ranking', { params: { period_type: periodType, limit } }).then(res => res.data),

  // Métodos legados para compatibilidade
  getAtendimentosRealizados: () =>
    api.get('/pacientes/overview', { params: { period_type: 'last_90_days' } }).then(res => res.data),

  getCreditosDisponiveis: () =>
    api.get('/pacientes/overview', { params: { period_type: 'last_90_days' } }).then(res => ({ total_creditos: 0 })), // Placeholder

  getOrcamentosAbertos: () =>
    api.get('/pacientes/overview', { params: { period_type: 'last_90_days' } }).then(res => ({ total: 0, valor_total: 0 })), // Placeholder

  getOrcamentosFechados: () =>
    api.get('/pacientes/overview', { params: { period_type: 'last_90_days' } }).then(res => ({ total_mes: 0, valor_aprovado: 0 })), // Placeholder
}
