import type { Meta, StoryObj } from '@storybook/react'
import LineChart from './LineChart'

const meta = {
  title: 'Components/Charts/LineChart',
  component: LineChart,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
# LineChart Component

Componente de gráfico de linha responsivo usando Recharts com:
- Múltiplas séries de dados
- Tooltips interativos
- Grid customizável
- Cores personalizáveis
- Responsividade automática

## Uso

\`\`\`tsx
<LineChart
  data={[
    { name: 'Jan', value: 400 },
    { name: 'Fev', value: 300 },
    { name: 'Mar', value: 500 }
  ]}
  dataKey="value"
  color="#2563eb"
  height={300}
/>
\`\`\`
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    data: {
      control: 'object',
      description: 'Array de dados para o gráfico',
    },
    dataKey: {
      control: 'text',
      description: 'Chave dos dados para o eixo Y',
    },
    color: {
      control: 'color',
      description: 'Cor da linha',
    },
    height: {
      control: 'number',
      description: 'Altura do gráfico em pixels',
    },
  },
} satisfies Meta<typeof LineChart>

export default meta
type Story = StoryObj<typeof meta>

// Dados de exemplo
const sampleData = [
  { name: 'Jan', value: 400, value2: 240 },
  { name: 'Fev', value: 300, value2: 139 },
  { name: 'Mar', value: 500, value2: 980 },
  { name: 'Abr', value: 278, value2: 390 },
  { name: 'Mai', value: 189, value2: 480 },
  { name: 'Jun', value: 239, value2: 380 },
]

const revenueData = [
  { name: '01/06', receita: 1200, acumulada: 1200 },
  { name: '02/06', receita: 1800, acumulada: 3000 },
  { name: '03/06', receita: 950, acumulada: 3950 },
  { name: '04/06', receita: 2100, acumulada: 6050 },
  { name: '05/06', receita: 1650, acumulada: 7700 },
  { name: '06/06', receita: 2300, acumulada: 10000 },
]

const atendimentosData = [
  { name: 'Seg', realizados: 12, cancelados: 3 },
  { name: 'Ter', realizados: 15, cancelados: 2 },
  { name: 'Qua', realizados: 18, cancelados: 4 },
  { name: 'Qui', realizados: 14, cancelados: 1 },
  { name: 'Sex', realizados: 20, cancelados: 5 },
  { name: 'Sab', realizados: 8, cancelados: 2 },
]

// Story básica
export const Default: Story = {
  args: {
    data: sampleData,
    dataKey: 'value',
    color: '#2563eb',
    height: 300,
  },
}

// Gráfico de Receita
export const Receita: Story = {
  args: {
    data: revenueData,
    dataKey: 'receita',
    color: '#059669',
    height: 400,
  },
}

// Gráfico de Atendimentos
export const Atendimentos: Story = {
  args: {
    data: atendimentosData,
    dataKey: 'realizados',
    color: '#dc2626',
    height: 350,
  },
}

// Gráfico Pequeno
export const Pequeno: Story = {
  args: {
    data: sampleData.slice(0, 4),
    dataKey: 'value',
    color: '#7c3aed',
    height: 200,
  },
}

// Gráfico com Muitos Dados
export const MuitosDados: Story = {
  args: {
    data: Array.from({ length: 30 }, (_, i) => ({
      name: `Dia ${i + 1}`,
      value: Math.floor(Math.random() * 1000) + 100,
    })),
    dataKey: 'value',
    color: '#ea580c',
    height: 400,
  },
}

// Múltiplos Gráficos
export const MultiplosGraficos: Story = {
  render: () => (
    <div className="space-y-6 p-4">
      <div>
        <h3 className="text-lg font-semibold mb-2">Receita Diária</h3>
        <LineChart
          data={revenueData}
          dataKey="receita"
          color="#059669"
          height={250}
        />
      </div>
      <div>
        <h3 className="text-lg font-semibold mb-2">Atendimentos por Dia</h3>
        <LineChart
          data={atendimentosData}
          dataKey="realizados"
          color="#dc2626"
          height={250}
        />
      </div>
    </div>
  ),
}

// Dados Vazios
export const DadosVazios: Story = {
  args: {
    data: [],
    dataKey: 'value',
    color: '#6b7280',
    height: 300,
  },
}

// Dados com Valores Zero
export const ValoresZero: Story = {
  args: {
    data: [
      { name: 'Jan', value: 0 },
      { name: 'Fev', value: 100 },
      { name: 'Mar', value: 0 },
      { name: 'Abr', value: 200 },
      { name: 'Mai', value: 0 },
    ],
    dataKey: 'value',
    color: '#f59e0b',
    height: 300,
  },
}

// Cores Diferentes
export const CoresDiferentes: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-4 p-4">
      <div>
        <h4 className="font-medium mb-2">Azul</h4>
        <LineChart
          data={sampleData.slice(0, 4)}
          dataKey="value"
          color="#2563eb"
          height={200}
        />
      </div>
      <div>
        <h4 className="font-medium mb-2">Verde</h4>
        <LineChart
          data={sampleData.slice(0, 4)}
          dataKey="value"
          color="#059669"
          height={200}
        />
      </div>
      <div>
        <h4 className="font-medium mb-2">Vermelho</h4>
        <LineChart
          data={sampleData.slice(0, 4)}
          dataKey="value"
          color="#dc2626"
          height={200}
        />
      </div>
      <div>
        <h4 className="font-medium mb-2">Roxo</h4>
        <LineChart
          data={sampleData.slice(0, 4)}
          dataKey="value"
          color="#7c3aed"
          height={200}
        />
      </div>
    </div>
  ),
}
