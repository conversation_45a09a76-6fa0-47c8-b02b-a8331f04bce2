import type { Meta, StoryObj } from '@storybook/react'
import BarChart from './BarChart'

const meta = {
  title: 'Components/Charts/BarChart',
  component: Bar<PERSON><PERSON>,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
# BarChart Component

Componente de gráfico de barras responsivo usando Recharts com:
- Barras verticais ou horizontais
- Múltiplas séries de dados
- Tooltips interativos
- Cores personalizáveis
- Responsividade automática

## Uso

\`\`\`tsx
<BarChart
  data={[
    { name: 'Jan', value: 400 },
    { name: 'Fev', value: 300 },
    { name: 'Mar', value: 500 }
  ]}
  dataKey="value"
  color="#2563eb"
  height={300}
/>
\`\`\`
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    data: {
      control: 'object',
      description: 'Array de dados para o gráfico',
    },
    dataKey: {
      control: 'text',
      description: 'Chave dos dados para o eixo Y',
    },
    color: {
      control: 'color',
      description: 'Cor das barras',
    },
    height: {
      control: 'number',
      description: 'Altura do gráfico em pixels',
    },
  },
} satisfies Meta<typeof BarChart>

export default meta
type Story = StoryObj<typeof meta>

// Dados de exemplo
const ocupacaoData = [
  { name: 'Segunda', atendimentos: 12, cancelamentos: 3 },
  { name: 'Terça', atendimentos: 15, cancelamentos: 2 },
  { name: 'Quarta', atendimentos: 18, cancelamentos: 4 },
  { name: 'Quinta', atendimentos: 14, cancelamentos: 1 },
  { name: 'Sexta', atendimentos: 20, cancelamentos: 5 },
  { name: 'Sábado', atendimentos: 8, cancelamentos: 2 },
]

const profissionaisData = [
  { name: 'Dr. Silva', atendimentos: 45, receita: 18000 },
  { name: 'Dra. Santos', atendimentos: 38, receita: 15200 },
  { name: 'Dr. Costa', atendimentos: 42, receita: 16800 },
  { name: 'Dra. Lima', atendimentos: 35, receita: 14000 },
  { name: 'Dr. Oliveira', atendimentos: 40, receita: 16000 },
]

const demografiaData = [
  { name: '0-18 anos', quantidade: 45 },
  { name: '19-30 anos', quantidade: 78 },
  { name: '31-50 anos', quantidade: 125 },
  { name: '51-65 anos', quantidade: 89 },
  { name: '65+ anos', quantidade: 67 },
]

const campanhasData = [
  { name: 'Google Ads', leads: 45, conversoes: 12 },
  { name: 'Facebook', leads: 38, conversoes: 8 },
  { name: 'Instagram', leads: 52, conversoes: 15 },
  { name: 'Indicação', leads: 28, conversoes: 18 },
  { name: 'Site', leads: 35, conversoes: 9 },
]

// Story básica
export const Default: Story = {
  args: {
    data: ocupacaoData,
    dataKey: 'atendimentos',
    color: '#2563eb',
    height: 300,
  },
}

// Gráfico de Profissionais
export const Profissionais: Story = {
  args: {
    data: profissionaisData,
    dataKey: 'atendimentos',
    color: '#059669',
    height: 350,
  },
}

// Gráfico de Demografia
export const Demografia: Story = {
  args: {
    data: demografiaData,
    dataKey: 'quantidade',
    color: '#dc2626',
    height: 300,
  },
}

// Gráfico de Campanhas
export const Campanhas: Story = {
  args: {
    data: campanhasData,
    dataKey: 'leads',
    color: '#7c3aed',
    height: 350,
  },
}

// Gráfico Pequeno
export const Pequeno: Story = {
  args: {
    data: demografiaData.slice(0, 3),
    dataKey: 'quantidade',
    color: '#f59e0b',
    height: 200,
  },
}

// Gráfico Alto
export const Alto: Story = {
  args: {
    data: ocupacaoData,
    dataKey: 'atendimentos',
    color: '#06b6d4',
    height: 500,
  },
}

// Múltiplos Gráficos
export const MultiplosGraficos: Story = {
  render: () => (
    <div className="space-y-6 p-4">
      <div>
        <h3 className="text-lg font-semibold mb-2">Atendimentos por Dia</h3>
        <BarChart
          data={ocupacaoData}
          dataKey="atendimentos"
          color="#2563eb"
          height={250}
        />
      </div>
      <div>
        <h3 className="text-lg font-semibold mb-2">Demografia de Pacientes</h3>
        <BarChart
          data={demografiaData}
          dataKey="quantidade"
          color="#059669"
          height={250}
        />
      </div>
    </div>
  ),
}

// Dados com Valores Altos
export const ValoresAltos: Story = {
  args: {
    data: [
      { name: 'Janeiro', value: 125000 },
      { name: 'Fevereiro', value: 98000 },
      { name: 'Março', value: 145000 },
      { name: 'Abril', value: 112000 },
      { name: 'Maio', value: 167000 },
    ],
    dataKey: 'value',
    color: '#059669',
    height: 350,
  },
}

// Dados Vazios
export const DadosVazios: Story = {
  args: {
    data: [],
    dataKey: 'value',
    color: '#6b7280',
    height: 300,
  },
}

// Cores Diferentes
export const CoresDiferentes: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-4 p-4">
      <div>
        <h4 className="font-medium mb-2">Azul</h4>
        <BarChart
          data={demografiaData.slice(0, 3)}
          dataKey="quantidade"
          color="#2563eb"
          height={200}
        />
      </div>
      <div>
        <h4 className="font-medium mb-2">Verde</h4>
        <BarChart
          data={demografiaData.slice(0, 3)}
          dataKey="quantidade"
          color="#059669"
          height={200}
        />
      </div>
      <div>
        <h4 className="font-medium mb-2">Vermelho</h4>
        <BarChart
          data={demografiaData.slice(0, 3)}
          dataKey="quantidade"
          color="#dc2626"
          height={200}
        />
      </div>
      <div>
        <h4 className="font-medium mb-2">Roxo</h4>
        <BarChart
          data={demografiaData.slice(0, 3)}
          dataKey="quantidade"
          color="#7c3aed"
          height={200}
        />
      </div>
    </div>
  ),
}
