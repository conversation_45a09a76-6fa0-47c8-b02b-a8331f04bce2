/**
 * Componente para exibir mensagens amigáveis quando o banco não está disponível
 * Exibe mensagens de erro apropriadas quando dados não estão disponíveis
 */

import React, { useState, useEffect } from 'react';
import { AlertTriangle, Database, RefreshCw, Wifi, WifiOff } from 'lucide-react';

interface DatabaseStatus {
  can_load_data: boolean;
  status: 'operational' | 'database_offline' | 'no_data';
  user_message: string;
  technical_message?: string;
  action: string;
  show_retry_button: boolean;
  timestamp: string;
}

interface DatabaseErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

const DatabaseErrorBoundary: React.FC<DatabaseErrorBoundaryProps> = ({ 
  children, 
  fallback 
}) => {
  const [dbStatus, setDbStatus] = useState<DatabaseStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [retryCount, setRetryCount] = useState(0);

  const checkDatabaseStatus = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/v1/health/frontend-status');
      
      if (response.ok) {
        const status: DatabaseStatus = await response.json();
        setDbStatus(status);
      } else {
        // Se o endpoint de health falhar, assume que o banco está offline
        setDbStatus({
          can_load_data: false,
          status: 'database_offline',
          user_message: 'Sistema temporariamente indisponível',
          technical_message: 'Não foi possível verificar status do banco',
          action: 'Tente novamente em alguns minutos',
          show_retry_button: true,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Erro ao verificar status do banco:', error);
      setDbStatus({
        can_load_data: false,
        status: 'database_offline',
        user_message: 'Sistema temporariamente indisponível',
        technical_message: 'Erro de conexão com o servidor',
        action: 'Verifique sua conexão e tente novamente',
        show_retry_button: true,
        timestamp: new Date().toISOString()
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkDatabaseStatus();
  }, [retryCount]);

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Verificando status do sistema...</p>
        </div>
      </div>
    );
  }

  // Se o banco está operacional, renderiza os children normalmente
  if (dbStatus?.can_load_data) {
    return <>{children}</>;
  }

  // Se há um fallback customizado, usa ele
  if (fallback) {
    return <>{fallback}</>;
  }

  // Renderiza mensagem de erro baseada no status
  const getErrorIcon = () => {
    switch (dbStatus?.status) {
      case 'database_offline':
        return <WifiOff className="w-16 h-16 text-red-500" />;
      case 'no_data':
        return <Database className="w-16 h-16 text-yellow-500" />;
      default:
        return <AlertTriangle className="w-16 h-16 text-orange-500" />;
    }
  };

  const getErrorColor = () => {
    switch (dbStatus?.status) {
      case 'database_offline':
        return 'border-red-200 bg-red-50';
      case 'no_data':
        return 'border-yellow-200 bg-yellow-50';
      default:
        return 'border-orange-200 bg-orange-50';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className={`max-w-md w-full ${getErrorColor()} border rounded-lg p-8 text-center`}>
        <div className="mb-6">
          {getErrorIcon()}
        </div>
        
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          {dbStatus?.user_message || 'Sistema Indisponível'}
        </h1>
        
        <p className="text-gray-600 mb-6">
          {dbStatus?.action || 'Tente novamente mais tarde'}
        </p>
        
        {dbStatus?.technical_message && (
          <div className="bg-white border border-gray-200 rounded p-3 mb-6">
            <p className="text-sm text-gray-500">
              <strong>Detalhes técnicos:</strong> {dbStatus.technical_message}
            </p>
          </div>
        )}
        
        {dbStatus?.show_retry_button && (
          <button
            onClick={handleRetry}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Tentar Novamente
          </button>
        )}
        
        <div className="mt-8 pt-6 border-t border-gray-200">
          <p className="text-xs text-gray-400">
            DataHub Clinic - Sistema de Análise Médica
          </p>
          <p className="text-xs text-gray-400 mt-1">
            Última verificação: {new Date(dbStatus?.timestamp || '').toLocaleTimeString()}
          </p>
        </div>
      </div>
    </div>
  );
};

export default DatabaseErrorBoundary;
