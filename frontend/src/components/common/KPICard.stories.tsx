import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { TrendingUp, TrendingDown, Users, DollarSign, Calendar, Heart } from 'lucide-react'
import KPICard from './KPICard'

const meta = {
  title: 'Components/Common/KPICard',
  component: KPICard,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
# KPICard Component

Componente para exibir KPIs (Key Performance Indicators) com suporte a:
- Valores numéricos formatados
- Trends com direção e percentual
- Ícones personalizáveis
- Categorias com cores específicas
- Tooltips informativos

## Uso

\`\`\`tsx
<KPICard
  name="Total Atendimentos"
  value={150}
  unit=""
  category="agenda"
  trend_value={8.5}
  trend_direction="up"
  description="Total de consultas realizadas"
/>
\`\`\`
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    name: {
      control: 'text',
      description: 'Nome do KPI',
    },
    value: {
      control: 'number',
      description: 'Valor numérico do KPI',
    },
    unit: {
      control: 'text',
      description: 'Unidade do valor (R$, %, etc)',
    },
    category: {
      control: 'select',
      options: ['dashboard', 'agenda', 'financeiro', 'paciente', 'amigocare'],
      description: 'Categoria que define a cor do card',
    },
    trend_value: {
      control: 'number',
      description: 'Valor da tendência em percentual',
    },
    trend_direction: {
      control: 'select',
      options: ['up', 'down', 'stable'],
      description: 'Direção da tendência',
    },
    description: {
      control: 'text',
      description: 'Descrição do KPI para tooltip',
    },
  },
} satisfies Meta<typeof KPICard>

export default meta
type Story = StoryObj<typeof meta>

// Story básica
export const Default: Story = {
  args: {
    name: 'Total Atendimentos',
    value: 150,
    unit: '',
    category: 'agenda',
    trend_value: 8.5,
    trend_direction: 'up',
    description: 'Total de consultas realizadas no período',
  },
}

// KPI Financeiro
export const Financeiro: Story = {
  args: {
    name: 'Receita Total',
    value: 45750.80,
    unit: 'R$',
    category: 'financeiro',
    trend_value: 15.3,
    trend_direction: 'up',
    description: 'Receita total do período',
  },
}

// KPI com Trend Negativo
export const TrendNegativo: Story = {
  args: {
    name: 'Taxa Cancelamento',
    value: 12.5,
    unit: '%',
    category: 'agenda',
    trend_value: -3.2,
    trend_direction: 'down',
    description: 'Percentual de consultas canceladas',
  },
}

// KPI Pacientes
export const Pacientes: Story = {
  args: {
    name: 'Pacientes Únicos',
    value: 340,
    unit: '',
    category: 'paciente',
    trend_value: 12.8,
    trend_direction: 'up',
    description: 'Número de pacientes únicos atendidos',
  },
}

// KPI AmigoCare
export const AmigoCare: Story = {
  args: {
    name: 'Taxa Conversão',
    value: 23.4,
    unit: '%',
    category: 'amigocare',
    trend_value: 5.7,
    trend_direction: 'up',
    description: 'Taxa de conversão de leads em pacientes',
  },
}

// KPI sem Trend
export const SemTrend: Story = {
  args: {
    name: 'Profissionais Ativos',
    value: 25,
    unit: '',
    category: 'dashboard',
    description: 'Número de profissionais com atendimentos',
  },
}

// Valores Grandes
export const ValoresGrandes: Story = {
  args: {
    name: 'Faturamento Anual',
    value: 1250000,
    unit: 'R$',
    category: 'financeiro',
    trend_value: 18.9,
    trend_direction: 'up',
    description: 'Faturamento total do ano',
  },
}

// Valores Decimais
export const ValoresDecimais: Story = {
  args: {
    name: 'Ticket Médio',
    value: 387.65,
    unit: 'R$',
    category: 'financeiro',
    trend_value: 2.1,
    trend_direction: 'up',
    description: 'Valor médio por atendimento',
  },
}

// Todas as Categorias
export const TodasCategorias: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-4 p-4">
      <KPICard
        name="Dashboard"
        value={100}
        unit=""
        category="dashboard"
        trend_value={5}
        trend_direction="up"
        description="Categoria dashboard"
      />
      <KPICard
        name="Agenda"
        value={200}
        unit=""
        category="agenda"
        trend_value={-2}
        trend_direction="down"
        description="Categoria agenda"
      />
      <KPICard
        name="Financeiro"
        value={300}
        unit="R$"
        category="financeiro"
        trend_value={8}
        trend_direction="up"
        description="Categoria financeiro"
      />
      <KPICard
        name="Paciente"
        value={400}
        unit=""
        category="paciente"
        trend_value={3}
        trend_direction="up"
        description="Categoria paciente"
      />
      <KPICard
        name="AmigoCare"
        value={500}
        unit="%"
        category="amigocare"
        trend_value={12}
        trend_direction="up"
        description="Categoria amigocare"
      />
    </div>
  ),
}
