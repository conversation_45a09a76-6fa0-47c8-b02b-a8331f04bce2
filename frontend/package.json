{"name": "datahub-clinic-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@nivo/bar": "^0.83.0", "@nivo/core": "^0.83.0", "@nivo/line": "^0.83.0", "@nivo/pie": "^0.83.0", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/blocks": "^8.6.14", "@storybook/react-vite": "^8.6.14", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "storybook": "^8.6.14", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}