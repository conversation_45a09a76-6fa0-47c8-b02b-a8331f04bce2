#!/bin/bash

# Script para testar todas as páginas e endpoints do sistema
# Verifica se frontend e backend estão funcionando corretamente

echo "🧪 Testando DataHub Clinic - Sistema Completo"
echo "=============================================="

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# URLs base
BACKEND_URL="http://localhost:8000"
FRONTEND_URL="http://localhost:3001"

# Função para testar endpoint
test_endpoint() {
    local url=$1
    local description=$2
    local expected_status=${3:-200}
    
    echo -n "  Testing $description... "
    
    response=$(curl -s -w "%{http_code}" -o /tmp/response.json "$url")
    status_code="${response: -3}"
    
    if [ "$status_code" -eq "$expected_status" ]; then
        echo -e "${GREEN}✓ OK${NC} (${status_code})"
        return 0
    else
        echo -e "${RED}✗ FAIL${NC} (${status_code})"
        if [ -f /tmp/response.json ]; then
            echo "    Response: $(cat /tmp/response.json | head -c 200)..."
        fi
        return 1
    fi
}

# Função para testar se serviço está rodando
test_service() {
    local url=$1
    local service_name=$2
    
    echo -n "🔍 Checking $service_name... "
    
    if curl -s --connect-timeout 5 "$url" > /dev/null; then
        echo -e "${GREEN}✓ Running${NC}"
        return 0
    else
        echo -e "${RED}✗ Not running${NC}"
        return 1
    fi
}

# Testar serviços básicos
echo -e "\n${BLUE}1. Verificando Serviços Base${NC}"
test_service "$BACKEND_URL/health" "Backend (FastAPI)"
backend_running=$?

test_service "$FRONTEND_URL" "Frontend (Vite)"
frontend_running=$?

if [ $backend_running -ne 0 ] || [ $frontend_running -ne 0 ]; then
    echo -e "\n${RED}❌ Serviços não estão rodando. Inicie backend e frontend primeiro.${NC}"
    exit 1
fi

# Testar endpoints de health
echo -e "\n${BLUE}2. Testando Health Checks${NC}"
test_endpoint "$BACKEND_URL/health" "Backend Health"
test_endpoint "$BACKEND_URL/api/v1/health/" "API Health"
test_endpoint "$BACKEND_URL/api/v1/health/frontend-status" "Frontend Status Check"
test_endpoint "$FRONTEND_URL/api/v1/health/frontend-status" "Frontend Proxy Health"

# Testar endpoints do Dashboard
echo -e "\n${BLUE}3. Testando Dashboard APIs${NC}"
test_endpoint "$FRONTEND_URL/api/v1/dashboard/overview?period_type=last_90_days" "Dashboard Overview"
test_endpoint "$FRONTEND_URL/api/v1/dashboard/year-over-year?metric=receita" "Year over Year"
test_endpoint "$FRONTEND_URL/api/v1/dashboard/heatmap?period_type=last_90_days" "Specialties Heatmap"

# Testar endpoints da Agenda
echo -e "\n${BLUE}4. Testando Agenda APIs${NC}"
test_endpoint "$FRONTEND_URL/api/v1/agenda/overview?period_type=last_30_days" "Agenda Overview"
test_endpoint "$FRONTEND_URL/api/v1/agenda/analytics/ocupacao-semanal?period_type=last_30_days" "Ocupação Semanal"
test_endpoint "$FRONTEND_URL/api/v1/agenda/ranking-profissionais?period_type=last_90_days&limit=20" "Ranking Profissionais"
test_endpoint "$FRONTEND_URL/api/v1/agenda/cancelamentos?period_type=last_90_days" "Cancelamentos"

# Testar endpoints do Financeiro
echo -e "\n${BLUE}5. Testando Financeiro APIs${NC}"
test_endpoint "$FRONTEND_URL/api/v1/financeiro?period_type=last_90_days" "Financeiro Overview"
test_endpoint "$FRONTEND_URL/api/v1/financeiro/fluxo-caixa?period_type=last_12_months" "Fluxo de Caixa"
test_endpoint "$FRONTEND_URL/api/v1/financeiro/revenue-evolution?period_type=last_90_days" "Revenue Evolution"
test_endpoint "$FRONTEND_URL/api/v1/financeiro/contas-receber/aging" "Contas a Receber Aging"
test_endpoint "$FRONTEND_URL/api/v1/financeiro/formas-pagamento?period_type=last_90_days" "Formas de Pagamento"

# Testar endpoints de Pacientes
echo -e "\n${BLUE}6. Testando Pacientes APIs${NC}"
test_endpoint "$FRONTEND_URL/api/v1/pacientes/overview?period_type=last_90_days" "Pacientes Overview"
test_endpoint "$FRONTEND_URL/api/v1/pacientes/demografico?period_type=last_90_days" "Demografia"
test_endpoint "$FRONTEND_URL/api/v1/pacientes/fidelidade?period_type=last_12_months" "Fidelidade"
test_endpoint "$FRONTEND_URL/api/v1/pacientes/ranking?period_type=last_12_months&limit=50" "Ranking Pacientes"

# Testar endpoints do AmigoCare
echo -e "\n${BLUE}7. Testando AmigoCare APIs${NC}"
test_endpoint "$FRONTEND_URL/api/v1/amigocare?period_type=last_90_days" "AmigoCare Overview"
test_endpoint "$FRONTEND_URL/api/v1/amigocare/funil-vendas?period_type=last_90_days" "Funil de Vendas"
test_endpoint "$FRONTEND_URL/api/v1/amigocare/campanhas?period_type=last_90_days" "Campanhas"
test_endpoint "$FRONTEND_URL/api/v1/amigocare/fontes-leads?period_type=last_90_days" "Fontes de Leads"
test_endpoint "$FRONTEND_URL/api/v1/amigocare/timeline-conversoes?period_type=last_12_months" "Timeline Conversões"

# Testar páginas do frontend (verificar se carregam sem erro 500)
echo -e "\n${BLUE}8. Testando Páginas Frontend${NC}"
test_endpoint "$FRONTEND_URL/" "Dashboard Page"
test_endpoint "$FRONTEND_URL/agenda" "Agenda Module"
test_endpoint "$FRONTEND_URL/financeiro" "Financeiro Module"
test_endpoint "$FRONTEND_URL/paciente" "Paciente Module"
test_endpoint "$FRONTEND_URL/amigocare" "AmigoCare Module"

echo -e "\n${GREEN}✅ Teste completo finalizado!${NC}"
echo -e "\n${YELLOW}📋 Resumo:${NC}"
echo "- Backend: $BACKEND_URL"
echo "- Frontend: $FRONTEND_URL"
echo "- Banco de dados: PostgreSQL (conectado)"
echo "- Dados: 100% reais (sem mock data)"

echo -e "\n${BLUE}🌐 Acesse o sistema em: $FRONTEND_URL${NC}"
